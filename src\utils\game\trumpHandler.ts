import { GameState, selectTrump } from "./gameLogic";
import { Suit, Rank, Card } from "./cardUtils";
import { audioManager } from "../audio/AudioManager";

export type TrumpAnnouncement = {
  suit: Suit | null;
  playerName: string;
  visible: boolean;
};

/**
 * Checks if a hand contains a Maraffa (A, 2, 3 of the same suit)
 */
const hasMaraffaForSuit = (hand: Card[], suit: Suit): boolean => {
  const hasAce = hand.some(
    (card) => card.suit === suit && card.rank === Rank.Ace
  );
  const hasTwo = hand.some(
    (card) => card.suit === suit && card.rank === Rank.Two
  );
  const hasThree = hand.some(
    (card) => card.suit === suit && card.rank === Rank.Three
  );

  return hasAce && hasTwo && hasThree;
};

/**
 * Finds the suit with <PERSON><PERSON><PERSON> if available, otherwise returns null
 */
const findMaraffaSuit = (hand: Card[]): Suit | null => {
  const suits: Suit[] = [Suit.Coins, Suit.Cups, Suit.Swords, Suit.Clubs];

  for (const suit of suits) {
    if (hasMaraffaForSuit(hand, suit)) {
      return suit;
    }
  }

  return null;
};

export const handleTrumpSelection = (
  gameState: GameState,
  difficulty: "easy" | "medium" | "hard",
  setTrumpAnnouncement: (announcement: TrumpAnnouncement) => void,
  setGameState: (state: GameState) => void
): void => {
  const suits: Suit[] = [Suit.Coins, Suit.Cups, Suit.Swords, Suit.Clubs];

  // 🎯 PRIORITÀ ASSOLUTA: Verifica se il giocatore corrente ha una Maraffa automatica rilevata
  // MA solo se NON è il giocatore umano (id 0)
  if (
    gameState.automaticMaraffa?.hasMaraffa &&
    gameState.automaticMaraffa.playerIndex === gameState.currentPlayer &&
    gameState.currentPlayer !== 0 // Solo per CPU, non per giocatore umano
  ) {
    const maraffaSuit = gameState.automaticMaraffa.maraffaSuit;
    const playerName = gameState.players[gameState.currentPlayer].name;

    console.log(
      `🎯 AI ha rilevato Maraffa automatica in ${maraffaSuit}! Seleziona automaticamente come briscola.`
    );

    setTrumpAnnouncement({
      suit: maraffaSuit,
      playerName: playerName,
      visible: true,
    });

    setTimeout(() => {
      setTrumpAnnouncement({ suit: null, playerName: "", visible: false });
      // Applica automaticamente la selezione della briscola
      import("./gameLogic").then(({ applyAutomaticMaraffa }) => {
        setGameState(applyAutomaticMaraffa(gameState));
      });
    }, 2500);
    return;
  }

  // Se è il giocatore umano, non fare nulla e lascia che scelga manualmente
  if (gameState.currentPlayer === 0) {
    return; // Il giocatore umano sceglierà tramite UI
  }

  if (difficulty !== "easy") {
    const cpuHand = gameState.players[gameState.currentPlayer].hand;

    // PRIORITÀ SECONDARIA: Controlla manualmente se ha una Maraffa (fallback per sicurezza)
    const maraffaSuit = findMaraffaSuit(cpuHand);
    if (maraffaSuit) {
      console.log(
        `🎯 AI ha trovato Maraffa manualmente in ${maraffaSuit}! Seleziona automaticamente come briscola.`
      );

      const playerName = gameState.players[gameState.currentPlayer].name;
      setTrumpAnnouncement({
        suit: maraffaSuit,
        playerName: playerName,
        visible: true,
      });

      setTimeout(() => {
        setTrumpAnnouncement({ suit: null, playerName: "", visible: false });
        setGameState(selectTrump(gameState, maraffaSuit));
      }, 2500);
      return;
    }

    // Se non ha Maraffa, usa la logica esistente basata sul numero di carte per seme
    const suitCounts: Record<string, number> = {
      [Suit.Coins]: 0,
      [Suit.Cups]: 0,
      [Suit.Swords]: 0,
      [Suit.Clubs]: 0,
    };

    cpuHand.forEach((card) => {
      suitCounts[card.suit]++;
    });

    let bestSuit = Suit.Coins;
    let maxCount = 0;

    for (const [suit, count] of Object.entries(suitCounts)) {
      if (count > maxCount) {
        maxCount = count;
        bestSuit = suit as Suit;
      }
    }

    const playerName = gameState.players[gameState.currentPlayer].name;
    setTrumpAnnouncement({
      suit: bestSuit,
      playerName: playerName,
      visible: true,
    });

    setTimeout(() => {
      setTrumpAnnouncement({ suit: null, playerName: "", visible: false });
      setGameState(selectTrump(gameState, bestSuit));
    }, 2500);
    return;
  }

  // Modalità facile: SEMPRE riconoscere la Maraffa manualmente (100% probabilità) - fallback
  const cpuHand = gameState.players[gameState.currentPlayer].hand;
  const maraffaSuit = findMaraffaSuit(cpuHand);

  if (maraffaSuit) {
    console.log(
      `🎯 AI Easy ha trovato Maraffa manualmente in ${maraffaSuit}! (100% probabilità)`
    );

    const playerName = gameState.players[gameState.currentPlayer].name;
    setTrumpAnnouncement({
      suit: maraffaSuit,
      playerName: playerName,
      visible: true,
    });

    setTimeout(() => {
      setTrumpAnnouncement({ suit: null, playerName: "", visible: false });
      // Applica automaticamente la selezione della briscola
      import("./gameLogic").then(({ applyAutomaticMaraffa }) => {
        setGameState(applyAutomaticMaraffa(gameState));
      });
    }, 2500);
    return;
  }

  // Se non ha Maraffa, selezione basata sulla frequenza dei semi (modalità facile)
  const suitCounts: Record<string, number> = {
    [Suit.Coins]: 0,
    [Suit.Cups]: 0,
    [Suit.Swords]: 0,
    [Suit.Clubs]: 0,
  };

  cpuHand.forEach((card) => {
    suitCounts[card.suit]++;
  });

  let bestSuit = Suit.Coins;
  let maxCount = 0;

  for (const [suit, count] of Object.entries(suitCounts)) {
    if (count > maxCount) {
      maxCount = count;
      bestSuit = suit as Suit;
    }
  }

  const playerName = gameState.players[gameState.currentPlayer].name;
  setTrumpAnnouncement({
    suit: bestSuit,
    playerName: playerName,
    visible: true,
  });

  setTimeout(() => {
    setTrumpAnnouncement({ suit: null, playerName: "", visible: false });
    setGameState(selectTrump(gameState, bestSuit));
  }, 2500);
};

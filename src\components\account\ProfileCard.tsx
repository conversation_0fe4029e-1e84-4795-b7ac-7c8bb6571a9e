import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Check } from "lucide-react";
import LocalStatsDisplay from "@/components/profile/LocalStatsDisplay";
import AuthenticationSection from "./AuthenticationSection";
import {
  getProgressToNextLevel,
  type PlayerStats,
} from "@/services/localStatsService";
import { getXpRequiredForLevel } from "@/services/experienceSystem";

interface ProfileCardProps {
  accountInfo: {
    name: string;
    level: number;
    xp: number;
    nextLevelXp: number;
    rankImage: string;
    titleDescription: string;
    isOnline: boolean;
  };
  playerStats: PlayerStats;
  isEditingName: boolean;
  tempName: string;
  userStateLoading: boolean;
  authLoading: boolean;
  authUser: any;
  isLoggedIn: boolean;
  isSyncing: boolean;
  onEditName: () => void;
  onSaveName: () => void;
  onCancelEdit: () => void;
  onNameChange: (value: string) => void;
  onNameKeyPress: (e: React.KeyboardEvent) => void;
  onLogout: () => void;
  onGoogleLogin: () => void;
  onFacebookLogin: () => void;
  onRankRoadmapOpen: () => void;
}

const ProfileCard = ({
  accountInfo,
  playerStats,
  isEditingName,
  tempName,
  userStateLoading,
  authLoading,
  authUser,
  isLoggedIn,
  isSyncing,
  onEditName,
  onSaveName,
  onCancelEdit,
  onNameChange,
  onNameKeyPress,
  onLogout,
  onGoogleLogin,
  onFacebookLogin,
  onRankRoadmapOpen,
}: ProfileCardProps) => {
  return (
    <Card className="border-2 border-amber-800/30 shadow-md bg-amber-50/80 backdrop-blur-sm">
      <CardContent className="p-5">
        <div className="flex items-center gap-3 sm:gap-5 mb-4">
          <div className="relative flex-shrink-0">
            <div
              className="h-18 w-18 sm:h-20 sm:w-20 md:h-24 md:w-24 border-3 border-amber-800/60 shadow-lg rounded-full bg-gradient-to-br from-amber-100 to-yellow-100 flex items-center justify-center cursor-pointer hover:scale-110 hover:rotate-12 transition-all duration-300 group"
              onClick={onRankRoadmapOpen}
            >
              <img
                src={accountInfo.rankImage}
                alt={accountInfo.name}
                className="w-12 h-12 sm:w-12 sm:h-12 md:w-14 md:h-14 object-contain transform transition-transform duration-300 group-hover:scale-110"
              />
            </div>
            {/* Badge livello sovrapposto */}
            <div className="absolute -top-1 -right-1 sm:-top-2 sm:-right-2 bg-gradient-to-r from-amber-600 to-yellow-500 text-white px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-full text-xs font-bold shadow-lg border-2 border-white">
              <span className="text-xs">LV</span> {accountInfo.level}
            </div>
          </div>
          <div className="flex-1">
            {/* Titolo giocatore con badge integrato */}
            <div className="mb-3">
              {isEditingName ? (
                // Modalità editing nome
                <div className="space-y-2">
                  <Input
                    value={tempName}
                    onChange={(e) => onNameChange(e.target.value)}
                    onKeyDown={onNameKeyPress}
                    className="text-base sm:text-lg font-bold bg-white border-amber-300 focus:border-amber-500"
                    placeholder="Inserisci il tuo nome"
                    autoFocus
                    maxLength={18}
                  />
                  <div className="flex gap-2">
                    <Button
                      onClick={onSaveName}
                      size="sm"
                      className="bg-green-600 hover:bg-green-700 text-white text-xs"
                    >
                      <Check className="h-3 w-3 mr-1" />
                      Salva
                    </Button>
                    <Button
                      onClick={onCancelEdit}
                      size="sm"
                      variant="outline"
                      className="text-xs"
                    >
                      Annulla
                    </Button>
                  </div>
                </div>
              ) : (
                // Modalità visualizzazione nome
                <div
                  className={`text-lg sm:text-xl font-bold text-amber-900 flex items-center gap-1 sm:gap-2 ${
                    accountInfo.isOnline
                      ? "cursor-pointer hover:text-amber-700 transition-colors group"
                      : ""
                  }`}
                  onClick={accountInfo.isOnline ? onEditName : undefined}
                  title={
                    accountInfo.isOnline
                      ? "Clicca per modificare il nome"
                      : undefined
                  }
                  style={{
                    maxWidth: "100%",
                    wordBreak: "break-word",
                    whiteSpace: "pre-line",
                    lineHeight: "1.2",
                  }}
                >
                  <span
                    className="pr-1 sm:pr-2 text-balance text-left w-full block"
                    style={{
                      display: "block",
                      wordBreak: "break-word",
                      whiteSpace: "normal",
                      lineHeight: "1.2",
                      overflowWrap: "break-word",
                    }}
                  >
                    {accountInfo.name}
                  </span>
                  {accountInfo.isOnline && (
                    <span className="text-sm sm:text-base opacity-60 group-hover:opacity-100 transition-opacity flex-shrink-0">
                      ✏️
                    </span>
                  )}
                </div>
              )}
              <div className="text-xs text-amber-700 italic bg-amber-100/50 px-3 py-1 rounded-full inline-block border border-amber-200">
                {accountInfo.titleDescription}
              </div>
            </div>
            {/* Barra XP con design migliorato */}
            <div className="bg-white/70 rounded-lg p-3 border border-amber-200 shadow-md">
              <div className="flex justify-between items-center mb-2">
                <span className="text-xs font-semibold text-amber-800">
                  Esperienza
                </span>
                <span className="text-xs font-bold text-amber-900">
                  {accountInfo.xp - getXpRequiredForLevel(accountInfo.level)}/
                  {accountInfo.nextLevelXp} XP
                </span>
              </div>
              <div className="relative h-4 bg-gradient-to-r from-amber-200 to-yellow-200 rounded-full overflow-hidden shadow-inner border border-amber-300/70">
                <div
                  className="h-full bg-gradient-to-r from-amber-500 via-yellow-500 to-orange-500 rounded-full transition-all duration-1000 ease-out relative overflow-hidden"
                  style={{
                    width: `${getProgressToNextLevel(
                      accountInfo.xp,
                      accountInfo.level
                    )}%`,
                  }}
                >
                  {/* Effetto shimmer animato */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent animate-pulse"></div>
                  {/* Highlight superiore */}
                  <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-yellow-300 to-orange-300 rounded-full opacity-80"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* Statistiche integrate */}
        <div className="border-t border-amber-200 pt-4">
          <LocalStatsDisplay stats={playerStats} />
        </div>
        {/* Sezione Accesso Online integrata */}
        <div className="border-t border-amber-200 pt-4 mt-4">
          <AuthenticationSection
            userStateLoading={userStateLoading}
            authLoading={authLoading}
            authUser={authUser}
            isLoggedIn={isLoggedIn}
            isSyncing={isSyncing}
            onLogout={onLogout}
            onGoogleLogin={onGoogleLogin}
            onFacebookLogin={onFacebookLogin}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileCard;

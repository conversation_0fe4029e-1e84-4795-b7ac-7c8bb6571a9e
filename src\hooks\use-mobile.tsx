
import * as React from "react"

const MOBILE_BREAKPOINT = 768

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean>(
    typeof window !== 'undefined' ? window.innerWidth < MOBILE_BREAKPOINT : false
  )

  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }
    
    // Set on mount
    checkMobile()
    
    // Add event listener
    window.addEventListener('resize', checkMobile)
    
    // Cleanup
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  return isMobile
}

// Mobile game layout hook for game-specific adjustments
export interface GameMobileLayoutStyles {
  gameContainer: string;
  gameBoard: string; 
  playerInfo: string;
  actionArea: string;
  cardsArea: string;
  cardScale: "xs" | "sm" | "md" | "lg"; // Updated to use specific string literals
}

export function useGameMobileLayout() {
  const isMobile = useIsMobile()
  
  // Game-specific styles for mobile
  const styles = React.useMemo<GameMobileLayoutStyles>(() => {
    if (!isMobile) {
      return {
        gameContainer: "",
        gameBoard: "",
        playerInfo: "",
        actionArea: "",
        cardsArea: "",
        cardScale: "md" // Default scale for desktop
      }
    }
    
    return {
      gameContainer: "flex flex-col pt-2",
      gameBoard: "order-1 mt-2",
      playerInfo: "order-3 flex justify-around mt-2",
      actionArea: "order-2",
      cardsArea: "order-4",
      cardScale: "sm" // Smaller card size for mobile
    }
  }, [isMobile])
  
  return { isMobile, styles }
}

var n=Object.defineProperty;var c=(a,e,t)=>e in a?n(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t;var i=(a,e,t)=>c(a,typeof e!="symbol"?e+"":e,t);class h{constructor(){i(this,"cache",new Map);i(this,"DEFAULT_TTL",5*60*1e3);i(this,"SHORT_TTL",2*60*1e3);i(this,"LONG_TTL",10*60*1e3)}set(e,t,s){const r={data:t,timestamp:Date.now(),ttl:s||this.DEFAULT_TTL};this.cache.set(e,r)}get(e){const t=this.cache.get(e);return t?Date.now()-t.timestamp>t.ttl?(this.cache.delete(e),null):t.data:null}isValid(e){const t=this.cache.get(e);return t?Date.now()-t.timestamp<=t.ttl:!1}delete(e){this.cache.delete(e)}cleanup(){const e=Date.now();for(const[t,s]of this.cache.entries())e-s.timestamp>s.ttl&&this.cache.delete(t)}clear(){this.cache.clear()}getStats(){const e=Date.now();let t=0,s=0;for(const r of this.cache.values())e-r.timestamp<=r.ttl?t++:s++;return{totalEntries:this.cache.size,validEntries:t,expiredEntries:s}}setFriends(e){this.set("friends",e,this.DEFAULT_TTL)}getFriends(){return this.get("friends")}setLeaderboard(e){this.set("leaderboard",e,this.LONG_TTL)}getLeaderboard(){return this.get("leaderboard")}setSearchResults(e,t){this.set(`search:${e.toLowerCase()}`,t,this.SHORT_TTL)}getSearchResults(e){return this.get(`search:${e.toLowerCase()}`)}setProfile(e,t){this.set(`profile:${e}`,t,this.DEFAULT_TTL)}getProfile(e){return this.get(`profile:${e}`)}setStats(e,t){this.set(`stats:${e}`,t,this.DEFAULT_TTL)}getUserStats(e){return this.get(`stats:${e}`)}}const o=new h;setInterval(()=>{o.cleanup()},10*60*1e3);export{o as default,o as unifiedCache};

# 🍎 iOS Setup Commands

## Required Package Installations

```bash
# Install iOS platform
npm install @capacitor/ios

# Install iOS-specific plugins
npm install capacitor-app-tracking-transparency
npm install capacitor-sign-in-with-apple

# Add iOS platform
npx cap add ios

# Sync and update
npx cap sync ios
npx cap update ios
```

## Required Environment Variables

Add these to your `.env` file:

```env
# iOS AdMob
VITE_ADMOB_IOS_APP_ID=ca-app-pub-XXXXXXXXXXXXXXXX~XXXXXXXXXX
VITE_ADMOB_IOS_BANNER_UNIT_ID=ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX

# Apple OAuth (get from Apple Developer Console)
VITE_APPLE_CLIENT_ID=com.eliazavatta.maraffa.signin
VITE_APPLE_REDIRECT_URI=https://your-supabase-url.supabase.co/auth/v1/callback
```

## Xcode Configuration Steps

### 1. Signing & Capabilities
- Open `ios/App/App.xcworkspace` in Xcode
- Select your Team in Signing & Capabilities
- Add these capabilities:
  - Sign In with Apple
  - App Tracking Transparency (if using analytics/ads)

### 2. Info.plist Updates
Add these keys to `ios/App/App/Info.plist`:

```xml
<!-- App Tracking Transparency -->
<key>NSUserTrackingUsageDescription</key>
<string>This app uses data to provide personalized gaming experience and improve app performance.</string>

<!-- Apple Sign-In -->
<key>com.apple.developer.applesignin</key>
<array>
    <string>Default</string>
</array>

<!-- URL Schemes -->
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>com.eliazavatta.maraffa</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>com.eliazavatta.maraffa</string>
        </array>
    </dict>
</array>
```

### 3. AdMob Configuration
- Add `GoogleService-Info.plist` to `ios/App/App/`
- Configure AdMob App ID in Capacitor config

### 4. Build Settings
- Set iOS Deployment Target to 13.0 or higher
- Enable "Sign In with Apple" capability
- Configure proper Bundle Identifier

## Testing Commands

```bash
# Open in Xcode
npx cap open ios

# Run on simulator
npx cap run ios

# Build for device (requires Apple Developer account)
# Configure signing in Xcode first
```

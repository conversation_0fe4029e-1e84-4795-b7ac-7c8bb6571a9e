# Guida al Repository Marafone Romagnolo

## 📋 Panoramica del Progetto

**Marafone Romagnolo** è un'applicazione web progressiva (PWA) che implementa il tradizionale gioco di carte romagnolo "Marafone". Il progetto è sviluppato con **React + TypeScript** e può essere compilato come app Android usando **Capacitor**.

### 🎯 Obiettivi del Progetto

- Preservare e digitalizzare un gioco tradizionale della cultura romagnola
- Offrire esperienza di gioco sia locale (vs AI) che online (multiplayer)
- Fornire un'interfaccia moderna e responsiva per tutti i dispositivi
- Creare una base di codice manutenibile e estendibile per sviluppi futuri

---

## 🏗️ Architettura del Progetto

### Stack Tecnologico

| Componente        | Tecnologia           | Versione | Descrizione                             |
| ----------------- | -------------------- | -------- | --------------------------------------- |
| **Frontend**      | React                | 18.x     | Libreria UI principale                  |
| **Linguaggio**    | TypeScript           | 5.x      | Tipizzazione statica                    |
| **Styling**       | Tailwind CSS         | 3.x      | Framework CSS utility-first             |
| **UI Components** | Radix UI + shadcn/ui | Latest   | Componenti accessibili e customizzabili |
| **Build Tool**    | Vite                 | 5.x      | Bundler e dev server veloce             |
| **Backend**       | Supabase             | Latest   | Database PostgreSQL e Auth              |
| **Mobile Build**  | Capacitor            | 6.x      | Wrapper nativo per Android              |
| **Routing**       | React Router         | 6.x      | Navigazione client-side                 |

### Principi Architetturali

- **Component-Based**: Architettura a componenti riutilizzabili
- **Type Safety**: Utilizzo intensivo di TypeScript per la sicurezza dei tipi
- **Responsive Design**: Design mobile-first con supporto desktop
- **Performance**: Ottimizzazioni per caricamento immagini e rendering
- **Accessibility**: Interfaccia accessibile seguendo le linee guida WCAG

---

## 📁 Struttura del Progetto

### Directory Principale

```
marafone-romagnolo/
├── 📱 android/                    # Build Android (Capacitor)
├── 🏗️ build-scripts/              # Script di build e deploy
├── 🎨 public/                     # Asset statici
│   ├── images/                    # Immagini del gioco
│   │   ├── cards/                 # Carte da gioco
│   │   ├── logos/                 # Loghi e branding
│   │   └── semi/                  # Immagini dei semi
│   └── manifest.webmanifest       # PWA manifest
├── 📦 src/                        # Codice sorgente
└── 📚 docs/                       # Documentazione aggiuntiva
```

### Struttura `/src` Dettagliata

```
src/
├── 🎯 components/                 # Componenti React
│   ├── ui/                        # Componenti UI base (shadcn/ui)
│   ├── friends/                   # Gestione amici
│   ├── play/                      # Modalità di gioco
│   ├── profile/                   # Profili utente
│   └── [altri componenti]         # Componenti specifici del gioco
│
├── 🔧 utils/                      # Funzioni di utilità
│   ├── game/                      # Logic di gioco
│   │   ├── gameLogic.ts           # Logica principale del gioco
│   │   ├── cardUtils.ts           # Utility per le carte
│   │   ├── gameStateHelpers.ts    # Helper per stato del gioco
│   │   └── trumpHandler.ts        # Gestione briscola
│   ├── ai/                        # Intelligenza artificiale
│   │   ├── aiLogic.ts            # Logica AI
│   │   └── aiHandler.ts          # Handler AI
│   └── ui/                        # Utility UI
│       ├── imageOptimizer.ts      # Ottimizzazione immagini
│       └── imageConfig.ts         # Configurazione immagini
│
├── 🎣 hooks/                      # Custom React Hooks
│   ├── useGameHandlers.ts         # Handler di gioco
│   ├── useGameEffects.ts          # Effetti di gioco
│   ├── useGameImagePreload.ts     # Precaricamento immagini
│   └── use-mobile.ts              # Rilevamento mobile
│
├── 📄 pages/                      # Pagine principali
│   ├── Home.tsx                   # Homepage
│   ├── Game.tsx                   # Pagina di gioco
│   ├── Profile.tsx                # Profilo utente
│   └── Friends.tsx                # Gestione amicizie
│
├── 🌐 services/                   # Servizi API
│   ├── gameService.ts             # Servizio gioco locale
│   ├── onlineGameService.ts       # Servizio gioco online
│   ├── gameSessionService.ts      # Gestione sessioni
│   └── progressionService.ts      # Sistema progressione
│
├── 🏷️ types/                       # Definizioni TypeScript
│   ├── gameTypes.ts               # Tipi per il gioco
│   ├── database.ts                # Tipi database Supabase
│   └── dialogTypes.ts             # Tipi per dialoghi
│
├── 🎭 context/                    # React Context
│   └── auth-context.tsx           # Contesto autenticazione
│
└── 🔗 integrations/               # Integrazioni esterne
    └── supabase/                  # Configurazione Supabase
```

---

## 🎮 Logica di Gioco

### Componenti Principali

#### 1. **GameLogic** (`utils/game/gameLogic.ts`)

- Gestisce lo stato del gioco e le transizioni
- Implementa le regole della Marafone Romagnolo
- Gestisce punteggi, round e fine partita

#### 2. **CardUtils** (`utils/game/cardUtils.ts`)

- Definisce i tipi di carte e semi
- Implementa logica di validazione mosse
- Gestisce calcolo punti e vincitore presa

#### 3. **AI System** (`utils/ai/`)

- **aiLogic.ts**: Algoritmi di intelligenza artificiale
- **aiHandler.ts**: Gestione mosse AI con difficoltà variabile
- Supporta 3 livelli: Easy, Medium, Hard

#### 4. **Game State Management**

- State centralizzato tramite React hooks
- Gestione eventi sincrona e asincrona
- Supporto per modalità locale e online

### Flusso di Gioco

```mermaid
graph TD
    A[Inizio Partita] --> B[Distribuzione Carte]
    B --> C[Selezione Briscola]
    C --> D[Fase di Gioco]
    D --> E[Controllo Fine Mano]
    E -->|Mano Finita| F[Calcolo Punteggi]
    E -->|Continua| D
    F --> G[Controllo Fine Partita]
    G -->|Partita Finita| H[Dichiarazione Vincitore]
    G -->|Continua| I[Nuovo Round]
    I --> B
```

---

## 🎨 Sistema UI e Design

### Design System

#### Colori Principali (Tema Romagnolo)

```css
/* Palette ispirata alla tradizione romagnola */
--romagna-rust: #B8573C        /* Terracotta tipica */
--romagna-cream: #F5E6D3       /* Crema della piadina */
--romagna-terracotta: #C66B47   /* Rosso mattone */
--romagna-gold: #DAA520         /* Oro del grano */
--romagna-wood: #8B4513         /* Legno delle osterie */
--romagna-darkWood: #654321     /* Legno scuro */
```

#### Tipografia

- **Font Primario**: Playfair Display (elegante per titoli)
- **Font Secondario**: Inter (leggibile per il corpo)
- **Font Monospace**: Fira Code (per elementi tecnici)

### Componenti UI

#### Struttura Componenti

```
components/
├── ui/                          # Componenti base (shadcn/ui)
│   ├── button.tsx               # Pulsanti
│   ├── card.tsx                 # Card container
│   ├── dialog.tsx               # Modali
│   └── ...
├── Game*/                       # Componenti specifici del gioco
│   ├── GameBoard.tsx            # Tavolo di gioco
│   ├── PlayerHand.tsx           # Mano del giocatore
│   ├── GameControls.tsx         # Controlli di gioco
│   └── ...
└── Social*/                     # Componenti sociali
    ├── FriendsList.tsx          # Lista amici
    ├── ProfileStats.tsx         # Statistiche profilo
    └── ...
```

#### Responsive Design

- **Mobile First**: Design ottimizzato per dispositivi mobili
- **Breakpoint**: 768px per transizione mobile/desktop
- **Layout Adattivo**: Interfaccia che si adatta alle dimensioni schermo

---

## 🔧 Sistema di Build e Deploy

### Configurazioni Build

#### Ambiente di Sviluppo

```bash
# Installazione dipendenze
npm install

# Server di sviluppo
npm run dev

# Type checking
npm run type-check
```

#### Build Produzione

```bash
# Build web
npm run build

# Preview build
npm run preview

# Build Android
npm run build:android
```

#### Script Personalizzati

- **`build-apk.ps1`**: Build automatico APK Android
- **`generate-android-icons.ps1`**: Generazione icone Android
- **`compress-header-logo.ps1`**: Ottimizzazione loghi

### Configurazione Android (Capacitor)

```typescript
// capacitor.config.ts
export default {
  appId: "com.maraffa.romagnola72",
  appName: "Marafone Romagnolo",
  webDir: "dist",
  server: {
    androidScheme: "https",
  },
};
```

---

## 💾 Database e Backend

### Supabase Schema

#### Tabelle Principali

```sql
-- Profili utente
profiles (
  id: uuid PRIMARY KEY,
  username: text UNIQUE,
  email: text,
  avatar_url: text,
  created_at: timestamp
)

-- Statistiche di gioco
game_stats (
  id: uuid PRIMARY KEY,
  user_id: uuid REFERENCES profiles(id),
  games_played: integer,
  games_won: integer,
  total_points: integer,
  level: integer,
  xp: integer
)

-- Sessioni di gioco
games (
  id: uuid PRIMARY KEY,
  status: text CHECK (status IN ('waiting', 'active', 'completed')),
  created_by: uuid REFERENCES profiles(id),
  created_at: timestamp,
  updated_at: timestamp
)

-- Amicizie
friendships (
  id: uuid PRIMARY KEY,
  user_id: uuid REFERENCES profiles(id),
  friend_id: uuid REFERENCES profiles(id),
  status: text CHECK (status IN ('pending', 'accepted', 'blocked'))
)
```

#### Row Level Security (RLS)

- **Sicurezza**: Accesso ai dati limitato per utente
- **Policies**: Definite per ogni tabella
- **Auth**: Integrazione con sistema di autenticazione Supabase

---

## 🚀 Performance e Ottimizzazioni

### Ottimizzazioni Immagini

#### Sistema di Precaricamento Intelligente

```typescript
// Hook personalizzato per precaricamento
useGameImagePreload({
  playerCards: currentPlayerCards, // Priorità alta
  trumpSuit: selectedTrumpSuit, // Priorità media
  preloadLevel: "normal", // 'minimal' | 'normal' | 'aggressive'
  enabled: true,
});
```

#### Configurazioni Ottimizzazione

- **Cache Browser**: Implementata cache intelligente
- **Lazy Loading**: Caricamento immagini on-demand
- **Image Optimizer**: Ridimensionamento automatico per dispositivo
- **Progressive Loading**: Caricamento progressivo con placeholder

### Bundle Optimization

- **Code Splitting**: Divisione automatica del codice
- **Tree Shaking**: Eliminazione codice inutilizzato
- **Asset Optimization**: Compressione automatica asset

---

## 🧪 Testing e Qualità del Codice

### Strumenti di Qualità

#### Linting e Formatting

```json
// eslint.config.js
{
  "extends": ["@typescript-eslint/recommended"],
  "plugins": ["react-hooks"],
  "rules": {
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn"
  }
}
```

#### TypeScript Configuration

```json
// tsconfig.json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "android"]
}
```

### Best Practices Implementate

- **Type Safety**: Utilizzo rigoroso TypeScript
- **Error Boundaries**: Gestione errori React
- **Performance Monitoring**: Monitoraggio performance in development
- **Code Comments**: Documentazione inline del codice

---

## 🔐 Sicurezza

### Implementazioni di Sicurezza

#### Autenticazione

- **Supabase Auth**: Sistema di autenticazione robusto
- **JWT Tokens**: Token sicuri per sessioni
- **Email Verification**: Verifica email per nuovi account

#### Protezione Dati

- **RLS Policies**: Row Level Security su database
- **Input Validation**: Validazione input lato client e server
- **HTTPS Enforced**: Connessioni sicure obbligatorie

#### Anti-Cheat (Gioco Online)

- **Server Validation**: Validazione mosse lato server
- **State Verification**: Verifica coerenza stato di gioco
- **Rate Limiting**: Limitazione richieste per prevenire spam

---

## 🚀 Deployment e Hosting

### Ambienti di Deploy

#### Web (Vercel/Netlify)

```bash
# Build automatico da GitHub
git push origin main
# → Deploy automatico su Vercel
```

#### Android (Play Store)

```bash
# Generazione APK firmato
npm run build:android:release

# Upload su Play Console
# → Review e pubblicazione
```

### Variabili di Ambiente

```env
# .env.local
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_APP_VERSION=1.0.0
```

---

## 📈 Roadmap e Sviluppi Futuri

### Funzionalità Pianificate

#### Versione 1.1

- [ ] **Tornei Online**: Sistema torneo con classifiche
- [ ] **Chat di Gioco**: Comunicazione tra giocatori
- [ ] **Replay System**: Salvataggio e revisione partite
- [ ] **Achievement System**: Sistema achievement e badge

#### Versione 1.2

- [ ] **AI Avanzata**: Machine learning per AI più intelligente
- [ ] **Modalità Spettatore**: Osservazione partite altrui
- [ ] **Statistiche Avanzate**: Analytics dettagliate di gioco
- [ ] **Customizzazione**: Temi e personalizzazioni UI

#### Versione 2.0

- [ ] **Cross-Platform**: App iOS nativa
- [ ] **Modalità Offline**: Gioco completamente offline
- [ ] **Integrazione Social**: Condivisione su social network
- [ ] **Monetizzazione**: Sistema premium opzionale

### Considerazioni Tecniche Future

- **Migrazione a Next.js**: Per SSR e migliori performance
- **Implementazione PWA Avanzata**: Supporto offline completo
- **Microservizi**: Separazione backend in microservizi
- **Real-time Improvements**: WebSocket per gioco più fluido

---

## 👥 Contribuire al Progetto

### Setup Ambiente di Sviluppo

```bash
# 1. Clone del repository
git clone https://github.com/username/marafone-romagnolo.git
cd marafone-romagnolo

# 2. Installazione dipendenze
npm install

# 3. Setup environment variables
cp .env.example .env.local
# Modificare .env.local con le proprie chiavi

# 4. Avvio development server
npm run dev
```

### Convenzioni di Sviluppo

#### Git Workflow

```bash
# Feature branch workflow
git checkout -b feature/nome-feature
git commit -m "feat: descrizione feature"
git push origin feature/nome-feature
# → Creare Pull Request
```

#### Commit Messages (Conventional Commits)

```
feat: aggiunta nuova funzionalità
fix: correzione bug
docs: aggiornamento documentazione
style: modifiche styling
refactor: refactoring codice
test: aggiunta test
chore: maintenance generale
```

#### Code Style

- **Prettier**: Formattazione automatica
- **ESLint**: Controllo qualità codice
- **Naming**: camelCase per variabili, PascalCase per componenti
- **File Structure**: Organizzazione logica per funzionalità

### Review Process

1. **Code Review**: Revisione codice da parte di maintainer
2. **Testing**: Verifica funzionamento su diversi dispositivi
3. **Documentation**: Aggiornamento documentazione se necessario
4. **Merge**: Merge del branch feature in main

---

## 📞 Supporto e Contatti

### Bug Report e Feature Request

- **GitHub Issues**: [Link al repository GitHub]
- **Email**: <EMAIL>
- **Discord**: [Link server Discord community]

### Documentazione Aggiuntiva

- **API Documentation**: `/docs/api/`
- **Component Storybook**: `/docs/storybook/`
- **Game Rules**: `/docs/rules/REGOLE_MARAFFA.md`
- **Performance Guide**: `/docs/OTTIMIZZAZIONI_IMMAGINI.md`

### Risorse Utili

- [React Documentation](https://react.dev)
- [TypeScript Handbook](https://typescriptlang.org/docs)
- [Supabase Docs](https://supabase.com/docs)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [Capacitor Docs](https://capacitorjs.com/docs)

---

## 📜 Licenza

Questo progetto è rilasciato sotto licenza **MIT**. Vedere il file `LICENSE` per i dettagli completi.

---

## 🔗 Documentazione Correlata

- **[📚 Indice Documentazione](./README.md)** - Panoramica completa di tutta la documentazione
- **[📋 Panoramica App](./APP_OVERVIEW.md)** - Funzionalità e caratteristiche dell'applicazione
- **[🎮 Logica di Gioco](./logica-di-gioco.md)** - Regole e implementazione del gioco
- **[⚡ Ottimizzazioni Performance](./OTTIMIZZAZIONI_IMMAGINI.md)** - Sistema avanzato di ottimizzazioni
- **[🎨 Gestione Assets](./IMAGE_ASSETS.md)** - Organizzazione immagini e risorse
- **[🔊 Audio Assets](./AUDIO_ASSETS.md)** - Gestione file audio e musica
- **[🛠️ Build Scripts](./BUILD_SCRIPTS.md)** - Tool di sviluppo e deployment

## 🙏 Riconoscimenti

- **Tradizione Romagnola**: Grazie alla ricca tradizione culturale della Romagna
- **Community**: Grazie a tutti i contributor e beta tester
- **Open Source**: Grazie alle librerie open source che rendono possibile questo progetto

---

_Documento creato il 1 Giugno 2025 - Versione 2.0_  
_Ultimo aggiornamento: 7 Giugno 2025 - Riorganizzazione documentazione e miglioramento struttura_

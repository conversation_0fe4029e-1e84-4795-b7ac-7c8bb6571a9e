export interface Friend {
  id: string;
  friend_id: string;
  user_id: string;
  created_at: string;
  friend?: {
    id: string;
    username: string;
    level?: number; // Add level information
  };
  username?: string;
  online?: boolean;
  level?: number; // Add level information directly on Friend
}

export interface FriendRequest {
  id: string;
  sender_id: string;
  receiver_id: string;
  created_at: string;
  status: string;
  sender?: {
    id: string;
    username: string;
  };
}

export interface GameInvite {
  id: string;
  sender_id: string;
  receiver_id: string;
  created_at: string;
  updated_at: string;
  status: "pending" | "accepted" | "rejected";
  sender?: {
    id: string;
    username: string;
  };
  sender_username?: string; // Add this field to match usage in GameInvites.tsx
}

export interface Profile {
  id: string;
  username: string;
  created_at: string;
  updated_at: string;
}

export interface GameStats {
  id: string;
  user_id: string;
  games_played: number;
  games_won: number;
  level: number;
  xp: number;
  created_at: string;
  updated_at: string;
}

export interface GameMatchmaking {
  id: string;
  user_id: string;
  status: "searching" | "matched" | "cancelled";
  skill_level: number;
  created_at: string;
  updated_at: string;
  session_id?: string; // Added the missing session_id property
}

// Adding missing types for onlineGameService.ts
export interface GameRow {
  id: string;
  started_at: string;
  completed_at?: string | null;
  team1_score: number;
  team2_score: number;
  winner_team?: 0 | 1;
  custom_metadata?: string;
}

export type GameMatchmakingRow = GameMatchmaking;

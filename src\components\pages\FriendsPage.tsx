import React, { useState } from "react";
import { Users, Trophy } from "lucide-react";
import FriendsContent from "./FriendsContent";
import LeaderboardContent from "./LeaderboardContent";

const FriendsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<"friends" | "leaderboard">(
    "friends"
  );

  const tabs = [
    {
      id: "friends" as const,
      label: "<PERSON>ici",
      icon: Users,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200",
    },
    {
      id: "leaderboard" as const,
      label: "Classifiche",
      icon: Trophy,
      color: "text-amber-600",
      bgColor: "bg-amber-50",
      borderColor: "border-amber-200",
    },
  ];

  return (
    <div className="flex flex-col h-full">
      {/* Tab Header */}
      <div className="bg-white border-b border-gray-200 px-4 pt-4">
        <div className="flex space-x-1">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;

            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  flex items-center space-x-2 px-4 py-3 rounded-t-lg font-medium text-sm
                  transition-all duration-200 flex-1 justify-center
                  ${
                    isActive
                      ? `${tab.color} ${
                          tab.bgColor
                        } border-b-2 ${tab.borderColor.replace(
                          "border-",
                          "border-b-"
                        )}`
                      : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                  }
                `}
              >
                <Icon size={18} />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        {activeTab === "friends" && <FriendsContent />}
        {activeTab === "leaderboard" && <LeaderboardContent />}
      </div>
    </div>
  );
};

export default FriendsPage;

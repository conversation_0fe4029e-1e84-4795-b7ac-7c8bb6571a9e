import React from "react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { getAllTitles, getPlayerTitle } from "@/services/playerTitlesService";
import { cn } from "@/lib/utils";
import { Crown, Lock, CheckCircle } from "lucide-react";

interface RankRoadmapProps {
  isOpen: boolean;
  onClose: () => void;
  currentLevel: number;
  currentXp: number;
}

const RankRoadmap: React.FC<RankRoadmapProps> = ({
  isOpen,
  onClose,
  currentLevel,
  currentXp,
}) => {
  const allTitles = getAllTitles();
  const currentTitle = getPlayerTitle(currentLevel);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] bg-gradient-to-br from-amber-50 to-yellow-50 rounded-xl">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-amber-900 flex items-center gap-2">
            <Crown className="h-6 w-6 text-amber-600" />
            Rank
          </DialogTitle>
          <p className="text-amber-700 text-sm">
            Tutti i titoli che puoi sbloccare nel tuo viaggio!
          </p>
        </DialogHeader>

        <ScrollArea className="h-[60vh] pr-4">
          <div className="space-y-3">
            {allTitles.map((title, index) => {
              const isUnlocked = currentLevel >= title.level;
              const isCurrent = currentTitle.level === title.level;
              const isNext =
                !isUnlocked &&
                title.level > currentLevel &&
                (index === 0 || allTitles[index - 1].level <= currentLevel);

              return (
                <div
                  key={title.level}
                  className={cn(
                    "relative p-4 rounded-xl border transition-all duration-300",
                    isCurrent
                      ? "bg-gradient-to-r from-amber-100 to-yellow-100 border-amber-400 shadow-lg ring-2 ring-amber-400/50"
                      : isUnlocked
                      ? "bg-green-50 border-green-300 shadow-md"
                      : isNext
                      ? "bg-blue-50 border-blue-300 shadow-sm"
                      : "bg-gray-50 border-gray-300 opacity-70"
                  )}
                >
                  {/* Badge Stato */}
                  <div className="absolute top-2 right-2">
                    {isCurrent ? (
                      <Badge className="bg-amber-600 text-white text-xs">
                        <Crown className="h-3 w-3 mr-1" />
                        Attuale
                      </Badge>
                    ) : isUnlocked ? (
                      <Badge className="bg-green-600 text-white text-xs">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Sbloccato
                      </Badge>
                    ) : isNext ? (
                      <Badge className="bg-blue-600 text-white text-xs">
                        Prossimo
                      </Badge>
                    ) : (
                      <Badge
                        variant="outline"
                        className="text-gray-500 text-xs"
                      >
                        <Lock className="h-3 w-3 mr-1" />
                        Bloccato
                      </Badge>
                    )}
                  </div>{" "}
                  <div className="flex items-center gap-4">
                    {/* Immagine Rank e Livello */}
                    <div className="flex flex-col items-center">
                      <div
                        className={cn(
                          "w-16 h-16 mb-1  flex items-center justify-center rounded-full border-2",
                          isCurrent
                            ? "border-amber-400 bg-amber-100"
                            : "border-gray-300 bg-white",
                          isUnlocked ? "grayscale-0" : "grayscale opacity-50"
                        )}
                      >
                        <img
                          src={title.rankImage}
                          alt={title.title}
                          className="w-12 h-12 object-contain"
                        />{" "}
                      </div>
                      <div
                        className={cn(
                          "text-sm font-bold px-2 py-1 rounded-full",
                          isCurrent
                            ? "bg-amber-600 text-white"
                            : isUnlocked
                            ? "bg-green-600 text-white"
                            : "bg-gray-400 text-white"
                        )}
                      >
                        Lv.{title.level}
                      </div>
                    </div>

                    {/* Info Titolo */}
                    <div className="flex-1">
                      <h3
                        className={cn(
                          "text-lg font-bold mb-1",
                          isCurrent
                            ? "text-amber-900"
                            : isUnlocked
                            ? "text-green-900"
                            : "text-gray-600"
                        )}
                      >
                        {title.title}
                      </h3>
                      <p
                        className={cn(
                          "text-sm italic",
                          isCurrent
                            ? "text-amber-700"
                            : isUnlocked
                            ? "text-green-700"
                            : "text-gray-500"
                        )}
                      >
                        {title.description}
                      </p>

                      {/* Progress bar per il prossimo rank */}
                      {isNext && (
                        <div className="mt-3">
                          <div className="flex justify-between text-xs text-blue-700 mb-1">
                            <span>Progresso verso questo titolo</span>
                            <span>
                              {currentLevel}/{title.level}
                            </span>
                          </div>
                          <Progress
                            value={(currentLevel / title.level) * 100}
                            className="h-2"
                          />
                          <p className="text-xs text-blue-600 mt-1">
                            {title.level - currentLevel} livelli rimanenti
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default RankRoadmap;

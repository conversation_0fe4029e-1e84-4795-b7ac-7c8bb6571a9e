/**
 * 🍎 Apple Sign-In Service for iOS
 * Required when offering third-party authentication on iOS
 */

import { Capacitor } from "@capacitor/core";
import { supabase } from "@/lib/supabase";

// Import condizionale per Apple Sign-In
let SignInWithApple: any = null;

async function loadAppleSignIn() {
  if (Capacitor.getPlatform() === "ios") {
    try {
      // You'll need to install: npm install capacitor-sign-in-with-apple
      const appleModule = await import("capacitor-sign-in-with-apple");
      SignInWithApple = appleModule.SignInWithApple;
      console.log("✅ Apple Sign-In loaded");
    } catch (error) {
      console.warn("⚠️ Apple Sign-In not available:", error);
    }
  }
}

export class AppleAuthService {
  private static instance: AppleAuthService;

  static getInstance(): AppleAuthService {
    if (!AppleAuthService.instance) {
      AppleAuthService.instance = new AppleAuthService();
    }
    return AppleAuthService.instance;
  }

  /**
   * Check if Apple Sign-In is available
   */
  async isAvailable(): Promise<boolean> {
    if (Capacitor.getPlatform() !== "ios") {
      return false;
    }

    try {
      await loadAppleSignIn();
      return !!SignInWithApple;
    } catch {
      return false;
    }
  }

  /**
   * Sign in with Apple
   */
  async signIn(): Promise<{ data: any; error: any }> {
    try {
      console.log("🍎 Starting Apple Sign-In...");
      
      await loadAppleSignIn();
      
      if (!SignInWithApple) {
        throw new Error("Apple Sign-In not available");
      }

      // Request Apple Sign-In
      const result = await SignInWithApple.authorize({
        requestedScopes: ["email", "fullName"],
      });

      console.log("✅ Apple Sign-In successful:", {
        user: result.response.user,
        email: result.response.email,
        givenName: result.response.givenName,
        familyName: result.response.familyName,
      });

      // Sign in with Supabase using Apple identity token
      const { data, error } = await supabase.auth.signInWithIdToken({
        provider: "apple",
        token: result.response.identityToken,
      });

      if (error) {
        console.error("❌ Supabase Apple auth error:", error);
        throw error;
      }

      console.log("✅ Apple authentication completed successfully");
      return { data, error: null };
    } catch (error) {
      console.error("❌ Apple Sign-In failed:", error);
      return { data: null, error };
    }
  }
}

export default AppleAuthService.getInstance();

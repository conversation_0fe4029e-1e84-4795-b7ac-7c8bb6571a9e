import { useLocation } from "react-router-dom";
import AdMobService from "@/services/adMobService";

/**
 * Hook per determinare se il banner AdMob deve essere mostrato
 * Il banner non viene mostrato:
 * - Nella pagina di gioco
 * - Se l'utente ha il pass premium
 * - Su piattaforme web
 */
export const useAdBannerVisibility = () => {
  const location = useLocation();

  // Pagine dove NON mostrare il banner
  const excludedPaths = [
    "/game",
    "/play",
    "/terms",
    "/privacy-policy",
    "/rules",
  ];

  const isExcludedPath = excludedPaths.includes(location.pathname);
  const shouldShowAds = AdMobService.shouldShowAds();

  // Banner mostrato se non è in una pagina esclusa e gli annunci sono abilitati
  const shouldShowBanner = !isExcludedPath && shouldShowAds;

  return {
    shouldShowBanner,
    currentPath: location.pathname,
    isExcludedPath,
    hasPremiumPass: AdMobService.hasPremiumPass(),
  };
};

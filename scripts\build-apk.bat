@echo off
REM Cambia directory alla root del progetto
cd /d "%~dp0\.."

echo ======================================
echo    Creazione APK Marafone Romagnolo
echo ======================================
echo.

echo [1/4] Pulizia e preparazione...
if exist "android\app\build\outputs\apk" (
    echo Rimozione vecchi APK...
    rmdir /S /Q "android\app\build\outputs\apk"
)

echo.
echo [2/4] Generazione build ottimizzata...
call npm run build
if %ERRORLEVEL% NEQ 0 (
    echo Errore durante la build! Uscita.
    exit /b %ERRORLEVEL%
)

echo.
echo [3/4] Sincronizzazione con Android...
call npx cap sync android
if %ERRORLEVEL% NEQ 0 (
    echo Errore durante la sincronizzazione! Uscita.
    exit /b %ERRORLEVEL%
)

echo.
echo [4/4] Compilazione APK...
cd android
call .\gradlew.bat assembleDebug
if %ERRORLEVEL% NEQ 0 (
    echo Errore durante la compilazione dell'APK! Uscita.
    exit /b %ERRORLEVEL%
)
cd ..

echo.
echo ======================================
echo    APK creato con successo!
echo ======================================
echo.

REM Crea il nome del file con timestamp
set TIMESTAMP=%date:~6,4%%date:~3,2%%date:~0,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set APK_FILENAME=maraffa-romagnola_%TIMESTAMP%.apk

REM Assicura che la cartella di output esista
if not exist "apk-output" mkdir apk-output

REM Copia l'APK nella cartella di output con il nuovo nome
copy "android\app\build\outputs\apk\debug\app-debug.apk" "apk-output\%APK_FILENAME%"

echo L'APK si trova in:
echo apk-output\%APK_FILENAME%
echo.
echo Vuoi installare l'APK su un dispositivo collegato? (S/N)
set /p installDevice=

if /I "%installDevice%"=="S" (
    echo.
    echo Installazione APK sul dispositivo...
    call npx cap run android
) else (
    echo.
    echo Per installare manualmente, copia il file APK sul tuo dispositivo.
)

echo.
echo Operazione completata!
pause

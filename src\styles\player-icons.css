/* 
 * Stili specializzati per le icone dei giocatori
 * Migliora la distinzione visiva tra team e rank
 */

/* Animazioni per le icone rank */
.rank-icon-pulse {
  animation: rank-pulse 2s infinite;
}

@keyframes rank-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Effetti hover per le icone giocatore e team */
.player-icon-container:hover,
.team-icon-container:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease-in-out;
}

/* Effetti hover per le icone giocatore e team */
.player-icon-container:hover,
.team-icon-container:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease-in-out;
}

/* Effetti speciali per il giocatore corrente */
.current-player-glow {
  position: relative;
}

.current-player-glow::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #3b82f6, #1d4ed8, #3b82f6, #1d4ed8);
  background-size: 400% 400%;
  border-radius: inherit;
  z-index: -1;
  animation: gradient-flow 3s ease infinite;
}

@keyframes gradient-flow {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Stili per i badge livello */
.level-badge {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 700;
  letter-spacing: -0.025em;
}

/* Effetti team specifici */
.team-yellow-effect {
  box-shadow: 0 0 12px rgba(217, 119, 6, 0.2);
}

.team-red-effect {
  box-shadow: 0 0 12px rgba(220, 38, 38, 0.2);
}

/* Effetti per team score */
.team-score-glow-yellow {
  box-shadow: 0 2px 8px rgba(217, 119, 6, 0.2);
}

.team-score-glow-red {
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.2);
}

/* Transizioni fluide */
.player-info-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Container player che si adatta al contenuto */
.player-info-container {
  width: auto !important;
  min-width: 110px !important;
  max-width: none !important;
  padding: 0.25rem 0.5rem !important;
  white-space: nowrap !important;
  overflow: visible !important;
  box-sizing: border-box !important;
  display: flex !important;
  align-items: center !important;
  flex-shrink: 0 !important;
}

/* Forza il testo a rimanere dentro il container */
.player-info-container span {
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: clip !important;
  flex-shrink: 0 !important;
  display: inline-block !important;
}

/* Transizioni fluide per team score */
.team-score-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Icone team score più grandi che ignorano il padding */
.team-score-icon-large {
  width: 2rem !important;
  height: 2rem !important;
  margin-left: -0.25rem;
  margin-right: -0.25rem;
  z-index: 10;
  position: relative;
}

/* Micro-interazioni */
.player-info-container:hover .rank-image {
  transform: rotate(5deg) scale(1.1);
  transition: transform 0.2s ease-in-out;
}

.player-info-container:hover .level-badge {
  animation: bounce-subtle 0.6s ease-in-out;
}

/* Animazioni per score updates */
.score-update-animation {
  animation: score-bounce 0.6s ease-out;
}

@keyframes bounce-subtle {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

@keyframes score-bounce {
  0% {
    transform: scale3d(1, 1, 1);
  }
  50% {
    transform: scale3d(1.15, 1.15, 1);
  }
  100% {
    transform: scale3d(1, 1, 1);
  }
}

/* Ottimizzazioni specifiche per Android */
@media screen and (max-width: 768px) {
  .score-update-animation {
    animation: score-bounce-mobile 0.5s ease-out;
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  @keyframes score-bounce-mobile {
    0% {
      transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
    }
    50% {
      transform: translate3d(0, 0, 0) scale3d(1.1, 1.1, 1);
    }
    100% {
      transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
    }
  }
}

/* OVERRIDE AGGRESSIVO per risolvere definitivamente il problema della larghezza */
div.player-info-container.absolute {
  width: auto !important;
  /* min-width: fit-content !important; */
  max-width: none !important;
  overflow: visible !important;
  white-space: nowrap !important;
  flex-shrink: 0 !important;
}

div.player-info-container.absolute span {
  width: auto !important;
  min-width: fit-content !important;
  max-width: none !important;
  overflow: visible !important;
  white-space: nowrap !important;
  flex-shrink: 0 !important;
  text-overflow: clip !important;
}

import React, { useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import { Shield, Star, User, Cpu } from "lucide-react";
import { motion } from "framer-motion";
import { Capacitor } from "@capacitor/core";

interface TeamScoreProps {
  team: number;
  score: number;
  roundScore: number;
  figureTokens?: number; // I segnalini figura (0, 1, 2)
  className?: string;
  isOnlineMode?: boolean; // Per distinguere tra modalità online e offline
}

const TeamScore = ({
  team,
  score,
  roundScore,
  figureTokens = 0,
  className,
  isOnlineMode = true,
}: TeamScoreProps) => {
  const [prevRoundScore, setPrevRoundScore] = useState(0);
  const [prevFigureTokens, setPrevFigureTokens] = useState(0);
  const [showScoreAnimation, setShowScoreAnimation] = useState(false);

  // Rileva se siamo su dispositivo mobile/Android
  const isMobile =
    typeof window !== "undefined" &&
    (window.innerWidth <= 768 ||
      /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      ));

  // Monitora i cambiamenti nel punteggio della mano E nelle figure
  useEffect(() => {
    if (roundScore > prevRoundScore || figureTokens > prevFigureTokens) {
      setShowScoreAnimation(true);
      // Durata ridotta su mobile per migliore performance
      // Su Android usiamo animazioni più brevi per evitare lag
      const isAndroid = Capacitor.getPlatform() === "android";
      const duration = isAndroid ? 1000 : isMobile ? 1500 : 2000;
      const timer = setTimeout(() => setShowScoreAnimation(false), duration);
      return () => clearTimeout(timer);
    }
    setPrevRoundScore(roundScore);
    setPrevFigureTokens(figureTokens);
  }, [roundScore, prevRoundScore, figureTokens, prevFigureTokens, isMobile]);

  // Estrai solo la parte intera del punteggio del round
  const wholePoints = Math.floor(roundScore);

  // Formatta i punteggi con massimo una cifra decimale
  const formatScore = (value: number) => {
    // Se è un numero intero, mostra solo l'intero
    if (Math.floor(value) === value) {
      return value.toString();
    }
    // Altrimenti mostra massimo una cifra decimale
    return value.toFixed(1);
  };
  const displayScore = formatScore(score);

  // Stili migliorati per i team seguendo il design delle icone giocatore
  const teamStyles = {
    0: {
      bg: "bg-gradient-to-br from-amber-50/95 to-yellow-100/95",
      border: "border-amber-600",
      icon: "text-amber-700",
      text: "text-amber-800",
      highlight: "bg-amber-200/50",
      pointsBg: "bg-gradient-to-br from-amber-100 to-yellow-200",
      pointsText: "text-amber-800",
      tokenBg: "bg-amber-100/80",
      tokenBorder: "border-amber-600/50",
      shadowColor: "shadow-amber-400/30",
    },
    1: {
      bg: "bg-gradient-to-br from-red-50/95 to-rose-100/95",
      border: "border-red-600",
      icon: "text-red-700",
      text: "text-red-800",
      highlight: "bg-red-200/50",
      pointsBg: "bg-gradient-to-br from-red-100 to-rose-200",
      pointsText: "text-red-800",
      tokenBg: "bg-red-100/80",
      tokenBorder: "border-red-600/50",
      shadowColor: "shadow-red-400/30",
    },
  };

  const style = teamStyles[team as 0 | 1];

  // Determina quale icona mostrare in base alla modalità di gioco
  const getTeamIcon = (teamNumber: number) => {
    if (isOnlineMode) {
      // Modalità online: usa le icone originali (scudo e stella) con design migliorato
      return teamNumber === 0 ? (
        <div
          className={`team-icon-container w-6 h-6 rounded-full ${style.pointsBg} ${style.border} border-2 ${style.shadowColor} shadow-sm flex items-center justify-center transition-all duration-200`}
        >
          <Shield className={`h-3 w-3 ${style.icon} drop-shadow-sm`} />
        </div>
      ) : (
        <div
          className={`team-icon-container w-6 h-6 rounded-full ${style.pointsBg} ${style.border} border-2 ${style.shadowColor} shadow-sm flex items-center justify-center transition-all duration-200`}
        >
          <Star className={`h-3 w-3 ${style.icon} drop-shadow-sm`} />
        </div>
      );
    } else {
      // Modalità offline vs CPU: Team 0 (giocatore + CPU partner) usa userpollo, Team 1 (CPU avversari) usa cpupollo
      // Rimuoviamo i cerchi e sfondi, ingrandiamo le icone
      return teamNumber === 0 ? (
        <img
          src="/images/icons/userpollo.png"
          alt="User Team"
          className="object-contain drop-shadow-sm team-score-icon-large"
        />
      ) : (
        <img
          src="/images/icons/cpupollo.png"
          alt="CPU Team"
          className="object-contain drop-shadow-sm team-score-icon-large"
        />
      );
    }
  };
  return (
    <div
      className={cn(
        `px-2 py-1.5 rounded-xl border-2 backdrop-blur-sm flex items-center gap-2 team-score-transition ${style.bg} ${style.border} ${style.shadowColor} shadow-md`,
        "w-[125px] overflow-visible pr-3", // Larghezza leggermente aumentata e padding destro per frazione
        className,
        showScoreAnimation && "score-update-animation", // Solo animazione CSS
        team === 0 ? "team-score-glow-yellow" : "team-score-glow-red"
      )}
      style={{
        // Padding fisso per garantire spazio all'animazione senza influenzare il layout
        margin: "0px 3px", // Margin fisso sempre presente
        // Z-index per portare in primo piano durante l'animazione
        zIndex: showScoreAnimation ? 20 : 1,
        position: "relative",
        // Transform origin al centro per animazione uniforme
        transformOrigin: "center",
        // Transizione fluida solo per z-index
        transition: "z-index 0.3s ease-out",
      }}
    >
      {/* Icona del team senza badge overlay */}
      <div className="flex-shrink-0">{getTeamIcon(team)}</div>

      <div className="flex items-center gap-1 min-w-0 flex-1 pr-1">
        {/* Punteggio totale più prominente */}
        <div
          className={cn(
            `text-base font-bold leading-none ${style.pointsText}`,
            "flex-shrink-0"
          )}
        >
          {displayScore}
        </div>

        {/* Punteggio della mano corrente (sempre mostrato con +) */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0.5 }}
          animate={{
            scale: showScoreAnimation ? [1.2, 1] : 1,
            opacity: 1,
          }}
          transition={{ duration: 0.3 }}
          className={cn(
            `text-sm px-1.5 py-0.5 rounded-md font-bold font-serif leading-none ${style.tokenBg} ${style.text} border border-opacity-20 flex-shrink-0`,
            team === 0 ? "border-amber-600/40" : "border-red-600/40"
          )}
        >
          +{wholePoints}
        </motion.div>

        {/* Simboli per le figure residue (frazioni) */}
        {figureTokens > 0 && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3 }}
            className="flex-shrink-0"
          >
            <span
              className={cn(
                `text-sm font-bold ${style.pointsText}`,
                "select-none"
              )}
            >
              {figureTokens === 1 ? "⅓" : figureTokens === 2 ? "⅔" : ""}
            </span>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default TeamScore;

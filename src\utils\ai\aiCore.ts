import { GameState } from "../game/gameLogic";
import { Card, Suit, Rank } from "../game/cardUtils";
import { AIDifficulty } from "./types";
import { getUnifiedAIMove } from "./strategies/unifiedStrategy";
import { analyzeCardMemory, createSafeCardMemory } from "./memory/analyzer";
import { globalMemoryManager } from "./memory/manager";
import { CardEvaluator } from "./core/cardEvaluator";

/**
 * Main AI move selection function - SIMPLIFIED VERSION
 * Determines the best card to play based on game state and difficulty level
 */
export const getAIMove = (
  state: GameState,
  playerIndex: number,
  difficulty: AIDifficulty
): Card | null => {
  // Log identificativo per debug
  const playerTeam = state.players?.[playerIndex]?.team || "UNKNOWN";
  const playerName = `CPU${playerIndex} (Team ${playerTeam}) [${difficulty}]`;
  console.log(`\n🤖 ===== ${playerName} STA PENSANDO... =====`);

  // Validazione input
  if (
    !state ||
    !state.players ||
    !state.players[playerIndex] ||
    !state.players[playerIndex].hand
  ) {
    console.log(`❌ ${playerName} - ERRORE: dati giocatore non validi`);
    return null;
  }

  const player = state.players[playerIndex];
  let availableCards = [...player.hand];

  // 🎯 PRIORITÀ ASSOLUTA 1: MARAFFA - Se ho la maraffa e sono primo del turno nel primo trick, DEVO giocare l'Asso
  if (!state.currentTrick || state.currentTrick.length === 0) {
    const trickNumber = state.trickNumber ?? 1;

    // CONTROLLO MARAFFA CRITICO
    if (
      trickNumber === 1 && // Primo turno della mano
      state.trumpSuit && // Briscola selezionata
      state.lastTrumpSelector === playerIndex // Io ho scelto la briscola
    ) {
      // Verifica se ho la maraffa completa
      const hasAce = availableCards.some(
        (card) => card.suit === state.trumpSuit && card.rank === "A"
      );
      const hasTwo = availableCards.some(
        (card) => card.suit === state.trumpSuit && card.rank === "2"
      );
      const hasThree = availableCards.some(
        (card) => card.suit === state.trumpSuit && card.rank === "3"
      );

      if (hasAce && hasTwo && hasThree) {
        const maraffaAce = availableCards.find(
          (card) => card.suit === state.trumpSuit && card.rank === "A"
        );
        if (maraffaAce) {
          console.log(
            `🎯🎯🎯 ${playerName} - MARAFFA OBBLIGATORIA! Gioco Asso di ${maraffaAce.suit} per 3 punti bonus!`
          );
          return maraffaAce;
        }
      }
    }

    // 🎯 PRIORITÀ 2: APERTURA CON 3 NON-BRISCOLA SE PRIMO DEL TURNO
    console.log(
      `🎯 ${playerName} - PRIMO DEL TURNO - CONTROLLO 3 NON-BRISCOLA!`
    );

    const opening3s = availableCards.filter(
      (card) => card.rank === "3" && card.suit !== state.trumpSuit
    );

    if (opening3s.length > 0) {
      const best3 = opening3s[0];
      console.log(
        `🎯 ${playerName} - STRATEGIA APERTURA: GIOCO 3 di ${best3.suit}!`
      );
      return best3;
    }
  }

  // Filtra per seme di uscita se necessario
  if (state.leadSuit && state.currentTrick && state.currentTrick.length > 0) {
    const suitCards = availableCards.filter(
      (card) => card.suit === state.leadSuit
    );
    if (suitCards.length > 0) {
      availableCards = suitCards;
      console.log(
        `🎴 ${playerName} - Devo seguire il seme: ${state.leadSuit} (${suitCards.length} carte)`
      );
    }
  }

  if (availableCards.length === 0) {
    console.log(`❌ ${playerName} - ERRORE: nessuna carta disponibile`);
    return null;
  }

  // 🤝 CONTROLLO ANTI-SUPERAMENTO COMPAGNO (SEMPLIFICATO)
  // Nota: Logica semplificata senza tracking preciso delle carte del compagno
  // La strategia unificata gestisce meglio la cooperazione

  // 🔥 CONTROLLO ASSO SUL TAVOLO - PRIORITÀ MASSIMA
  if (state.currentTrick && state.currentTrick.length > 0) {
    const hasAceOnTable = state.currentTrick.some(
      (card) => card && card.rank === "A"
    );

    if (hasAceOnTable) {
      console.log(`🔥 ${playerName} - ASSO SUL TAVOLO! Cerco di prenderlo...`);

      const evaluator = new CardEvaluator();
      const winningCards = availableCards.filter((card) => {
        const testTrick = [...state.currentTrick, card];
        return evaluator.canWinCurrentTrick(
          card,
          testTrick,
          state.leadSuit,
          state.trumpSuit
        );
      });

      if (winningCards.length > 0) {
        const cardToPlay = winningCards[0];
        console.log(
          `🔥 ${playerName} - PRENDO ASSO CON ${cardToPlay.rank} di ${cardToPlay.suit}!`
        );
        return cardToPlay;
      }
    }
  }

  // Aggiorna memoria globale
  try {
    globalMemoryManager.updateMemory(state);
  } catch (error) {
    console.log(`⚠️ ${playerName} - Errore aggiornamento memoria:`, error);
  }

  // Utilizza la strategia unificata
  console.log(`🎯 ${playerName} - STRATEGIA UNIFICATA [${difficulty}]`);
  const result = getUnifiedAIMove(
    availableCards,
    state,
    playerIndex,
    difficulty
  );

  console.log(
    `✅ ${playerName} - DECISIONE FINALE: ${result?.rank || "NULL"} di ${
      result?.suit || "NULL"
    }`
  );

  return result;
};

// Export types and utilities for backward compatibility
export { AIDifficulty } from "./types";
export type { CardMemory } from "./types";

// Export utility functions for external use
export { getCardValue, getCardOrder, findMaraffa } from "./utils/cardUtils";

export { analyzeCardMemory, createSafeCardMemory } from "./memory/analyzer";

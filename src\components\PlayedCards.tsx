import React from "react";
import Card from "@/components/Card";
import TrickGatherAnimation from "@/components/TrickGatherAnimation";
import { cn } from "@/lib/utils";
import { Card as CardType } from "@/utils/game/cardUtils";
import { GameState } from "@/utils/game/gameLogic";
import { determineTrickWinner } from "@/utils/game/gameStateHelpers";

type PlayedCardsProps = {
  displayCards: CardType[];
  gameState: GameState;
  lastPlayedCard: CardType | null;
  getPlayedCardPosition: (trickIndex: number) => {
    className: string;
    zIndex: number;
  };
  getPlayerTeam: (index: number) => number;
  isMobile: boolean;
  showingLastTrick?: boolean;
  isDesktopLayout?: boolean;
};

const PlayedCards: React.FC<PlayedCardsProps> = ({
  displayCards,
  gameState,
  lastPlayedCard,
  getPlayedCardPosition,
  getPlayerTeam,
  isMobile,
  showingLastTrick = false,
  isDesktopLayout = false,
}) => {
  const [showAnimation, setShowAnimation] = React.useState(false);
  const [highlightWinningCard, setHighlightWinningCard] = React.useState(false);
  const [gatheringToCenter, setGatheringToCenter] = React.useState(false);
  const [animatingCards, setAnimatingCards] = React.useState<Set<string>>(
    new Set()
  );
  const [visibleCards, setVisibleCards] = React.useState<Set<string>>(
    new Set()
  );

  // Funzione per ottenere l'animazione di gathering specifica per direzione
  const getGatherAnimation = (playerIndex: number) => {
    const suffix = isDesktopLayout ? "-desktop" : "";
    switch (playerIndex) {
      case 0: // Sud (bottom)
        return `animate-gather-from-bottom${suffix}`;
      case 1: // Est (right)
        return `animate-gather-from-right${suffix}`;
      case 2: // Nord (top)
        return `animate-gather-from-top${suffix}`;
      case 3: // Ovest (left)
        return `animate-gather-from-left${suffix}`;
      default:
        return "";
    }
  };

  // Funzione per determinare la direzione dell'animazione basata sul playerIndex
  const getCardAnimationDirection = (playerIndex: number): string => {
    switch (playerIndex) {
      case 0: // Sud (bottom) - player principale - PERFETTAMENTE VERTICALE
        return "animate-in slide-in-from-bottom fade-in duration-700 ease-out";
      case 1: // Est (right) - PERFETTAMENTE ORIZZONTALE
        return "animate-in slide-in-from-right fade-in duration-700 ease-out";
      case 2: // Nord (top) - PERFETTAMENTE VERTICALE
        return "animate-in slide-in-from-top fade-in duration-700 ease-out";
      case 3: // Ovest (left) - PERFETTAMENTE ORIZZONTALE
        return "animate-in slide-in-from-left fade-in duration-700 ease-out";
      default:
        return "animate-in zoom-in-95 fade-in duration-700 ease-out";
    }
  }; // Quando showingLastTrick diventa true, aspetta un momento prima di iniziare l'animazione
  React.useEffect(() => {
    if (showingLastTrick && displayCards.length === 4) {
      // Aspetta che tutte le animazioni delle carte siano completate (500ms tempo normale)
      const waitForAnimationsTimer = setTimeout(() => {
        // Prima evidenzia la carta vincente per 800ms (tempo rapido ma visibile)
        setHighlightWinningCard(true);

        const highlightTimer = setTimeout(() => {
          setHighlightWinningCard(false);

          // Inizia la fase di gathering verso il centro
          const gatherTimer = setTimeout(() => {
            setGatheringToCenter(true);

            // Dopo che le carte si sono mosse al centro, inizia l'animazione finale
            const finalAnimationTimer = setTimeout(() => {
              setGatheringToCenter(false);
              setShowAnimation(true);

              // Mantieni l'animazione visibile per 1.5 secondi per vedere tutto
              const keepAnimationTimer = setTimeout(() => {
                setShowAnimation(false);
              }, 1500);

              return () => clearTimeout(keepAnimationTimer);
            }, 600); // Tempo per completare l'animazione di gathering

            return () => clearTimeout(finalAnimationTimer);
          }, 200); // Breve pausa dopo l'evidenziazione

          return () => clearTimeout(gatherTimer);
        }, 800);

        return () => clearTimeout(highlightTimer);
      }, 700); // Aspetta 700ms per permettere alla quarta carta di completare l'animazione

      return () => clearTimeout(waitForAnimationsTimer);
    } else {
      setShowAnimation(false);
      setHighlightWinningCard(false);
      setGatheringToCenter(false);
    }
  }, [showingLastTrick, displayCards.length]); // Effetto per gestire l'animazione di comparsa delle nuove carte
  React.useEffect(() => {
    if (lastPlayedCard) {
      setAnimatingCards((prev) => new Set(prev).add(lastPlayedCard.id)); // Rimuovi l'animazione dopo 700ms e rendi la carta permanentemente visibile
      const timer = setTimeout(() => {
        setAnimatingCards((prev) => {
          const newSet = new Set(prev);
          newSet.delete(lastPlayedCard.id);
          return newSet;
        });
        setVisibleCards((prev) => new Set(prev).add(lastPlayedCard.id));
      }, 700);

      return () => clearTimeout(timer);
    }
  }, [lastPlayedCard]);

  // Determina se dobbiamo mostrare l'animazione di raccolta delle carte
  const shouldShowGatherAnimation = showAnimation && displayCards.length === 4;

  // Calcola il vincitore della presa se è completa e determina la carta vincente da evidenziare
  const trickWinner =
    shouldShowGatherAnimation ||
    (highlightWinningCard && displayCards.length === 4)
      ? determineTrickWinner(
          displayCards,
          gameState.leadSuit,
          gameState.trumpSuit
        )
      : 0;

  // Determina quale carta evidenziare (solo quando highlightWinningCard è true)
  const winningCardIndex =
    highlightWinningCard && displayCards.length === 4 ? trickWinner : -1;

  // Se dovremmo mostrare l'animazione, usa il componente TrickGatherAnimation
  if (shouldShowGatherAnimation) {
    const actualWinnerIndex = (gameState.leadPlayer + trickWinner) % 4;

    return (
      <div className="absolute inset-0 pointer-events-none">
        <TrickGatherAnimation
          cards={displayCards}
          gameState={gameState}
          trickWinner={actualWinnerIndex}
          isVisible={true}
          getPlayerTeam={getPlayerTeam}
          isMobile={isMobile}
        />
      </div>
    );
  }
  // Altrimenti, mostra le carte normalmente
  return (
    <div className="absolute inset-0 pointer-events-none">
      {displayCards.map((card, index) => {
        const position = getPlayedCardPosition(index);
        const playerIndex = (gameState.leadPlayer + index) % 4;
        const team = getPlayerTeam(playerIndex);
        const isWinningCard = winningCardIndex === index;
        const isAnimating = animatingCards.has(card.id);
        const isVisible = visibleCards.has(card.id);

        // Ottieni l'animazione specifica per la direzione durante il gathering
        const gatherAnimationClass = gatheringToCenter
          ? getGatherAnimation(playerIndex)
          : "";

        return (
          <div
            key={card.id}
            className={cn(
              position.className,
              "transition-all duration-300 ease-in-out played-card",
              isAnimating
                ? getCardAnimationDirection(playerIndex)
                : "opacity-0",
              (isAnimating || isVisible) && "opacity-100",
              gatherAnimationClass
            )}
            style={{ zIndex: position.zIndex }}
          >
            <div
              className={cn(
                "relative p-1 rounded-lg transition-all duration-300",
                team === 0 ? "bg-team-0-primary/50" : "bg-team-1-primary/50",
                isWinningCard &&
                  highlightWinningCard &&
                  "animate-winning-card-spectacular"
              )}
            >
              <Card
                card={card}
                isRevealed={true}
                scale={isMobile ? "sm" : "md"}
              />
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default PlayedCards;

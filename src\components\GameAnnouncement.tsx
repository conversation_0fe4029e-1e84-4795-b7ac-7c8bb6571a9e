import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";

// Define suit images mapping
const suitImages: Record<string, string> = {
  coins: "/images/semi/denari.png",
  cups: "/images/semi/coppe.png",
  swords: "/images/semi/spade.png",
  clubs: "/images/semi/bastoni.png",
};

// Define suit names
const suitNames: Record<string, string> = {
  coins: "Denari",
  cups: "Coppe",
  swords: "Spade",
  clubs: "Bastoni",
};

interface GameAnnouncementProps {
  title: string;
  message: string;
  suit?: string;
}

const GameAnnouncement = ({ title, message, suit }: GameAnnouncementProps) => {
  const [isVisible, setIsVisible] = useState(true);

  // Automatically hide the announcement after 2.5 seconds
  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 2500);
      return () => clearTimeout(timer);
    }
  }, [isVisible]);

  const getSuitImage = () => {
    return suit && suitImages[suit] ? suitImages[suit] : "";
  };

  const getSuitName = () => {
    return suit && suitNames[suit] ? suitNames[suit] : "";
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center pointer-events-none"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          transition={{ type: "spring", damping: 20, stiffness: 300 }}
        >
          <div className="bg-gradient-to-br from-yellow-50 to-amber-100 border-2 border-yellow-500 rounded-lg shadow-lg p-6 max-w-md text-center">
            <h2 className="text-2xl font-bold text-yellow-800 mb-2">{title}</h2>

            {suit && (
              <div className="flex items-center justify-center mb-3">
                <img
                  src={getSuitImage()}
                  alt={getSuitName()}
                  className="w-12 h-12 object-contain animate-pulse transform -rotate-12"
                />
              </div>
            )}

            <p className="text-amber-900 font-medium">{message}</p>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default GameAnnouncement;

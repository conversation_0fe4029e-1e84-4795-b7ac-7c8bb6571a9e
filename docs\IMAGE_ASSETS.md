# 🎨 Gestione Assets Immagini - Marafone Romagnolo

Questa documentazione descrive l'organizzazione completa delle immagini utilizzate nel gioco Marafone Romagnolo, incluse convenzioni di denominazione, struttura delle cartelle e best practices per l'ottimizzazione.

---

## 📁 Struttura delle Cartelle

### `/public/images/`

Tutte le immagini del gioco sono organizzate nella cartella `public/images/` con la seguente struttura:

```
public/images/
├── 🏠 logos/              # Loghi e branding
├── 🃏 cards/             # Carte da gioco
│   ├── suits/            # Carte divise per seme
│   │   ├── denari/       # Carte del seme Denari
│   │   ├── coppe/        # Carte del seme Coppe
│   │   ├── spade/        # Carte del seme Spade
│   │   └── bastoni/      # Carte del seme Bastoni
│   └── backs/            # Dorsi delle carte
├── 🎯 semi/              # Simboli dei semi
├── 🎮 modes/             # Icone modalità di gioco
└── 🖼️ wallpapers/        # Sfondi e texture
```

---

## 🃏 Sistema delle Carte

### Struttura `/cards/suits/`

Ogni seme contiene 13 carte organizzate secondo il sistema romagnolo tradizionale:

#### **Carte Numeriche** (1-10)

- `1_{seme}.png` - Asso (carta speciale)
- `2_{seme}.png` - Due (carta speciale)
- `3_{seme}.png` - Tre (carta speciale)
- `4_{seme}.png` - Quattro (carta più bassa)
- `5_{seme}.png` - Cinque
- `6_{seme}.png` - Sei
- `7_{seme}.png` - Sette

#### **Figure** (Jack, Horse, King)

- `jack_{seme}.png` - Fante
- `horse_{seme}.png` - Cavallo
- `king_{seme}.png` - Re

### Esempi di Denominazione

```
/cards/suits/denari/
├── 1_denari.png      # Asso di Denari
├── 4_denari.png      # 4 di Denari (carta speciale nel gioco)
├── jack_coppe.png    # Fante di Coppe
├── horse_spade.png   # Cavallo di Spade
└── king_bastoni.png  # Re di Bastoni
```

### Dorsi delle Carte

```
/cards/backs/
├── card_back.png         # Dorso principale
├── card_placeholder.png  # Placeholder per caricamento
└── card_error.png        # Fallback per errori
```

---

## 🏠 Loghi e Branding

### `/logos/`

Contiene tutti i loghi utilizzati nell'interfaccia:

- **`maraffa_logo.png`** - Logo principale dell'applicazione
- **`romagna_logo.svg`** - Logo della Romagna
- **`rooster_logo.png`** - Logo del gallo (simbolo romagnolo)
- **`app_icon.png`** - Icona dell'app per stores

---

## 🎯 Simboli e Icone

### `/semi/`

Simboli dei quattro semi utilizzati nel gioco:

- `denari.svg` - Simbolo Denari
- `coppe.svg` - Simbolo Coppe
- `spade.svg` - Simbolo Spade
- `bastoni.svg` - Simbolo Bastoni

### `/modes/`

Icone per le diverse modalità di gioco:

- `ai_easy.png` - Modalità AI Facile
- `ai_medium.png` - Modalità AI Medio
- `ai_hard.png` - Modalità AI Difficile
- `multiplayer.png` - Modalità Multiplayer
- `tutorial.png` - Modalità Tutorial

---

## 🖼️ Sfondi e Texture

### `/wallpapers/`

Sfondi e texture per l'interfaccia:

- `game_background.jpg` - Sfondo principale di gioco
- `menu_background.jpg` - Sfondo menu
- `wood_texture.jpg` - Texture legno per il tavolo
- `paper_texture.jpg` - Texture carta per i punteggi

---

## 📏 Specifiche Tecniche

### Formati Supportati

| Tipo Asset | Formato Preferito | Formato Alternativo | Note                         |
| ---------- | ----------------- | ------------------- | ---------------------------- |
| **Carte**  | PNG               | SVG                 | PNG per compatibilità mobile |
| **Loghi**  | SVG               | PNG                 | SVG per scalabilità          |
| **Icone**  | SVG               | PNG                 | SVG preferito per UI         |
| **Sfondi** | JPG               | PNG                 | JPG per dimensioni file      |

### Dimensioni Consigliate

| Tipo Asset   | Dimensioni       | Note                  |
| ------------ | ---------------- | --------------------- |
| **Carte**    | 200x300px        | Rapporto 2:3 standard |
| **Logo App** | 512x512px        | Per store e manifest  |
| **Icone UI** | 24x24px, 48x48px | Multiple sizes        |
| **Sfondi**   | 1920x1080px      | Full HD per desktop   |

---

## ⚡ Ottimizzazioni

### Sistema di Cache

Le immagini utilizzano il sistema di ottimizzazione descritto in **[OTTIMIZZAZIONI_IMMAGINI.md](./OTTIMIZZAZIONI_IMMAGINI.md)**:

- **Cache intelligente** per evitare download ripetuti
- **Compressione adattiva** basata sul dispositivo
- **Precaricamento prioritario** delle carte più probabili
- **Lazy loading** per immagini non critiche

### Best Practices

1. **Nomenclatura consistente** - Seguire sempre le convenzioni
2. **Ottimizzazione dimensioni** - Bilanciare qualità e performance
3. **Formati appropriati** - SVG per scalabilità, PNG per dettagli
4. **Fallback robusti** - Sempre fornire immagini alternative

---

## 🔧 Accesso Programmatico

### URL delle Immagini

Le immagini sono accessibili tramite URL relativi:

```typescript
// Esempi di percorsi
const cardImage = "/images/cards/suits/denari/4_denari.png";
const logoImage = "/images/logos/maraffa_logo.png";
const suitIcon = "/images/semi/denari.svg";
const gameBackground = "/images/wallpapers/game_background.jpg";
```

### Utility Helper

Il progetto include helper per la gestione delle immagini:

```typescript
// da utils/imageConfig.ts
import { getCardImagePath } from "@/utils/imageConfig";

const cardPath = getCardImagePath("4", "denari");
// Ritorna: "/images/cards/suits/denari/4_denari.png"
```

---

## 🔗 Documentazione Correlata

- **[📚 Indice Documentazione](./README.md)** - Panoramica completa
- **[⚡ Ottimizzazioni Performance](./OTTIMIZZAZIONI_IMMAGINI.md)** - Sistema di cache avanzato
- **[🔊 Audio Assets](./AUDIO_ASSETS.md)** - Gestione file audio
- **[🏗️ Architettura Progetto](./GUIDA_REPOSITORY.md)** - Implementazione tecnica
- **[🛠️ Build Scripts](./BUILD_SCRIPTS.md)** - Tool per ottimizzazione assets

---

_Documento versione 2.0 - Ultimo aggiornamento: 7 Giugno 2025_

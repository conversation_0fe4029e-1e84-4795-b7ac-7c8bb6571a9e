import { useEffect, useRef, useCallback } from 'react';
import { Capacitor } from '@capacitor/core';

interface ScrollOptimizationOptions {
  /** Soglia di scroll per attivare il lazy loading (default: 200px) */
  threshold?: number;
  /** Debounce time per gli eventi di scroll (default: 16ms per 60fps) */
  debounceTime?: number;
  /** Callback chiamato quando si raggiunge la soglia di scroll */
  onThresholdReached?: () => void;
  /** Se true, ottimizza solo su Android (default: true) */
  androidOnly?: boolean;
}

/**
 * Hook per ottimizzare lo scrolling su Android
 * Implementa debouncing, lazy loading e gestione della memoria
 */
export const useAndroidScrollOptimization = (options: ScrollOptimizationOptions = {}) => {
  const {
    threshold = 200,
    debounceTime = 16, // ~60fps
    onThresholdReached,
    androidOnly = true
  } = options;

  const scrollTimeoutRef = useRef<NodeJS.Timeout>();
  const lastScrollY = useRef(0);
  const isScrolling = useRef(false);

  // Verifica se siamo su Android
  const isAndroid = Capacitor.getPlatform() === 'android';
  const shouldOptimize = androidOnly ? isAndroid : true;

  const handleScroll = useCallback(() => {
    if (!shouldOptimize) return;

    const currentScrollY = window.scrollY;
    const scrollHeight = document.documentElement.scrollHeight;
    const clientHeight = window.innerHeight;

    // Calcola se siamo vicini al fondo
    const distanceFromBottom = scrollHeight - currentScrollY - clientHeight;

    // Chiama il callback se raggiungiamo la soglia
    if (distanceFromBottom <= threshold && onThresholdReached) {
      onThresholdReached();
    }

    lastScrollY.current = currentScrollY;
    isScrolling.current = true;

    // Debounce per fermare lo scrolling
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    scrollTimeoutRef.current = setTimeout(() => {
      isScrolling.current = false;
    }, debounceTime * 3); // Aspetta un po' di più per considerare lo scroll finito

  }, [threshold, onThresholdReached, shouldOptimize, debounceTime]);

  // Throttled scroll handler per performance migliori
  const throttledScrollHandler = useCallback(() => {
    if (!shouldOptimize) return;
    
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    scrollTimeoutRef.current = setTimeout(handleScroll, debounceTime);
  }, [handleScroll, debounceTime, shouldOptimize]);

  useEffect(() => {
    if (!shouldOptimize) return;

    // Aggiungi listener con opzioni passive per performance
    window.addEventListener('scroll', throttledScrollHandler, { passive: true });

    return () => {
      window.removeEventListener('scroll', throttledScrollHandler);
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [throttledScrollHandler, shouldOptimize]);

  return {
    isScrolling: isScrolling.current,
    lastScrollY: lastScrollY.current,
    isAndroid,
    shouldOptimize
  };
};

/**
 * Hook per ottimizzare il rendering di liste lunghe su Android
 */
export const useVirtualizedList = <T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) => {
  const isAndroid = Capacitor.getPlatform() === 'android';
  const scrollY = useRef(0);

  const handleScroll = useCallback((e: Event) => {
    if (!isAndroid) return;
    const target = e.target as HTMLElement;
    scrollY.current = target.scrollTop;
  }, [isAndroid]);

  // Calcola gli elementi visibili
  const getVisibleRange = useCallback(() => {
    if (!isAndroid) {
      // Su web mostra tutti gli elementi
      return { start: 0, end: items.length };
    }

    const start = Math.max(0, Math.floor(scrollY.current / itemHeight) - overscan);
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const end = Math.min(items.length, start + visibleCount + overscan * 2);

    return { start, end };
  }, [items.length, itemHeight, containerHeight, overscan, isAndroid]);

  const visibleRange = getVisibleRange();
  const visibleItems = items.slice(visibleRange.start, visibleRange.end);

  return {
    visibleItems,
    visibleRange,
    handleScroll,
    totalHeight: items.length * itemHeight,
    offsetY: visibleRange.start * itemHeight,
    isAndroid
  };
};

/**
 * Hook per gestire il lazy loading delle immagini su Android
 */
export const useAndroidImageLazyLoading = () => {
  const isAndroid = Capacitor.getPlatform() === 'android';
  const observerRef = useRef<IntersectionObserver>();

  useEffect(() => {
    if (!isAndroid) return;

    // Crea l'observer solo su Android
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            const src = img.dataset.src;
            
            if (src) {
              img.src = src;
              img.removeAttribute('data-src');
              observerRef.current?.unobserve(img);
            }
          }
        });
      },
      {
        rootMargin: '50px', // Carica le immagini 50px prima che diventino visibili
        threshold: 0.1
      }
    );

    return () => {
      observerRef.current?.disconnect();
    };
  }, [isAndroid]);

  const observeImage = useCallback((img: HTMLImageElement) => {
    if (!isAndroid || !observerRef.current) return;
    observerRef.current.observe(img);
  }, [isAndroid]);

  return {
    observeImage,
    isAndroid
  };
};

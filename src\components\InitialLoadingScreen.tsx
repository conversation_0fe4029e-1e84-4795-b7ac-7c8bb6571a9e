import React, { useEffect, useState } from "react";
import <PERSON><PERSON> from "lottie-react";

/**
 * ⚠️ COMPONENTE NON PIÙ UTILIZZATO
 * Questo componente è stato sostituito da SplashScreen.tsx
 * Conservato per riferimento ma non viene più usato nell'app
 */

interface InitialLoadingScreenProps {
  message?: string;
}

// Fallback animated dots component
const AnimatedDots: React.FC<{ className?: string }> = ({ className }) => (
  <div className={`flex items-center justify-center space-x-1 ${className}`}>
    <div
      className="w-3 h-3 bg-amber-600 rounded-full animate-bounce"
      style={{ animationDelay: "-0.3s" }}
    ></div>
    <div
      className="w-3 h-3 bg-amber-600 rounded-full animate-bounce"
      style={{ animationDelay: "-0.15s" }}
    ></div>
    <div className="w-3 h-3 bg-amber-600 rounded-full animate-bounce"></div>
  </div>
);

const InitialLoadingScreen: React.FC<InitialLoadingScreenProps> = ({
  message = "Caricamento Marafone",
}) => {
  const [animationData, setAnimationData] = useState<any>(null);
  const [animationError, setAnimationError] = useState(false);

  useEffect(() => {
    // Carica l'animazione Lottie del gallo
    const loadAnimation = async () => {
      try {
        console.log("🐓 Caricamento animazione gallo...");
        const response = await fetch("/animations/loading-animation.json");

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // Verifica che i dati dell'animazione siano validi
        if (!data || !data.layers || !Array.isArray(data.layers)) {
          throw new Error("Invalid animation data structure");
        }

        console.log("✅ Animazione gallo caricata con successo");
        setAnimationData(data);
        setAnimationError(false);
      } catch (error) {
        console.error("❌ Errore nel caricamento animazione gallo:", error);
        setAnimationError(true);
      }
    };

    loadAnimation();
  }, []);
  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center"
      style={{
        background: `radial-gradient(
          ellipse at center,
          #fefce8 0%,
          #fef3c7 25%,
          #fed7aa 60%,
          #fed7d7 85%,
          #fecaca 100%
        )`,
        backgroundAttachment: "fixed",
      }}
    >
      {/* Pattern di sfondo con loghi (come nell'app principale) */}
      <div
        className="fixed inset-0 pointer-events-none"
        style={{
          backgroundImage: 'url("/images/logos/logo_bastoni_compressed.png")',
          backgroundRepeat: "space",
          opacity: 0.12,
          filter:
            "sepia(80%) saturate(120%) hue-rotate(15deg) brightness(0.7) contrast(1.1)",
          transform: "rotate(8deg) scale(1.1)",
          transformOrigin: "center center",
          backgroundSize: "60px 60px",
          backgroundPosition: "0 0",
          zIndex: -1,
        }}
      />

      <div className="text-center relative z-10">
        {/* Solo animazione del gallo */}
        <div className="flex flex-col items-center gap-6">
          {animationData && !animationError ? (
            <div className="relative">
              <Lottie
                animationData={animationData}
                className="w-40 h-40"
                loop={true}
                autoplay={true}
                style={{
                  filter:
                    "drop-shadow(0 12px 24px rgba(0,0,0,0.4)) drop-shadow(0 4px 8px rgba(0,0,0,0.2))",
                }}
              />
            </div>
          ) : (
            <div
              className="relative"
              style={{
                filter:
                  "drop-shadow(0 12px 24px rgba(0,0,0,0.4)) drop-shadow(0 4px 8px rgba(0,0,0,0.2))",
              }}
            >
              <AnimatedDots className="w-40 h-40" />
            </div>
          )}

          {/* Messaggio semplice */}
          <span className="text-amber-800 font-medium text-xl text-center">
            {message}
          </span>
        </div>
      </div>
    </div>
  );
};

export default InitialLoadingScreen;

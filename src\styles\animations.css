/* Animazioni per il carosello delle modalità di gioco */
.mode-carousel-container {
  transition: opacity 0.3s ease;
}

/* Animazioni per il carosello rotante */
@keyframes rotateNextMode {
  0% {
    transform: translateX(0) scale(0.75);
  }
  100% {
    transform: translateX(-80px) scale(1.25);
  }
}

@keyframes rotatePrevMode {
  0% {
    transform: translateX(0) scale(0.75);
  }
  100% {
    transform: translateX(80px) scale(1.25);
  }
}

@keyframes rotateCurrentToNext {
  0% {
    transform: translateX(0) scale(1.25);
  }
  100% {
    transform: translateX(80px) scale(0.75);
  }
}

@keyframes rotateCurrentToPrev {
  0% {
    transform: translateX(0) scale(1.25);
  }
  100% {
    transform: translateX(-80px) scale(0.75);
  }
}

.rotate-to-current {
  animation: rotateNextMode 0.4s ease-out forwards;
}

.rotate-to-next {
  animation: rotateCurrentToNext 0.4s ease-out forwards;
}

.rotate-to-prev {
  animation: rotateCurrentToPrev 0.4s ease-out forwards;
}

.rotate-from-next {
  animation: rotatePrevMode 0.4s ease-out forwards;
}

/* Animazione specifica per l'icona della modalità */
@keyframes modeIconSlideLeft {
  0% {
    transform: translateX(50px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes modeIconSlideRight {
  0% {
    transform: translateX(-50px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.icon-slide-left {
  animation: modeIconSlideLeft 0.4s ease-out forwards;
}

.icon-slide-right {
  animation: modeIconSlideRight 0.4s ease-out forwards;
}

/* Animazioni per elementi che entrano e escono */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease forwards;
}

.animate-fade-out {
  animation: fadeOut 0.3s ease forwards;
}

/* Animazione per modalità selezionata */
@keyframes selectedPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

.animate-selected-pulse {
  animation: selectedPulse 2s ease-in-out infinite;
}

/* Animazione per scorrimento orizzontale */
@keyframes slideLeft {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideRight {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.slide-left {
  animation: slideLeft 0.4s ease-out forwards;
}

.slide-right {
  animation: slideRight 0.4s ease-out forwards;
}

/* Animazioni per icone di sfondo decorative */
@keyframes backgroundIconFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.05;
  }
  25% {
    transform: translateY(-2px) rotate(1deg);
    opacity: 0.06;
  }
  50% {
    transform: translateY(-4px) rotate(0deg);
    opacity: 0.07;
  }
  75% {
    transform: translateY(-2px) rotate(-1deg);
    opacity: 0.06;
  }
}

@keyframes backgroundIconPulse {
  0%,
  100% {
    opacity: 0.05;
    transform: scale(1);
  }
  50% {
    opacity: 0.08;
    transform: scale(1.02);
  }
}

@keyframes backgroundPatternShift {
  0% {
    background-position: 0px 0px;
  }
  25% {
    background-position: 2px 1px;
  }
  50% {
    background-position: 0px 2px;
  }
  75% {
    background-position: -1px 1px;
  }
  100% {
    background-position: 0px 0px;
  }
}

@keyframes subtleRotate {
  0% {
    transform: rotate(8deg) scale(1.1);
  }
  50% {
    transform: rotate(8.5deg) scale(1.105);
  }
  100% {
    transform: rotate(8deg) scale(1.1);
  }
}

/* Animazioni più aggressive per i loghi di sfondo */
@keyframes logoFloat {
  0%,
  100% {
    transform: rotate(8deg) scale(1.1) translateY(0px);
    opacity: 0.12;
  }
  25% {
    transform: rotate(8.2deg) scale(1.12) translateY(-3px);
    opacity: 0.15;
  }
  50% {
    transform: rotate(7.8deg) scale(1.08) translateY(-6px);
    opacity: 0.18;
  }
  75% {
    transform: rotate(8.1deg) scale(1.11) translateY(-3px);
    opacity: 0.15;
  }
}

@keyframes logoShimmer {
  0% {
    filter: sepia(80%) saturate(120%) hue-rotate(15deg) brightness(0.7)
      contrast(1.1);
    opacity: 0.12;
  }
  25% {
    filter: sepia(85%) saturate(140%) hue-rotate(18deg) brightness(0.8)
      contrast(1.15);
    opacity: 0.15;
  }
  50% {
    filter: sepia(90%) saturate(160%) hue-rotate(22deg) brightness(0.9)
      contrast(1.2);
    opacity: 0.18;
  }
  75% {
    filter: sepia(85%) saturate(140%) hue-rotate(18deg) brightness(0.8)
      contrast(1.15);
    opacity: 0.15;
  }
  100% {
    filter: sepia(80%) saturate(120%) hue-rotate(15deg) brightness(0.7)
      contrast(1.1);
    opacity: 0.12;
  }
}

@keyframes logoWave {
  0% {
    background-position: 30px 30px;
    transform: rotate(8deg) scale(1.1);
  }
  25% {
    background-position: 35px 32px;
    transform: rotate(8.3deg) scale(1.12);
  }
  50% {
    background-position: 30px 35px;
    transform: rotate(7.7deg) scale(1.08);
  }
  75% {
    background-position: 25px 32px;
    transform: rotate(8.2deg) scale(1.11);
  }
  100% {
    background-position: 30px 30px;
    transform: rotate(8deg) scale(1.1);
  }
}

/* Classi per applicare le animazioni */
.animate-background-float {
  animation: backgroundIconFloat 8s ease-in-out infinite;
}

.animate-background-pulse {
  animation: backgroundIconPulse 6s ease-in-out infinite;
}

.animate-pattern-shift {
  animation: backgroundPatternShift 12s ease-in-out infinite;
}

.animate-subtle-rotate {
  animation: subtleRotate 15s ease-in-out infinite;
}

/* Nuove classi per animazioni più visibili dei loghi */
.animate-logo-float {
  animation: logoFloat 12s ease-in-out infinite;
}

.animate-logo-shimmer {
  animation: logoShimmer 8s ease-in-out infinite;
}

.animate-logo-wave {
  animation: logoWave 10s ease-in-out infinite;
}

.animate-logo-combo {
  animation: logoFloat 12s ease-in-out infinite,
    logoShimmer 8s ease-in-out infinite 2s, logoWave 10s ease-in-out infinite 1s;
}

/* ========== ANIMAZIONE CARTA VINCENTE ELEGANTE ========== */

@keyframes winningCardShine {
  0% {
    background-position: -100% 0;
    opacity: 0;
  }
  25% {
    opacity: 0.6;
  }
  50% {
    background-position: 100% 0;
    opacity: 0.8;
  }
  75% {
    opacity: 0.6;
  }
  100% {
    background-position: 200% 0;
    opacity: 0;
  }
}

.animate-winning-card-spectacular {
  position: relative;
  z-index: 100;
  transform: scale(1.08);
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.6), 0 0 25px rgba(255, 215, 0, 0.4);
  transition: all 0.3s ease-out;
}

.animate-winning-card-spectacular::before {
  content: "";
  position: absolute;
  inset: -2px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 215, 0, 0.5),
    transparent
  );
  background-size: 200% 100%;
  border-radius: inherit;
  animation: winningCardShine 1.5s ease-in-out;
  z-index: -1;
}

/* ========== ANIMAZIONI RACCOLTA CARTE VERSO IL CENTRO ========== */

@keyframes gatherToCenterFromBottom {
  0% {
    transform: translateY(0) scale(1) rotate(0deg);
  }
  100% {
    transform: translateY(-4rem) scale(0.9) rotate(-5deg);
  }
}

@keyframes gatherToCenterFromRight {
  0% {
    transform: translateX(0) scale(1) rotate(0deg);
  }
  100% {
    transform: translateX(-4rem) scale(0.9) rotate(-10deg);
  }
}

@keyframes gatherToCenterFromTop {
  0% {
    transform: translateY(0) scale(1) rotate(0deg);
  }
  100% {
    transform: translateY(4rem) scale(0.9) rotate(5deg);
  }
}

@keyframes gatherToCenterFromLeft {
  0% {
    transform: translateX(0) scale(1) rotate(0deg);
  }
  100% {
    transform: translateX(4rem) scale(0.9) rotate(10deg);
  }
}

/* Animazioni per mobile (distanza 4rem) */
.animate-gather-from-bottom {
  animation: gatherToCenterFromBottom 0.6s ease-out forwards;
  z-index: 200;
}

.animate-gather-from-right {
  animation: gatherToCenterFromRight 0.6s ease-out forwards;
  z-index: 200;
}

.animate-gather-from-top {
  animation: gatherToCenterFromTop 0.6s ease-out forwards;
  z-index: 200;
}

.animate-gather-from-left {
  animation: gatherToCenterFromLeft 0.6s ease-out forwards;
  z-index: 200;
}

/* Animazioni per desktop (distanza 2rem) */
@keyframes gatherToCenterFromBottomDesktop {
  0% {
    transform: translateY(0) scale(1) rotate(0deg);
  }
  100% {
    transform: translateY(-2rem) scale(0.9) rotate(-5deg);
  }
}

@keyframes gatherToCenterFromRightDesktop {
  0% {
    transform: translateX(0) scale(1) rotate(0deg);
  }
  100% {
    transform: translateX(-2rem) scale(0.9) rotate(-10deg);
  }
}

@keyframes gatherToCenterFromTopDesktop {
  0% {
    transform: translateY(0) scale(1) rotate(0deg);
  }
  100% {
    transform: translateY(2rem) scale(0.9) rotate(5deg);
  }
}

@keyframes gatherToCenterFromLeftDesktop {
  0% {
    transform: translateX(0) scale(1) rotate(0deg);
  }
  100% {
    transform: translateX(2rem) scale(0.9) rotate(10deg);
  }
}

.animate-gather-from-bottom-desktop {
  animation: gatherToCenterFromBottomDesktop 0.6s ease-out forwards;
  z-index: 200;
}

.animate-gather-from-right-desktop {
  animation: gatherToCenterFromRightDesktop 0.6s ease-out forwards;
  z-index: 200;
}

.animate-gather-from-top-desktop {
  animation: gatherToCenterFromTopDesktop 0.6s ease-out forwards;
  z-index: 200;
}

.animate-gather-from-left-desktop {
  animation: gatherToCenterFromLeftDesktop 0.6s ease-out forwards;
  z-index: 200;
}

/* =====================================================
   OTTIMIZZAZIONI ANDROID & CARICAMENTI
   ===================================================== */

/* Animazioni ottimizzate per Android WebView */
@media (max-width: 768px) {
  /* Forza l'accelerazione hardware per tutti gli spinner e loading */
  .animate-spin,
  .animate-pulse,
  .animate-bounce {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: transform;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  /* Ottimizzazione specifica per spinner di caricamento */
  [class*="border-t-"]:not([class*="border-t-transparent"]) {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  /* Migliora la performance delle animazioni skeleton */
  [class*="bg-amber-"][class*="animate-pulse"] {
    animation-duration: 1.5s;
    animation-timing-function: ease-in-out;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
}

/* Animazioni di caricamento robuste per tutti i dispositivi */
@keyframes androidCompatibleSpin {
  0% {
    transform: rotate(0deg) translateZ(0);
  }
  100% {
    transform: rotate(360deg) translateZ(0);
  }
}

@keyframes androidCompatiblePulse {
  0%,
  100% {
    opacity: 1;
    transform: translateZ(0);
  }
  50% {
    opacity: 0.5;
    transform: translateZ(0);
  }
}

@keyframes androidCompatibleBounce {
  0%,
  100% {
    transform: translateY(0) translateZ(0);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(-25%) translateZ(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

/* Classi alternative per Android se le animazioni Tailwind non funzionano */
.android-spin {
  animation: androidCompatibleSpin 1s linear infinite;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

.android-pulse {
  animation: androidCompatiblePulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

.android-bounce {
  animation: androidCompatibleBounce 1s infinite;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Ottimizzazioni per il caricamento iniziale dell'app */
.initial-loading-screen {
  /* Evita il flicker durante il caricamento */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Assicura rendering fluido */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  will-change: opacity;
}

.initial-loading-screen * {
  /* Applica l'accelerazione hardware a tutti i figli */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Transizioni fluide per il passaggio dal loading all'app */
.app-transition-enter {
  opacity: 0;
  transform: translateY(10px);
}

.app-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms ease-out, transform 300ms ease-out;
}

.app-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.app-transition-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 300ms ease-in, transform 300ms ease-in;
}

/* Animazioni per transizioni tra pagine */
@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutToRight {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes slideOutToLeft {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-100%);
    opacity: 0;
  }
}

/* Classi per le animazioni di pagina */
.page-slide-in-right {
  animation: slideInFromRight 0.3s ease-out forwards;
}

.page-slide-in-left {
  animation: slideInFromLeft 0.3s ease-out forwards;
}

.page-slide-out-right {
  animation: slideOutToRight 0.3s ease-out forwards;
}

.page-slide-out-left {
  animation: slideOutToLeft 0.3s ease-out forwards;
}

/* Ottimizzazioni Android per animazioni più fluide */
@media (pointer: coarse) {
  /* Solo elementi che effettivamente si animano usano hardware acceleration */
  [class*="animate-"],
  [class*="transition-"],
  [class*="motion-"],
  .card-animation,
  [data-card],
  .played-card,
  [class*="page-slide-"] {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000;
    perspective: 1000;
    will-change: transform, opacity;
  }

  /* Durate più lunghe per animazioni più fluide su Android */
  .duration-500 {
    animation-duration: 0.7s !important;
    transition-duration: 0.7s !important;
  }

  .duration-300 {
    animation-duration: 0.4s !important;
    transition-duration: 0.4s !important;
  }

  .duration-200 {
    animation-duration: 0.3s !important;
    transition-duration: 0.3s !important;
  }
}

/* Ottimizzazioni specifiche per GPU acceleration su mobile */
@media (max-width: 768px) {
  .card-animation,
  [data-card],
  .played-card {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    will-change: transform, opacity;
  }
}

/* Fix per problemi di rendering su Android */
@media (pointer: coarse) and (max-width: 768px) {
  /* Assicura che contenitori principali non abbiano transform che interferisce */
  .min-h-screen,
  .flex-1,
  main,
  [role="main"] {
    -webkit-transform: none !important;
    transform: none !important;
    will-change: auto;
  }

  /* Ripristina il rendering normale per elementi di layout */
  .space-y-8 > *,
  .grid > *,
  .flex > * {
    -webkit-transform: none;
    transform: none;
    -webkit-backface-visibility: visible;
    backface-visibility: visible;
  }

  /* Forza il reflow per assicurare il rendering completo */
  .card,
  .bg-gradient-to-br,
  .bg-gradient-to-r {
    contain: none;
    isolation: auto;
  }
}

# Integrazione AdMob - Documentazione

## 📋 Panoramica

Questo documento descrive l'implementazione dell'integrazione AdMob nell'app Marafone Romagnolo, configurata per mostrare banner pubblicitari in tutte le schermate eccetto quella di gioco.

## 🔧 Configurazione

### ID App e Unità Pubblicitaria

- **App ID**: `ca-app-pub-3013811216506035~1249225182`
- **Banner Ad Unit ID**: `ca-app-pub-3013811216506035/4166020803`
- **Tipo Banner**: Standard Banner (320x50px)
- **Posizione**: Fissa in fondo allo schermo

### Plugin Utilizzato

- **Plugin**: `@capacitor-community/admob` v7.0.3
- **Compatibilità**: Capacitor v7.x

## 🏗️ Architettura

### Componenti Principali

#### 1. AdMobService (`src/services/adMobService.ts`)

Servizio singleton per gestire:

- Inizializzazione di AdMob
- Controllo del premium pass
- Gestione degli ID delle unità pubblicitarie

#### 2. AdMobBanner (`src/components/ads/AdMobBanner.tsx`)

Componente React per renderizzare il banner:

- Gestisce il ciclo di vita del banner
- Mostra placeholder durante il caricamento
- Gestisce gli errori di caricamento

#### 3. AppLayout (`src/components/layout/AppLayout.tsx`)

Layout wrapper che:

- Posiziona il banner in modo fisso
- Gestisce il padding del contenuto
- Coordina con il footer esistente

#### 4. useAdBannerVisibility (`src/hooks/useAdBannerVisibility.ts`)

Hook per determinare la visibilità del banner:

- Escluso dalle pagine di gioco (`/game`, `/play`)
- Rispetta il premium pass
- Solo su piattaforme native

## 📱 Comportamento

### Pagine con Banner

- **Home** (`/`)
- **Profilo** (`/account`)
- **Amici** (`/friends`)
- **Negozio** (`/shop`)
- **Regole** (`/rules`)

### Pagine senza Banner

- **Gioco** (`/game`, `/play`) - Per non disturbare l'esperienza di gioco

### Posizionamento

- **Z-index**: 40 (sotto il footer nativo con z-index 50)
- **Posizione**: `fixed bottom-0` (o `bottom-[50px]` per il footer)
- **Altezza**: 50px fissi per mantenere la consistenza del layout

## 🎯 Logica Premium

### Controllo Premium Pass

```typescript
// Nel servizio AdMobService
hasPremiumPass(): boolean {
  const passValue = import.meta.env.VITE_PASS_PREMIUM;
  return passValue === "true";
}
```

### Variabile d'Ambiente

- **Nome**: `VITE_PASS_PREMIUM`
- **Valore Default**: "false" (mostra annunci)
- **Premium**: "true" (nasconde annunci)

## 🔄 Flusso di Inizializzazione

1. **App Startup**: `AppNavigationHandler` inizializza AdMobService
2. **Service Init**: AdMobService.initialize() configura il plugin
3. **Banner Load**: AdMobBanner monta e carica l'annuncio
4. **Layout Adjust**: AppLayout e MobileFooter si adattano

## 🚨 Gestione Errori

### Tipi di Errore Gestiti

- Errori di inizializzazione AdMob
- Errori di caricamento banner
- Timeout di rete
- Piattaforme non supportate

### Fallback

- Su web: banner non mostrato
- Su errore: placeholder informativo
- Premium user: banner completamente nascosto

## 📏 Layout e Spacing

### Aggiustamenti Layout

- **PageContainer**: `footerHeight` aumentato da 67px a 117px quando banner visibile
- **MobileFooter**: posizione `bottom-[50px]` quando banner presente
- **Contenuto principale**: `pb-[50px]` per evitare sovrapposizioni

### Responsive Design

- Banner mantiene altezza fissa di 50px
- Layout si adatta automaticamente alla presenza/assenza del banner
- Animazioni smooth per transizioni

## 🔧 Configurazioni Capacitor

### capacitor.config.ts

```typescript
plugins: {
  AdMob: {
    appId: "ca-app-pub-3013811216506035~1249225182",
    testingDevices: [],
  },
}
```

## 📊 Conformità AdMob

### Best Practices Implementate

- ✅ Banner non interferisce con navigazione
- ✅ Posizionamento fisso e visibile
- ✅ Esclusione dalle pagine di gioco
- ✅ Gestione corretta del ciclo di vita
- ✅ Rispetto delle policy AdMob su visibilità

### Policy Compliance

- Banner sempre visibile quando mostrato
- Non coperto da altri elementi UI
- Posizionamento non ingannevole
- Cleanup corretto alla chiusura dell'app

## 🧪 Testing

### Testing in Sviluppo

```typescript
// Per testing, modificare in AdMobService:
isTesting: true; // in BannerAdOptions
```

### Verifica Funzionalità

1. Banner appare su tutte le pagine tranne gioco
2. Premium pass nasconde correttamente gli annunci
3. Layout si adatta correttamente
4. Nessuna interferenza con il gameplay
5. Cleanup corretto al cambio pagina

## 🔮 Estensioni Future

### Possibili Miglioramenti

- Annunci interstitial tra le partite
- Rewarded ads per bonus in-game
- Personalizzazione posizione banner
- A/B testing per ottimizzazione revenue

### Integrazione Shop

Il sistema è già pronto per integrarsi con:

- Acquisto rimozione pubblicità
- Gestione premium pass via in-app purchase
- Sincronizzazione stato premium con backend

## 📋 Checklist Implementazione

- [x] Plugin AdMob installato e configurato
- [x] Servizio AdMobService implementato
- [x] Componente banner creato
- [x] Layout aggiornato per gestire spazio banner
- [x] Hook visibilità implementato
- [x] Esclusione pagine di gioco
- [x] Gestione premium pass
- [x] Inizializzazione app integrata
- [x] Configurazione Capacitor aggiornata
- [x] Gestione errori implementata
- [x] Documentazione completata

## 🏁 Deployment

### Build Production

```bash
npm run build
npx cap sync
npx cap build android
```

### Verifica Pre-Release

1. Test su dispositivo fisico
2. Verifica caricamento annunci
3. Test esclusione pagine gioco
4. Verifica premium pass
5. Test layout responsive

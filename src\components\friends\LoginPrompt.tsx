import React, { useState } from "react";
import { Users, LogIn } from "lucide-react";
import ActionButton from "@/components/ui/ActionButton";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import LoginForm from "@/components/LoginForm";

const LoginPrompt: React.FC = () => {
  const [loginOpen, setLoginOpen] = useState(false);

  return (
    <Card className="overflow-hidden border-2 border-red-200 shadow-lg">
      <CardHeader className="bg-gradient-to-r from-red-500 to-yellow-500 text-white">
        <CardTitle className="text-xl font-bold flex items-center gap-2">
          <Users className="h-5 w-5" /> La tua rete di amici
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <div className="text-center mb-6">
          <div className="flex items-center justify-center">
            <Users className="h-16 w-16 text-red-500" />
          </div>
          <h2 className="text-xl font-bold text-red-700 mt-2">
            Accedi per vedere i tuoi amici
          </h2>
          <p className="text-sm text-gray-600 mt-2">
            Devi effettuare l'accesso per visualizzare e gestire la tua lista
            amici
          </p>
        </div>

        <Dialog open={loginOpen} onOpenChange={setLoginOpen}>
          <DialogTrigger asChild>
            <ActionButton
              size="lg"
              onClick={() => setLoginOpen(true)}
              className="flex items-center mx-auto bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600 shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all text-white py-3 px-6 justify-center gap-2 rounded-lg"
            >
              <LogIn className="h-5 w-5" /> Accedi
            </ActionButton>
          </DialogTrigger>
          <DialogContent className="p-0 border-none bg-transparent shadow-none max-w-md">
            <DialogTitle className="sr-only">Login Form</DialogTitle>
            <LoginForm onClose={() => setLoginOpen(false)} />
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default LoginPrompt;

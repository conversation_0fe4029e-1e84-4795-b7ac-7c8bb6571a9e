# Esempio di configurazione keystore per la firma degli AAB
# Copia questo file come 'keystore.properties' nella cartella android/ e compila con i tuoi dati

# Percorso al file keystore (relativo alla cartella android/)
storeFile=app/release-key.keystore

# Password del keystore
storePassword=YOUR_KEYSTORE_PASSWORD

# Alias della chiave nel keystore
keyAlias=YOUR_KEY_ALIAS

# Password della chiave
keyPassword=YOUR_KEY_PASSWORD

# NOTA IMPORTANTE:
# 1. Non committare mai questo file nel repository git
# 2. Il file keystore.properties è già incluso nel .gitignore
# 3. Assicurati che il file keystore (release-key.keystore) sia presente nella cartella app/
# 4. Per creare un nuovo keystore, usa il comando:
#    keytool -genkey -v -keystore release-key.keystore -alias your-alias -keyalg RSA -keysize 2048 -validity 10000

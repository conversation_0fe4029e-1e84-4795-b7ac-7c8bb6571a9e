import React from "react";
import { useUserState } from "@/hooks/useUserState";
import { getPlayerTitle } from "@/services/playerTitlesService";

interface UserProfileFooterProps {
  className?: string;
  showXP?: boolean;
  showLevel?: boolean;
  showRank?: boolean;
}

const UserProfileFooter: React.FC<UserProfileFooterProps> = ({
  className = "",
  showXP = true,
  showLevel = true,
  showRank = true,
}) => {
  const { userState, isLoading } = useUserState();
  const playerTitle = getPlayerTitle(userState.level);

  if (isLoading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
        <div className="flex flex-col gap-1">
          <div className="w-20 h-3 bg-gray-200 rounded animate-pulse"></div>
          <div className="w-16 h-2 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      {/* Avatar/Rank Icon */}
      <div className="relative flex-shrink-0">
        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-amber-100 to-yellow-100 border-2 border-amber-300 flex items-center justify-center">
          <img
            src={playerTitle.rankImage}
            alt={playerTitle.title}
            className="w-5 h-5 object-contain"
          />
        </div>
        {showLevel && (
          <div className="absolute -top-1 -right-1 bg-amber-600 text-white text-xs px-1 rounded-full min-w-[16px] h-4 flex items-center justify-center font-bold">
            {userState.level}
          </div>
        )}
      </div>

      {/* User Info */}
      <div className="flex flex-col min-w-0 flex-1">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-900 truncate">
            {userState.username}
          </span>
          {!userState.isOfflineMode && (
            <div className="w-2 h-2 bg-green-400 rounded-full" title="Online" />
          )}
        </div>

        <div className="flex items-center gap-2 text-xs text-gray-600">
          {showRank && <span className="truncate">{userState.rank}</span>}
          {showXP && showRank && <span>•</span>}
          {showXP && <span>{userState.xp} XP</span>}
        </div>
      </div>
    </div>
  );
};

export default UserProfileFooter;

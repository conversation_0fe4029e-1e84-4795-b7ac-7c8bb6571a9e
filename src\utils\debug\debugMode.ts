/**
 * 🔍 Utilità per il Debug Mode
 *
 * Consente di vedere le carte degli altri giocatori per capire la logica CPU
 * Solo su web, non su Android
 */

import { Card } from "../game/cardUtils";
import { Capacitor } from "@capacitor/core";

/**
 * Verifica se il debug mode è attivato
 * Su Android è sempre disabilitato per evitare confusione agli utenti
 */
export const isDebugModeEnabled = (): boolean => {
  // Su Android il debug mode è sempre disabilitato
  if (Capacitor.isNativePlatform()) {
    return false;
  }

  // Su web controlla la variabile d'ambiente
  return import.meta.env.VITE_DEBUG_SHOW_ALL_CARDS === "true";
};

/**
 * Log di debug che viene mostrato solo se il debug mode è attivo
 */
export const debugLog = (message: string, ...args: unknown[]) => {
  if (isDebugModeEnabled()) {
    console.log(`🔍 [DEBUG]`, message, ...args);
  }
};

/**
 * Log specifico per le decisioni AI
 */
export const debugAI = (
  playerIndex: number,
  decision: string,
  card: Card,
  reasoning?: string
) => {
  if (isDebugModeEnabled()) {
    const playerName = `Giocatore ${playerIndex + 1}`;
    console.group(`🤖 [AI DEBUG] ${playerName}`);
    console.log(`🎯 Decisione: ${decision}`);
    console.log(`🃏 Carta scelta: ${card.rank} di ${card.suit}`);
    if (reasoning) {
      console.log(`💭 Ragionamento: ${reasoning}`);
    }
    console.groupEnd();
  }
};

/**
 * Log per situazioni critiche (assi sul tavolo, ecc.)
 */
export const debugCritical = (situation: string, details: unknown) => {
  if (isDebugModeEnabled()) {
    console.warn(`🚨 [DEBUG CRITICO] ${situation}`, details);
  }
};

/**
 * Avviso di debug mostrato nella console
 */
export const showDebugWarning = () => {
  if (isDebugModeEnabled()) {
    console.warn(
      `🔍 DEBUG MODE ATTIVO!

Puoi vedere le carte di tutti i giocatori.
Per disattivare, imposta VITE_DEBUG_SHOW_ALL_CARDS=false nel file .env

Questo è utile per:
- Capire la logica delle decisioni CPU
- Verificare se l'AI risponde correttamente agli assi
- Testare le strategie collaborative
- Debug delle strategie anti-spreco
- Forzare la maraffa per testare la logica`
    );
  }
};

/**
 * Verifica se è attiva la modalità debug per forzare la maraffa al giocatore
 */
export const isForcePlayerMaraffaEnabled = (): boolean => {
  if (!isDebugModeEnabled()) return false;
  return localStorage.getItem("debug_force_player_maraffa") === "true";
};

/**
 * Verifica se è attiva la modalità debug per forzare la maraffa alla CPU
 */
export const isForceCpuMaraffaEnabled = (): boolean => {
  if (!isDebugModeEnabled()) return false;
  return localStorage.getItem("debug_force_cpu_maraffa") === "true";
};

/**
 * Log specifico per il debug della maraffa forzata
 */
export const debugForceMaraffa = (playerIndex: number, suit: string) => {
  if (isDebugModeEnabled()) {
    console.log(
      `🎯 [DEBUG MARAFFA] Maraffa forzata per giocatore ${
        playerIndex + 1
      } con seme ${suit}`
    );
  }
};

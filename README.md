# 🐓 Marafone Romagnolo

Un'implementazione digitale del tradizionale gioco di carte romagnolo "Marafone", sviluppato con React, TypeScript e Vite.

## 🎮 Caratteristiche

- **Gioco completo** - Implementazione fedele delle regole del Marafone Romagnolo
- **AI intelligente** - 3 livelli di difficoltà (Facile, Medio, Difficile)
- **Design autentico** - Tema rustico romagnolo con carte tradizionali
- **Performance ottimizzate** - Sistema avanzato di cache e precaricamento immagini
- **Mobile-first** - Design responsive per tutti i dispositivi
- **PWA Ready** - Installabile come app su dispositivi mobili

## 🚀 Quick Start

## Come Modificare il Codice

Puoi modificare il codice in diversi modi:

**Usa il tuo IDE preferito**

Puoi clonare questo repository e fare push delle modifiche direttamente.

Il solo requisito è avere Node.js & npm installati - [installa con nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## ⚡ Installazione e Sviluppo

```bash
# Clona il repository
git clone <YOUR_GIT_URL>

# Naviga nella cartella del progetto
cd marafone-romagnolo

# Installa le dipendenze
npm install

# Avvia il server di sviluppo
npm run dev

# Build per produzione
npm run build

# Preview build
npm run preview
```

## 📱 Build APK (Android)

```powershell
# Usa lo script PowerShell ottimizzato
.\scripts\build-apk.ps1

# O lo script batch semplificato
.\scripts\build-apk.bat
```

## 🏗️ Architettura del Progetto

```
├── 📁 docs/              # Documentazione completa
│   ├── GUIDA_REPOSITORY.md      # Architettura e guida sviluppatori
│   ├── logica-di-gioco.md       # Regole e logica implementata
│   └── OTTIMIZZAZIONI_IMMAGINI.md # Performance e ottimizzazioni
├── 📁 scripts/           # Script di utilità e build
├── 📁 src/
│   ├── 📁 components/     # Componenti React
│   ├── 📁 utils/
│   │   ├── 📁 game/       # Logica di gioco e carte
│   │   ├── 📁 ai/         # Sistema di intelligenza artificiale
│   │   └── 📁 ui/         # Utility UI e ottimizzazioni immagini
│   ├── 📁 pages/          # Pagine dell'applicazione
│   ├── 📁 hooks/          # Custom React hooks
│   └── 📁 types/          # Definizioni TypeScript
└── 📁 public/
    └── 📁 images/         # Assets grafici (carte, loghi, semi)
```

## 🎯 Funzionalità Principali

### 🎲 Sistema di Gioco

- **Regole autentiche** del Marafone Romagnolo
- **Gestione completa** di punteggi, round e partite
- **Sistema di annunci** (busso, striscio, volo)
- **Logica Marafone** (asso, due, tre di briscola)

### 🤖 Intelligenza Artificiale

- **3 livelli di difficoltà** con strategie diverse
- **AI adattiva** che analizza la situazione di gioco
- **Gestione avanzata** di cooperazione tra compagni di squadra

### 🎨 Design e UX

- **Tema rustico romagnolo** con texture autentiche
- **Animazioni fluide** e feedback visivi
- **Design responsive** ottimizzato per mobile e desktop
- **Accessibilità** con indicatori chiari per azioni disponibili

## 🛠️ Tecnologie Utilizzate

### Core

- **React 18** - Libreria UI moderna
- **TypeScript** - Type safety e developer experience
- **Vite** - Build tool veloce e moderno

### Styling

- **Tailwind CSS** - Framework CSS utility-first
- **shadcn/ui** - Componenti UI pre-costruiti
- **CSS Custom Properties** - Variabili per temi personalizzati

### Build e Deploy

- **Capacitor** - Wrapper per app native
- **Android SDK** - Build APK per dispositivi Android
- **PWA** - Progressive Web App capabilities

## 📖 Documentazione

La documentazione completa del progetto è disponibile nella cartella `docs/`:

- **[📚 Indice Documentazione](./docs/README.md)** - Panoramica completa di tutta la documentazione
- **[📋 Panoramica App](./docs/APP_OVERVIEW.md)** - Funzionalità e caratteristiche principali
- **[🏗️ Guida Repository](./docs/GUIDA_REPOSITORY.md)** - Architettura e guida per sviluppatori
- **[🎮 Logica di Gioco](./docs/logica-di-gioco.md)** - Regole e implementazione del gioco
- **[⚡ Ottimizzazioni](./docs/OTTIMIZZAZIONI_IMMAGINI.md)** - Sistema di performance avanzato

### 🛠️ Per Sviluppatori

- **[Build Scripts](./docs/BUILD_SCRIPTS.md)** - Tool di sviluppo e deployment
- **[Assets Guide](./docs/IMAGE_ASSETS.md)** - Gestione immagini e risorse

### 📱 Per Publishing

- **[Play Store](./docs/PLAY_STORE_LISTING.md)** - Informazioni per la pubblicazione
- **[Privacy Policy](./docs/PRIVACY_POLICY.md)** - Policy sulla privacy

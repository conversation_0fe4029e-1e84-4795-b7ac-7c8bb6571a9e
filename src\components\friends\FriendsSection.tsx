
import React, { useState, useEffect } from "react";
import UserSearch from "@/components/UserSearch";
import FriendsList from "@/components/FriendsList";
import FriendRequests from "@/components/FriendRequests";
import { useAuth } from "@/context/auth-context";
import { FriendRequest } from "@/types/database";

const FriendsSection: React.FC = () => {
  const [pendingRequests, setPendingRequests] = useState<FriendRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { getFriendRequests, acceptFriendRequest, rejectFriendRequest } = useAuth();

  useEffect(() => {
    checkPendingRequests();

    // Set up periodic check for requests
    const interval = setInterval(checkPendingRequests, 60000);
    return () => clearInterval(interval);
  }, []);

  const checkPendingRequests = async () => {
    setIsLoading(true);
    try {
      const requests = await getFriendRequests();
      setPendingRequests(requests);
    } catch (error) {
      console.error("Error checking friend requests:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAcceptRequest = async (requestId: string) => {
    await acceptFriendRequest(requestId);
    checkPendingRequests(); // Refresh the list after accepting
  };

  const handleDeclineRequest = async (requestId: string) => {
    await rejectFriendRequest(requestId);
    checkPendingRequests(); // Refresh the list after declining
  };

  return (
    <div className="space-y-6">
      <UserSearch />

      <FriendsList />
      
      <div>
        <h3 className="text-lg font-medium text-red-700 mb-4">
          Richieste di Amicizia
        </h3>
        {pendingRequests.length > 0 ? (
          <FriendRequests 
            requests={pendingRequests}
            onAccept={handleAcceptRequest}
            onDecline={handleDeclineRequest}
            onRefresh={checkPendingRequests}
            isLoading={isLoading}
          />
        ) : (
          <p className="text-gray-500">
            Non ci sono richieste di amicizia in sospeso.
          </p>
        )}
      </div>
    </div>
  );
};

export default FriendsSection;

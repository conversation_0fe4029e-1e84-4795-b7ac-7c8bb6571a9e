export enum Suit {
  Coins = "coins",
  Cups = "cups",
  Swords = "swords",
  Clubs = "clubs",
}

export enum Rank {
  Ace = "A",
  Two = "2",
  Three = "3",
  Four = "4",
  Five = "5",
  Six = "6",
  Seven = "7",
  <PERSON> = "J",
  Horse = "H",
  <PERSON> = "<PERSON>",
}

// Nomi italiani dei ranghi delle carte
export const rankNames: Record<string, string> = {
  A: "Asso",
  2: "Due",
  3: "Tre",
  4: "Quattro",
  5: "Cinque",
  6: "Sei",
  7: "<PERSON><PERSON>",
  J: "<PERSON><PERSON>",
  H: "Cava<PERSON>",
  K: "Re",
};

// Italian names for suits
export const suitNames: Record<string, string> = {
  coins: "Denari",
  cups: "Coppe",
  swords: "Spade",
  clubs: "Bastoni",
};

// Aggiungo nomi italiani per le carte in formato "setta di denari"
export const rankNamesItalian: Record<string, string> = {
  A: "asso",
  "3": "tre",
  "2": "due",
  K: "re",
  H: "cavallo",
  J: "fante",
  "7": "setta",
  "6": "sei",
  "5": "cinque",
  "4": "quattro",
};

export interface Card {
  id: string;
  suit: Suit;
  rank: Rank;
  displayName: string;
  order: number;
  value: number;
}

// Aggiorno la funzione createDeck per utilizzare il nuovo formato
export const createDeck = (): Card[] => {
  const deck: Card[] = [];

  Object.values(Suit).forEach((suit) => {
    Object.values(Rank).forEach((rank) => {
      let order = 0;
      let value = 0;

      // Assign order and value based on rank
      // Nuovo sistema: value viene diviso per 10 per ottenere i punti reali
      // Figure: 3/10 = 0.3 punti, Assi: 10/10 = 1.0 punto
      switch (rank) {
        case Rank.Three:
          order = 10; // Highest
          value = 3; // 0.3 point (3/10)
          break;
        case Rank.Two:
          order = 9;
          value = 3; // 0.3 point (3/10)
          break;
        case Rank.Ace:
          order = 8;
          value = 10; // 1 point (10/10)
          break;
        case Rank.King:
          order = 7;
          value = 3; // 0.3 point (3/10)
          break;
        case Rank.Horse:
          order = 6;
          value = 3; // 0.3 point (3/10)
          break;
        case Rank.Jack:
          order = 5;
          value = 3; // 0.3 point (3/10)
          break;
        case Rank.Seven:
          order = 4;
          value = 0;
          break;
        case Rank.Six:
          order = 3;
          value = 0;
          break;
        case Rank.Five:
          order = 2;
          value = 0;
          break;
        case Rank.Four:
          order = 1; // Lowest
          value = 0;
          break;
        default:
          break;
      }

      // Nuovo formato "setta di denari" invece di "denari sette"
      const displayName = `${rankNamesItalian[rank]} di ${suitNames[suit]}`;
      const id = generateUUID();

      deck.push({ id, suit, rank, displayName, order, value });
    });
  });

  return deck;
};

// Function to shuffle a deck of cards
export const shuffleDeck = (deck: Card[]): Card[] => {
  const shuffled = [...deck];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

// Function to deal cards to players
export const dealCards = (
  deck: Card[],
  numPlayers: number,
  cardsPerPlayer: number
): Card[][] => {
  const hands: Card[][] = Array(numPlayers)
    .fill([])
    .map(() => []);

  for (let i = 0; i < cardsPerPlayer; i++) {
    for (let j = 0; j < numPlayers; j++) {
      if (deck.length > 0) {
        const card = deck.pop()!;
        hands[j] = [...hands[j], card];
      }
    }
  }

  return hands;
};

// Function to get valid cards to play based on lead suit
export const getValidCards = (hand: Card[], leadSuit: Suit | null): Card[] => {
  if (!leadSuit) {
    return hand; // If no lead suit, all cards are valid
  }

  const cardsOfLeadSuit = hand.filter((card) => card.suit === leadSuit);

  if (cardsOfLeadSuit.length > 0) {
    return cardsOfLeadSuit; // Must follow lead suit if possible
  }

  return hand; // If no cards of lead suit, any card is valid
};

// Function to determine the winning card in a trick
export const getWinningCard = (
  trick: Card[],
  leadSuit: Suit,
  trumpSuit: Suit
): Card => {
  if (trick.length === 0)
    throw new Error("Cannot determine winner of empty trick");

  // Find highest trump card if any
  const trumpCards = trick.filter((card) => card.suit === trumpSuit);
  if (trumpCards.length > 0) {
    // Sort by order (higher is better)
    return trumpCards.reduce(
      (highest, current) => (current.order > highest.order ? current : highest),
      trumpCards[0]
    );
  }

  // No trump cards, find highest card of lead suit
  const leadSuitCards = trick.filter((card) => card.suit === leadSuit);
  if (leadSuitCards.length > 0) {
    // Sort by order (higher is better)
    return leadSuitCards.reduce(
      (highest, current) => (current.order > highest.order ? current : highest),
      leadSuitCards[0]
    );
  }

  // No lead suit cards either (shouldn't happen in standard rules)
  // Return highest card in trick
  return trick.reduce(
    (highest, current) => (current.order > highest.order ? current : highest),
    trick[0]
  );
};

// Function to calculate the total points in a trick using the new precise system
export const calculateTrickPoints = (
  cards: Card[]
): {
  acePoints: number;
  figureCount: number;
} => {
  // Usa il nuovo sistema di contatori separati
  let acePoints = 0;
  let figureCount = 0;

  cards.forEach((card) => {
    const cardResult = calculateCardPoints(card);
    acePoints += cardResult.acePoints;
    figureCount += cardResult.figureCount;
  });

  return { acePoints, figureCount };
};

// Add a function to generate UUIDs for game entities
export const generateUUID = (): string => {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

// Funzione per calcolare i punti di una carta usando il nuovo sistema preciso
export const calculateCardPoints = (
  card: Card
): {
  acePoints: number;
  figureCount: number;
} => {
  // Sistema preciso: contatori separati per assi e figure
  // 4 Assi = 4 punti + 20 Figure (ogni 3 = 1 punto) = 6 punti + 1 ultima presa = 11 punti totali
  switch (card.rank) {
    case Rank.Ace:
      return { acePoints: 1, figureCount: 0 }; // Asso vale 1 punto immediato
    case Rank.Three:
    case Rank.Two:
    case Rank.King:
    case Rank.Horse:
    case Rank.Jack:
      return { acePoints: 0, figureCount: 1 }; // Figura conta verso il gruppo di 3
    default:
      return { acePoints: 0, figureCount: 0 }; // Carte dal quattro al sette non valgono nulla
  }
};

// Suit SVG images for display
export const suitImages: Record<string, string> = {
  coins: "/images/semi/denari.png",
  cups: "/images/semi/coppe.png",
  swords: "/images/semi/spade.png",
  clubs: "/images/semi/bastoni.png",
};

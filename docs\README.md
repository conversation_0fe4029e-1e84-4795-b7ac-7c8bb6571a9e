# 📚 Documentazione - Marafone Romagnolo

Benvenuti nella documentazione completa del progetto **Marafone Romagnolo**, un'implementazione digitale del tradizionale gioco di carte romagnolo sviluppato con React, TypeScript e tecnologie moderne.

---

## 🗂️ Indice della Documentazione

### 📋 **Panoramica del Progetto**

- **[APP_OVERVIEW.md](./APP_OVERVIEW.md)** - Panoramica completa dell'applicazione, funzionalità e caratteristiche principali

### 🏗️ **Architettura e Sviluppo**

- **[GUIDA_REPOSITORY.md](./GUIDA_REPOSITORY.md)** - Guida completa all'architettura, stack tecnologico e organizzazione del codice
- **[OTTIMIZZAZIONI_IMMAGINI.md](./OTTIMIZZAZIONI_IMMAGINI.md)** - Sistema avanzato di ottimizzazione per il caricamento delle immagini

### 🎮 **Logica di Gioco**

- **[logica-di-gioco.md](./logica-di-gioco.md)** - Regole complete della Marafone Romagnolo e implementazione digitale

### 🛠️ **Build e Deployment**

- **[BUILD_SCRIPTS.md](./BUILD_SCRIPTS.md)** - Script di utilità per sviluppo, build e deployment
- **[BUILD_AAB.md](./BUILD_AAB.md)** - Guida per la generazione di file AAB per il Play Store

### 🎨 **Assets e Risorse**

- **[IMAGE_ASSETS.md](./IMAGE_ASSETS.md)** - Organizzazione e convenzioni per immagini, carte e loghi
- **[AUDIO_ASSETS.md](./AUDIO_ASSETS.md)** - Gestione file audio, effetti sonori e musica di sottofondo

### 📱 **Pubblicazione e Store**

- **[PLAY_STORE_LISTING.md](./PLAY_STORE_LISTING.md)** - Informazioni per la pubblicazione su Google Play Store
- **[PRIVACY_POLICY.md](./PRIVACY_POLICY.md)** - Policy sulla privacy e gestione dati utente

---

## 🚀 Guida Rapida per Iniziare

### Per **Nuovi Sviluppatori**:

1. 📖 Inizia con **[APP_OVERVIEW.md](./APP_OVERVIEW.md)** per capire cosa fa l'app
2. 🏗️ Leggi **[GUIDA_REPOSITORY.md](./GUIDA_REPOSITORY.md)** per l'architettura del progetto
3. 🎮 Consulta **[logica-di-gioco.md](./logica-di-gioco.md)** per le regole implementate

### Per **Contribuitori**:

1. 🛠️ Consulta **[BUILD_SCRIPTS.md](./BUILD_SCRIPTS.md)** per i tool di sviluppo
2. 🎨 Vedi **[IMAGE_ASSETS.md](./IMAGE_ASSETS.md)** e **[AUDIO_ASSETS.md](./AUDIO_ASSETS.md)** per gestire assets
3. ⚡ Leggi **[OTTIMIZZAZIONI_IMMAGINI.md](./OTTIMIZZAZIONI_IMMAGINI.md)** per le performance

### Per **Publishing**:

1. 📱 Segui **[BUILD_AAB.md](./BUILD_AAB.md)** per generare l'app
2. 🏪 Usa **[PLAY_STORE_LISTING.md](./PLAY_STORE_LISTING.md)** per il Play Store
3. 🔒 Includi **[PRIVACY_POLICY.md](./PRIVACY_POLICY.md)** per la privacy policy

---

## 🎯 Caratteristiche del Progetto

### ✨ **Tecnologie Principali**

- **React 18** + **TypeScript** per l'interfaccia utente
- **Tailwind CSS** + **shadcn/ui** per lo styling
- **Vite** per il build system
- **Capacitor** per l'app nativa Android

### 🎮 **Funzionalità**

- Gioco completo del Marafone Romagnolo con regole autentiche
- Sistema AI con 3 livelli di difficoltà
- Design responsive e ottimizzazioni mobile
- Sistema avanzato di cache per le performance
- PWA ready con installazione nativa

### 📊 **Prestazioni**

- Caricamento immagini ottimizzato con cache intelligente
- Precaricamento adattivo basato su dispositivo e connessione
- Bundle splitting e lazy loading per tempi di caricamento veloci

---

## 📝 Standard di Documentazione

### Convenzioni Utilizzate

- **📁 File markdown** organizzati per argomento
- **🔗 Link interni** tra documenti correlati
- **📋 Tabelle** per informazioni strutturate
- **💡 Esempi pratici** per illustrare concetti
- **⚠️ Note importanti** evidenziate chiaramente

### Mantenimento

- ✅ Documentazione aggiornata ad ogni release
- ✅ Esempi di codice testati e funzionanti
- ✅ Link verificati e funzionanti
- ✅ Versioning sincronizzato con il codice

---

## 🔗 Riferimenti Utili

### Documentazione Tecnica

- [React Documentation](https://react.dev/) - Libreria UI principale
- [TypeScript Handbook](https://www.typescriptlang.org/docs/) - Type safety
- [Tailwind CSS](https://tailwindcss.com/docs) - Framework CSS
- [Vite Guide](https://vitejs.dev/guide/) - Build tool

### Deployment e Mobile

- [Capacitor Documentation](https://capacitorjs.com/docs) - Wrapper nativo
- [Android Developer Guide](https://developer.android.com/docs) - Sviluppo Android
- [PWA Guidelines](https://web.dev/progressive-web-apps/) - Progressive Web Apps

---

_Ultima modifica: 7 giugno 2025 | Versione documentazione: 2.0_

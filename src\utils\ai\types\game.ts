import { Card } from "../../game/cardUtils";

/**
 * Tipi relativi alle meccaniche di gioco per l'AI
 */

export interface TrickWinner {
  playerIndex: number;
  winningCard: Card;
  trickValue: number;
}

export interface GamePhaseAnalysis {
  phase: "early" | "middle" | "late";
  tricksRemaining: number;
  criticalPhase: boolean;
  endGameStrategy: boolean;
}

export interface TeamPosition {
  playerIndex: number;
  team: number;
  isTeammate: boolean;
  isWinning: boolean;
  cardPlayed?: Card;
}

export interface TrumpAnalysis {
  trumpSuit: string | null;
  strongTrumpsPlayed: Card[];
  trumpsRemaining: number;
  opponentTrumpsEstimate: number;
  trumpExhaustionViable: boolean;
}

export interface SuitAnalysis {
  suit: string;
  cardsRemaining: number;
  strongestRemaining: Card | null;
  isDominant: boolean;
  safeToPlay: boolean;
}

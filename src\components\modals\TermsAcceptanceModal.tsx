import React, { useState } from "react";
import { TERMS_OF_SERVICE, PRIVACY_POLICY } from "@/data/legalTexts";
import ReactMarkdown from "react-markdown";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { ShieldCheck, FileText, X, ArrowLeft } from "lucide-react";

interface TermsAcceptanceModalProps {
  isOpen: boolean;
  onAccept: () => void;
  onDecline: () => void;
}

const TermsAcceptanceModal: React.FC<TermsAcceptanceModalProps> = ({
  isOpen,
  onAccept,
  onDecline,
}) => {
  const [isChecked, setIsChecked] = useState(true); // Default: già accettato
  const [currentView, setCurrentView] = useState<"main" | "terms" | "privacy">(
    "main"
  );

  const handleAccept = () => {
    if (isChecked) {
      onAccept();
    }
  };

  const showTerms = () => {
    setCurrentView("terms");
  };

  const showPrivacy = () => {
    setCurrentView("privacy");
  };

  const backToMain = () => {
    setCurrentView("main");
  };

  const handleDecline = () => {
    // Chiude l'app in ambiente mobile
    try {
      // Per Capacitor
      const capacitorApp = (
        window as unknown as { App?: { exitApp?: () => void } }
      ).App;
      if (capacitorApp?.exitApp) {
        capacitorApp.exitApp();
      }
      // Per ambiente web
      else {
        window.close();
      }
    } catch (error) {
      console.log("Impossibile chiudere l'app:", error);
    }
    onDecline();
  };

  // Contenuto della vista principale (versione compatta)
  const MainView = () => (
    <>
      {/* Header con gradiente */}
      <div className="bg-gradient-to-r from-amber-500 to-amber-600 p-4 rounded-t-2xl">
        <DialogHeader>
          <DialogTitle className="text-lg font-bold text-white flex items-center w-full">
            <div className="flex items-center gap-3 w-full">
              <div className="flex-shrink-0 w-10 h-10 rounded-xl bg-white/30 flex items-center justify-center shadow border border-amber-300 overflow-hidden">
                <img
                  src="/images/logos/logo-new_nobg.png"
                  alt="Marafone Romagnolo"
                  className="w-12 h-12 object-contain"
                  draggable={false}
                  style={{ background: "transparent" }}
                />
              </div>
              <span className="ml-1 text-left block w-full truncate">
                Benvenuto!
              </span>
            </div>
          </DialogTitle>
        </DialogHeader>
      </div>

      {/* Contenuto principale */}
      <div className="p-4 space-y-4">
        <div className="text-center space-y-2">
          <p className="text-gray-700 leading-relaxed text-sm">
            Prima di iniziare a giocare, devi accettare i nostri termini legali.
          </p>

          <div className="flex gap-1 justify-center">
            <Button
              variant="ghost"
              size="sm"
              className="text-blue-600 hover:bg-blue-50 text-xs px-2 py-1 rounded-lg"
              onClick={showTerms}
            >
              <FileText className="h-3 w-3 mr-1" />
              Termini
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-green-600 hover:bg-green-50 text-xs px-2 py-1 rounded-lg"
              onClick={showPrivacy}
            >
              <ShieldCheck className="h-3 w-3 mr-1" />
              Privacy
            </Button>
          </div>
        </div>

        {/* Clickwrap Checkbox */}
        <div className="bg-gray-50 rounded-xl p-3 border border-gray-200">
          <div className="flex items-start space-x-2">
            <Checkbox
              id="terms-acceptance"
              checked={isChecked}
              onCheckedChange={(checked) => setIsChecked(checked === true)}
              className="mt-1"
            />
            <label
              htmlFor="terms-acceptance"
              className="text-xs text-gray-700 leading-relaxed cursor-pointer select-none"
            >
              Accetto i{" "}
              <button
                onClick={showTerms}
                className="text-blue-600 hover:text-blue-800 underline font-medium"
              >
                Termini di Utilizzo
              </button>{" "}
              e la{" "}
              <button
                onClick={showPrivacy}
                className="text-green-600 hover:text-green-800 underline font-medium"
              >
                Privacy Policy
              </button>
            </label>
          </div>
        </div>

        {/* Bottoni di azione */}
        <div className="space-y-2">
          <Button
            onClick={handleAccept}
            disabled={!isChecked}
            className={`w-full py-2 rounded-xl text-white font-semibold transition-all duration-200 text-sm break-words whitespace-normal text-center ${
              isChecked
                ? "bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 shadow-lg hover:shadow-xl"
                : "bg-gray-300 cursor-not-allowed"
            }`}
            style={{ wordBreak: "break-word", whiteSpace: "normal" }}
          >
            {isChecked
              ? "Accetta - Inizia a Giocare!"
              : "Seleziona la casella per continuare"}
          </Button>

          <Button
            variant="ghost"
            onClick={handleDecline}
            className="w-full py-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-xl text-xs break-words whitespace-normal text-center"
            style={{ wordBreak: "break-word", whiteSpace: "normal" }}
          >
            <X className="h-3 w-3 mr-1" />
            Non accetto - Esci dall'app
          </Button>
        </div>
      </div>
    </>
  );

  // Contenuto della vista termini (versione semplificata)
  const TermsView = () => (
    <>
      <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-4 rounded-t-2xl">
        <DialogHeader>
          <DialogTitle className="text-lg font-bold text-white flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={backToMain}
              className="mr-2"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            Termini d'Utilizzo
          </DialogTitle>
        </DialogHeader>
      </div>
      <div className="p-4 max-h-[60vh] overflow-y-auto prose prose-sm">
        <ReactMarkdown>{TERMS_OF_SERVICE}</ReactMarkdown>
      </div>
    </>
  );

  // Contenuto della vista privacy (versione semplificata)
  const PrivacyView = () => (
    <>
      <div className="bg-gradient-to-r from-green-500 to-green-600 p-4 rounded-t-2xl">
        <DialogHeader>
          <DialogTitle className="text-lg font-bold text-white flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={backToMain}
              className="mr-2"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            Privacy Policy
          </DialogTitle>
        </DialogHeader>
      </div>
      <div className="p-4 max-h-[60vh] overflow-y-auto prose prose-sm">
        <ReactMarkdown>{PRIVACY_POLICY}</ReactMarkdown>
      </div>
    </>
  );

  // --- FINE PrivacyView ---

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent
        className="w-full max-w-[400px] mx-auto rounded-2xl p-0 border-0 shadow-2xl [&_button[aria-label=Close]]:hidden"
        style={{ width: "100vw", maxWidth: "400px", padding: 0 }}
      >
        {currentView === "main" && <MainView />}
        {currentView === "terms" && <TermsView />}
        {currentView === "privacy" && <PrivacyView />}
      </DialogContent>
    </Dialog>
  );
};

export default TermsAcceptanceModal;

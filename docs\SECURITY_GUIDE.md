# 🔒 Guida alla Sicurezza - Marafone Romagnolo

## 🚨 Configurazioni Sensibili

### ⚠️ CRITICO - Non Committare Mai

- `.env.local` - Configurazioni locali di sviluppo
- `.env.production` - Configurazioni di produzione
- `.env.development` - Configurazioni di sviluppo
- `android/keystore.properties` - Credenziali di firma Android
- `android/app/release-key.keystore` - Keystore di firma
- `android/app/google-services.json` - Configurazioni Firebase/Google

### 📋 Checklist Sicurezza Pre-Deploy

#### 1. Variabili d'Ambiente

- [ ] Tutte le chiavi API sono nel file `.env` (non committato)
- [ ] `VITE_DEBUG_SHOW_ALL_CARDS=false` per deploy pubblico
- [ ] `VITE_ENABLE_LOGGING=false` per deploy pubblico
- [ ] URL di redirect OAuth corretti

#### 2. Configurazioni Supabase

- [ ] Row Level Security (RLS) abilitato su tutte le tabelle
- [ ] Policies RLS configurate correttamente
- [ ] JWT expiry configurato (max 1 ora)
- [ ] Rate limiting abilitato

#### 3. Configurazioni Android

- [ ] Keystore protetto con password forte
- [ ] `allowMixedContent: false` in produzione
- [ ] Proguard/R8 abilitato per offuscazione
- [ ] Certificati SSL pinning (raccomandato)

## 🛡️ Implementazioni di Sicurezza

### 1. Gestione Sessioni

```typescript
// Timeout automatico sessioni
const SESSION_TIMEOUT = 15000; // 15 secondi
const MAX_RETRY_ATTEMPTS = 3;
```

### 2. Validazione Input

- Sanitizzazione username (max 18 caratteri)
- Validazione email lato client e server
- Escape di caratteri speciali

### 3. Protezione API

- Headers di sicurezza personalizzati
- Timeout su tutte le richieste
- Retry logic con backoff exponenziale

## 🔧 Setup Sicuro per Sviluppatori

### 1. Primo Setup

```bash
# Copia il template delle variabili
cp .env.example .env

# Compila con i tuoi valori reali
nano .env
```

### 2. Configurazione Android Signing

```bash
# Copia il template keystore
cp android/keystore.properties.example android/keystore.properties

# Genera keystore per sviluppo
keytool -genkey -v -keystore android/app/debug.keystore \
  -alias androiddebugkey -keyalg RSA -keysize 2048 -validity 10000
```

### 3. Verifica Sicurezza

```bash
# Controlla che i file sensibili non siano tracciati
git status --ignored

# Verifica build di produzione
npm run build
```

## 🚀 Deploy Sicuro

### 1. Variabili d'Ambiente per CI/CD

```yaml
# GitHub Actions / Vercel / Netlify
VITE_SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
VITE_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
VITE_ADMOB_APP_ID: ${{ secrets.ADMOB_APP_ID }}
```

### 2. Configurazioni Supabase Produzione

- Abilita 2FA per account admin
- Configura backup automatici
- Monitora logs di accesso
- Imposta alerting per attività sospette

### 3. Monitoraggio Sicurezza

- Log di autenticazione
- Monitoraggio rate limiting
- Alerting per tentativi di accesso non autorizzati
- Audit trail per modifiche critiche

## 🔍 Audit Sicurezza Regolare

### Mensile

- [ ] Rotazione chiavi API
- [ ] Review logs di sicurezza
- [ ] Update dipendenze con vulnerabilità
- [ ] Test penetration di base

### Trimestrale

- [ ] Audit completo configurazioni
- [ ] Review policies RLS
- [ ] Test disaster recovery
- [ ] Aggiornamento documentazione sicurezza

## 📞 Incident Response

### In caso di compromissione:

1. **Immediato**: Revocare tutte le chiavi API
2. **Entro 1 ora**: Cambiare password database
3. **Entro 24 ore**: Audit completo e patch
4. **Entro 48 ore**: Comunicazione agli utenti se necessario

### Contatti Emergenza

- Supabase Support: [<EMAIL>]
- Google Play Console: [<EMAIL>]
- Team interno: [<EMAIL>]

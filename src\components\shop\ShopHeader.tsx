import { Sparkles } from "lucide-react";

interface ShopHeaderProps {
  playerLevel: number;
}

const ShopHeader = ({ playerLevel }: ShopHeaderProps) => {
  return (
    <div
      className="p-4 flex items-center justify-between"
      style={{ paddingTop: `calc(1rem + env(safe-area-inset-top, 0px))` }}
    >
      <div className="flex items-center gap-4">
        <h1 
          className="text-2xl font-bold text-romagna-darkWood flex items-center gap-2"
          style={{
            fontFamily: "'DynaPuff', cursive",
            fontWeight: 600,
          }}
        >
          Negozio
        </h1>
      </div>
      {/* Informazioni livello giocatore */}
      <div className="flex items-center gap-2 bg-amber-100/80 backdrop-blur-sm border border-amber-300/50 rounded-full px-3 py-2">
        <Sparkles className="h-4 w-4 text-amber-600" />
        <span className="text-sm font-medium text-amber-800">
          Livello {playerLevel}
        </span>
      </div>
    </div>
  );
};

export default ShopHeader;


// This file consolidates game session services and exports them in a unified way

// Import from online service
import { 
  createGameSession as onlineCreateGameSession,
  getAvailableGameSessions as onlineGetAvailableGameSessions,
  joinGameSession as onlineJoinGameSession,
  getSessionData,
  updateGameState,
  updateGameCompleted,
  updatePlayerConnection,
  recordGameResult
} from './onlineGameService';

// Export the online versions by default
export const createGameSession = onlineCreateGameSession;
export const getAvailableGameSessions = onlineGetAvailableGameSessions;
export const joinGameSession = onlineJoinGameSession;

// Also export the other functions
export {
  getSessionData,
  updateGameState,
  updateGameCompleted,
  updatePlayerConnection,
  recordGameResult
};

// Add mock functions for testing - renamed to avoid conflicts
export const mockCreateGameSession = async (userId: string, username: string): Promise<string> => {
  console.log("Creating mock game session with userId:", userId, "and username:", username);
  return "mock-session-id";
};

export const mockGetAvailableGameSessions = async () => {
  console.log("Getting mock available game sessions");
  // Return mock game sessions for display
  return [
    {
      id: "mock-session-1",
      status: "waiting" as const,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      created_by: "user-123",
      players: [
        {
          id: "0",
          user_id: "user-123",
          username: "Marco",
          position: "north" as const,
          team: 0 as const,
          connected: true
        }
      ],
      current_state: null,
      winner_team: undefined
    },
    {
      id: "mock-session-2",
      status: "waiting" as const,
      created_at: new Date(Date.now() - 120000).toISOString(),
      updated_at: new Date(Date.now() - 120000).toISOString(),
      created_by: "user-456",
      players: [
        {
          id: "0",
          user_id: "user-456",
          username: "Giulia",
          position: "north" as const,
          team: 0 as const,
          connected: true
        },
        {
          id: "1",
          user_id: "user-789",
          username: "Lorenzo",
          position: "east" as const,
          team: 1 as const,
          connected: true
        }
      ],
      current_state: null,
      winner_team: undefined
    }
  ];
};

export const mockJoinGameSession = async (sessionId: string, userId: string, username: string): Promise<boolean> => {
  console.log("Joining mock game session with sessionId:", sessionId, "userId:", userId, "and username:", username);
  return true;
};

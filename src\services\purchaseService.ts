import { Capacitor } from "@capacitor/core";

// Import condizionale di RevenueCat per evitare errori su web
let Purchases:
  | typeof import("@revenuecat/purchases-capacitor").Purchases
  | null = null;

// Inizializza l'import solo su piattaforme native
async function loadPurchases() {
  if (Capacitor.isNativePlatform() && !Purchases) {
    try {
      const purchasesModule = await import("@revenuecat/purchases-capacitor");
      Purchases = purchasesModule.Purchases;
      console.log("✅ RevenueCat Purchases caricato");
    } catch (error) {
      console.warn("⚠️ RevenueCat Purchases non disponibile:", error);
    }
  }
}

// Configurazione prodotti
export const SUBSCRIPTION_PRODUCTS = {
  MONTHLY: "maraffa_premium_monthly",
  LIFETIME: "maraffa_premium_lifetime",
} as const;

export const SUBSCRIPTION_PRICES = {
  MONTHLY: {
    original: "2.99€",
    discounted: "2.49€",
    discount: "-17%",
  },
  LIFETIME: {
    original: "14.99€",
    discounted: "11.99€",
    discount: "-20%",
  },
} as const;

export interface PurchaseInfo {
  productId: string;
  price: string;
  title: string;
  description: string;
  isActive: boolean;
}

class PurchaseService {
  private isInitialized = false;
  private isPremiumActive = false;

  /**
   * Inizializza RevenueCat
   */
  async initialize(): Promise<void> {
    if (this.isInitialized || !Capacitor.isNativePlatform()) {
      console.log("PurchaseService: Inizializzazione saltata");
      return;
    }

    try {
      console.log("🛒 Inizializzazione PurchaseService...");

      await loadPurchases();

      if (!Purchases) {
        console.warn("RevenueCat non disponibile");
        return;
      }

      // Configura RevenueCat con la tua API key
      const revenueCatApiKey = import.meta.env.VITE_REVENUECAT_API_KEY;
      if (!revenueCatApiKey) {
        console.warn("⚠️ VITE_REVENUECAT_API_KEY non configurata");
        return;
      }

      await Purchases.configure({
        apiKey: revenueCatApiKey,
        appUserID: null, // Usa l'ID utente anonimo
      });

      this.isInitialized = true;
      console.log("✅ PurchaseService inizializzato");

      // Controlla lo stato premium attuale
      await this.checkPremiumStatus();
    } catch (error) {
      console.error("❌ Errore inizializzazione PurchaseService:", error);
      this.isInitialized = false;
    }
  }

  /**
   * Controlla se l'utente ha un abbonamento premium attivo
   */
  async checkPremiumStatus(): Promise<boolean> {
    if (!this.isInitialized || !Purchases) {
      return false;
    }

    try {
      const customerInfo = await Purchases.getCustomerInfo();

      // Controlla se ha entitlements attivi
      const hasPremium =
        customerInfo.entitlements?.active?.["premium"] !== undefined;

      this.isPremiumActive = hasPremium;
      console.log(`🔍 Status Premium: ${hasPremium ? "ATTIVO" : "NON ATTIVO"}`);

      return hasPremium;
    } catch (error) {
      console.error("❌ Errore controllo status premium:", error);
      return false;
    }
  }

  /**
   * Ottiene i prodotti disponibili
   */
  async getAvailableProducts(): Promise<PurchaseInfo[]> {
    if (!this.isInitialized || !Purchases) {
      return [];
    }

    try {
      const offerings = await Purchases.getOfferings();
      const currentOffering = offerings.current;

      if (!currentOffering) {
        console.warn("Nessuna offering disponibile");
        return [];
      }

      const products: PurchaseInfo[] = [];

      // Prodotto mensile
      const monthlyPackage = currentOffering.monthly;
      if (monthlyPackage) {
        products.push({
          productId: monthlyPackage.product.identifier,
          price: monthlyPackage.product.priceString,
          title: "Premium Mensile",
          description: "Rimuove le pubblicità per un mese",
          isActive: false,
        });
      }

      // Prodotto lifetime
      const lifetimePackage = currentOffering.lifetime;
      if (lifetimePackage) {
        products.push({
          productId: lifetimePackage.product.identifier,
          price: lifetimePackage.product.priceString,
          title: "Premium Lifetime",
          description: "Rimuove le pubblicità per sempre",
          isActive: false,
        });
      }

      return products;
    } catch (error) {
      console.error("❌ Errore recupero prodotti:", error);
      return [];
    }
  }

  /**
   * Acquista un prodotto
   */
  async purchaseProduct(productId: string): Promise<boolean> {
    if (!this.isInitialized || !Purchases) {
      console.error("PurchaseService non inizializzato");
      return false;
    }

    try {
      console.log(`🛒 Tentativo acquisto: ${productId}`);

      const offerings = await Purchases.getOfferings();
      const currentOffering = offerings.current;

      if (!currentOffering) {
        throw new Error("Nessuna offering disponibile");
      }

      let packageToPurchase = null;

      if (productId === SUBSCRIPTION_PRODUCTS.MONTHLY) {
        packageToPurchase = currentOffering.monthly;
      } else if (productId === SUBSCRIPTION_PRODUCTS.LIFETIME) {
        packageToPurchase = currentOffering.lifetime;
      }

      if (!packageToPurchase) {
        throw new Error(`Prodotto non trovato: ${productId}`);
      }

      const purchaseResult = await Purchases.purchasePackage({
        aPackage: packageToPurchase,
      });

      if (purchaseResult.customerInfo.entitlements?.active?.["premium"]) {
        console.log("✅ Acquisto completato con successo!");
        this.isPremiumActive = true;
        return true;
      } else {
        console.warn("⚠️ Acquisto completato ma premium non attivo");
        return false;
      }
    } catch (error) {
      console.error("❌ Errore durante l'acquisto:", error);
      return false;
    }
  }

  /**
   * Ripristina gli acquisti
   */
  async restorePurchases(): Promise<boolean> {
    if (!this.isInitialized || !Purchases) {
      return false;
    }

    try {
      console.log("🔄 Ripristino acquisti...");

      const customerInfo = await Purchases.restorePurchases();
      const hasPremium =
        customerInfo.entitlements?.active?.["premium"] !== undefined;

      this.isPremiumActive = hasPremium;
      console.log(
        `✅ Ripristino completato. Premium: ${
          hasPremium ? "ATTIVO" : "NON ATTIVO"
        }`
      );

      return hasPremium;
    } catch (error) {
      console.error("❌ Errore ripristino acquisti:", error);
      return false;
    }
  }

  /**
   * Verifica se l'utente ha il premium attivo
   */
  isPremium(): boolean {
    return this.isPremiumActive;
  }

  /**
   * Verifica se il servizio è inizializzato
   */
  isServiceInitialized(): boolean {
    return this.isInitialized;
  }
}

// Esporta un'istanza singleton
export const PurchaseServiceInstance = new PurchaseService();
export default PurchaseServiceInstance;

-- Script per pulire le policy duplicate e mantenere solo quelle corrette
-- Eseguire questo script nel SQL Editor del Dashboard Supabase

-- ====================================================================
-- PULIZIA POLICY DUPLICATE PER FRIENDSHIPS
-- ====================================================================

-- Rimuovi le policy duplicate che sono troppo restrittive
DROP POLICY IF EXISTS "Users can delete their own friendships" ON friendships;
DROP POLICY IF EXISTS "Users can create their own friendships" ON friendships;
DROP POLICY IF EXISTS "Users can view their own friendships" ON friendships;

-- ====================================================================
-- PULIZIA POLICY DUPLICATE PER FRIEND_REQUESTS
-- ====================================================================

-- Rimuovi le policy duplicate per friend_requests
DROP POLICY IF EXISTS "Users can create friend requests they send" ON friend_requests;
DROP POLICY IF EXISTS "Users can delete friend requests they sent or received" ON friend_requests;
DROP POLICY IF EXISTS "Users can see friend requests they sent or received" ON friend_requests;

-- ====================================================================
-- VERIFICA POLICY FINALI
-- ====================================================================

-- Verifica che rimangano solo le policy corrette
SELECT tablename, policyname, cmd, qual, with_check
FROM pg_policies 
WHERE tablename IN ('friendships', 'friend_requests')
ORDER BY tablename, policyname;

-- ====================================================================
-- POLICY CORRETTE FINALI DOVREBBERO ESSERE:
-- ====================================================================

-- FRIENDSHIPS:
-- - Users can delete own friendships (DELETE con user_id OR friend_id) ✅
-- - Users can insert own friendships (INSERT con user_id) ✅
-- - Users can update own friendships (UPDATE con user_id OR friend_id) ✅
-- - Users can view own friendships (SELECT con user_id OR friend_id) ✅

-- FRIEND_REQUESTS:
-- - Users can delete own friend_requests (DELETE con sender_id OR receiver_id) ✅
-- - Users can insert own friend_requests (INSERT con sender_id) ✅
-- - Users can update own friend_requests (UPDATE con sender_id OR receiver_id) ✅
-- - Users can view own friend_requests (SELECT con sender_id OR receiver_id) ✅

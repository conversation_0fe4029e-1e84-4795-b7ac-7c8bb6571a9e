import { useMemo } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Check, Dices, Crown } from "lucide-react";
import { tableMats, type TableMat } from "@/data/shopData";
import ShopImage from "@/components/ui/ShopImage";

// CSS per l'animazione jelly
const jellyButtonStyles = `
  @keyframes jellyShimmer {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }
  
  @keyframes jellyBounce {
    0%, 100% { transform: perspective(1000px) rotateX(5deg) scale(1); }
    50% { transform: perspective(1000px) rotateX(8deg) scale(1.02); }
  }
`;

/**
 * TableMatsSection - Componente ottimizzato per la visualizzazione dei tappetini da gioco
 *
 * Caratteristiche di ottimizzazione implementate:
 * - Uso di immagini originali ad alta risoluzione invece delle versioni 50x50
 * - Precaricamento intelligente con priorità (prima tappetini sbloccati/gratuiti)
 * - Fallback automatico alle immagini 50x50 in caso di errore
 * - Lazy loading e decoding async per le performance
 * - Gestione della cache e riduzione del layout shift
 * - Indicatori di caricamento per una migliore UX
 * - Bottoni in stile jelly/glossy con animazioni e nome tappetino
 */

interface TableMatsSectionProps {
  selectedTableMat: string;
  hasAdPass: boolean;
  onTableMatSelect: (matId: string) => void;
  isItemUnlocked: (item: TableMat) => boolean;
}

const TableMatsSection = ({
  selectedTableMat,
  hasAdPass,
  onTableMatSelect,
  isItemUnlocked,
}: TableMatsSectionProps) => {
  // Stili comuni per i bottoni per ridurre duplicazione
  const getButtonStyles = useMemo(
    () => ({
      base: {
        fontFamily: "'Nunito', sans-serif",
        fontWeight: "800",
        textShadow: "0 1px 2px rgba(0,0,0,0.5)",
        boxShadow: `
        0 12px 30px rgba(0,0,0,0.25),
        0 6px 20px rgba(0,0,0,0.15),
        inset 0 2px 4px rgba(255,255,255,0.5),
        inset 0 -2px 4px rgba(0,0,0,0.2),
        inset 0 0 20px rgba(255,255,255,0.1)
      `,
        border: "1px solid rgba(255,255,255,0.2)",
        transform: "perspective(1000px) rotateX(5deg)",
      },
      gradients: {
        selected:
          "linear-gradient(145deg, #34e89e 0%, #10b981 30%, #6ee7b7 100%)",
        default:
          "linear-gradient(145deg, #f97316 0%, #ea580c 20%, #dc2626 40%, #b91c1c 70%, #991b1b 100%)",
        disabled:
          "linear-gradient(145deg, #6b7280 0%, #4b5563 20%, #374151 40%, #1f2937 70%, #111827 100%)",
        purchase:
          "linear-gradient(145deg, #3b82f6 0%, #2563eb 20%, #1d4ed8 40%, #1e40af 70%, #1e3a8a 100%)",
        premium:
          "linear-gradient(145deg, #8b5cf6 0%, #7c3aed 20%, #6d28d9 40%, #5b21b6 70%, #4c1d95 100%)",
      },
    }),
    []
  );

  // Componente bottone riutilizzabile
  const MatButton = ({
    mat,
    children,
  }: {
    mat: TableMat;
    children: React.ReactNode;
  }) => {
    const isSelected = selectedTableMat === mat.id;
    const isUnlocked = isItemUnlocked(mat);
    const isDisabled = mat.unlockType === "level" && !isUnlocked;

    const gradient = isSelected
      ? getButtonStyles.gradients.selected
      : !isUnlocked
      ? mat.unlockType === "purchase"
        ? getButtonStyles.gradients.purchase
        : mat.unlockType === "premium"
        ? getButtonStyles.gradients.premium
        : getButtonStyles.gradients.disabled
      : getButtonStyles.gradients.default;

    return (
      <button
        onClick={() =>
          (!isDisabled || mat.unlockType !== "level") &&
          onTableMatSelect(mat.id)
        }
        disabled={isDisabled}
        className="w-full h-8 px-2.5 rounded-2xl text-xs font-bold overflow-hidden relative transition-all duration-300 hover:scale-105 active:scale-95 text-white disabled:cursor-not-allowed disabled:hover:scale-100"
        style={{
          ...getButtonStyles.base,
          background: gradient,
          opacity: isDisabled ? 0.7 : 1,
        }}
      >
        <span className="relative z-10 drop-shadow-lg">{children}</span>

        {/* Gradiente animato */}
        <div
          className="absolute inset-0 rounded-2xl opacity-90"
          style={{
            background: gradient,
            backgroundSize: "400% 400%",
            animation: "jellyShimmer 3s ease-in-out infinite",
          }}
        />

        {/* Riflesso glossy */}
        <div
          className="absolute inset-0 rounded-2xl pointer-events-none z-[1]"
          style={{
            background:
              "linear-gradient(145deg, rgba(255,255,255,0.6) 0%, rgba(255,255,255,0.3) 30%, transparent 50%, rgba(0,0,0,0.1) 100%)",
          }}
        />

        {/* Riflesso liquido */}
        <div
          className="absolute inset-0 rounded-2xl pointer-events-none z-[3]"
          style={{
            background:
              "radial-gradient(ellipse at top, rgba(255,255,255,0.4) 0%, transparent 70%)",
          }}
        />
      </button>
    );
  };
  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: jellyButtonStyles }} />
      <Card
        className="border-2 border-orange-300 shadow-lg bg-gradient-to-br from-orange-50 to-yellow-50 relative overflow-hidden"
        style={{
          borderRadius: "20px",
          boxShadow: "0 4px 16px rgba(255, 152, 0, 0.2)",
        }}
      >
        <CardHeader className="pb-4 bg-gradient-to-r from-orange-500 to-red-500 text-white relative overflow-hidden">
          <div className="relative z-10">
            <CardTitle
              className="text-xl md:text-2xl text-white flex items-center gap-3 font-bold"
              style={{
                fontFamily: "'DynaPuff', cursive",
                fontWeight: 600,
              }}
            >
              <div className="relative bg-white/20 rounded-full p-2 border-2 border-white/40">
                <Dices className="h-6 w-6 text-yellow-300" />
              </div>
              <span className="flex-1 drop-shadow-lg uppercase">
                TAPPETINI DA GIOCO
              </span>
            </CardTitle>
            {/* <p className="text-white/95 text-sm md:text-base drop-shadow-md font-medium">
            Scegli la superficie perfetta!
          </p> */}
          </div>
        </CardHeader>

        <CardContent className="pt-6 px-4 pb-6">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {tableMats.map((mat) => (
              <div
                key={mat.id}
                className="group relative bg-white rounded-xl border-2 border-gray-200 hover:border-orange-400 transition-all duration-200 overflow-hidden shadow-sm hover:shadow-md"
                style={{
                  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                  background: "#F5F5DC",
                }}
              >
                {/* Contenuto carta */}
                <div className="p-2 text-center">
                  {/* Immagine grande del tappetino */}
                  <div className="relative mb-3">
                    <div
                      className={`w-28 h-28 md:w-32 md:h-32 mx-auto rounded-2xl border-3 shadow-lg flex items-center justify-center text-4xl md:text-5xl bg-gradient-to-br from-white to-gray-50 transition-all duration-300 group-hover:scale-110 group-hover:shadow-xl ${
                        !isItemUnlocked(mat) ? "opacity-50 grayscale" : ""
                      } ${
                        mat.unlockType === "premium"
                          ? "border-purple-400 shadow-purple-200"
                          : mat.unlockLevel >= 7
                          ? "border-red-400 shadow-red-200"
                          : mat.unlockLevel >= 3
                          ? "border-blue-400 shadow-blue-200"
                          : "border-orange-400 shadow-orange-200"
                      }`}
                    >
                      {mat.preview ? (
                        <ShopImage
                          src={mat.preview}
                          alt={mat.name}
                          className="w-full h-full object-cover rounded-xl"
                          containerClassName="w-full h-full"
                          priority={isItemUnlocked(mat)} // Priorità per tappetini sbloccati
                        />
                      ) : (
                        mat.icon
                      )}
                    </div>
                  </div>

                  {/* Bottone d'azione */}
                  <div className="mt-auto">
                    {mat.unlockType === "free" && (
                      <MatButton mat={mat}>
                        {selectedTableMat === mat.id ? (
                          <Check className="h-3 w-3 mx-auto" />
                        ) : (
                          mat.name
                        )}
                      </MatButton>
                    )}

                    {mat.unlockType === "level" && (
                      <MatButton mat={mat}>
                        {isItemUnlocked(mat) ? (
                          selectedTableMat === mat.id ? (
                            <Check className="h-3 w-3 mx-auto" />
                          ) : (
                            mat.name
                          )
                        ) : (
                          `LV.${mat.unlockLevel}`
                        )}
                      </MatButton>
                    )}

                    {mat.unlockType === "purchase" && (
                      <MatButton mat={mat}>
                        {isItemUnlocked(mat) ? (
                          selectedTableMat === mat.id ? (
                            <Check className="h-3 w-3 mx-auto" />
                          ) : (
                            mat.name
                          )
                        ) : (
                          <div className="flex items-center justify-center gap-1">
                            <span>{mat.price}</span>
                            💎
                          </div>
                        )}
                      </MatButton>
                    )}

                    {mat.unlockType === "premium" && (
                      <MatButton mat={mat}>
                        {isItemUnlocked(mat) ? (
                          selectedTableMat === mat.id ? (
                            <Check className="h-3 w-3 mx-auto" />
                          ) : (
                            mat.name
                          )
                        ) : !hasAdPass ? (
                          <Crown className="h-3 w-3 mx-auto" />
                        ) : (
                          `LV.${mat.unlockLevel}`
                        )}
                      </MatButton>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default TableMatsSection;

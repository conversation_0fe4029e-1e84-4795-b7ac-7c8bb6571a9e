/**
 * Interfaccia base per tutte le modali di conferma
 */
export interface ConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

/**
 * Interfaccia per modali di conferma con titolo e messaggio personalizzabili
 */
export interface CustomConfirmDialogProps extends ConfirmDialogProps {
  title?: string;
  message?: string;
}

/**
 * Interfaccia per la modale di notifica livello
 */
export interface LevelUpNotificationProps {
  level: number;
  isOpen: boolean;
  onClose: () => void;
}

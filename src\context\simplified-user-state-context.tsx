/**
 * 🎯 CONTEXT UTENTE SEMPLIFICATO
 * Sostituisce user-state-context.tsx con il sistema unificato
 */

import React, { createContext, useContext } from "react";
import { useUnifiedStorage } from "@/hooks/useUnifiedStorage";
import { getPlayerTitle } from "@/services/playerTitlesService";
import { useAuth } from "@/context/auth-context";

// Interface per compatibilità con codice esistente
export interface UserState {
  username: string;
  level: number;
  xp: number;
  rank: string;
  title: string;
  gamesPlayed: number;
  gamesWon: number;
  winRate: number;
  isOfflineMode: boolean;
  lastUpdated: string;
}

interface UserStateContextType {
  userState: UserState;
  isLoading: boolean;
  isSyncing: boolean;
  error: string | null;
  updateStateFromProcessedStats: (stats: any) => Promise<void>;
  refreshState: () => Promise<void>;
  updateUsername: (username: string) => Promise<void>;
}

const UserStateContext = createContext<UserStateContextType | undefined>(
  undefined
);

export const useUserState = (): UserStateContextType => {
  const context = useContext(UserStateContext);
  if (!context) {
    throw new Error("useUserState must be used within a UserStateProvider");
  }
  return context;
};

interface UserStateProviderProps {
  children: React.ReactNode;
}

export const UserStateProvider: React.FC<UserStateProviderProps> = ({
  children,
}) => {
  const { data, isLoading, recordGameResult } = useUnifiedStorage();
  const { updateProfile: updateAuthProfile } = useAuth();

  // Converte i dati unificati nel formato UserState per compatibilità
  const userState: UserState = data
    ? {
        username: data.username,
        level: data.level,
        xp: data.xp,
        rank: data.rank,
        title: data.title,
        gamesPlayed: data.gamesPlayed,
        gamesWon: data.gamesWon,
        winRate: data.winRate,
        isOfflineMode: data.isOfflineMode,
        lastUpdated: data.updatedAt,
      }
    : (() => {
        // Ottieni il titolo dinamico per il livello 1
        const defaultTitle = getPlayerTitle(1).title;
        return {
          username: defaultTitle,
          level: 1,
          xp: 0,
          rank: "Principiante",
          title: defaultTitle,
          gamesPlayed: 0,
          gamesWon: 0,
          winRate: 0,
          isOfflineMode: true,
          lastUpdated: new Date().toISOString(),
        };
      })();

  // Context pronto per l'uso

  // Funzioni di compatibilità
  const updateStateFromProcessedStats = async (stats: any): Promise<void> => {
    // Delega al sistema unificato
    recordGameResult(stats.gamesWon > userState.gamesWon);
  };

  const refreshState = async (): Promise<void> => {
    // Il sistema unificato si aggiorna automaticamente
    console.log("🔄 Refresh state delegato al sistema unificato");
  };

  const updateUsername = async (username: string): Promise<void> => {
    console.log("🔄 updateUsername chiamato con:", username);
    try {
      // Usa la funzione updateProfile del context di autenticazione
      await updateAuthProfile({ username });
      console.log("✅ Username aggiornato tramite auth context");
    } catch (error) {
      console.error("❌ Errore updateUsername:", error);
      throw error;
    }
  };

  const value: UserStateContextType = {
    userState,
    isLoading,
    isSyncing: false, // Il sistema unificato non ha sync separato
    error: null, // Il sistema unificato gestisce errori internamente
    updateStateFromProcessedStats,
    refreshState,
    updateUsername,
  };

  return (
    <UserStateContext.Provider value={value}>
      {children}
    </UserStateContext.Provider>
  );
};

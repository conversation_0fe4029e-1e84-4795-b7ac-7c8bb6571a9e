import React, { useMemo } from "react";
import { getPlayerTitle } from "@/services/playerTitlesService";
import { useUserState } from "@/hooks/useUserState";

interface MobileFooterProps {
  currentPage: "home" | "profile" | "friends" | "shop";
  onPageChange: (page: "home" | "profile" | "friends" | "shop") => void;
}

const MobileFooter: React.FC<MobileFooterProps> = ({
  currentPage,
  onPageChange,
}) => {
  // 🚀 NUOVO: Usa il sistema di stato globale persistente
  const { userState } = useUserState();

  // Ottieni il titolo del giocatore basato sul livello corrente dallo stato globale
  const currentPlayerTitle = getPlayerTitle(userState.level); // Usa useMemo per ricreare l'array footerItems quando cambiano le statistiche del sistema globale
  // Aggiorniamo le dipendenze per assicurarci che si aggiorni quando cambia il livello
  const footerItems = useMemo(
    () => [
      {
        id: "shop" as const,
        iconSrc: "/images/icons/shop_optimized.png",
        label: "Negozio",
        // Colori esatti dal bottone shop nel NewMobileMenu
        inactiveStyle: "text-blue-700",
        activeBg:
          "bg-gradient-to-br from-blue-100/90 via-blue-200/90 to-blue-300/90",
        activeText: "text-blue-700",
        hoverBg: "hover:bg-blue-100/50",
      },
      {
        id: "home" as const,
        iconSrc: "/images/icons/logo-new_nobg 100x100.png",
        label: "Gioca!",
        // Colori esatti dal bottone "Gioca!" nel NewMobileMenu
        inactiveStyle: "text-romagna-darkWood",
        activeBg: "bg-gradient-to-r from-romagna-rust to-romagna-terracotta",
        activeText: "text-white",
        hoverBg: "hover:bg-amber-100/50",
      },
      {
        id: "friends" as const,
        iconSrc: "/images/icons/amici 100x100.png",
        label: "Amici",
        // Colori esatti dal bottone amici nel NewMobileMenu
        inactiveStyle: "text-green-700",
        activeBg:
          "bg-gradient-to-br from-green-100/90 via-green-200/90 to-green-300/90",
        activeText: "text-green-700",
        hoverBg: "hover:bg-green-100/50",
      },
      {
        id: "profile" as const,
        iconSrc: currentPlayerTitle.rankImage, // Ora si aggiorna automaticamente dal sistema globale
        label: "Profilo",
        // Colori esatti dal bottone profilo nel NewMobileMenu
        inactiveStyle: "text-amber-700",
        activeBg:
          "bg-gradient-to-br from-amber-100/90 via-amber-200/90 to-yellow-200/90",
        activeText: "text-amber-700",
        hoverBg: "hover:bg-amber-100/50",
      },
    ],
    [currentPlayerTitle.rankImage] // Dipende solo dall'immagine del rank (che già include il livello globale)
  );
  return (
    <div className="fixed left-0 right-0 bottom-0 bg-gradient-to-r from-romagna-cream via-amber-50 to-romagna-cream border-t-2 border-romagna-gold/30 shadow-lg backdrop-blur-md z-50 overflow-visible">
      <div className="flex items-center justify-around py-2 px-4 max-w-md mx-auto relative">
        {" "}
        {footerItems.map((item) => {
          const isActive = currentPage === item.id;

          return (
            <button
              key={item.id}
              onClick={() => onPageChange(item.id)}
              className={`
                flex flex-col items-center justify-center p-2 px-6 rounded-xl
                transition-all duration-300 min-w-[64px] transform border-2
                relative overflow-visible
                ${
                  isActive
                    ? `${item.activeBg} ${item.activeText} scale-110 shadow-lg border-white/30 hover:shadow-xl`
                    : `${item.inactiveStyle} ${item.hoverBg} hover:scale-105 border-transparent`
                }
              `}
            >
              <div className="w-12 h-12 drop-shadow-lg absolute -top-6 left-1/2 transform -translate-x-1/2 z-10">
                <img
                  src={item.iconSrc}
                  alt={item.label}
                  className={`w-full h-full object-contain`}
                />
              </div>
              <span
                className={`text-xs font-semibold drop-shadow-sm mt-4 ${
                  isActive ? "font-bold" : "font-medium"
                }`}
              >
                {item.label}
              </span>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default MobileFooter;

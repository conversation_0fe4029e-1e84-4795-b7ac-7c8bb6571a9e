import React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { LogOut } from "lucide-react";

interface GameExitConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

const GameExitConfirmDialog: React.FC<GameExitConfirmDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
}) => {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="bg-white p-6 rounded-lg max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle className="text-xl font-bold text-red-700">
            Abbandona partita
          </AlertDialogTitle>
          <AlertDialogDescription className="text-gray-600">
            Sei sicuro di voler abbandonare la partita in corso? Verrà segnata
            come sconfitta.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <AlertDialogFooter className="flex gap-3 mt-6">
          <AlertDialogCancel className="flex-1 border-red-200 text-red-700 hover:bg-red-50">
            Annulla
          </AlertDialogCancel>

          <AlertDialogAction
            className="flex-1 bg-red-600 hover:bg-red-700 text-white flex items-center justify-center gap-2"
            onClick={onConfirm}
          >
            <LogOut className="h-4 w-4" /> Abbandona partita
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default GameExitConfirmDialog;

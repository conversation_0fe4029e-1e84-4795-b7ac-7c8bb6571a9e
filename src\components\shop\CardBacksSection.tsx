import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Check, Lock, Crown } from "lucide-react";
import { cardBacks, type CardBack } from "@/data/shopData";
import ShopImage from "@/components/ui/ShopImage";

interface CardBacksSectionProps {
  selectedCardBack: string;
  playerLevel: number;
  hasAdPass: boolean;
  onCardBackSelect: (cardBackId: string) => void;
  isItemUnlocked: (item: CardBack) => boolean;
}

const CardBacksSection = ({
  selectedCardBack,
  playerLevel,
  hasAdPass,
  onCardBackSelect,
  isItemUnlocked,
}: CardBacksSectionProps) => {
  return (
    <Card
      className="border border-indigo-300/40 shadow-lg bg-gradient-to-br from-indigo-50/80 via-purple-50/80 to-pink-50/80 relative"
      style={{ overflow: "visible" }}
    >
      <CardHeader className="pb-4 bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white relative overflow-hidden rounded-t-xl">
        {/* Sfondo decorativo */}
        <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/20 via-purple-600/20 to-pink-600/20"></div>
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute -bottom-2 -left-2 w-16 h-16 bg-white/10 rounded-full blur-lg"></div>

        <div className="relative z-10">
          <CardTitle
            className="text-xl md:text-2xl text-white flex items-center gap-3 mb-2"
            style={{
              fontFamily: "'DynaPuff', cursive",
              fontWeight: 600,
            }}
          >
            <div className="relative">
              <div className="animate-bounce">🃏</div>
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-yellow-300 rounded-full animate-pulse"></div>
            </div>
            <span className="flex-1 drop-shadow-md">Retri delle Carte</span>
          </CardTitle>
          <p className="text-white/90 text-sm md:text-base drop-shadow-sm">
            Personalizza lo stile delle tue carte
          </p>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <div
          className="grid gap-3 md:gap-4"
          style={{ paddingTop: "8px", paddingBottom: "8px" }}
        >
          {cardBacks.map((cardBack) => (
            <div
              key={cardBack.id}
              className="group relative p-3 md:p-4 bg-white/70 rounded-xl border border-indigo-200/50 hover:border-indigo-300 hover:bg-white/80 transition-all duration-200 overflow-visible"
              style={{ transformStyle: "preserve-3d" }}
            >
              <div className="flex items-center gap-3 relative z-10">
                {/* Contenuto principale */}
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  {/* Anteprima carta con badge */}
                  <div className="relative flex-shrink-0">
                    <div
                      className={`w-12 h-16 md:w-14 md:h-20 border-2 shadow-md flex items-center justify-center text-lg md:text-xl transition-transform duration-200 group-hover:scale-105 overflow-hidden ${
                        !isItemUnlocked(cardBack) ? "opacity-50" : ""
                      } ${
                        cardBack.rarity === "leggendario"
                          ? "border-orange-400 shadow-orange-200/50"
                          : cardBack.rarity === "epico"
                          ? "border-purple-400 shadow-purple-200/50"
                          : cardBack.rarity === "raro"
                          ? "border-blue-400 shadow-blue-200/50"
                          : "border-gray-300 shadow-gray-200/50"
                      }`}
                      style={{
                        background: cardBack.preview,
                        borderRadius: "12px",
                      }}
                    >
                      {cardBack.icon}
                      {/* Effetto sparkle semplificato per rarità leggendaria */}
                      {cardBack.rarity === "leggendario" && (
                        <div className="absolute top-1 right-1 w-1 h-1 bg-yellow-400 rounded-full opacity-80"></div>
                      )}
                    </div>
                    {/* Check per item selezionato */}
                    {selectedCardBack === cardBack.id &&
                      isItemUnlocked(cardBack) && (
                        <div className="absolute -top-1 -right-1 bg-green-500 rounded-full p-1 shadow-md">
                          <Check className="h-3 w-3 text-white" />
                        </div>
                      )}
                  </div>

                  {/* Informazioni */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3
                        className={`text-base md:text-lg font-bold truncate ${
                          cardBack.rarity === "leggendario"
                            ? "text-orange-800"
                            : cardBack.rarity === "epico"
                            ? "text-purple-800"
                            : cardBack.rarity === "raro"
                            ? "text-blue-800"
                            : "text-indigo-800"
                        }`}
                      >
                        {cardBack.name}
                      </h3>
                    </div>
                    <p className="text-indigo-600 text-xs md:text-sm line-clamp-2">
                      {cardBack.description}
                    </p>
                  </div>
                </div>

                {/* Bottoni ottimizzati */}
                <div className="flex flex-col gap-1 flex-shrink-0 ml-auto min-w-0 max-w-[120px]">
                  {cardBack.unlockType === "free" && (
                    <button
                      onClick={() => onCardBackSelect(cardBack.id)}
                      className={`px-2 py-1.5 rounded-lg text-xs font-medium transition-colors whitespace-nowrap ${
                        selectedCardBack === cardBack.id
                          ? "bg-green-500 text-white"
                          : "bg-blue-500 hover:bg-blue-600 text-white"
                      }`}
                    >
                      {selectedCardBack === cardBack.id ? (
                        <Check className="h-3 w-3 mx-auto" />
                      ) : (
                        "Usa"
                      )}
                    </button>
                  )}
                  {cardBack.unlockType === "level" && (
                    <button
                      onClick={() =>
                        isItemUnlocked(cardBack) &&
                        onCardBackSelect(cardBack.id)
                      }
                      disabled={!isItemUnlocked(cardBack)}
                      className={`px-2 py-1.5 rounded-lg text-xs font-medium transition-colors whitespace-nowrap ${
                        isItemUnlocked(cardBack)
                          ? selectedCardBack === cardBack.id
                            ? "bg-green-500 text-white"
                            : "bg-blue-500 hover:bg-blue-600 text-white"
                          : "bg-gray-400 text-white cursor-not-allowed"
                      }`}
                    >
                      {isItemUnlocked(cardBack) ? (
                        selectedCardBack === cardBack.id ? (
                          <Check className="h-3 w-3 mx-auto" />
                        ) : (
                          "Usa"
                        )
                      ) : (
                        `Lv.${cardBack.unlockLevel}`
                      )}
                    </button>
                  )}
                  {cardBack.unlockType === "purchase" && (
                    <button
                      onClick={() => onCardBackSelect(cardBack.id)}
                      className={`px-2 py-1.5 rounded-lg text-xs font-medium transition-colors whitespace-nowrap ${
                        isItemUnlocked(cardBack)
                          ? selectedCardBack === cardBack.id
                            ? "bg-green-500 text-white"
                            : "bg-blue-500 hover:bg-blue-600 text-white"
                          : "bg-blue-500 hover:bg-blue-600 text-white"
                      }`}
                    >
                      {isItemUnlocked(cardBack) ? (
                        selectedCardBack === cardBack.id ? (
                          <Check className="h-3 w-3 mx-auto" />
                        ) : (
                          "Usa"
                        )
                      ) : (
                        cardBack.price
                      )}
                    </button>
                  )}
                  {cardBack.unlockType === "premium" && (
                    <button
                      onClick={() => onCardBackSelect(cardBack.id)}
                      className={`px-2 py-1.5 rounded-lg text-xs font-medium transition-colors whitespace-nowrap flex items-center justify-center gap-1 ${
                        isItemUnlocked(cardBack)
                          ? selectedCardBack === cardBack.id
                            ? "bg-green-500 text-white"
                            : "bg-blue-500 hover:bg-blue-600 text-white"
                          : !hasAdPass
                          ? "bg-purple-500 hover:bg-purple-600 text-white cursor-pointer"
                          : "bg-amber-500 text-white cursor-not-allowed"
                      }`}
                    >
                      {isItemUnlocked(cardBack) ? (
                        selectedCardBack === cardBack.id ? (
                          <Check className="h-3 w-3" />
                        ) : (
                          "Usa"
                        )
                      ) : !hasAdPass ? (
                        <>
                          <Lock className="h-3 w-3" />
                          <Crown className="h-3 w-3" />
                        </>
                      ) : (
                        <>
                          <Lock className="h-3 w-3" />
                          <span>Lv.{cardBack.unlockLevel}</span>
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default CardBacksSection;

/**
 * 🔥 INDICE PRINCIPALE AI - STRUTTURA ORGANIZZATA
 * Export centrale per tutto il sistema AI della Maraffa Romagnola
 */

// === STRATEGIES ===
export * from "./strategies";
export { selectCardByDifficulty } from "./strategies";

// === MEMORY SYSTEM ===
export * from "./memory";

// === UTILITIES ===
export * from "./utils";

// Performance modules removed for simplification

// === TYPES ===
export * from "./types";

// === MAIN AI FUNCTIONS ===
export { getAIMove } from "./aiCore";
export { handleAIMove } from "./aiHandler";

// === BACKWARD COMPATIBILITY ===
// Re-export delle funzioni più importanti per compatibilità
export {
  getCardValue,
  getCardOrder,
  canWinCurrentTrick,
  findLowestValidCard,
} from "./utils/cardUtils";

export {
  analyzeCardWaste,
  getOptimalCards,
  isCardWasteToOpponent,
  findBestDiscardForWorthlessTrick,
} from "./utils/antiWaste";

export { analyzeCardMemory, createSafeCardMemory } from "./memory/analyzer";

// === TYPES RE-EXPORT ===
export type {
  AIDifficulty,
  CardMemory,
  AIStrategy,
  CardWasteAnalysis,
  OptimalCardsResult,
} from "./types";

// === STRATEGY FUNCTIONS ===
export { selectCardByDifficulty as selectAICard } from "./strategies";

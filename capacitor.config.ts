import type { CapacitorConfig } from "@capacitor/cli";

// Leggi variabili d'ambiente con fallback
const APP_ID = process.env.VITE_APP_ID || "com.eliazavatta.maraffa";
const APP_NAME = process.env.VITE_APP_NAME || "Marafone Romagnolo";
const ADMOB_APP_ID =
  process.env.VITE_ADMOB_APP_ID || "ca-app-pub-3013811216506035~1249225182";

const config: CapacitorConfig = {
  appId: APP_ID,
  appName: APP_NAME,
  webDir: "dist",
  server: {
    androidScheme: "https",
    iosScheme: "https",
  },
  android: {
    buildOptions: {
      keystorePath: undefined,
      keystoreAlias: undefined,
      keystorePassword: undefined,
      keystoreAliasPassword: undefined,
      releaseType: "APK",
    },
    // Configurazione per deep links
    allowMixedContent: true,
  },
  ios: {
    scheme: "Marafone Romagnolo",
    contentInset: "automatic",
    scrollEnabled: true,
    backgroundColor: "#781d02",
    allowsLinkPreview: false,
    handleApplicationNotifications: false,
  },
  plugins: {
    SplashScreen: {
      launchShowDuration: 2000,
      launchAutoHide: true,
      backgroundColor: "#781d02",
      androidSplashResourceName: "splash",
      androidScaleType: "CENTER_CROP",
      iosLaunchAnimation: "fade",
      iosSpinnerStyle: "large",
      showSpinner: true,
      androidSpinnerStyle: "large",
      spinnerColor: "#FCD34D",
    },
    StatusBar: {
      style: "dark",
      backgroundColor: "#FFFFFF",
      overlaysWebView: true,
      hide: true,
    },
    AdMob: {
      appId: ADMOB_APP_ID,
      testingDevices: [],
    },
  },
};

export default config;

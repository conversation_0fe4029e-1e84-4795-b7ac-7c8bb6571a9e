import { Suit, Card } from "../../game/cardUtils";
import { GameState } from "../../game/gameLogic";
import { CardMemory } from "../types";
import {
  createFullDeck,
  initializeCardMemory,
  updateCardMemory,
  updateTrumpsRemaining,
  getSafeCards,
  isCardSafe,
  calculateCardRisk,
  getHighestSafeCardInSuit,
  getCardOrder,
} from "../utils/cardUtils";
// Removed performance modules for simplification

export const analyzeCardMemory = (state: GameState): CardMemory => {
  // Analisi completa della memoria basata sullo stato del gioco

  const fullDeck = createFullDeck();
  const playedCards: Card[] = [];
  const playedBySuit: Record<string, Card[]> = {};
  const playedByPlayer: Record<number, Card[]> = {};

  // Inizializza le strutture
  [Suit.Coins, Suit.Cups, Suit.Swords, Suit.Clubs].forEach((suit) => {
    playedBySuit[suit] = [];
  });

  for (let i = 0; i < 4; i++) {
    playedByPlayer[i] = [];
  }

  // RACCOLTA CARTE GIOCATE: Analizza tutte le carte vinte dai team
  const allWonCards = [
    ...state.teams[0].tricksWon,
    ...state.teams[1].tricksWon,
  ];

  // Aggiungi tutte le carte vinte
  allWonCards.forEach((card) => {
    if (card) {
      playedCards.push(card);
      if (card.suit) {
        playedBySuit[card.suit].push(card);
      }
    }
  });

  // Aggiungi le carte della presa corrente
  if (state.currentTrick && state.currentTrick.length > 0) {
    state.currentTrick.forEach((card, index) => {
      if (card) {
        playedCards.push(card);
        if (card.suit) {
          playedBySuit[card.suit].push(card);
        }
        // Calcola quale giocatore ha giocato questa carta
        const playerIndex = ((state.leadPlayer ?? 0) + index) % 4;
        playedByPlayer[playerIndex].push(card);
      }
    });
  }

  console.log(
    `[MEMORY ANALYSIS] 📊 Carte giocate totali: ${playedCards.length}`
  );
  console.log(
    `[MEMORY ANALYSIS] 📊 Dettaglio per seme:`,
    Object.keys(playedBySuit)
      .map((suit) => `${suit}: ${playedBySuit[suit].length}`)
      .join(", ")
  );

  // Analizza le prese vinte da ogni squadra
  if (state.teams) {
    state.teams.forEach((team) => {
      if (team.tricksWon && Array.isArray(team.tricksWon)) {
        team.tricksWon.forEach((trick) => {
          // trick è un array di Card (una singola presa)
          if (Array.isArray(trick)) {
            trick.forEach((card, cardIndex) => {
              if (card && card.suit && card.rank) {
                playedCards.push(card);
                playedBySuit[card.suit.toString()].push(card);

                // Determina quale giocatore ha giocato questa carta
                const leadPlayer = state.leadPlayer ?? 0;
                const playerIndex = (leadPlayer + cardIndex) % 4;
                playedByPlayer[playerIndex].push(card);
              }
            });
          }
        });
      }
    });
  }

  // Aggiungi le carte della presa corrente
  if (state.currentTrick && Array.isArray(state.currentTrick)) {
    state.currentTrick.forEach((card, index) => {
      if (card && card.suit && card.rank) {
        playedCards.push(card);
        playedBySuit[card.suit.toString()].push(card);

        const playerIndex = ((state.leadPlayer ?? 0) + index) % 4;
        playedByPlayer[playerIndex].push(card);
      }
    });
  }

  // Calcola le carte rimanenti
  const remainingCards = fullDeck.filter(
    (deckCard) =>
      !playedCards.some(
        (playedCard) =>
          deckCard.suit === playedCard.suit && deckCard.rank === playedCard.rank
      )
  );

  // Calcola la distribuzione per seme delle carte rimanenti
  const suitDistribution: Record<string, number> = {};
  [Suit.Coins, Suit.Cups, Suit.Swords, Suit.Clubs].forEach((suit) => {
    suitDistribution[suit] = remainingCards.filter(
      (card) => card.suit === suit
    ).length;
  });

  // Identifica le briscole rimanenti
  const trumpsRemaining = remainingCards.filter(
    (card) => card.suit === state.trumpSuit
  );

  // Identifica le carte di valore rimanenti per seme
  const highCardsRemaining: Record<string, Card[]> = {};
  [Suit.Coins, Suit.Cups, Suit.Swords, Suit.Clubs].forEach((suit) => {
    highCardsRemaining[suit] = remainingCards.filter(
      (card) =>
        card.suit === suit &&
        ["A", "3", "2", "K", "H", "J"].includes(card.rank as string)
    );
  });
  const result = {
    playedCards,
    playedBySuit,
    playedByPlayer,
    remainingCards,
    suitDistribution,
    trumpsRemaining,
    highCardsRemaining,
  };

  return result;
};

export const createSafeCardMemory = (state: GameState): CardMemory => {
  // Se non abbiamo dati sufficienti, crea una memoria "sicura" con valori conservativi
  const emptyMemory: CardMemory = {
    playedCards: [],
    playedBySuit: {},
    playedByPlayer: {},
    remainingCards: createFullDeck(),
    suitDistribution: {},
    trumpsRemaining: [],
    highCardsRemaining: {},
  };

  // Inizializza le strutture base
  [Suit.Coins, Suit.Cups, Suit.Swords, Suit.Clubs].forEach((suit) => {
    emptyMemory.playedBySuit[suit] = [];
    emptyMemory.suitDistribution[suit] = 10; // Assumi che tutte le carte siano ancora in gioco
    emptyMemory.highCardsRemaining[suit] = createFullDeck().filter(
      (card) =>
        card.suit === suit &&
        ["A", "3", "2", "K", "H", "J"].includes(card.rank as string)
    );
  });

  for (let i = 0; i < 4; i++) {
    emptyMemory.playedByPlayer[i] = [];
  }

  // Almeno aggiungi le carte della presa corrente se disponibili
  if (state.currentTrick && state.currentTrick.length > 0) {
    state.currentTrick.forEach((card, index) => {
      emptyMemory.playedCards.push(card);
      if (card.suit) {
        emptyMemory.playedBySuit[card.suit.toString()].push(card);
      }

      const playerIndex = ((state.leadPlayer ?? 0) + index) % 4;
      emptyMemory.playedByPlayer[playerIndex].push(card);
    });
  }

  // Aggiorna le briscole rimanenti
  emptyMemory.trumpsRemaining = emptyMemory.remainingCards.filter(
    (card) => card.suit === state.trumpSuit
  );

  return emptyMemory;
};

/**
 * Analizza le carte sicure disponibili per l'AI
 */
export const analyzeSafeCards = (
  hand: Card[],
  memory: CardMemory,
  trumpSuit: string | null,
  leadSuit: string | null
): {
  safeCards: Card[];
  safestCard: Card | null;
  riskAnalysis: Array<{ card: Card; risk: number; isSafe: boolean }>;
} => {
  const safeCards = getSafeCards(
    hand,
    memory,
    trumpSuit as Suit | null,
    leadSuit as Suit | null
  );

  // Analisi del rischio per ogni carta
  const riskAnalysis = hand.map((card) => ({
    card,
    risk: calculateCardRisk(
      card,
      memory,
      trumpSuit as Suit | null,
      leadSuit as Suit | null
    ),
    isSafe: isCardSafe(
      card,
      memory,
      trumpSuit as Suit | null,
      leadSuit as Suit | null
    ),
  }));

  // Trova la carta più sicura (minimo rischio)
  const safestCard = riskAnalysis.reduce((safest, current) =>
    current.risk < safest.risk ? current : safest
  ).card;

  return {
    safeCards,
    safestCard,
    riskAnalysis,
  };
};

/**
 * Trova la migliore carta sicura per vincere una presa
 */
export const findBestSafeWinningCard = (
  hand: Card[],
  memory: CardMemory,
  currentTrick: Card[],
  trumpSuit: string | null,
  leadSuit: string | null
): Card | null => {
  const safeCards = getSafeCards(
    hand,
    memory,
    trumpSuit as Suit | null,
    leadSuit as Suit | null
  );

  // Filtra solo le carte sicure che possono vincere
  const winningSafeCards = safeCards.filter((card) => {
    // Logica semplificata per determinare se può vincere
    if (currentTrick.length === 0) return true;

    // Se è una briscola e nel trick non ci sono briscole più forti
    if (trumpSuit && card.suit === trumpSuit) {
      const trickTrumps = currentTrick.filter((c) => c.suit === trumpSuit);
      if (trickTrumps.length === 0) return true;

      return trickTrumps.every((tc) => getCardOrder(card) > getCardOrder(tc));
    }

    // Se è del seme di uscita e non ci sono briscole nel trick
    if (leadSuit && card.suit === leadSuit) {
      const hasTrumpsInTrick = currentTrick.some((c) => c.suit === trumpSuit);
      if (hasTrumpsInTrick) return false;

      const leadCards = currentTrick.filter((c) => c.suit === leadSuit);
      return leadCards.every((lc) => getCardOrder(card) > getCardOrder(lc));
    }

    return false;
  });

  if (winningSafeCards.length === 0) return null;

  // Restituisci la carta sicura più forte che può vincere
  return winningSafeCards.reduce((strongest, card) =>
    getCardOrder(card) > getCardOrder(strongest) ? card : strongest
  );
};

/**
 * Analizza la situazione di memoria per determinare la strategia ottimale
 */
export const analyzeMemoryStrategy = (
  hand: Card[],
  memory: CardMemory,
  state: GameState
): {
  strategy: "conservative" | "aggressive" | "balanced";
  safeCardsCount: number;
  riskLevel: "low" | "medium" | "high";
  recommendations: string[];
} => {
  const { safeCards, riskAnalysis } = analyzeSafeCards(
    hand,
    memory,
    state.trumpSuit,
    state.leadSuit
  );

  const safeCardsCount = safeCards.length;
  const totalCards = hand.length;
  const safeRatio = safeCardsCount / totalCards;

  // Determina il livello di rischio generale
  const averageRisk =
    riskAnalysis.reduce((sum, r) => sum + r.risk, 0) / riskAnalysis.length;
  let riskLevel: "low" | "medium" | "high";

  if (averageRisk < 0.3) riskLevel = "low";
  else if (averageRisk < 0.7) riskLevel = "medium";
  else riskLevel = "high";

  // Determina la strategia
  let strategy: "conservative" | "aggressive" | "balanced";
  const recommendations: string[] = [];

  if (safeRatio > 0.6) {
    strategy = "aggressive";
    recommendations.push(
      "Molte carte sicure disponibili - gioca aggressivamente"
    );
    recommendations.push("Usa carte sicure per supportare il compagno");
  } else if (safeRatio < 0.3) {
    strategy = "conservative";
    recommendations.push("Poche carte sicure - conserva le migliori");
    recommendations.push("Evita rischi inutili");
  } else {
    strategy = "balanced";
    recommendations.push("Situazione equilibrata - valuta caso per caso");
  }

  // Consigli specifici per le briscole
  const trumpsRemaining = memory.trumpsRemaining.length;
  if (trumpsRemaining < 3) {
    recommendations.push(
      "Poche briscole rimaste - le tue briscole sono più sicure"
    );
  }

  // Consigli per carte alte
  const highCardsInHand = hand.filter((card) =>
    ["3", "2", "A"].includes(card.rank as string)
  ).length;

  if (highCardsInHand > 3) {
    recommendations.push("Molte carte alte - cerca di massimizzare i punti");
  }

  return {
    strategy,
    safeCardsCount,
    riskLevel,
    recommendations,
  };
};

/**
 * 🗣️ AI Handler per Dichiarazioni Strategiche
 *
 * Gestisce la logica AI per decidere quando e come fare dichiarazioni strategiche
 */

import { GameState } from "../../game/gameLogic";
import { Card, Suit } from "../../game/cardUtils";
import {
  StrategicAnnouncement,
  evaluateStrategicAnnouncement,
  updateAIMemoryWithAnnouncement,
  applyTeammateAnnouncementStrategy,
} from "../../game/strategicAnnouncements";

/**
 * 🎯 Determina se l'AI deve fare una dichiarazione strategica
 */
export const handleAIStrategicAnnouncement = (
  gameState: GameState,
  playerIndex: number,
  intendedCard: Card,
  difficulty: "easy" | "medium" | "hard"
): {
  shouldAnnounce: boolean;
  announcement: StrategicAnnouncement;
  reason: string;
} => {
  // Solo il primo giocatore del turno può dichiarare
  if (gameState.currentTrick.length > 0) {
    return {
      shouldAnnounce: false,
      announcement: null,
      reason: "Non è il primo del turno",
    };
  }

  const player = gameState.players[playerIndex];
  const announcement = evaluateStrategicAnnouncement(
    player,
    gameState,
    intendedCard,
    difficulty
  );

  if (!announcement) {
    return {
      shouldAnnounce: false,
      announcement: null,
      reason: "Nessuna dichiarazione strategica appropriata",
    };
  }

  // Log per debugging
  console.log(
    `[AI STRATEGIC] Player ${playerIndex} (${player.name}) considera dichiarazione: ${announcement}`
  );

  return {
    shouldAnnounce: true,
    announcement,
    reason: `Dichiarazione strategica: ${announcement} (${intendedCard.rank} di ${intendedCard.suit})`,
  };
};

/**
 * 🤝 Applica la strategia AI basata sulle dichiarazioni del compagno
 */
export const applyAIAnnouncementStrategy = (
  availableCards: Card[],
  gameState: GameState,
  playerIndex: number
): {
  filteredCards: Card[];
  strategy: string;
  confidence: number;
} => {
  const result = applyTeammateAnnouncementStrategy(
    availableCards,
    gameState,
    playerIndex
  );

  // Calcola la confidenza in base al tipo di strategia applicata
  let confidence = 0.5; // Base confidence

  if (result.strategy.includes("BUSSO")) {
    confidence = 0.9; // Alta confidenza per BUSSO
  } else if (result.strategy.includes("VOLO")) {
    confidence = 0.8; // Buona confidenza per VOLO
  } else if (result.strategy.includes("STRISCIO")) {
    confidence = 0.6; // Moderata confidenza per STRISCIO
  }

  console.log(
    `[AI ANNOUNCEMENT STRATEGY] ${result.strategy} (Confidenza: ${(
      confidence * 100
    ).toFixed(0)}%)`
  );

  return {
    filteredCards: result.recommendedCards,
    strategy: result.strategy,
    confidence,
  };
};

/**
 * 📝 Processa una dichiarazione ricevuta e aggiorna la memoria AI
 */
export const processReceivedAnnouncement = (
  gameState: GameState,
  playerIndex: number,
  announcement: StrategicAnnouncement,
  cardSuit: Suit
): void => {
  if (!announcement) return;

  const announcementInfo = {
    type: announcement,
    playerIndex,
    suit: cardSuit,
    trickNumber: gameState.trickNumber,
  };

  // Aggiorna la memoria AI globale
  updateAIMemoryWithAnnouncement(announcementInfo, gameState);

  // Log per debugging
  const player = gameState.players[playerIndex];
  console.log(
    `[AI MEMORY UPDATE] Processed announcement: ${
      player.name
    } declares ${announcement.toUpperCase()} on ${cardSuit}`
  );
};

/**
 * 🎮 Integra le dichiarazioni strategiche nel processo decisionale AI
 */
export const integrateAnnouncementsInAIDecision = (
  availableCards: Card[],
  gameState: GameState,
  playerIndex: number,
  difficulty: "easy" | "medium" | "hard"
): {
  cards: Card[];
  strategicInfo: string;
  useAnnouncement: boolean;
} => {
  // 🎯🎯🎯 PRIORITÀ ASSOLUTA: SE PRIMO DEL TURNO E HAI 3 NON-BRISCOLA, NON FILTRARE MAI! 🎯🎯🎯
  const isFirstOfTrick =
    !gameState.currentTrick || gameState.currentTrick.length === 0;

  if (isFirstOfTrick) {
    const opening3s = availableCards.filter(
      (card) => card.rank === "3" && card.suit !== gameState.trumpSuit
    );

    if (opening3s.length > 0) {
      console.log(
        `[STRATEGIC HANDLER] 🎯🎯🎯 PRIMO DEL TURNO CON 3 NON-BRISCOLA - BLOCCO FILTRI! 🎯🎯🎯`
      );
      return {
        cards: availableCards, // NON FILTRARE! Lascia tutte le carte disponibili
        strategicInfo:
          "PRIORITÀ ASSOLUTA: 3 non-briscola disponibile - no filtri strategici",
        useAnnouncement: false,
      };
    }
  }

  // 1. Prima applica la strategia basata sulle dichiarazioni del compagno
  const announcementStrategy = applyAIAnnouncementStrategy(
    availableCards,
    gameState,
    playerIndex
  );

  // 2. Se la strategia basata su dichiarazioni ha alta confidenza, usala
  if (announcementStrategy.confidence >= 0.7) {
    return {
      cards: announcementStrategy.filteredCards,
      strategicInfo: `Strategia basata su dichiarazione: ${announcementStrategy.strategy}`,
      useAnnouncement: true,
    };
  }

  // 3. Altrimenti usa la strategia normale
  return {
    cards: availableCards,
    strategicInfo: "Strategia normale - no dichiarazioni influenti",
    useAnnouncement: false,
  };
};

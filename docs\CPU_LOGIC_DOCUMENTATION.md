# 🤖 Documentazione Completa della Logica CPU - Maraffa Romagnola

## Indice

1. [Panoramica Generale](#panoramica-generale)
2. [Architettura del Sistema AI](#architettura-del-sistema-ai)
3. [Livelli di Difficoltà](#livelli-di-difficoltà)
4. [Sistema di Memoria delle Carte](#sistema-di-memoria-delle-carte)
5. [Sistema Anti-Spreco Unificato](#sistema-anti-spreco-unificato)
6. [Strategie di Gioco](#strategie-di-gioco)
7. [Sistema di Valutazione delle Carte](#sistema-di-valutazione-delle-carte)
8. [Ottimizzazioni e Performance](#ottimizzazioni-e-performance)
9. [Analisi e Decisioni Cooperative](#analisi-e-decisioni-cooperative)
10. [Sistema di Briscole Intelligente](#sistema-di-briscole-intelligente)
11. [Logging e Debug](#logging-e-debug)

---

## 1. Panoramica Generale

Il sistema AI della Maraffa Romagnola è progettato come un'architettura modulare e scalabile che implementa tre livelli di difficoltà crescente. Ogni livello utilizza algoritmi progressivamente più sofisticati per simulare giocatori umani di abilità diverse.

### Obiettivi Principali

- **Simulazione Realistica**: Comportamenti che imitano giocatori umani reali
- **Difficoltà Graduata**: Tre livelli che offrono sfide appropriate per ogni livello di esperienza
- **Ottimizzazione Anti-Spreco**: Sistema avanzato per evitare mosse strategicamente svantaggiose
- **Cooperazione di Squadra**: Logica per massimizzare i punti della propria squadra

---

## 2. Architettura del Sistema AI

### 2.1 Struttura Modulare

```
src/utils/ai/
├── aiCore.ts              # Punto di ingresso principale
├── aiHandler.ts           # Gestione interfaccia utente
├── cardUtils.ts           # Utilità valutazione carte (CORE)
├── memoryManager.ts       # Gestione memoria globale
├── memoryAnalyzer.ts      # Analisi intelligente memoria
├── types.ts               # Definizioni tipi TypeScript
├── evaluators.ts          # Valutatori specializzati
├── optimizations.ts       # Ottimizzazioni performance
├── performance.ts         # Monitoraggio prestazioni
├── cache.ts               # Sistema di caching
├── aiAnalytics.ts         # Analytics decisioni AI
└── strategies/            # Strategie per livello difficoltà
    ├── easyStrategy.ts    # Logica Easy AI
    ├── mediumStrategy.ts  # Logica Medium AI
    ├── hardStrategy.ts    # Logica Hard AI
    ├── cooperativeStrategy.ts  # Logica cooperativa
    └── trumpStrategy.ts   # Gestione briscole avanzata
```

### 2.2 Flusso Decisionale

```mermaid
graph TD
    A[Richiesta Mossa AI] --> B[aiCore.getAIMove]
    B --> C[Validazione Carte]
    C --> D[Memoria Manager Sync]
    D --> E[Fast Move Detection]
    E --> F{Mossa Ovvia?}
    F -->|Sì| G[Ritorna Carta]
    F -->|No| H[Strategia per Difficoltà]
    H --> I[Easy/Medium/Hard Strategy]
    I --> J[Sistema Anti-Spreco]
    J --> K[Valutazione Carte]
    K --> L[Decisione Finale]
    L --> G
```

---

## 3. Livelli di Difficoltà

### 3.1 Easy AI

**Caratteristiche:**

- Casualità del 50% nelle decisioni per imprevedibilità
- Sistema anti-spreco attivo solo al 40%
- Memoria carte base (solo per evitare errori grossolani)
- Maraffa riconosciuta solo al 30%

**Logica Decisionale:**

```typescript
// Controllo anti-spreco ridotto
if (Math.random() < 0.4) {
  // Applica sistema unificato con soglie basse
  const optimalAnalysis = getOptimalCards(
    cards,
    currentTrick,
    state,
    playerIndex
  );
}

// Logica semplificata per carte di valore
if (Math.random() < 0.5) {
  const lowValueCards = cards.filter((card) => getCardValue(card) === 0);
  if (lowValueCards.length > 0) {
    return randomChoice(lowValueCards);
  }
}
```

### 3.2 Medium AI

**Caratteristiche:**

- Sistema anti-spreco attivo al 70%
- Analisi memoria migliorata con strategie dinamiche
- Cooperazione con compagno al 70%
- Riconoscimento Maraffa al 75%
- Gestione intelligente carte di valore

**Funzionalità Avanzate:**

- Analisi sicurezza carte con `analyzeSafeCards()`
- Sistema cooperativo per massimizzare punti squadra
- Valutazione dinamica rischio/beneficio
- Taglio intelligente con briscole appropriate

### 3.3 Hard AI

**Caratteristiche:**

- Sistema anti-spreco sempre attivo
- **Perfect Memory Mode**: Valutazione perfetta basata su memoria completa
- Analisi debolezze avversari
- Strategia dinamica basata su fase di gioco
- Cooperazione squadra ottimizzata

**Algoritmi Avanzati:**

```typescript
// Perfect Memory Evaluation
const perfectEvaluation = selectBestCardWithPerfectMemory(
  cards,
  memory,
  state,
  strategyMode
);

// Analisi avversari
const opponentAnalysis = analyzeOpponentWeaknesses(memory, state);

// Sistema decisionale unificato con alta confidenza
if (quickAnalysis.recommendation.confidence > 0.85) {
  return cardDecision; // Decisione immediata
}
```

---

## 4. Sistema di Memoria delle Carte

### 4.1 MemoryManager Globale

La classe `MemoryManager` mantiene traccia di tutte le carte giocate durante la partita:

```typescript
export class MemoryManager {
  private memory: CardMemory;

  // Sincronizzazione con stato gioco
  public syncWithGameState(gameState: GameState): void;

  // Memoria read-only per AI
  public getMemory(): Readonly<CardMemory>;
}
```

### 4.2 Struttura CardMemory

```typescript
interface CardMemory {
  playedCards: Card[]; // Tutte le carte giocate
  playedBySuit: Record<string, Card[]>; // Carte per seme
  playedByPlayer: Card[][]; // Carte per giocatore
  remainingCards: Card[]; // Carte ancora in gioco
  trumpsRemaining: Card[]; // Briscole rimaste
  trickHistory: TrickRecord[]; // Storia delle prese
}
```

### 4.3 Analisi Intelligente Memoria

La funzione `analyzeMemoryStrategy()` determina la strategia ottimale:

```typescript
const memoryStrategy = analyzeMemoryStrategy(cards, memory, state);
// Risultato: "conservative" | "aggressive" | "balanced"

// Raccomandazioni dinamiche
const recommendations = [
  "Poche carte sicure - conserva le migliori",
  "Situazione equilibrata - valuta caso per caso",
  "Molte carte sicure - gioca aggressivamente",
];
```

---

## 5. Sistema Anti-Spreco Unificato

### 5.1 Panoramica

Il sistema anti-spreco è stato completamente unificato per eliminare duplicazioni e migliorare l'efficienza. Sostituisce le vecchie funzioni frammentate con un approccio olistico.

### 5.2 Funzione Core: analyzeCardWaste()

```typescript
interface CardWasteAnalysis {
    isWaste: boolean;
    reason: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    alternativesExist: boolean;
}

export const analyzeCardWaste = (
    card: Card,
    availableCards: Card[],
    currentTrick: Card[],
    state: GameState,
    playerIndex: number,
    memory: CardMemory | null
): CardWasteAnalysis
```

### 5.3 Controlli Implementati

1. **🔥 CONTROLLO 1**: Carte di Alto Valore agli Avversari

   - Blocca assi, 2 e 3 quando vince l'avversario
   - Severità: `critical`

2. **🔥 CONTROLLO 2**: Spreco di Briscole per Taglio

   - Verifica se esistono briscole più deboli per tagliare
   - Severità: `high`

3. **🔥 CONTROLLO 3**: Carte Dominanti Sprecate

   - Evita di sprecare carte che dominano un seme
   - Severità: `medium`

4. **🔥 CONTROLLO 4**: Carte Strategiche in Fase Iniziale
   - Conserva 2 e 3 nelle prime 3 prese
   - Severità: `medium`

### 5.4 Sistema Unificato getOptimalCards()

```typescript
const getOptimalCards = (
    cards: Card[],
    currentTrick: Card[],
    state: GameState,
    playerIndex: number
) => {
    return {
        optimalCards: Card[];                    // Carte non-spreco
        wasteAnalysis: CardWasteAnalysis[];      // Analisi dettagliata
        recommendation: {                        // Raccomandazione intelligente
            bestCard: Card | null;
            reason: string;
            confidence: number;                  // 0.0 - 1.0
        };
    };
}
```

---

## 6. Strategie di Gioco

### 6.1 Strategia Cooperativa

La logica cooperativa massimizza i punti della squadra attraverso:

**Analisi Compagno:**

```typescript
const analyzeTeammatePosition = (state: GameState, playerIndex: number) => {
  // Determina se il compagno sta vincendo
  // Calcola valore potenziale della presa
  // Identifica opportunità di supporto
};
```

**Massimizzazione Punti Squadra:**

```typescript
const getTeamPointMaximizationStrategy = (
  state: GameState,
  playerIndex: number,
  cards: Card[],
  memory: CardMemory
) => {
  if (teammateWinning && isLastPlayer) {
    // Gioca la carta con più punti appropriata
    return { strategy: "maximize_points", recommendedCard };
  }
};
```

### 6.2 Strategia Briscole Intelligente

**Conservazione Strategica:**

- Analisi briscole rimaste vs briscole in mano
- Timing ottimale per uso briscole di valore
- Sistema di taglio intelligente

**Esaurimento Briscole:**

```typescript
const getTrumpExhaustionStrategy = (state, playerIndex, memory) => {
  // Determina se forzare l'esaurimento delle briscole avversarie
  // Calcola momento ottimale per giocare briscole dominanti
};
```

### 6.3 Gestione Fase di Gioco

**Early Game (Prese 1-3):**

- Conservazione carte strategiche (2, 3)
- Stabilimento controllo semi
- Analisi forza mani avversarie

**Mid Game (Prese 4-7):**

- Massimizzazione punti quando possibile
- Cooperazione squadra intensificata
- Gestione intelligente briscole

**Late Game (Prese 8-10):**

- Uso aggressivo carte rimaste
- Ottimizzazione punti finali
- Controllo dell'ultima presa

---

## 7. Sistema di Valutazione delle Carte

### 7.1 Valutazione Dinamica

La funzione `calculateDynamicCardValue()` considera:

```typescript
const evaluation = {
    baseValue: number;        // Valore punti carta (0-11)
    safetyScore: number;      // Sicurezza da superamento (0-10)
    winningPotential: number; // Probabilità vincita (0-10)
    strategicValue: number;   // Valore strategico futuro (0-10)
    overallScore: number;     // Score finale normalizzato
    reasoning: string[];      // Spiegazioni decisioni
}
```

### 7.2 Perfect Memory Mode

Per la difficoltà Hard, il sistema utilizza memoria perfetta:

```typescript
const selectBestCardWithPerfectMemory = (
  hand: Card[],
  memory: CardMemory,
  gameState: GameState,
  strategy: "conservative" | "aggressive" | "balanced"
) => {
  // Valuta ogni carta con informazioni complete
  // Considera tutte le carte giocate e rimaste
  // Ottimizza per strategia selezionata
};
```

### 7.3 Analisi Sicurezza Carte

```typescript
const isCardSafe = (card: Card, memory: CardMemory, trumpSuit, leadSuit) => {
  // Verifica se la carta può essere superata
  // Considera briscole rimaste e carte più forti
  // Calcola probabilità di successo
};
```

---

## 8. Ottimizzazioni e Performance

### 8.1 Fast Move Detection

Sistema di identificazione mosse ovvie per ridurre calcoli:

```typescript
class FastMoveDetector {
  static detectObviousMove(
    cards: Card[],
    state: GameState,
    memory: CardMemory
  ): Card | null {
    // Una sola carta disponibile
    // Obbligo di seguire il seme con una carta
    // Briscola più alta per presa di valore
    // Ultimo giocatore con carta vincente sicura
  }
}
```

### 8.2 Strategy Optimizer

```typescript
class StrategyOptimizer {
  prefilterCards(
    cards: Card[],
    state: GameState,
    difficulty: AIDifficulty
  ): Card[] {
    // Pre-filtra carte ovviamente inappropriate
    // Riduce spazio di ricerca per AI
    // Mantiene only carte strategicamente valide
  }
}
```

### 8.3 Sistema di Caching

- Cache per valutazioni carte ripetute
- Memoizzazione risultati costosi
- Invalidazione intelligente cache

### 8.4 Performance Monitor

```typescript
class PerformanceMonitor {
  startOperation(): void;
  endOperation(operation: string): number;
  recordDifficultyPerformance(difficulty: AIDifficulty, duration: number): void;
}
```

---

## 9. Analisi e Decisioni Cooperative

### 9.1 Analisi Compagno

```typescript
const analyzeTeammatePosition = (state: GameState, playerIndex: number): TeammateAnalysis => {
    return {
        isWinning: boolean;           // Compagno sta vincendo la presa
        cardPlayed: Card | null;      // Carta giocata dal compagno
        canBeBeaten: boolean;         // Può essere superato
        shouldSupport: boolean;       // Dovrei supportarlo
        recommendedAction: string;    // Azione consigliata
    };
}
```

### 9.2 Massimizzazione Punti Squadra

La logica cooperative si attiva quando:

- Il compagno sta vincendo la presa
- Sono l'ultimo a giocare
- La presa ha valore ≥ 1 punto O siamo in endgame
- È l'ultima presa (+1 punto bonus)

**Selezione Carte per Supporto:**

```typescript
const getTeammateCards = (cards: Card[]): Card[] => {
  // Restituisce solo A, Re, Cavallo, Fante
  // ESCLUDE 2 e 3 (carte strategiche da conservare)
  return cards.filter((card) => ["A", "K", "Q", "J"].includes(card.rank));
};
```

### 9.3 Prevenzione Override Compagno

```typescript
const wouldOverrideTeammate = (
  card: Card,
  teammateCard: Card,
  state: GameState
): boolean => {
  // Verifica se la mia carta supererebbe quella del compagno
  // Considera briscole vs semi normali
  // Evita di "rubare" prese al compagno
};
```

---

## 10. Sistema di Briscole Intelligente

### 10.1 Gestione Conservativa

```typescript
const getTrumpPlayingStrategy = (
  trumpCards: Card[],
  state: GameState,
  trickNumber: number,
  totalTricks: number
): Card | null => {
  // Conserva briscole migliori per endgame
  // Usa briscole deboli per prese intermedie
  // Considera timing ottimale per briscole di valore
};
```

### 10.2 Taglio Intelligente

```typescript
const findBestTrumpForCutting = (
  trumpCards: Card[],
  state: GameState,
  memory: CardMemory | null
): Card | null => {
  // Priorità: briscole senza punti
  // Seconda scelta: briscole con meno punti
  // Evita sprechi di briscole preziose
};
```

### 10.3 Rilevamento Spreco Briscole

```typescript
const isWastingTrumpForCutting = (
  trump: Card,
  trumpCards: Card[],
  currentTrick: Card[],
  state: GameState
): boolean => {
  // Verifica se esistono briscole più deboli
  // Considera valore della presa da tagliare
  // Previene sprechi in prese di basso valore
};
```

---

## 11. Logging e Debug

### 11.1 Sistema di Logging Strutturato

Tutti i componenti implementano logging dettagliato:

```typescript
// Esempio di log strutturato
console.log(`[Hard AI] 🧠 PERFECT MEMORY MODE ATTIVO`);
console.log(`[Hard AI] Carta migliore: ${card.rank} di ${card.suit}`);
console.log(
  `[Hard AI] Score: ${score.toFixed(2)} | Reasoning: ${reasoning[0]}`
);
console.log(
  `[Hard AI] Strategia memoria: ${strategy}, Carte sicure: ${safeCards.length}/${total}`
);
```

### 11.2 Categorie di Log

- **🔥 SISTEMA ANTI-SPRECO UNIFICATO**: Decisioni di ottimizzazione
- **🧠 PERFECT MEMORY**: Valutazioni con memoria completa
- **🎯 RACCOMANDAZIONE**: Suggerimenti ad alta confidenza
- **⚠️ TAGLIO INTELLIGENTE**: Gestione briscole
- **✅ COOPERAZIONE**: Azioni di squadra

### 11.3 Analytics AI

```typescript
const trackAIDecision = (
  gameId: string,
  decisionType: string,
  metrics: AIMetrics,
  context: GameContext
) => {
  // Traccia decisioni per analisi post-game
  // Identifica pattern e ottimizzazioni
  // Fornisce insights per miglioramenti futuri
};
```

---

## Conclusioni

Il sistema AI della Maraffa Romagnola rappresenta un'implementazione sofisticata che bilancia realismo, performance e divertimento. L'architettura modulare facilita manutenzione e espansioni future, mentre il sistema di difficoltà graduata garantisce un'esperienza appropriata per tutti i livelli di giocatori.

### Vantaggi Chiave

1. **Modularità**: Ogni componente ha responsabilità specifiche e ben definite
2. **Scalabilità**: Facile aggiunta di nuove strategie e ottimizzazioni
3. **Performance**: Ottimizzazioni intelligenti riducono calcoli non necessari
4. **Debugging**: Sistema di logging dettagliato facilita manutenzione
5. **Realismo**: Comportamenti che imitano giocatori umani reali

### Possibili Sviluppi Futuri

- Apprendimento automatico da partite giocate
- Personalizzazione stili di gioco per AI
- Analisi avanzata pattern giocatori umani
- Bilanciamento dinamico difficoltà
- Sistema di suggerimenti per giocatori principianti

---

_Documentazione generata per Maraffa Romagnola v2.0_  
_Ultima modifica: $(date)_

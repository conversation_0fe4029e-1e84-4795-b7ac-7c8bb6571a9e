import { Card } from "../../game/cardUtils";

/**
 * Tipi relativi al sistema di memoria dell'AI
 */

export interface CardMemory {
  playedCards: Card[];
  playedBySuit: Record<string, Card[]>;
  playedByPlayer: Record<number, Card[]>;
  remainingCards: Card[];
  suitDistribution: Record<string, number>;
  trumpsRemaining: Card[];
  highCardsRemaining: Record<string, Card[]>;
}

export interface MemoryAnalysis {
  safeCards: Card[];
  dangerousCards: Card[];
  dominantCards: Card[];
  trumpAnalysis: {
    strongTrumpsRemaining: number;
    weakTrumpsRemaining: number;
    opponentTrumpsEstimate: number;
  };
}

export interface SafeCardAnalysis {
  safeCards: Card[];
  safestCard: Card;
  riskAnalysis: Array<{
    card: Card;
    risk: number;
    isSafe: boolean;
  }>;
}

/* Stili per adattare l'app al contesto Android - Modalità Fullscreen */

/* Rimuove safe area per modalità fullscreen completa, ma manteniamo quelle native */
:root {
  --ion-safe-area-top: 0px;
  --ion-safe-area-bottom: 0px;
  --ion-safe-area-left: 0px;
  --ion-safe-area-right: 0px;
}

/* Supporto per notch e aree sicure native del browser */
@supports (padding-top: env(safe-area-inset-top)) {
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }
}

/* Evita overscroll su Android */
html,
body {
  overscroll-behavior: none;
  /* Assicura che l'app occupi tutto lo schermo */
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100vw;
}

/* Adatta l'app al contesto mobile */
@media (max-width: 768px) {
  /* Migliora il tap feedback su elementi interattivi */
  a,
  button,
  [role="button"],
  input[type="button"],
  input[type="submit"],
  input[type="reset"] {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  /* Assicura che gli input non causino zoom indesiderato */
  input,
  textarea,
  select {
    font-size: 16px; /* Evita zoom automatico sui dispositivi mobili */
  }

  /* Ottimizzazioni per la pagina delle regole */
  .rules-container {
    margin: 0;
    padding: 0;
  }

  .rules-content {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .rules-section {
    margin-bottom: 1rem;
  }

  .rules-text {
    line-height: 1.5;
    margin-bottom: 0.75rem;
  }

  /* Assicura che la pagina regole parta dalla cima */
  .rules-page {
    padding-top: 0 !important;
    margin-top: 0 !important;
  }

  /* Scroll sempre dall'inizio per le regole */
  .rules-page * {
    scroll-margin-top: 0;
  }
  main,
  .main-content {
    padding-bottom: calc(var(--ion-safe-area-bottom, 0px) + 1rem);
  }

  /* Aggiunge padding in alto per evitare che elementi siano nascosti dalla barra di stato */
  header,
  .app-header,
  #root {
    padding-top: var(--ion-safe-area-top, 0px);
  }

  /* Assicura che il contenuto del gioco non vada sotto la status bar */
  .game-content,
  .game-table {
    padding-top: calc(var(--ion-safe-area-top, 0px) + 0.5rem);
    top: calc(var(--ion-safe-area-top, 0px) + 0.5rem);
  }

  /* Assicura che la pagina di gioco non abbia scroll */
  .game-page {
    overflow: hidden !important;
    height: 100vh !important;
    max-height: 100vh !important;
    width: 100vw !important;
  }
}

/* Migliora l'esperienza di scrolling su Android */
* {
  -webkit-overflow-scrolling: touch;
}

/* Esclude la pagina di gioco dalle ottimizzazioni di scroll */
.game-page * {
  -webkit-overflow-scrolling: auto;
}

/* Assicura che tutti gli elementi della pagina Game non causino overflow */
.game-page,
.game-page * {
  box-sizing: border-box;
}

.game-page > * {
  max-height: 100%;
  overflow: visible;
}

/* Disabilita scroll globale quando la pagina Game è attiva */
body.game-active,
html.game-active {
  overflow: hidden !important;
  height: 100vh !important;
  max-height: 100vh !important;
}

/* Forza la pagina Game a non avere scroll e occupare tutto lo schermo */
.game-page {
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 1000 !important;
  width: 100vw !important;
  height: 100vh !important;
  max-height: 100vh !important;
  padding: 1rem !important;
  box-sizing: border-box !important;
}

/* Ottimizzazioni specifiche per scroll container Android */
.android-optimized-scroll {
  /* Migliora le performance di scroll */
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;

  /* Forza l'accelerazione hardware */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;

  /* Ottimizza il rendering */
  will-change: scroll-position;
  contain: layout style paint;
}

/* Ottimizzazioni per elementi che si caricano durante lo scroll */
.lazy-load-element {
  /* Placeholder durante il caricamento */
  min-height: 50px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Riduce il reflow durante lo scroll su Android */
@media (max-width: 768px) and (pointer: coarse) {
  .scroll-optimized * {
    /* Evita layout thrashing */
    contain: layout;
  }

  .scroll-optimized img {
    /* Evita reflow delle immagini */
    height: auto;
    max-width: 100%;
    display: block;
  }

  /* Ottimizza le animazioni durante lo scroll */
  .scroll-optimized [class*="animate-"] {
    animation-play-state: paused;
  }

  .scroll-optimized:not(.scrolling) [class*="animate-"] {
    animation-play-state: running;
  }
}

/* Assicura che lo sfondo delle pagine si estenda completamente */
.page-background-full {
  min-height: 100vh;
  min-height: 100dvh; /* Dynamic viewport height per mobile */
}

/* Fix per PlayerProfileModal su Android - assicura che appaia come overlay */
@media (max-width: 768px) {
  .player-profile-modal-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 99999 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: rgba(0, 0, 0, 0.6) !important;
    backdrop-filter: blur(4px) !important;
    -webkit-backdrop-filter: blur(4px) !important;
    /* Forza il rendering come layer separato */
    -webkit-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
    will-change: opacity !important;
    contain: layout style paint !important;
  }

  .player-profile-modal-content {
    position: relative !important;
    z-index: 100000 !important;
    /* Evita che il contenuto sia influenzato da transform dei parent */
    -webkit-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
    will-change: transform, opacity !important;
  }
}

/* Nasconde la selezione di testo non necessaria */
.no-select {
  user-select: none;
  -webkit-user-select: none;
}

/* Disabilita la selezione del testo su Android per tutti gli elementi */
@media (max-width: 768px) {
  * {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
  }

  /* Eccezioni per input, textarea e elementi editabili */
  input,
  textarea,
  [contenteditable="true"],
  [contenteditable] {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
    -webkit-touch-callout: default;
  }

  /* Prevenzione overflow del punteggio su Android mobile */
  .team-score-container {
    max-width: 120px;
    overflow: hidden;
    white-space: nowrap;
  }

  .team-score-text {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    max-width: 100%;
  }

  /* Container player che si adatta al contenuto */
  .player-info-container {
    width: auto !important;
    min-width: 100px !important;
    max-width: none !important;
    overflow: visible !important;
    white-space: nowrap !important;
    padding: 0.25rem 0.5rem !important;
    box-sizing: border-box !important;
    display: flex !important;
    align-items: center !important;
    flex-shrink: 0 !important;
  }

  .player-info-container .text-xs {
    white-space: nowrap !important;
    overflow: visible !important;
    width: auto !important;
    flex-shrink: 0 !important;
  }

  /* Per player sui lati, permettiamo larghezza auto per nomi completi */
  .game-page .absolute.left-0 .player-info-container,
  .game-page .absolute.right-0 .player-info-container {
    max-width: none !important;
    width: auto !important;
  }

  /* Assicura che i contenitori del punteggio non escano dai bordi */
  .game-scores-container {
    max-width: 100vw;
    overflow-x: hidden;
    box-sizing: border-box;
  }
}

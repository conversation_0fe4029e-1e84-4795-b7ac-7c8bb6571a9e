# Script per generare un AAB (Android App Bundle) firmato dell'applicazione Marafone Romagnolo
Write-Host "=== Inizio del processo di build dell'AAB per Marafone Romagnolo ===" -ForegroundColor Green

# Cambia directory alla root del progetto
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition
$projectRoot = Split-Path -Parent $scriptPath
Set-Location $projectRoot

Write-Host "Directory di lavoro: $projectRoot" -ForegroundColor Yellow

$androidFolder = Join-Path $projectRoot "android"
$outputFolder = Join-Path $projectRoot "aab-output"
$aabReleasePath = Join-Path $androidFolder "app\build\outputs\bundle\release\app-release.aab"
$buildGradlePath = Join-Path $androidFolder "app\build.gradle"

# Incrementa automaticamente il versionCode e versionName
Write-Host "Incremento del versionCode e versionName..." -ForegroundColor Cyan
$buildGradleContent = Get-Content $buildGradlePath -Raw

# Incrementa versionCode
$versionCodePattern = 'versionCode\s+(\d+)'
$currentVersionCode = [regex]::Match($buildGradleContent, $versionCodePattern).Groups[1].Value
$newVersionCode = [int]$currentVersionCode + 1

# Incrementa versionName (da 1.0 a 1.1, da 1.9 a 2.0, ecc.)
$versionNamePattern = 'versionName\s+"([^"]+)"'
$currentVersionName = [regex]::Match($buildGradleContent, $versionNamePattern).Groups[1].Value
$versionParts = $currentVersionName.Split('.')
$majorVersion = [int]$versionParts[0]
$minorVersion = [int]$versionParts[1]

# Incrementa la versione minore, se arriva a 10 resetta a 0 e incrementa la maggiore
$minorVersion++
$newVersionName = "$majorVersion.$minorVersion"

# Applica i cambiamenti
$newBuildGradleContent = $buildGradleContent -replace $versionCodePattern, "versionCode $newVersionCode"
$newBuildGradleContent = $newBuildGradleContent -replace $versionNamePattern, "versionName `"$newVersionName`""
Set-Content $buildGradlePath -Value $newBuildGradleContent -NoNewline

Write-Host "VersionCode aggiornato da $currentVersionCode a $newVersionCode" -ForegroundColor Green
Write-Host "VersionName aggiornato da $currentVersionName a $newVersionName" -ForegroundColor Green

# Genera un timestamp per il nome del file
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$finalAabPath = Join-Path $outputFolder "marafone-romagnolo_v$newVersionName`_build$newVersionCode`_$timestamp.aab"

# Crea la cartella di output se non esiste
if (-not (Test-Path $outputFolder)) {
    New-Item -ItemType Directory -Path $outputFolder | Out-Null
    Write-Host "Creata cartella di output per gli AAB: $outputFolder" -ForegroundColor Yellow
}

# Pulisci le build precedenti
Write-Host "Pulizia delle build precedenti..." -ForegroundColor Cyan
if (Test-Path $aabReleasePath) {
    Remove-Item $aabReleasePath -Force
}

# Verifica se esiste il keystore per la firma
$keystorePath = Join-Path $androidFolder "app\release-key.keystore"
$keystorePropertiesPath = Join-Path $androidFolder "keystore.properties"

if (-not (Test-Path $keystorePropertiesPath)) {
    Write-Host "Attenzione: File keystore.properties non trovato in $keystorePropertiesPath" -ForegroundColor Yellow
    Write-Host "Creazione dell'AAB senza firma digitale..." -ForegroundColor Yellow
    $buildCommand = "bundleRelease"
} else {
    Write-Host "File keystore.properties trovato. Creazione dell'AAB firmato..." -ForegroundColor Green
    $buildCommand = "bundleRelease"
}

# Esegui la build dell'app web
Write-Host "Esecuzione della build web con npm..." -ForegroundColor Cyan
Set-Location $projectRoot
npm run build

# Verifica se la build è riuscita
if (-not $?) {
    Write-Host "Errore durante la build web. Processo interrotto." -ForegroundColor Red
    exit 1
}

# Sincronizza con Capacitor
Write-Host "Sincronizzazione con Capacitor..." -ForegroundColor Cyan
npx cap sync android

# Esegui Gradle per creare l'AAB
Write-Host "Creazione dell'AAB con Gradle..." -ForegroundColor Cyan
Set-Location $androidFolder
.\gradlew.bat $buildCommand

# Verifica se la build è riuscita
if (-not $?) {
    Write-Host "Errore durante la build dell'AAB. Processo interrotto." -ForegroundColor Red
    exit 1
}

# Copia l'AAB nella cartella di output
if (Test-Path $aabReleasePath) {
    Copy-Item $aabReleasePath $finalAabPath -Force
    Write-Host "AAB generato con successo e copiato in: $finalAabPath" -ForegroundColor Green
    
    # Mostra le informazioni sul file
    $fileInfo = Get-Item $finalAabPath
    Write-Host "Dimensione del file: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -ForegroundColor Green
    Write-Host "Data di creazione: $($fileInfo.CreationTime)" -ForegroundColor Green
    
    # Informazioni sul formato AAB
    Write-Host "`nInformazioni sull'AAB:" -ForegroundColor Cyan
    Write-Host "- Questo e' un Android App Bundle (AAB), il formato di pubblicazione consigliato per Google Play Store" -ForegroundColor White
    Write-Host "- Google Play generera' automaticamente APK ottimizzati per ciascun dispositivo" -ForegroundColor White
    Write-Host "- Il file AAB non puo' essere installato direttamente su dispositivi" -ForegroundColor White
    
    # Verifica se il file è firmato
    if (Test-Path $keystorePropertiesPath) {
        Write-Host "`nAAB firmato digitalmente e pronto per la pubblicazione su Google Play Store" -ForegroundColor Green
    } else {
        Write-Host "`nAAB non firmato. Per la pubblicazione su Google Play Store e' necessaria la firma digitale." -ForegroundColor Yellow
        Write-Host "Configura keystore.properties con le informazioni del tuo keystore di rilascio." -ForegroundColor Yellow
    }
    
    # Istruzioni per il test locale
    Write-Host "`nPer testare l'AAB localmente con bundletool:" -ForegroundColor Yellow
    Write-Host "1. Scarica bundletool da: https://github.com/google/bundletool/releases" -ForegroundColor White
    Write-Host "2. Genera APK di test: java -jar bundletool.jar build-apks --bundle=`"$finalAabPath`" --output=test.apks" -ForegroundColor White
    Write-Host "3. Installa su dispositivo: java -jar bundletool.jar install-apks --apks=test.apks" -ForegroundColor White
} else {
    Write-Host "AAB non trovato dopo la build. Controlla eventuali errori." -ForegroundColor Red
    
    # Suggerimenti per la risoluzione dei problemi
    Write-Host "`nSuggerimenti per la risoluzione dei problemi:" -ForegroundColor Yellow
    Write-Host "1. Verifica che il progetto Android sia configurato correttamente" -ForegroundColor White
    Write-Host "2. Controlla che tutti i moduli Capacitor siano sincronizzati" -ForegroundColor White
    Write-Host "3. Verifica la configurazione del keystore se stai creando un build firmato" -ForegroundColor White
    
    exit 1
}

# Torna alla directory del progetto
Set-Location $projectRoot
Write-Host "`n=== Processo di build dell'AAB completato ===" -ForegroundColor Green
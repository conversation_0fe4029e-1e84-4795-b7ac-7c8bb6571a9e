/**
 * 🎯 SISTEMA DI CACHE IMMAGINI UNIFICATO
 * Sostituisce cardImageCache, imagePreloader e imagePrefetcher
 * con un unico sistema semplificato ma efficace
 */

import { cardImages } from "@/utils/game/cardImages";

interface CacheStats {
  cachedImages: number;
  loadingImages: number;
  preloadProgress: number;
  totalImages: number;
}

class UnifiedImageCache {
  // Cache principale per immagini caricate
  private imageCache = new Map<string, HTMLImageElement>();
  
  // Promesse di caricamento in corso
  private loadingPromises = new Map<string, Promise<HTMLImageElement>>();
  
  // Set di immagini precaricate con successo
  private preloadedImages = new Set<string>();
  
  // Prefetch tags HTML aggiunti
  private prefetchedUrls = new Set<string>();
  
  // Progress tracking
  private preloadProgress = 0;
  private totalImages = 0;
  private isPreloading = false;

  /**
   * 🚀 PRECARICAMENTO COMPLETO
   * Combina HTML prefetch + JavaScript preloading
   */
  async preloadAllImages(): Promise<void> {
    if (this.isPreloading) return;
    
    console.log("🎯 Avvio precaricamento unificato immagini...");
    this.isPreloading = true;

    try {
      // 1. HTML Prefetch (istantaneo)
      this.addHTMLPrefetchTags();

      // 2. JavaScript Preloading (asincrono)
      await this.preloadWithJavaScript();

      console.log("✅ Precaricamento completato!");
    } catch (error) {
      console.error("❌ Errore durante precaricamento:", error);
    } finally {
      this.isPreloading = false;
    }
  }

  /**
   * 🏷️ PREFETCH HTML NATIVO
   */
  private addHTMLPrefetchTags(): void {
    const allImageUrls = this.getAllImageUrls();

    allImageUrls.forEach((url) => {
      if (!this.prefetchedUrls.has(url)) {
        const link = document.createElement("link");
        link.rel = "prefetch";
        link.href = url;
        link.as = "image";
        document.head.appendChild(link);
        this.prefetchedUrls.add(url);
      }
    });

    console.log(`🏷️ Aggiunti ${allImageUrls.length} prefetch tags HTML`);
  }

  /**
   * ⚡ PRECARICAMENTO JAVASCRIPT
   */
  private async preloadWithJavaScript(): Promise<void> {
    const allImageUrls = this.getAllImageUrls();
    const imagesToLoad = allImageUrls.filter(url => !this.preloadedImages.has(url));
    
    this.totalImages = imagesToLoad.length;
    this.preloadProgress = 0;

    if (imagesToLoad.length === 0) {
      console.log("✅ Tutte le immagini già in cache");
      return;
    }

    console.log(`⚡ Precaricamento ${imagesToLoad.length} immagini...`);

    // Caricamento in batch paralleli (6 alla volta)
    const batchSize = 6;
    const batches = [];

    for (let i = 0; i < imagesToLoad.length; i += batchSize) {
      batches.push(imagesToLoad.slice(i, i + batchSize));
    }

    for (const batch of batches) {
      const batchPromises = batch.map(url => this.loadSingleImage(url));
      await Promise.allSettled(batchPromises);
      
      // Piccola pausa tra batch per non bloccare il browser
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }

  /**
   * 📷 CARICAMENTO SINGOLA IMMAGINE
   */
  private async loadSingleImage(src: string): Promise<HTMLImageElement> {
    // Se già in cache, restituisci immediatamente
    if (this.imageCache.has(src)) {
      return this.imageCache.get(src)!;
    }

    // Se già in caricamento, aspetta il completamento
    if (this.loadingPromises.has(src)) {
      return this.loadingPromises.get(src)!;
    }

    // Avvia nuovo caricamento
    const loadPromise = new Promise<HTMLImageElement>((resolve, reject) => {
      const img = new Image();

      img.onload = () => {
        this.imageCache.set(src, img);
        this.preloadedImages.add(src);
        this.loadingPromises.delete(src);
        this.preloadProgress++;
        resolve(img);
      };

      img.onerror = () => {
        this.loadingPromises.delete(src);
        this.preloadProgress++;
        reject(new Error(`Failed to load: ${src}`));
      };

      // Ottimizzazioni caricamento
      img.decoding = "async";
      img.loading = "eager";
      img.crossOrigin = "anonymous";
      img.src = src;
    });

    this.loadingPromises.set(src, loadPromise);
    return loadPromise;
  }

  /**
   * 📋 OTTIENI TUTTI GLI URL IMMAGINI
   */
  private getAllImageUrls(): string[] {
    const urls: string[] = [];

    // Carte di gioco
    Object.values(cardImages).forEach((suitCards) => {
      Object.values(suitCards).forEach((cardPath) => {
        urls.push(cardPath);
      });
    });

    // Immagini dei semi
    const suitImages = [
      "/images/semi/denari.png",
      "/images/semi/coppe.png", 
      "/images/semi/spade.png",
      "/images/semi/bastoni.png",
    ];
    urls.push(...suitImages);

    return urls;
  }

  /**
   * ✅ VERIFICA SE IMMAGINE È DISPONIBILE
   */
  isImageReady(src: string): boolean {
    return this.imageCache.has(src) || this.preloadedImages.has(src);
  }

  /**
   * 🎯 CARICA IMMAGINE ON-DEMAND
   */
  async loadImage(src: string): Promise<HTMLImageElement> {
    return this.loadSingleImage(src);
  }

  /**
   * 📊 STATISTICHE CACHE
   */
  getStats(): CacheStats {
    return {
      cachedImages: this.imageCache.size,
      loadingImages: this.loadingPromises.size,
      preloadProgress: this.preloadProgress,
      totalImages: this.totalImages,
    };
  }

  /**
   * 🧹 PULIZIA CACHE
   */
  clearCache(): void {
    this.imageCache.clear();
    this.loadingPromises.clear();
    this.preloadedImages.clear();
    this.preloadProgress = 0;
    this.totalImages = 0;
    
    // Rimuovi prefetch tags
    this.removePrefetchTags();
  }

  /**
   * 🗑️ RIMUOVI PREFETCH TAGS
   */
  private removePrefetchTags(): void {
    const prefetchLinks = document.querySelectorAll('link[rel="prefetch"]');
    prefetchLinks.forEach(link => link.remove());
    this.prefetchedUrls.clear();
  }

  /**
   * 🔄 PRECARICA CARTE SPECIFICHE
   */
  async preloadSpecificCards(cardSources: string[]): Promise<void> {
    const promises = cardSources.map(src => 
      this.loadSingleImage(src).catch(err => 
        console.warn(`Failed to preload card: ${src}`, err)
      )
    );
    await Promise.allSettled(promises);
  }
}

// Istanza globale unificata
export const unifiedImageCache = new UnifiedImageCache();

// Export per compatibilità con codice esistente
export const cardImageCache = unifiedImageCache;
export const imagePreloader = unifiedImageCache;
export const imagePrefetcher = unifiedImageCache;

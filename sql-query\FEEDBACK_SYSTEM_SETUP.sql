-- =====================================================
-- SETUP SISTEMA FEEDBACK E BACHECA FUNZIONALITÀ
-- =====================================================
-- Questo script crea le tabelle necessarie per il sistema
-- di feedback e bacheca delle funzionalità richieste dagli utenti

-- Tabella per le richieste di funzionalità/feedback
CREATE TABLE IF NOT EXISTS feedback_requests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL CHECK (length(title) <= 100),
    description TEXT NOT NULL CHECK (length(description) <= 500),
    category TEXT NOT NULL CHECK (category IN ('feature', 'improvement', 'bug', 'other')),
    author TEXT NOT NULL,
    votes INTEGER DEFAULT 0 CHECK (votes >= 0),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabella per i voti degli utenti sui feedback
CREATE TABLE IF NOT EXISTS feedback_votes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    feedback_id UUID NOT NULL REFERENCES feedback_requests(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(feedback_id, user_id) -- Un utente può votare una sola volta per feedback
);

-- Indici per migliorare le performance
CREATE INDEX IF NOT EXISTS idx_feedback_requests_votes ON feedback_requests(votes DESC);
CREATE INDEX IF NOT EXISTS idx_feedback_requests_created_at ON feedback_requests(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_feedback_requests_status ON feedback_requests(status);
CREATE INDEX IF NOT EXISTS idx_feedback_requests_category ON feedback_requests(category);
CREATE INDEX IF NOT EXISTS idx_feedback_votes_feedback_id ON feedback_votes(feedback_id);
CREATE INDEX IF NOT EXISTS idx_feedback_votes_user_id ON feedback_votes(user_id);

-- Trigger per aggiornare updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_feedback_requests_updated_at 
    BEFORE UPDATE ON feedback_requests 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Abilita RLS sulle tabelle
ALTER TABLE feedback_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE feedback_votes ENABLE ROW LEVEL SECURITY;

-- Policy per feedback_requests
-- Tutti possono leggere i feedback
CREATE POLICY "Tutti possono leggere i feedback" ON feedback_requests
    FOR SELECT USING (true);

-- Solo utenti autenticati possono creare feedback
CREATE POLICY "Utenti autenticati possono creare feedback" ON feedback_requests
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Solo l'autore può modificare il proprio feedback (per ora disabilitato)
-- CREATE POLICY "Autori possono modificare i propri feedback" ON feedback_requests
--     FOR UPDATE USING (auth.email() = author);

-- Policy per feedback_votes
-- Tutti possono leggere i voti (per conteggi)
CREATE POLICY "Tutti possono leggere i voti" ON feedback_votes
    FOR SELECT USING (true);

-- Solo utenti autenticati possono votare
CREATE POLICY "Utenti autenticati possono votare" ON feedback_votes
    FOR INSERT WITH CHECK (auth.role() = 'authenticated' AND auth.uid() = user_id);

-- Gli utenti possono rimuovere i propri voti
CREATE POLICY "Utenti possono rimuovere i propri voti" ON feedback_votes
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- FUNZIONI HELPER
-- =====================================================

-- Funzione per ottenere i feedback con i voti dell'utente corrente
CREATE OR REPLACE FUNCTION get_feedback_with_user_votes()
RETURNS TABLE (
    id UUID,
    title TEXT,
    description TEXT,
    category TEXT,
    author TEXT,
    votes INTEGER,
    status TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    user_has_voted BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        fr.id,
        fr.title,
        fr.description,
        fr.category,
        fr.author,
        fr.votes,
        fr.status,
        fr.created_at,
        CASE 
            WHEN auth.uid() IS NULL THEN false
            ELSE EXISTS (
                SELECT 1 FROM feedback_votes fv 
                WHERE fv.feedback_id = fr.id AND fv.user_id = auth.uid()
            )
        END as user_has_voted
    FROM feedback_requests fr
    ORDER BY fr.votes DESC, fr.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- DATI DI ESEMPIO (OPZIONALE)
-- =====================================================

-- Inserisci alcuni feedback di esempio per testare il sistema
INSERT INTO feedback_requests (title, description, category, author, votes, status) VALUES
('Modalità Torneo', 'Sarebbe fantastico avere una modalità torneo con eliminazione diretta per competere con altri giocatori online.', 'feature', '<EMAIL>', 15, 'pending'),
('Migliorare AI Difficile', 'L''intelligenza artificiale in modalità difficile potrebbe essere più strategica nella gestione delle briscole.', 'improvement', '<EMAIL>', 8, 'in_progress'),
('Statistiche Dettagliate', 'Aggiungere statistiche più dettagliate come percentuale di vittorie per tipo di carta giocata.', 'feature', '<EMAIL>', 12, 'pending'),
('Tema Scuro', 'Implementare un tema scuro per giocare di sera senza affaticare gli occhi.', 'feature', '<EMAIL>', 20, 'completed'),
('Suoni Personalizzabili', 'Permettere agli utenti di caricare i propri suoni personalizzati per le azioni di gioco.', 'feature', '<EMAIL>', 5, 'pending')
ON CONFLICT DO NOTHING;

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Assicurati che gli utenti autenticati possano accedere alle tabelle
GRANT SELECT, INSERT ON feedback_requests TO authenticated;
GRANT SELECT, INSERT, DELETE ON feedback_votes TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

-- Permetti l'esecuzione della funzione helper
GRANT EXECUTE ON FUNCTION get_feedback_with_user_votes() TO authenticated;
GRANT EXECUTE ON FUNCTION get_feedback_with_user_votes() TO anon;

COMMIT;

import { useToast } from "@/hooks/use-toast";
import { CheckCircle, XCircle, Info, X } from "lucide-react";
import { useEffect } from "react";

/**
 * Provider per toast avanzati con grafica migliorata e animazioni
 * I toast sono larghi quanto la pagina (con margine di 20px ai lati),
 * hanno animazioni di entrata/uscita e scompaiono automaticamente dopo 2 secondi
 */
export function AdvancedToastProvider() {
  const { toasts, dismiss } = useToast();

  // Auto-dismiss dei toast dopo la duration specificata
  useEffect(() => {
    toasts.forEach((toast) => {
      if (toast.duration && toast.duration > 0) {
        const timer = setTimeout(() => {
          dismiss(toast.id);
        }, toast.duration);

        return () => clearTimeout(timer);
      }
    });
  }, [toasts, dismiss]);
  const getToastType = (toast: {
    title?: React.ReactNode;
    variant?: string;
  }): "success" | "error" | "info" => {
    if (
      typeof toast.title === "string" &&
      toast.title.startsWith("__toast_type_")
    ) {
      const type = toast.title.replace("__toast_type_", "").replace("__", "");
      return type as "success" | "error" | "info";
    }
    return toast.variant === "destructive" ? "error" : "info";
  };

  const getToastIcon = (toastType: "success" | "error" | "info") => {
    switch (toastType) {
      case "error":
        return <XCircle className="w-5 h-5 text-red-500" />;
      case "success":
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      default:
        return <Info className="w-5 h-5 text-blue-500" />;
    }
  };

  const getToastStyles = (toastType: "success" | "error" | "info") => {
    const baseStyles = `
      fixed top-4 left-1/2 transform -translate-x-1/2 z-50
      w-[calc(100%-40px)] max-w-md
      bg-white border border-gray-200 rounded-lg shadow-lg
      flex items-center gap-3 p-4
      animation: toastSlideIn 0.3s ease-out forwards
    `;

    switch (toastType) {
      case "error":
        return `${baseStyles} border-red-200 bg-red-50`;
      case "success":
        return `${baseStyles} border-green-200 bg-green-50`;
      default:
        return `${baseStyles} border-blue-200 bg-blue-50`;
    }
  };

  if (toasts.length === 0) return null;

  return (
    <div className="toast-container">
      {toasts.map((toast) => {
        const toastType = getToastType(toast);
        return (
          <div
            key={toast.id}
            className={getToastStyles(toastType)}
            style={{
              animation:
                toast.open === false
                  ? "toastSlideOut 0.2s ease-in forwards"
                  : "toastSlideIn 0.3s ease-out forwards",
            }}
          >
            {getToastIcon(toastType)}

            <div className="flex-1">
              {toast.title &&
                !toast.title.toString().startsWith("__toast_type_") && (
                  <div className="font-semibold text-gray-900 mb-1">
                    {toast.title}
                  </div>
                )}
              {toast.description && (
                <div className="text-gray-700 text-sm">{toast.description}</div>
              )}
            </div>

            <button
              onClick={() => dismiss(toast.id)}
              className="ml-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        );
      })}
    </div>
  );
}

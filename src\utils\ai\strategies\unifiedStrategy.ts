import { GameState } from "../../game/gameLogic";
import { Card } from "../../game/cardUtils";
import { AIDifficulty } from "../types";
import { CardEvaluator } from "../core/cardEvaluator";
import { createSafeCardMemory, analyzeCardMemory } from "../memory/analyzer";
import { getOptimalCards } from "../utils/antiWaste";
import { getCooperativeStrategy } from "./specialized/cooperativeStrategy";
import {
  isCardSafe,
  getManyTrumpsStrategy,
  getEndgameStrategy,
  prioritize3Over2,
} from "../utils/cardUtils";

/**
 * 🎯 STRATEGIA AI UNIFICATA
 * Una sola strategia che adatta il comportamento in base alla difficoltà
 * usando probabilità di errore invece di logiche separate
 */

// Configurazione probabilità di errore per difficoltà
const DIFFICULTY_CONFIG = {
  [AIDifficulty.EASY]: {
    errorRate: 0.4, // 40% di probabilità di fare scelte subottimali
    cooperationRate: 0.6, // 60% di cooperazione con il compagno
    strategicRate: 0.3, // 30% di giocate strategiche
    memoryUsage: 0.4, // 40% di utilizzo della memoria
  },
  [AIDifficulty.MEDIUM]: {
    errorRate: 0.1, // 10% di probabilità di errore
    cooperationRate: 0.9, // 90% di cooperazione
    strategicRate: 0.6, // 60% di giocate strategiche
    memoryUsage: 0.7, // 70% di utilizzo della memoria
  },
  [AIDifficulty.HARD]: {
    errorRate: 0.0, // 0% di errore (gioco perfetto)
    cooperationRate: 1.0, // 100% di cooperazione
    strategicRate: 1.0, // 100% di giocate strategiche
    memoryUsage: 1.0, // 100% di utilizzo della memoria
  },
};

export const getUnifiedAIMove = (
  cards: Card[],
  state: GameState,
  playerIndex: number,
  difficulty: AIDifficulty
): Card => {
  const config = DIFFICULTY_CONFIG[difficulty];
  const playerTeam = state.players?.[playerIndex]?.team || "UNKNOWN";
  const playerName = `CPU${playerIndex} (Team ${playerTeam}) [${difficulty}]`;

  console.log(`\n🤖 [UNIFIED AI] ${playerName} - Thinking...`);

  // 🎯 PRIORITÀ ASSOLUTA 1: MARAFFA - Se ho la maraffa e sono primo del turno nel primo trick, DEVO giocare l'Asso
  if (!state.currentTrick || state.currentTrick.length === 0) {
    const trickNumber = state.trickNumber ?? 1;

    // CONTROLLO MARAFFA CRITICO
    if (
      trickNumber === 1 && // Primo turno della mano
      state.trumpSuit && // Briscola selezionata
      state.lastTrumpSelector === playerIndex // Io ho scelto la briscola
    ) {
      // Verifica se ho la maraffa completa
      const hasAce = cards.some(
        (card) => card.suit === state.trumpSuit && card.rank === "A"
      );
      const hasTwo = cards.some(
        (card) => card.suit === state.trumpSuit && card.rank === "2"
      );
      const hasThree = cards.some(
        (card) => card.suit === state.trumpSuit && card.rank === "3"
      );

      if (hasAce && hasTwo && hasThree) {
        const maraffaAce = cards.find(
          (card) => card.suit === state.trumpSuit && card.rank === "A"
        );
        if (maraffaAce) {
          console.log(
            `[UNIFIED AI] 🎯🎯🎯 MARAFFA OBBLIGATORIA! Gioco Asso di ${maraffaAce.suit} per 3 punti bonus!`
          );
          return maraffaAce;
        }
      }
    }

    // 🎯 PRIORITÀ 2: APERTURA STRATEGICA CON 3 NON-BRISCOLA
    const isEarlyGame = trickNumber <= 3;
    const strategicOpeningRate = isEarlyGame ? 0.8 : config.strategicRate;

    if (Math.random() < strategicOpeningRate) {
      console.log(
        `[UNIFIED AI] 🎯 Tentativo apertura strategica (${(
          strategicOpeningRate * 100
        ).toFixed(0)}%)`
      );

      const opening3s = cards.filter(
        (card) => card.rank === "3" && card.suit !== state.trumpSuit
      );

      if (opening3s.length > 0) {
        const best3 = opening3s[0];
        console.log(`[UNIFIED AI] ✅ Apertura con 3 di ${best3.suit}`);
        return best3;
      }
    }
  }

  // 🤝 STRATEGIA COOPERATIVA (basata su difficoltà)
  if (Math.random() < config.cooperationRate) {
    const memory = createSafeCardMemory(state);
    const cooperativeStrategy = getCooperativeStrategy(
      state,
      playerIndex,
      cards,
      memory
    );

    if (
      cooperativeStrategy.strategy === "support" &&
      cooperativeStrategy.recommendedCard
    ) {
      console.log(
        `[UNIFIED AI] 🤝 SUPPORTO TEAM (${(
          config.cooperationRate * 100
        ).toFixed(0)}%): ${cooperativeStrategy.reason}`
      );
      return cooperativeStrategy.recommendedCard;
    }

    if (
      cooperativeStrategy.strategy === "compete" &&
      cooperativeStrategy.recommendedCard
    ) {
      // CONTROLLO SPECIALE: Se sono primo del turno, verifica che la carta cooperativa sia appropriata
      const isFirstInTrick =
        !state.currentTrick || state.currentTrick.length === 0;

      if (isFirstInTrick) {
        const evaluator = new CardEvaluator();
        const cardValue = evaluator.getCardValue(
          cooperativeStrategy.recommendedCard
        );
        const isSmooth = ["4", "5", "6", "7"].includes(
          cooperativeStrategy.recommendedCard.rank
        );
        const isTrump =
          cooperativeStrategy.recommendedCard.suit === state.trumpSuit;

        if (cardValue > 0 && !isSmooth && !isTrump) {
          console.log(
            `[UNIFIED AI] ⚠️ BLOCCO strategia cooperativa - carta con punti (${cooperativeStrategy.recommendedCard.rank} di ${cooperativeStrategy.recommendedCard.suit}) non appropriata per primo turno`
          );
        } else {
          console.log(
            `[UNIFIED AI] 🚨 RECUPERO AGGRESSIVO (${(
              config.cooperationRate * 100
            ).toFixed(0)}%): ${cooperativeStrategy.reason}`
          );
          return cooperativeStrategy.recommendedCard;
        }
      } else {
        // Non sono primo del turno: strategia cooperativa OK
        console.log(
          `[UNIFIED AI] 🚨 RECUPERO AGGRESSIVO (${(
            config.cooperationRate * 100
          ).toFixed(0)}%): ${cooperativeStrategy.reason}`
        );
        return cooperativeStrategy.recommendedCard;
      }
    }
  }

  // 🔥 CONTROLLO ASSO SUL TAVOLO
  if (state.currentTrick && state.currentTrick.length > 0) {
    const hasAceOnTable = state.currentTrick.some(
      (card) => card && card.rank === "A"
    );

    if (hasAceOnTable) {
      console.log(`[UNIFIED AI] 🔥 ASSO SUL TAVOLO! Cerco di prenderlo...`);

      const evaluator = new CardEvaluator();
      const winningCards = cards.filter((card) => {
        const testTrick = [...state.currentTrick.filter(Boolean), card];
        return evaluator.canWinCurrentTrick(
          card,
          testTrick,
          state.leadSuit,
          state.trumpSuit
        );
      });

      if (winningCards.length > 0) {
        // Anche qui applichiamo la probabilità di errore
        if (Math.random() > config.errorRate) {
          const cardToPlay = winningCards[0];
          console.log(
            `[UNIFIED AI] 🔥 PRENDO ASSO CON ${cardToPlay.rank} di ${cardToPlay.suit}!`
          );
          return cardToPlay;
        } else {
          console.log(
            `[UNIFIED AI] 😵 ERRORE: Non riesco a prendere l'asso (${(
              config.errorRate * 100
            ).toFixed(0)}% errore)`
          );
        }
      }
    }
  }

  // 🧠 UTILIZZO MEMORIA (basato su difficoltà)
  if (Math.random() < config.memoryUsage) {
    console.log(
      `[UNIFIED AI] 🧠 Uso analisi memoria (${(
        config.memoryUsage * 100
      ).toFixed(0)}%)`
    );

    const optimalAnalysis = getOptimalCards(
      cards,
      state.currentTrick || [],
      state,
      playerIndex
    );

    if (
      optimalAnalysis.optimalCards.length > 0 &&
      Math.random() > config.errorRate
    ) {
      const optimalCard = optimalAnalysis.optimalCards[0];

      // CONTROLLO SPECIALE: Se sono primo del turno, evita briscole a meno che non abbia molte briscole
      const isFirstInTrick =
        !state.currentTrick || state.currentTrick.length === 0;
      if (isFirstInTrick && optimalCard.suit === state.trumpSuit) {
        const trumpCards = cards.filter((c) => c.suit === state.trumpSuit);
        if (trumpCards.length < 4) {
          console.log(
            `[UNIFIED AI] ⚠️ Memoria suggerisce briscola ${optimalCard.rank}, ma ho solo ${trumpCards.length} briscole - ignoro suggerimento`
          );
        } else {
          console.log(
            `[UNIFIED AI] ✅ Carta ottimale dalla memoria: ${optimalCard.rank} di ${optimalCard.suit}`
          );
          return optimalCard;
        }
      } else {
        console.log(
          `[UNIFIED AI] ✅ Carta ottimale dalla memoria: ${optimalCard.rank} di ${optimalCard.suit}`
        );
        return optimalCard;
      }
    }
  }

  // 🎲 INTRODUCI ERRORI CASUALI (basato su difficoltà)
  if (Math.random() < config.errorRate) {
    console.log(
      `[UNIFIED AI] 😵 ERRORE CASUALE (${(config.errorRate * 100).toFixed(
        0
      )}% probabilità)`
    );
    const randomCard = cards[Math.floor(Math.random() * cards.length)];
    console.log(
      `[UNIFIED AI] 🎲 Gioco carta casuale: ${randomCard.rank} di ${randomCard.suit}`
    );
    return randomCard;
  }

  // 🎯 LOGICA OTTIMALE (quando non ci sono errori)
  console.log(`[UNIFIED AI] 🎯 Logica ottimale`);

  // Se è primo del turno, implementa strategia corretta
  if (!state.currentTrick || state.currentTrick.length === 0) {
    console.log(
      `[UNIFIED AI] 🎯 PRIMO DEL TURNO - Strategia apertura intelligente`
    );

    const evaluator = new CardEvaluator();
    const memory = analyzeCardMemory(state);
    const trumpCards = cards.filter((card) => card.suit === state.trumpSuit);

    // CONTROLLO ENDGAME: Evita di giocare carte che dovrebbero essere conservate
    const endgameStrategy = getEndgameStrategy(cards, state, playerIndex);
    let availableCardsForPlay = cards;

    if (endgameStrategy.shouldConserve && endgameStrategy.cardToAvoid) {
      availableCardsForPlay = cards.filter(
        (card) =>
          !(
            card.suit === endgameStrategy.cardToAvoid?.suit &&
            card.rank === endgameStrategy.cardToAvoid?.rank
          )
      );
      console.log(`[UNIFIED AI] 🎯 ${endgameStrategy.reason}`);
    }

    // PRIORITÀ 1: Carte sicure che non possono essere superate
    // Il 3 è sempre buono, il 2 se il 3 è già uscito, ecc.
    // MA evita briscole a meno che non si abbia la strategia delle molte briscole
    const safeCards = availableCardsForPlay.filter((card) => {
      // Evita assi e re non-briscola per primo (troppo rischioso)
      if (
        (card.rank === "A" || card.rank === "K") &&
        card.suit !== state.trumpSuit
      )
        return false;

      // REGOLA IMPORTANTE: Evita briscole come apertura a meno che non si abbia molte briscole
      if (card.suit === state.trumpSuit) {
        const trumpCards = cards.filter((c) => c.suit === state.trumpSuit);
        if (trumpCards.length < 4) {
          console.log(
            `[UNIFIED AI] ⚠️ Evito briscola ${card.rank} come apertura - ho solo ${trumpCards.length} briscole`
          );
          return false;
        }
      }

      return isCardSafe(card, memory, state.trumpSuit, null);
    });

    // PRIORITÀ SPECIALE: Se ho sia 2 che 3 dello stesso seme, preferisci sempre il 3
    if (safeCards.length > 0) {
      const prioritizedSafeCards = prioritize3Over2(safeCards, memory);
      if (prioritizedSafeCards.length > 0) {
        const bestSafe = prioritizedSafeCards[0];
        console.log(
          `[UNIFIED AI] ✅ CARTA SICURA: ${bestSafe.rank} di ${bestSafe.suit}`
        );
        return bestSafe;
      }
    }

    // PRIORITÀ 2: Strategia molte briscole - Se ho 4+ briscole, far finire le briscole agli avversari
    const manyTrumpsStrategy = getManyTrumpsStrategy(
      availableCardsForPlay,
      state,
      playerIndex
    );
    if (
      manyTrumpsStrategy.shouldPlayTrump &&
      manyTrumpsStrategy.recommendedCard
    ) {
      console.log(`[UNIFIED AI] 🎯 ${manyTrumpsStrategy.reason}`);
      return manyTrumpsStrategy.recommendedCard;
    }

    // PRIORITÀ 3: Se non ho carte sicure, carte lisce (4,5,6,7) che non valgono punti
    const smoothCards = availableCardsForPlay.filter((card) => {
      const hasNoValue = evaluator.getCardValue(card) === 0; // Nessun valore in punti
      const isNotTrump = card.suit !== state.trumpSuit; // Non briscole
      const isSmooth = ["4", "5", "6", "7"].includes(card.rank); // Carte lisce
      return hasNoValue && isNotTrump && isSmooth;
    });

    if (smoothCards.length > 0) {
      // Tra le carte lisce, prendi quella più alta per sicurezza
      const bestSmooth = smoothCards.reduce((best, card) =>
        evaluator.getCardOrder(card) > evaluator.getCardOrder(best)
          ? card
          : best
      );
      console.log(
        `[UNIFIED AI] ✅ CARTA LISCIA: ${bestSmooth.rank} di ${bestSmooth.suit} (nessun punto da perdere)`
      );
      return bestSmooth;
    }

    // FALLBACK: Se tutte le carte hanno rischi, prendi quella con minor valore
    // Ma evita comunque assi e re non-briscola
    const lowRiskCards = availableCardsForPlay.filter((card) => {
      if (
        (card.rank === "A" || card.rank === "K") &&
        card.suit !== state.trumpSuit
      ) {
        return false; // Evita assi e re non-briscola
      }
      return true;
    });

    const fallbackCard = (
      lowRiskCards.length > 0 ? lowRiskCards : availableCardsForPlay
    ).reduce((best, card) =>
      evaluator.getCardValue(card) < evaluator.getCardValue(best) ? card : best
    );

    console.log(
      `[UNIFIED AI] ⚠️ FALLBACK: ${fallbackCard.rank} di ${fallbackCard.suit} (minor rischio, evito assi/re non-briscola)`
    );
    return fallbackCard;
  }

  // Fallback: carta di minor valore
  const evaluator = new CardEvaluator();
  const lowestCard = cards.reduce((lowest, card) =>
    evaluator.getCardValue(card) < evaluator.getCardValue(lowest)
      ? card
      : lowest
  );

  console.log(
    `[UNIFIED AI] 📉 Carta di minor valore: ${lowestCard.rank} di ${lowestCard.suit}`
  );
  return lowestCard;
};

import { Card } from "../../game/cardUtils";
import { GameState } from "../../game/gameLogic";

/**
 * Tipi core dell'AI e dell'analisi delle carte
 */

export interface CardEvaluation {
  card: Card;
  baseValue: number;
  strengthScore: number;
  winningPotential: number;
  safetyScore: number;
  strategicValue: number;
}

export interface CardWasteAnalysis {
  isWaste: boolean;
  reason: string;
  severity: "low" | "medium" | "high" | "critical";
  alternativesExist: boolean;
}

export interface OptimalCardsResult {
  optimalCards: Card[];
  wasteAnalysis: CardWasteAnalysis[];
  recommendation: {
    bestCard: Card | null;
    reason: string;
    confidence: number;
  };
}

export interface RiskBenefitAnalysis {
  risk: number;
  benefit: number;
  shouldPlay: boolean;
  confidence: number;
  reasoning: string[];
}

export interface HandQuality {
  strongCards: number;
  trumpCount: number;
  dominantCards: number;
  overallStrength: "weak" | "medium" | "strong";
}

export interface GameContext {
  state: GameState;
  playerIndex: number;
  difficulty: "easy" | "medium" | "hard";
  isEndGame: boolean;
  trickValue: number;
}

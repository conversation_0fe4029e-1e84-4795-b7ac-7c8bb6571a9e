import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAudio } from "@/hooks/useAudio";
import { Card as CardType, Suit, getValidCards } from "@/utils/game/cardUtils";
import {
  GameState,
  selectTrump,
  playCard,
  announceAction as announceActionLogic,
  startNewRound as startNewRoundLogic,
  declareGameWin,
  startNewGame as startNewGameLogic,
} from "@/utils/game/gameLogic";
import {
  ActionAnnouncementType,
  TrumpAnnouncementType,
} from "@/types/gameTypes";
import { cleanupGame } from "@/utils/game/cleanupHandler";
import { updateStatsAfterAbandonment } from "@/services/profileService";

interface UseGameHandlersProps {
  gameState: GameState;
  setGameState: React.Dispatch<React.SetStateAction<GameState>>;
  setValidCards: React.Dispatch<React.SetStateAction<CardType[]>>;
  setShowingLastTrick: React.Dispatch<React.SetStateAction<boolean>>;
  setLastPlayedCard: React.Dispatch<React.SetStateAction<CardType | null>>;
  setShowExitDialog: React.Dispatch<React.SetStateAction<boolean>>;
  setShowHelpSheet: React.Dispatch<React.SetStateAction<boolean>>;
  setCardHasBeenPlayed: React.Dispatch<React.SetStateAction<boolean>>;
  setActionAnnouncement: React.Dispatch<
    React.SetStateAction<ActionAnnouncementType>
  >;
  setTrumpAnnouncement: React.Dispatch<
    React.SetStateAction<TrumpAnnouncementType>
  >;
  setShowRoundSummary: React.Dispatch<React.SetStateAction<boolean>>;
  cardHasBeenPlayed: boolean;
  difficulty?: "easy" | "medium" | "hard";
}

export const useGameHandlers = ({
  gameState,
  setGameState,
  setValidCards,
  setShowingLastTrick,
  setLastPlayedCard,
  setShowExitDialog,
  setShowHelpSheet,
  setCardHasBeenPlayed,
  setActionAnnouncement,
  setTrumpAnnouncement,
  setShowRoundSummary,
  cardHasBeenPlayed,
  difficulty = "medium",
}: UseGameHandlersProps) => {
  const navigate = useNavigate();
  const { playSound } = useAudio();

  // Handler per selezionare la briscola
  const handleSelectTrump = (suit: Suit) => {
    // Nascondi immediatamente la modale di selezione
    const gameControls = document.querySelector("[data-trump-selector]");
    if (gameControls) {
      gameControls.classList.add("hidden");
    }

    // Show trump announcement animation
    const playerName = gameState.players[gameState.currentPlayer].name;
    setTrumpAnnouncement({
      suit: suit,
      playerName: playerName,
      visible: true,
    });

    // Hide the announcement after a delay
    setTimeout(() => {
      setTrumpAnnouncement((prev) => ({ ...prev, visible: false }));

      // Check if the current player has Asso, 2, and 3 of the selected trump suit
      // La maraffa è valida solo per il giocatore che sceglie la briscola
      const currentPlayerHand = gameState.players[gameState.currentPlayer].hand;
      const hasSpecialCards = ["A", "2", "3"].every((rank) =>
        currentPlayerHand.some(
          (card) => card.suit === suit && card.rank === rank
        )
      );

      // Check if player has special cards (maraffa) for announcement only
      if (hasSpecialCards) {
        // 🔊 Suono speciale per Maraffa
        playSound("maraffa");

        const updatedGameState = selectTrump(gameState, suit);

        // Notifica al giocatore che ha fatto maraffa
        setActionAnnouncement({
          type: "maraffa",
          playerIndex: gameState.currentPlayer,
        });

        // Nascondi l'annuncio dopo 3 secondi
        setTimeout(() => {
          setActionAnnouncement({ type: null, playerIndex: -1 });
        }, 2500);

        setGameState(updatedGameState);
      } else {
        setGameState(selectTrump(gameState, suit));
      }
    }, 2000);
  };

  // Handler per annunciare un'azione
  const handleAnnounceAction = (action: "busso" | "striscio" | "volo") => {
    playSound("notification");

    // Show the announcement badge
    setActionAnnouncement({
      type: action,
      playerIndex: gameState.currentPlayer,
    });

    // Hide the announcement after a few seconds
    setTimeout(() => {
      setActionAnnouncement({ type: null, playerIndex: -1 });
    }, 2500);

    setGameState(announceActionLogic(gameState, action));
  };
  // Handler per giocare una carta
  const handlePlayCard = (card: CardType) => {
    // Prevent playing another card if one has already been played in this turn
    if (gameState.currentPlayer === 0 && cardHasBeenPlayed) {
      return;
    }

    playSound("cardPlay", { playerId: 0 });

    // Mark that a card has been played in this turn
    setCardHasBeenPlayed(true);

    setLastPlayedCard(card);
    const newState = playCard(gameState, card.id);

    // Se la presa è completa (tutte le 4 carte sono state giocate)
    if (
      newState.currentTrick.length === 0 &&
      gameState.currentTrick.length === 3
    ) {
      // Rimuovi immediatamente la carta dalla mano del giocatore umano
      // senza applicare tutto il nuovo stato (per non interferire con le animazioni)
      const immediateState = {
        ...gameState,
        players: newState.players, // Solo aggiorna le mani dei giocatori
      };
      setGameState(immediateState);

      setShowingLastTrick(true);
      setTimeout(() => {
        setShowingLastTrick(false);
        setLastPlayedCard(null);
        setGameState(newState); // Applica il nuovo stato completo dopo le animazioni
        // Reset the flag when the trick is done
        setCardHasBeenPlayed(false);
      }, 2800); // Tempo sufficiente per animazioni veloci: 500ms + 800ms + 300ms + 1500ms = 3100ms + margine
    } else {
      setGameState(newState);

      // Reset the flag when it's a new player's turn
      if (newState.currentPlayer !== gameState.currentPlayer) {
        setCardHasBeenPlayed(false);
      }
    }
  };

  // Handler per dichiarare una vittoria
  const handleDeclareWin = (teamId: 0 | 1) => {
    playSound("victory-cheer");
    setGameState(declareGameWin(gameState, teamId));
  };
  // Handler per iniziare un nuovo round
  const handleStartNewRound = () => {
    setShowRoundSummary(false);
    const newState = startNewRoundLogic(gameState);

    // Non sovrascriviamo la gamePhase qui, manteniamo "selectTrump" come impostato da startNewRound
    // per permettere la scelta della briscola per il nuovo round
    Object.assign(newState, {
      currentTrick: [],
      leadSuit: null,
    });

    setGameState(newState);
    setCardHasBeenPlayed(false);
  };

  // Handler per iniziare un nuovo gioco
  const handleStartNewGame = () => {
    playSound("gameStart");
    setGameState(startNewGameLogic(gameState.victoryPoints));
    setCardHasBeenPlayed(false);
  };

  // Handler per aprire il menu
  const handleOpenMenu = () => {
    playSound("menuOpen");
    setShowHelpSheet(true);
  };
  // Handler per confermare l'uscita
  const confirmExit = () => {
    // Rileva abbandono e aggiorna statistiche se la partita non è finita naturalmente
    if (gameState.gamePhase !== "gameOver") {
      // Non bloccare l'uscita, aggiorna in background
      updateStatsAfterAbandonment({
        difficulty,
        playerTeam: 0,
        currentScore: [gameState.gameScore[0], gameState.gameScore[1]],
      })
        .then(() => {
          console.log("📊 Statistiche abbandono aggiornate nel profilo attivo");
        })
        .catch((error) => {
          console.error(
            "❌ Errore nell'aggiornamento statistiche abbandono:",
            error
          );
        });
    }

    cleanupGame(
      gameState,
      setShowExitDialog,
      setShowHelpSheet,
      setShowingLastTrick,
      setLastPlayedCard,
      setCardHasBeenPlayed,
      setActionAnnouncement,
      setTrumpAnnouncement
    );
    navigate("/", { replace: true, state: { fromGame: true } });
  };

  // Handler per tornare al menu
  const handleReturnToMenu = () => {
    confirmExit(); // Directly return to menu without confirmation
  };

  return {
    handleSelectTrump,
    handleAnnounceAction,
    handlePlayCard,
    handleDeclareWin,
    handleStartNewRound,
    handleStartNewGame,
    handleOpenMenu,
    confirmExit,
    handleReturnToMenu,
  };
};

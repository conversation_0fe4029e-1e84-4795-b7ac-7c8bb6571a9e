/**
 * Utility per la gestione delle date nel gioco
 */

/**
 * Ottiene la data attuale come stringa formattata
 */
export const getCurrentDateString = (): string => {
  const now = new Date();
  return now.toLocaleString("it-IT", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

/**
 * Formatta una data per la visualizzazione
 */
export const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString("it-IT", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  } catch {
    return dateString;
  }
};

/**
 * Formatta la durata in secondi in formato leggibile
 */
export const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  if (minutes === 0) {
    return `${remainingSeconds}s`;
  }

  if (minutes < 60) {
    return `${minutes}m ${remainingSeconds}s`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return `${hours}h ${remainingMinutes}m`;
};

/**
 * Calcola quanto tempo fa è avvenuto un evento
 */
export const getTimeAgo = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMinutes < 1) {
      return "Appena ora";
    } else if (diffMinutes < 60) {
      return `${diffMinutes} ${diffMinutes === 1 ? "minuto" : "minuti"} fa`;
    } else if (diffHours < 24) {
      return `${diffHours} ${diffHours === 1 ? "ora" : "ore"} fa`;
    } else if (diffDays < 7) {
      return `${diffDays} ${diffDays === 1 ? "giorno" : "giorni"} fa`;
    } else {
      return formatDate(dateString);
    }
  } catch {
    return "Data non valida";
  }
};

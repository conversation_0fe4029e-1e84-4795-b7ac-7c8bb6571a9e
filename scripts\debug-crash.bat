@echo off
REM Trova ADB automaticamente
set "ADB_CMD="
where adb >nul 2>&1 && set "ADB_CMD=adb"
if not defined ADB_CMD (
    if exist "%LOCALAPPDATA%\Android\Sdk\platform-tools\adb.exe" (
        set "ADB_CMD=%LOCALAPPDATA%\Android\Sdk\platform-tools\adb.exe"
    )
)
if not defined ADB_CMD (
    for /f "tokens=*" %%i in ('dir /s /b "C:\*adb.exe" 2^>nul ^| findstr platform-tools ^| head -1') do set "ADB_CMD=%%i"
)

if not defined ADB_CMD (
    echo ❌ ADB non trovato! Installa Android SDK Platform Tools
    echo winget install Google.PlatformTools
    pause
    exit /b 1
)

echo 📋 Monitor Crash Android - Maraffa Romagnola
echo ============================================

REM Verifica dispositivo
"%ADB_CMD%" devices | findstr "device" >nul
if errorlevel 1 (
    echo ❌ Dispositivo Android non trovato!
    pause
    exit /b 1
)
echo ✅ Dispositivo connesso

REM Verifica app installata
"%ADB_CMD%" shell pm list packages | findstr com.eliazavatta.maraffa >nul
if errorlevel 1 (
    echo ❌ App non installata! Usa build-and-install.bat prima
    pause
    exit /b 1
)
echo ✅ App installata

echo.
echo 🔍 ISTRUZIONI:
echo 1. Apri l'app manualmente sul telefono
echo 2. Se crasha, vedrai gli errori qui sotto
echo 3. Premi Ctrl+C per fermare il monitoraggio
echo.

REM Pulisci log precedenti
"%ADB_CMD%" logcat -c

REM Monitora crash in tempo reale
echo 📱 Monitoraggio attivo...
"%ADB_CMD%" logcat -v time | findstr /i "FATAL ERROR AndroidRuntime com.eliazavatta.maraffa Exception"

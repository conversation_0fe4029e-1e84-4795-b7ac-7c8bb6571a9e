import { But<PERSON> } from "@/components/ui/button";
import { Cloud, LogOut } from "lucide-react";

interface AuthenticationSectionProps {
  userStateLoading: boolean;
  authLoading: boolean;
  authUser: any;
  isLoggedIn: boolean;
  isSyncing: boolean;
  onLogout: () => void;
  onGoogleLogin: () => void;
  onFacebookLogin: () => void;
}

const AuthenticationSection = ({
  userStateLoading,
  authLoading,
  authUser,
  isLoggedIn,
  isSyncing,
  onLogout,
  onGoogleLogin,
  onFacebookLogin,
}: AuthenticationSectionProps) => {
  // DEBUG: Log per capire lo stato
  console.log("🔍 AuthenticationSection state:", {
    isLoggedIn,
    authUser: !!authUser,
    userStateLoading,
    authLoading,
  });

  if (userStateLoading || authLoading) {
    // Stato di caricamento durante l'inizializzazione
    return (
      <div className="flex items-center gap-2">
        <div className="w-4 h-4 border-2 border-amber-300 border-t-amber-600 rounded-full animate-spin"></div>
        <span className="text-sm text-amber-700">
          Caricamento stato account...
        </span>
      </div>
    );
  }

  // 🔒 SE LOGGATO: SOLO LOGOUT, NESSUN BOTTONE GOOGLE!
  // Controllo SUPER rigoroso - ENTRAMBI devono essere veri
  if (isLoggedIn === true && authUser && authUser.id) {
    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Cloud className="h-4 w-4 text-green-500" />
            <span className="text-xs text-green-700 font-medium">
              Account connesso
            </span>
            {isSyncing && (
              <div className="w-3 h-3 border border-green-300 border-t-green-600 rounded-full animate-spin"></div>
            )}
          </div>
          <div className="flex gap-2">
            <Button
              onClick={onLogout}
              disabled={authLoading}
              size="sm"
              variant="outline"
              className="border-red-300 text-red-700 hover:bg-red-50 h-8 text-xs"
            >
              <LogOut className="h-3 w-3 mr-1" />
              Logout
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // 🔒 SE NON LOGGATO: SOLO ALLORA MOSTRA GOOGLE
  // Se NON è loggato O non ha authUser O authUser non ha ID
  if (isLoggedIn !== true || !authUser || !authUser.id) {
    return (
      <div className="space-y-2">
        <p className="text-xs text-amber-700 mb-2">
          Accedi per multiplayer e sincronizzazione
        </p>
        <div className="flex gap-2">
          <Button
            onClick={onGoogleLogin}
            disabled={authLoading}
            size="sm"
            className="flex-1 bg-red-600 hover:bg-red-700 text-white h-8 text-xs"
          >
            <svg className="w-3 h-3 mr-1" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="currentColor"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="currentColor"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="currentColor"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            Google
          </Button>
          <Button
            onClick={onFacebookLogin}
            disabled={authLoading}
            size="sm"
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white h-8 text-xs"
          >
            <svg className="w-3 h-3 mr-1" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
              />
            </svg>
            Facebook
          </Button>
        </div>
      </div>
    );
  }

  // Fallback per stati inconsistenti - non dovrebbe mai accadere
  return (
    <div className="text-center py-2">
      <span className="text-xs text-gray-500">
        Stato autenticazione non valido
      </span>
    </div>
  );
};

export default AuthenticationSection;

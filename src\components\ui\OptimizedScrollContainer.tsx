import React, { ReactNode, useRef, useEffect, CSSProperties } from "react";
import { useAndroidScrollOptimization } from "@/hooks/useAndroidScrollOptimization";

interface OptimizedScrollContainerProps {
  children: ReactNode;
  className?: string;
  onLoadMore?: () => void;
  hasMore?: boolean;
  loading?: boolean;
  threshold?: number;
}

/**
 * Contenitore di scroll ottimizzato per Android
 * Implementa lazy loading, debouncing e gestione della memoria
 */
const OptimizedScrollContainer: React.FC<OptimizedScrollContainerProps> = ({
  children,
  className = "",
  onLoadMore,
  hasMore = false,
  loading = false,
  threshold = 200,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  // Usa l'hook di ottimizzazione per Android
  const { shouldOptimize } = useAndroidScrollOptimization({
    threshold,
    onThresholdReached: () => {
      if (hasMore && !loading && onLoadMore) {
        onLoadMore();
      }
    },
  });

  // Gestione del scroll ottimizzata
  useEffect(() => {
    if (!shouldOptimize || !containerRef.current) return;

    const container = containerRef.current;
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          ticking = false;
        });
        ticking = true;
      }
    };

    container.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      container.removeEventListener("scroll", handleScroll);
    };
  }, [shouldOptimize, threshold]);

  // Stili ottimizzati per Android
  const containerStyles: CSSProperties = shouldOptimize
    ? {
        // Ottimizzazioni per Android WebView
        WebkitOverflowScrolling: "touch" as const,
        transform: "translateZ(0)", // Forza l'accelerazione hardware
        willChange: "scroll-position",
      }
    : {};

  return (
    <div
      ref={containerRef}
      className={`${className} ${
        shouldOptimize ? "android-optimized-scroll" : ""
      }`}
      style={containerStyles}
    >
      {children}

      {/* Indicatore di caricamento per infinite scroll */}
      {loading && hasMore && (
        <div className="flex justify-center items-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-amber-600"></div>
          <span className="ml-2 text-sm text-amber-700">Caricamento...</span>
        </div>
      )}
    </div>
  );
};

export default OptimizedScrollContainer;

import React from "react";
import TeamScore from "@/components/TeamScore";
import { cn } from "@/lib/utils";
import { suitImages, suitNames } from "@/utils/game/cardUtils";
import { GameState } from "@/utils/game/gameLogic";

type GameScoresProps = {
  gameState: GameState;
  currentPoints: number[];
  isMobile: boolean;
  isOnlineMode: boolean;
};

const GameScores: React.FC<GameScoresProps> = ({
  gameState,
  currentPoints,
  isMobile,
  isOnlineMode,
}) => {
  // Formatta i punteggi per renderli più leggibili
  const formatMessage = (message: string) => {
    // Se il messaggio contiene il punteggio attuale, lo formatta meglio
    if (message.includes("Punteggio attuale:")) {
      return message.split("Punteggio attuale:")[0].trim();
    }
    return message;
  };

  // Ottiene i segnalini figura per ciascuna squadra dal nuovo sistema
  const team0Tokens = gameState.teams[0].currentRoundFigures || 0;
  const team1Tokens = gameState.teams[1].currentRoundFigures || 0;
  return (
    <div
      className={cn(
        "flex items-center gap-2",
        isMobile && "max-w-full overflow-hidden game-scores-container"
      )}
    >
      {!isMobile && (
        <div className="text-sm parchment border border-romagna-gold/30 p-2 rounded-lg romagna-shadow text-maraffa-text font-medium ml-2 flex items-center">
          <span className="font-medium text-maraffa-primary">
            {formatMessage(gameState.message)}
          </span>
        </div>
      )}{" "}
      <div
        className={cn("flex items-start", isMobile && "flex-1 justify-center")}
      >
        {" "}
        <TeamScore
          team={0}
          score={gameState.gameScore[0]}
          className={cn(
            isMobile ? "h-12 flex-1 max-w-[160px] team-score-container" : "h-12"
          )}
          roundScore={gameState.teams[0].currentRoundPoints}
          figureTokens={team0Tokens} // Passa i segnalini figura
          isOnlineMode={isOnlineMode}
        />
        <TeamScore
          team={1}
          score={gameState.gameScore[1]}
          className={cn(
            isMobile ? "h-12 flex-1 max-w-[160px] team-score-container" : "h-12"
          )}
          roundScore={gameState.teams[1].currentRoundPoints}
          figureTokens={team1Tokens} // Passa i segnalini figura
          isOnlineMode={isOnlineMode}
        />
      </div>
    </div>
  );
};

export default GameScores;

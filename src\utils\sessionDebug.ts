/**
 * 🔍 UTILITÀ DI DEBUG PER SESSIONI
 * Fornisce strumenti per diagnosticare problemi di sessione
 */

import { supabase, sessionManager } from "@/integrations/supabase/client";

export interface SessionDiagnostic {
  timestamp: Date;
  sessionExists: boolean;
  sessionValid: boolean;
  tokenExpired: boolean;
  minutesUntilExpiry: number | null;
  connectionHealthy: boolean;
  lastError: string | null;
  refreshAttempts: number;
}

class SessionDebugger {
  private static instance: SessionDebugger;
  private diagnostics: SessionDiagnostic[] = [];
  private readonly MAX_DIAGNOSTICS = 50;

  static getInstance(): SessionDebugger {
    if (!SessionDebugger.instance) {
      SessionDebugger.instance = new SessionDebugger();
    }
    return SessionDebugger.instance;
  }

  // Esegue una diagnosi completa della sessione
  async performDiagnostic(): Promise<SessionDiagnostic> {
    const diagnostic: SessionDiagnostic = {
      timestamp: new Date(),
      sessionExists: false,
      sessionValid: false,
      tokenExpired: false,
      minutesUntilExpiry: null,
      connectionHealthy: false,
      lastError: null,
      refreshAttempts: 0,
    };

    try {
      // Test connessione
      try {
        const response = await fetch('https://www.google.com/favicon.ico', {
          method: 'HEAD',
          mode: 'no-cors',
          signal: AbortSignal.timeout(5000)
        });
        diagnostic.connectionHealthy = response.ok || response.type === 'opaque';
      } catch {
        diagnostic.connectionHealthy = false;
      }

      // Test sessione
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          diagnostic.lastError = error.message;
        }

        if (session) {
          diagnostic.sessionExists = true;
          diagnostic.sessionValid = !!session.access_token;

          if (session.expires_at) {
            const expiresAt = session.expires_at * 1000;
            const now = Date.now();
            diagnostic.tokenExpired = expiresAt <= now;
            diagnostic.minutesUntilExpiry = Math.round((expiresAt - now) / 60000);
          }
        }
      } catch (sessionError) {
        diagnostic.lastError = sessionError instanceof Error ? sessionError.message : 'Unknown session error';
      }

      // Test SessionManager
      try {
        await sessionManager.ensureValidSession();
      } catch (managerError) {
        if (!diagnostic.lastError) {
          diagnostic.lastError = managerError instanceof Error ? managerError.message : 'SessionManager error';
        }
        diagnostic.refreshAttempts++;
      }

    } catch (error) {
      diagnostic.lastError = error instanceof Error ? error.message : 'Unknown diagnostic error';
    }

    // Salva diagnosi
    this.diagnostics.push(diagnostic);
    if (this.diagnostics.length > this.MAX_DIAGNOSTICS) {
      this.diagnostics = this.diagnostics.slice(-this.MAX_DIAGNOSTICS);
    }

    return diagnostic;
  }

  // Ottieni tutte le diagnosi
  getDiagnostics(): SessionDiagnostic[] {
    return [...this.diagnostics];
  }

  // Ottieni l'ultima diagnosi
  getLastDiagnostic(): SessionDiagnostic | null {
    return this.diagnostics.length > 0 ? this.diagnostics[this.diagnostics.length - 1] : null;
  }

  // Ottieni un riassunto dello stato
  getSummary(): {
    totalDiagnostics: number;
    recentFailures: number;
    averageMinutesUntilExpiry: number;
    connectionIssues: number;
    lastError: string | null;
  } {
    const recent = this.diagnostics.slice(-10); // Ultimi 10
    
    return {
      totalDiagnostics: this.diagnostics.length,
      recentFailures: recent.filter(d => !d.sessionValid).length,
      averageMinutesUntilExpiry: recent
        .filter(d => d.minutesUntilExpiry !== null)
        .reduce((sum, d) => sum + (d.minutesUntilExpiry || 0), 0) / recent.length || 0,
      connectionIssues: recent.filter(d => !d.connectionHealthy).length,
      lastError: this.getLastDiagnostic()?.lastError || null,
    };
  }

  // Pulisci diagnosi
  clearDiagnostics(): void {
    this.diagnostics = [];
  }

  // Log dettagliato per debug
  logDetailedStatus(): void {
    const last = this.getLastDiagnostic();
    const summary = this.getSummary();

    console.group("🔍 SESSION DEBUG STATUS");
    
    if (last) {
      console.log("📊 Ultima diagnosi:", {
        timestamp: last.timestamp.toLocaleTimeString(),
        sessionValid: last.sessionValid,
        tokenExpired: last.tokenExpired,
        minutesUntilExpiry: last.minutesUntilExpiry,
        connectionHealthy: last.connectionHealthy,
        lastError: last.lastError,
      });
    }

    console.log("📈 Riassunto:", summary);
    
    console.groupEnd();
  }
}

// Esporta istanza singleton
export const sessionDebugger = SessionDebugger.getInstance();

// Funzioni helper
export const debugSession = async (): Promise<SessionDiagnostic> => {
  return await sessionDebugger.performDiagnostic();
};

export const logSessionStatus = (): void => {
  sessionDebugger.logDetailedStatus();
};

// Funzione per test rapido da console
export const quickSessionDebug = async (): Promise<void> => {
  console.log("🔍 Avvio debug sessione...");
  const diagnostic = await debugSession();
  
  console.log("🔍 Risultato debug:", {
    sessionOK: diagnostic.sessionValid,
    connectionOK: diagnostic.connectionHealthy,
    minutesLeft: diagnostic.minutesUntilExpiry,
    error: diagnostic.lastError,
  });
  
  if (!diagnostic.sessionValid || !diagnostic.connectionHealthy) {
    console.warn("⚠️ Problemi rilevati! Esegui logSessionStatus() per dettagli");
  }
};

// Rendi disponibili globalmente per debug
if (typeof window !== 'undefined') {
  (window as any).debugSession = quickSessionDebug;
  (window as any).logSessionStatus = logSessionStatus;
  (window as any).sessionDebugger = sessionDebugger;
}

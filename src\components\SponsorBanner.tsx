import React, { useState, useEffect, useRef, useCallback } from "react";
import { Heart, ChevronLeft, ChevronRight, Crown } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useAudio } from "@/hooks/useAudio";

const SponsorBanner: React.FC = () => {
  const navigate = useNavigate();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [lastInteraction, setLastInteraction] = useState(Date.now());
  const [isTransitioning, setIsTransitioning] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const startX = useRef<number>(0);
  const isDragging = useRef<boolean>(false);
  const { playSound } = useAudio();
  const bannerItems = [
    {
      id: 1,
      title: "Supporta & Collabora",
      description: "Fai una donazione o proponi una collaborazione",
      icon: Heart,
      color: "bg-gradient-to-r from-pink-500 to-rose-600",
      textColor: "text-white",
    },
    {
      id: 3,
      title: "Diventa Premium",
      description: "Sblocca tutte le personalizzazioni e rimuovi le pubblicità",
      icon: Crown,
      color: "bg-gradient-to-r from-purple-500 to-indigo-600",
      textColor: "text-white",
    },
  ];

  // Per lo scroll infinito, creiamo un array esteso con duplicati
  const extendedItems = [...bannerItems, ...bannerItems, ...bannerItems];
  const totalItems = bannerItems.length;

  // Funzione per normalizzare l'indice per lo scroll infinito
  const normalizeIndex = useCallback(
    (index: number) => {
      return ((index % totalItems) + totalItems) % totalItems;
    },
    [totalItems]
  );

  // Gestione touch events
  const handleTouchStart = (e: React.TouchEvent) => {
    startX.current = e.touches[0].clientX;
    isDragging.current = true;
    setLastInteraction(Date.now());
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging.current) return;
    e.preventDefault(); // Previeni lo scroll della pagina
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (!isDragging.current) return;

    const endX = e.changedTouches[0].clientX;
    const diffX = startX.current - endX;
    const threshold = 50; // Soglia minima per lo swipe

    isDragging.current = false;

    if (Math.abs(diffX) > threshold) {
      if (diffX > 0) {
        // Swipe left - slide successivo
        nextSlide();
      } else {
        // Swipe right - slide precedente
        prevSlide();
      }
    }
  };

  // Gestione mouse events per desktop
  const handleMouseDown = (e: React.MouseEvent) => {
    startX.current = e.clientX;
    isDragging.current = true;
    setLastInteraction(Date.now());
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging.current) return;
    e.preventDefault();
  };

  const handleMouseUp = (e: React.MouseEvent) => {
    if (!isDragging.current) return;

    const endX = e.clientX;
    const diffX = startX.current - endX;
    const threshold = 50;

    isDragging.current = false;

    if (Math.abs(diffX) > threshold) {
      if (diffX > 0) {
        nextSlide();
      } else {
        prevSlide();
      }
    }
  }; // Auto-scroll ogni 5 secondi, con reset quando c'è interazione manuale o modali aperte
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now(); // Procedi con l'auto-scroll solo se sono passati almeno 5 secondi dall'ultima interazione
      if (now - lastInteraction >= 5000 && !isDragging.current) {
        // Usa la stessa logica di nextSlide per mantenere animazione e scroll infinito
        setIsTransitioning(true);
        setCurrentSlide((prev) => prev + 1);

        // Reset per scroll infinito (stesso di nextSlide)
        setTimeout(() => {
          setCurrentSlide((prev) => {
            if (prev >= totalItems * 2) {
              setIsTransitioning(false);
              return totalItems;
            }
            setIsTransitioning(false);
            return prev;
          });
        }, 500);
      }
    }, 5000);
    return () => clearInterval(interval);
  }, [lastInteraction, totalItems]);

  // Inizializza con il secondo set di items per permettere scroll infinito
  useEffect(() => {
    setCurrentSlide(totalItems); // Inizia dal secondo set
  }, [totalItems]);

  const nextSlide = () => {
    playSound("buttonClick");
    setIsTransitioning(true);
    setCurrentSlide((prev) => prev + 1);
    setLastInteraction(Date.now());

    // Reset per scroll infinito
    setTimeout(() => {
      setCurrentSlide((prev) => {
        if (prev >= totalItems * 2) {
          setIsTransitioning(false);
          return totalItems;
        }
        setIsTransitioning(false);
        return prev;
      });
    }, 500);
  };

  const prevSlide = () => {
    playSound("buttonClick");
    setIsTransitioning(true);
    setCurrentSlide((prev) => prev - 1);
    setLastInteraction(Date.now());

    // Reset per scroll infinito
    setTimeout(() => {
      setCurrentSlide((prev) => {
        if (prev < totalItems) {
          setIsTransitioning(false);
          return totalItems * 2 - 1;
        }
        setIsTransitioning(false);
        return prev;
      });
    }, 500);
  };

  const goToSlide = (index: number) => {
    playSound("buttonClick");
    setCurrentSlide(totalItems + index); // Vai al secondo set + indice
    setLastInteraction(Date.now());
  };
  const handleCardClick = (item: (typeof bannerItems)[0]) => {
    playSound("buttonClick");

    // Azioni specifiche per ogni banner
    if (item.id === 1) {
      // Banner "Supporta & Contatta" - naviga alla sezione Elia Zavatta nella pagina account
      navigate("/account#elia-zavatta-section");
    } else if (item.id === 3) {
      // Banner "Diventa Premium" - naviga al negozio
      navigate("/shop");
    }
  };
  return (
    <>
      {" "}
      <div
        className="relative w-full h-24 overflow-hidden rounded-lg shadow-lg select-none cursor-grab active:cursor-grabbing pointer-events-auto"
        ref={containerRef}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={() => (isDragging.current = false)}
      >
        {/* Contenitore slides */}
        <div
          className={`flex h-full ${
            isTransitioning
              ? "transition-transform duration-500 ease-in-out"
              : ""
          }`}
          style={{
            transform: `translateX(-${
              currentSlide * (100 / extendedItems.length)
            }%)`,
            width: `${extendedItems.length * 100}%`,
          }}
        >
          {extendedItems.map((item, index) => {
            const IconComponent = item.icon;
            return (
              <div
                key={`${item.id}-${index}`}
                className={`h-full ${item.color} flex items-center justify-center px-4 relative overflow-hidden transition-all duration-300 cursor-pointer hover:brightness-110 active:scale-[0.98] pointer-events-auto`}
                style={{ width: `${100 / extendedItems.length}%` }}
                onClick={() => handleCardClick(item)}
              >
                {/* Contenuto */}
                <div className="flex items-center space-x-3 flex-1">
                  <div className="bg-white/20 p-2 rounded-full">
                    <IconComponent className={`h-5 w-5 ${item.textColor}`} />
                  </div>
                  <div className="flex-1">
                    <h3 className={`font-bold text-sm ${item.textColor}`}>
                      {item.title}
                    </h3>
                    <p className={`text-xs ${item.textColor} opacity-90`}>
                      {item.description}
                    </p>
                  </div>
                </div>

                {/* Elemento decorativo */}
                <div className="absolute -right-8 -top-8 w-16 h-16 bg-white/10 rounded-full" />
                <div className="absolute -right-4 -bottom-4 w-12 h-12 bg-white/5 rounded-full" />
              </div>
            );
          })}
        </div>
        {/* Controlli di navigazione */}{" "}
        <button
          onClick={prevSlide}
          className="absolute left-1 top-1/2 -translate-y-1/2 p-1 rounded-full transition-all duration-300 z-10 bg-black/20 hover:bg-black/30 pointer-events-auto"
        >
          <ChevronLeft className="h-4 w-4 text-white" />
        </button>
        <button
          onClick={nextSlide}
          className="absolute right-1 top-1/2 -translate-y-1/2 p-1 rounded-full transition-all duration-300 z-10 bg-black/20 hover:bg-black/30 pointer-events-auto"
        >
          <ChevronRight className="h-4 w-4 text-white" />
        </button>{" "}
        {/* Indicatori dots */}
        <div className="absolute bottom-1 left-1/2 -translate-x-1/2 flex space-x-1">
          {bannerItems.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                normalizeIndex(currentSlide) === index
                  ? "bg-white scale-125"
                  : "bg-white/50 hover:bg-white/70"
              } pointer-events-auto`}
            />
          ))}
        </div>{" "}
      </div>
    </>
  );
};

export default SponsorBanner;

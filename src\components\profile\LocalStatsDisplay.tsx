import React from "react";
import { Trophy, Target, Clock, TrendingUp, Medal, Zap } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { PlayerStats } from "@/services/localStatsService";
import { getTimeAgo } from "../../utils/dateUtils";

interface LocalStatsDisplayProps {
  stats: PlayerStats;
  className?: string;
}

const LocalStatsDisplay: React.FC<LocalStatsDisplayProps> = ({
  stats,
  className = "",
}) => {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "easy":
        return "bg-green-100 text-green-800 border-green-300";
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-300";
      case "hard":
        return "bg-red-100 text-red-800 border-red-300";
      default:
        return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  const getDifficultyIcon = (difficulty: string) => {
    switch (difficulty) {
      case "easy":
        return "🟢";
      case "medium":
        return "🟡";
      case "hard":
        return "🔴";
      default:
        return "⚪";
    }
  };
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Statistiche Principali */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-3">
        {/* Vittorie */}
        {/* Vittorie */}
        <Card className="border-2 border-green-800/30 shadow-md bg-white/80 relative flex flex-col justify-between py-3 px-4 min-h-[88px]">
          <img
            src="/images/icons/vittorie icon.png"
            alt="Vittorie"
            className="h-10 w-10 absolute top-2 right-2 drop-shadow-md opacity-90"
          />
          <div className="flex flex-col items-start justify-center h-full">
            <div className="text-3xl font-extrabold text-green-800 leading-tight">
              {stats.gamesWon}
            </div>
            <div className="text-xs text-green-700 mt-1 font-medium">
              Vittorie
            </div>
          </div>
        </Card>
        {/* Partite Totali */}
        <Card className="border-2 border-orange-800/30 shadow-md bg-white/80 relative flex flex-col justify-between py-3 px-4 min-h-[88px]">
          <img
            src="/images/icons/partite num icon.png"
            alt="Partite Totali"
            className="h-10 w-10 absolute top-2 right-2 drop-shadow-md opacity-90"
          />
          <div className="flex flex-col items-start justify-center h-full">
            <div className="text-3xl font-extrabold text-orange-800 leading-tight">
              {stats.totalGames}
            </div>
            <div className="text-xs text-orange-700 mt-1 font-medium">
              Partite Totali
            </div>
          </div>
        </Card>
        {/* Tasso di Vittoria */}
        <Card className="border-2 border-blue-800/30 shadow-md bg-white/80 relative flex flex-col justify-between py-3 px-4 min-h-[88px]">
          <img
            src="/images/icons/vittorie perc icon.png"
            alt="Tasso di Vittoria"
            className="h-10 w-10 absolute top-2 right-2 drop-shadow-md opacity-90"
          />
          <div className="flex flex-col items-start justify-center h-full">
            <div className="text-3xl font-extrabold text-blue-800 leading-tight">
              {/* 🔒 CALCOLO FISSO E CORRETTO - NON CAMBIA MAI! */}
              {stats.totalGames > 0
                ? Math.round((stats.gamesWon / stats.totalGames) * 100)
                : 0}
              %
            </div>
            <div className="text-xs text-blue-700 mt-1 font-medium">
              Tasso di Vittoria
            </div>
          </div>
        </Card>
        {/* Maraffe */}
        <Card className="border-2 border-purple-800/30 shadow-md bg-white/80 relative flex flex-col justify-between py-3 px-4 min-h-[88px]">
          <img
            src="/images/icons/maraffa icon.png"
            alt="Maraffe"
            className="h-10 w-10 absolute top-2 right-2 drop-shadow-md opacity-90"
          />
          <div className="flex flex-col items-start justify-center h-full">
            <div className="text-3xl font-extrabold text-purple-800 leading-tight">
              {stats.maraffeMade}
            </div>
            <div className="text-xs text-purple-700 mt-1 font-medium">
              Maraffe
            </div>
          </div>
        </Card>
      </div>

      {/* Ultime Partite */}
      {stats.recentGames.length > 0 && (
        <Card className="border-2 border-gray-800/30 shadow-md bg-gray-50/80">
          <CardHeader className="p-3">
            <CardTitle className="text-base text-gray-900 flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Ultime 5 Partite
            </CardTitle>
          </CardHeader>{" "}
          <CardContent className="p-3 pt-0">
            <div className="grid grid-cols-5 gap-2">
              {stats.recentGames.slice(0, 5).map((game, index) => (
                <div
                  key={game.id || index}
                  className={`aspect-square rounded-md flex flex-col items-center justify-center ${
                    game.result === "Vittoria"
                      ? "bg-green-100 border border-green-300"
                      : "bg-red-100 border border-red-300"
                  }`}
                >
                  <div
                    className={`text-xs font-bold ${
                      game.result === "Vittoria"
                        ? "text-green-700"
                        : "text-red-700"
                    }`}
                  >
                    {game.result === "Vittoria" ? "V" : "S"}
                  </div>
                  <div className="text-xs mt-0.5 font-mono">
                    {game.score === "Abbandonata" ? "ABB" : game.score}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default LocalStatsDisplay;

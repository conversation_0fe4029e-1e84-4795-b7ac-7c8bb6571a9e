import { useToast } from "./use-toast";

/**
 * Hook personalizzato che estende useToast con metodi di convenienza
 * per avere la stessa API e UX dei toast di Account.tsx
 */
export function useCustomToast() {
  const { toast: originalToast, ...rest } = useToast();
  const showToast = (
    message: string,
    type: "success" | "error" | "info" = "info"
  ) => {
    const variant = type === "error" ? "destructive" : "default";

    originalToast({
      description: message,
      variant,
      duration: 2000,
      // Passiamo il tipo nel titolo per identificarlo nell'AdvancedToastProvider
      title: `__toast_type_${type}__`,
    });
  };

  const toast = {
    success: (message: string) => showToast(message, "success"),
    error: (message: string) => showToast(message, "error"),
    info: (message: string) => showToast(message, "info"),
  };

  return {
    ...rest,
    toast: originalToast,
    showToast,
    success: toast.success,
    error: toast.error,
    info: toast.info,
  };
}

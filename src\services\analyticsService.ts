/**
 * 📊 SERVIZIO ANALYTICS
 * Gestisce il tracking degli eventi per Firebase Analytics e AdMob
 */

import { Capacitor } from "@capacitor/core";
import { env } from "@/config/environment";
import AppTrackingService from "./appTrackingService";

// Import condizionale di Firebase Analytics per evitare errori su web
let FirebaseAnalytics:
  | typeof import("@capacitor-community/firebase-analytics").FirebaseAnalytics
  | null = null;

// Inizializza l'import solo su piattaforme native
async function loadFirebaseAnalytics() {
  if (Capacitor.isNativePlatform() && !FirebaseAnalytics) {
    try {
      const analyticsModule = await import(
        "@capacitor-community/firebase-analytics"
      );
      FirebaseAnalytics = analyticsModule.FirebaseAnalytics;
      console.log("✅ Firebase Analytics caricato");
    } catch (error) {
      console.warn("⚠️ Firebase Analytics non disponibile:", error);
    }
  }
}

class AnalyticsService {
  private static instance: AnalyticsService;
  private isInitialized = false;

  static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  /**
   * Inizializza Firebase Analytics
   */
  async initialize(): Promise<void> {
    if (this.isInitialized || !env.features.enableAnalytics) {
      console.log(
        "Analytics: Inizializzazione saltata (già inizializzato o disabilitato)"
      );
      return;
    }

    try {
      console.log("📊 Inizializzazione Analytics...");

      // 🍎 iOS: Request tracking permission first
      const trackingAllowed =
        await AppTrackingService.requestTrackingPermission();
      console.log("📱 Tracking permission:", trackingAllowed);

      await loadFirebaseAnalytics();

      if (!FirebaseAnalytics) {
        console.warn("Analytics: Firebase Analytics non disponibile");
        return;
      }

      // Enable data collection only if tracking is allowed
      await FirebaseAnalytics.setEnabled({ enabled: trackingAllowed });

      // Imposta proprietà utente di base
      await this.setUserProperties();

      this.isInitialized = true;
      console.log("✅ Analytics inizializzato con successo");

      // Traccia l'avvio dell'app
      await this.trackEvent("app_start", {
        platform: Capacitor.getPlatform(),
        app_version: env.app.version,
      });
    } catch (error) {
      console.error("❌ Errore nell'inizializzazione di Analytics:", error);
      this.isInitialized = false;
    }
  }

  /**
   * Imposta le proprietà utente per Analytics
   */
  private async setUserProperties(): Promise<void> {
    if (!FirebaseAnalytics || !this.isInitialized) return;

    try {
      await FirebaseAnalytics.setUserProperty({
        name: "platform",
        value: Capacitor.getPlatform(),
      });

      await FirebaseAnalytics.setUserProperty({
        name: "app_version",
        value: env.app.version,
      });
    } catch (error) {
      console.warn("⚠️ Errore impostazione proprietà utente:", error);
    }
  }

  /**
   * Traccia un evento personalizzato
   */
  async trackEvent(
    eventName: string,
    parameters: Record<string, any> = {}
  ): Promise<void> {
    if (
      !env.features.enableAnalytics ||
      !FirebaseAnalytics ||
      !this.isInitialized
    ) {
      console.log(
        `📊 Analytics disabilitato - evento: ${eventName}`,
        parameters
      );
      return;
    }

    try {
      await FirebaseAnalytics.logEvent({
        name: eventName,
        parameters,
      });
      console.log(`📊 Evento tracciato: ${eventName}`, parameters);
    } catch (error) {
      console.warn(`⚠️ Errore tracking evento ${eventName}:`, error);
    }
  }

  /**
   * Traccia l'inizio di una partita
   */
  async trackGameStart(
    difficulty: string,
    victoryPoints: number
  ): Promise<void> {
    await this.trackEvent("game_start", {
      difficulty,
      victory_points: victoryPoints,
      timestamp: Date.now(),
    });
  }

  /**
   * Traccia la fine di una partita
   */
  async trackGameEnd(
    difficulty: string,
    isWinner: boolean,
    duration: number,
    finalScore: [number, number],
    maraffeMade: number
  ): Promise<void> {
    await this.trackEvent("game_end", {
      difficulty,
      is_winner: isWinner,
      duration_seconds: Math.round(duration / 1000),
      player_score: finalScore[0],
      opponent_score: finalScore[1],
      maraffe_made: maraffeMade,
      timestamp: Date.now(),
    });
  }

  /**
   * Traccia il livello raggiunto
   */
  async trackLevelUp(newLevel: number, xpGained: number): Promise<void> {
    await this.trackEvent("level_up", {
      level: newLevel,
      xp_gained: xpGained,
      timestamp: Date.now(),
    });
  }

  /**
   * Traccia la visualizzazione di un annuncio
   */
  async trackAdImpression(
    adType: "banner" | "interstitial" | "rewarded"
  ): Promise<void> {
    await this.trackEvent("ad_impression", {
      ad_type: adType,
      timestamp: Date.now(),
    });
  }

  /**
   * Traccia il click su un annuncio
   */
  async trackAdClick(
    adType: "banner" | "interstitial" | "rewarded"
  ): Promise<void> {
    await this.trackEvent("ad_click", {
      ad_type: adType,
      timestamp: Date.now(),
    });
  }

  /**
   * Traccia la richiesta di recensione
   */
  async trackReviewRequest(
    action: "shown" | "accepted" | "declined"
  ): Promise<void> {
    await this.trackEvent("review_request", {
      action,
      timestamp: Date.now(),
    });
  }

  /**
   * Traccia l'acquisto premium
   */
  async trackPurchase(
    productId: string,
    price: number,
    currency: string
  ): Promise<void> {
    await this.trackEvent("purchase", {
      product_id: productId,
      price,
      currency,
      timestamp: Date.now(),
    });
  }

  /**
   * Traccia la navigazione tra pagine
   */
  async trackScreenView(screenName: string): Promise<void> {
    if (!FirebaseAnalytics || !this.isInitialized) return;

    try {
      await FirebaseAnalytics.setCurrentScreen({
        screenName,
        screenClassOverride: screenName,
      });
      console.log(`📊 Screen view: ${screenName}`);
    } catch (error) {
      console.warn(`⚠️ Errore tracking screen view ${screenName}:`, error);
    }
  }

  /**
   * Imposta l'ID utente per il tracking
   */
  async setUserId(userId: string): Promise<void> {
    if (!FirebaseAnalytics || !this.isInitialized) return;

    try {
      await FirebaseAnalytics.setUserId({ userId });
      console.log(`📊 User ID impostato: ${userId}`);
    } catch (error) {
      console.warn("⚠️ Errore impostazione user ID:", error);
    }
  }

  /**
   * Verifica se Analytics è inizializzato
   */
  isAnalyticsInitialized(): boolean {
    return this.isInitialized;
  }
}

export default AnalyticsService.getInstance();

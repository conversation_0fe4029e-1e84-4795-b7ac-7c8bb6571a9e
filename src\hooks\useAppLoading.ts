import { useState, useEffect } from "react";
import { useAuth } from "@/context/auth-context";
import { useUserState } from "@/hooks/useUserState";
import AnalyticsService from "@/services/analyticsService";

interface UseAppLoadingState {
  isInitialLoading: boolean;
  loadingMessage: string;
  isReady: boolean;
}

/**
 * Hook per gestire il caricamento iniziale dell'app
 * Coordina il caricamento dei sistemi critici (auth, user state, ecc.)
 */
export const useAppLoading = (): UseAppLoadingState => {
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [loadingMessage, setLoadingMessage] = useState("Inizializzazione...");
  const [isReady, setIsReady] = useState(false);
  const [hasInitialized, setHasInitialized] = useState(false);

  const { isLoading: authLoading } = useAuth();
  const { isLoading: userStateLoading } = useUserState();
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Se l'app è già stata inizializzata, caricamento rapido
        if (hasInitialized) {
          setLoadingMessage("Caricamento...");
          await new Promise((resolve) => setTimeout(resolve, 1000)); // Solo 1 secondo
          setIsReady(true);
          setIsInitialLoading(false);
          return;
        }

        // Prima inizializzazione completa
        // Fase 1: Caricamento configurazione di base
        setLoadingMessage("Caricamento configurazione...");
        await new Promise((resolve) => setTimeout(resolve, 500)); // Aumentato per dare più tempo

        // Fase 2: Preload risorse critiche (icone, suoni, immagini)
        setLoadingMessage("Caricamento risorse...");

        // Preload delle immagini critiche (rimosso elia-gallo per evitare flash)
        const criticalImages = [
          "/images/ranks/1-contadino.png",
          "/images/ranks/2-bracciante.png",
          "/images/ranks/3-massaro.png",
          "/images/logos/logo-new_nobg.png", // Logo principale per loading screen
        ];

        const imagePromises = criticalImages.map((src) => {
          return new Promise<void>((resolve) => {
            const img = new Image();
            img.onload = () => resolve();
            img.onerror = () => resolve(); // Non bloccare se un'immagine non si carica
            img.src = src;
            // Timeout per non aspettare troppo
            setTimeout(() => resolve(), 1000);
          });
        });

        await Promise.all(imagePromises);

        // Fase 3: Attesa inizializzazione auth context
        setLoadingMessage("Connessione in corso...");

        // Controlla se i context sono ancora in caricamento
        if (authLoading || userStateLoading) {
          // Aspetta un po' di più e poi continua comunque
          await new Promise((resolve) => setTimeout(resolve, 1500)); // Aumentato per dare più tempo
        }

        // Fase 4: Inizializzazione servizi (Analytics, AdMob, ecc.)
        setLoadingMessage("Inizializzazione servizi...");
        try {
          await AnalyticsService.initialize();
        } catch (error) {
          console.warn("⚠️ Errore inizializzazione Analytics:", error);
        }

        // Fase 5: Preparazione interfaccia
        setLoadingMessage("Preparazione interfaccia...");
        await new Promise((resolve) => setTimeout(resolve, 600)); // Aumentato

        // Fase 6: Finalizzazione
        setLoadingMessage("Completamento...");
        await new Promise((resolve) => setTimeout(resolve, 400)); // Aumentato

        setIsReady(true);
        setHasInitialized(true); // Marca l'app come inizializzata

        // Piccolo delay finale per transizione fluida
        setTimeout(() => {
          setIsInitialLoading(false);
        }, 300);
      } catch (error) {
        console.error("❌ Errore durante l'inizializzazione app:", error);
        setLoadingMessage("Errore di caricamento");

        // Anche in caso di errore, continua dopo un breve delay
        setTimeout(() => {
          setIsReady(true);
          setHasInitialized(true); // Marca come inizializzata anche in caso di errore
          setIsInitialLoading(false);
        }, 1000);
      }
    };

    // Avvia l'inizializzazione solo al primo mount
    if (isInitialLoading) {
      initializeApp();
    }
  }, [authLoading, userStateLoading, isInitialLoading, hasInitialized]);

  return {
    isInitialLoading,
    loadingMessage,
    isReady,
  };
};

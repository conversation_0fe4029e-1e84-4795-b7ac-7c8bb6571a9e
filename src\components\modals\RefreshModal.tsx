import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { RotateCcw, CheckCircle } from "lucide-react";

interface RefreshModalProps {
  isOpen: boolean;
  onRefresh: () => void;
  onDismiss: () => void;
}

const RefreshModal: React.FC<RefreshModalProps> = ({
  isOpen,
  onRefresh,
  onDismiss,
}) => {
  const handleRefresh = () => {
    onRefresh();
    window.location.reload();
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="w-full max-w-xs mx-2 rounded-2xl p-0 border-0 shadow-2xl [&_button[aria-label=Close]]:hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-t-2xl">
          <DialogHeader>
            <DialogTitle className="text-lg font-bold text-white flex items-center gap-2">
              <CheckCircle className="w-6 h-6" />
              Login Completato!
            </DialogTitle>
          </DialogHeader>
        </div>

        {/* Contenuto */}
        <div className="p-6 space-y-4">
          <p className="text-gray-700 text-sm text-center">
            Hai completato il login con Google. Per vedere i tuoi dati
            aggiornati, chiudi e riapri l'app.
          </p>

          <div className="space-y-3">
            <Button
              onClick={handleRefresh}
              className="w-full py-3 rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Ricarica l'app
            </Button>

            {/* <Button
              variant="ghost"
              onClick={onDismiss}
              className="w-full py-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-xl text-sm"
            >
              Continua senza aggiornare
            </Button> */}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default RefreshModal;

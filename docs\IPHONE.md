Maraffa Romagnola su iOS
📋 Prerequisiti

1. Hardware e Software Necessari
   Mac con macOS (obbligatorio per sviluppo iOS)
   Xcode (ultima versione disponibile su App Store)
   Account Apple Developer ($99/anno per pubblicare su App Store)
   CocoaPods installato (sudo gem install cocoapods)
2. Verifica Ambiente di Sviluppo
   🔧 Passaggi di Configurazione
3. Aggiungere Piattaforma iOS
4. Aggiornare capacitor.config.ts
5. Configurare AdMob per iOS
   A. Creare App iOS su AdMob
   Vai su AdMob Console
   Clicca "Aggiungi app"
   Seleziona "iOS"
   Inserisci nome app: "Marafone Romagnolo"
   Copia l'App ID iOS generato
   B. Creare Unità Pubblicitarie iOS
   Nella tua app iOS su AdMob
   Vai su "Unità pubblicitarie"
   Crea "Banner" per la schermata principale
   Copia l'Ad Unit ID generato
   C. Aggiornare Variabili Ambiente
   Crea/aggiorna il file .env:

6. Aggiornare Servizio AdMob
   Modifica src/services/adMobService.ts:

7. Configurare OAuth Google per iOS
   A. Google Cloud Console
   Vai su Google Cloud Console
   Nel tuo progetto esistente, vai su "Credentials"
   Crea nuovo "OAuth 2.0 Client ID" per iOS:
   Application type: iOS
   Bundle ID: com.eliazavatta.maraffa
   Scarica il file GoogleService-Info.plist
   B. Aggiungere GoogleService-Info.plist
   Apri Xcode: npx cap open ios
   Trascina GoogleService-Info.plist nel progetto Xcode
   Assicurati sia aggiunto al target principale
8. Configurare Icone e Splash Screen
   A. Icone App (obbligatorie)
   Crea icone nelle seguenti dimensioni e salvale in ios/App/App/Assets.xcassets/AppIcon.appiconset/:

20x20, 29x29, 40x40, 58x58, 60x60, 80x80, 87x87, 120x120, 180x180, 1024x1024
B. Splash Screen
Crea immagini splash per diverse risoluzioni
Aggiungile in ios/App/App/Assets.xcassets/Splash.imageset/ 7. Configurazioni Specifiche iOS
A. Info.plist
Aggiungi in ios/App/App/Info.plist:

B. Podfile (se necessario)
Il file ios/App/Podfile dovrebbe essere generato automaticamente.

8. Build e Test
   A. Sincronizzare Progetto
   B. Aprire in Xcode
   C. Configurare Signing
   In Xcode, seleziona il progetto
   Vai su "Signing & Capabilities"
   Seleziona il tuo Team Developer
   Xcode configurerà automaticamente il Bundle Identifier
   D. Test su Simulatore
   Seleziona un simulatore iOS
   Clicca "Run" (▶️)
   🚀 Pubblicazione su App Store
1. Preparazione
   Crea screenshots per diverse dimensioni iPhone/iPad
   Scrivi descrizione app in italiano
   Prepara icona 1024x1024 per App Store
   Definisci keywords per SEO
1. App Store Connect
   Vai su App Store Connect
   Crea nuova app
   Compila tutte le informazioni richieste
   Carica build da Xcode
1. Review Process
   Apple richiede 1-7 giorni per review
   Potrebbero richiedere modifiche per compliance
   ⚠️ Problemi Comuni e Soluzioni
1. Plugin Capacitor
   Alcuni plugin potrebbero non essere compatibili con iOS:

Verifica compatibilità di ogni plugin
Cerca alternative iOS-native se necessario 2. Differenze UI/UX
iOS ha linee guida diverse da Android
Testa accuratamente su dispositivi iOS reali
Considera adattamenti per iOS (es. safe areas) 3. Performance
iOS può avere performance diverse
Testa su dispositivi più vecchi
Ottimizza se necessario
📱 Comandi Utili
🔄 Workflow di Sviluppo Consigliato
Sviluppo: Testa principalmente su web/Android
Pre-release: Testa su simulatore iOS
Release: Testa su dispositivo iOS reale
Deploy: Build e carica su App Store Connect
Nota: Molti di questi passaggi richiedono un Mac e Xcode. Se non hai accesso a un Mac, potresti considerare:

Servizi cloud come MacInCloud
Collaborazione con uno sviluppatore iOS
Servizi di build come Ionic Appflow
Vuoi che proceda con qualche configurazione specifica o hai domande su alcuni passaggi?

0 files changed

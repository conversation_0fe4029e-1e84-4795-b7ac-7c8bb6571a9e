/**
 * 🔍 Componente Debug per visualizzare le carte degli altri giocatori
 * Mostra il ventaglio completo delle carte in mano agli AI per debug
 */

import React, { useState } from "react";
import { Card as CardType } from "@/utils/game/cardUtils";
import Card from "./Card";
import { cn } from "@/lib/utils";
import { isDebugModeEnabled } from "@/utils/debug/debugMode";
import { X, Eye, EyeOff } from "lucide-react";

interface DebugPlayerHandsProps {
  players: Array<{
    id: number;
    name: string;
    hand: CardType[];
    team: 0 | 1;
    position: "north" | "east" | "south" | "west";
  }>;
  trumpSuit?: string;
  isVisible?: boolean;
  onToggleVisibility?: () => void;
}

const DebugPlayerHands: React.FC<DebugPlayerHandsProps> = ({
  players,
  trumpSuit,
  isVisible: externalIsVisible,
  onToggleVisibility,
}) => {
  const [internalIsVisible, setInternalIsVisible] = useState(true);

  // Use external visibility control if provided, otherwise use internal state
  const isVisible =
    externalIsVisible !== undefined ? externalIsVisible : internalIsVisible;
  const toggleVisibility =
    onToggleVisibility || (() => setInternalIsVisible(!internalIsVisible));

  // Non mostrare nulla se debug mode è disattivato
  if (!isDebugModeEnabled()) {
    return null;
  }

  // Filtra solo i giocatori AI (non il giocatore umano che è sempre l'indice 0)
  const aiPlayers = players.filter((player, index) => index !== 0);

  if (aiPlayers.length === 0) {
    return null;
  }
  return (
    <div>
      {/* Modale delle carte AI */}
      {isVisible && (
        <div className="fixed top-20 right-4 z-40 bg-black/90 border-2 border-red-500 rounded-lg p-4 max-w-screen-lg">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <div className="bg-red-500 text-white text-xs px-2 py-1 rounded font-bold animate-pulse">
                🔍 DEBUG
              </div>
              <span className="text-white text-sm font-semibold">
                Carte in mano agli AI
              </span>
            </div>{" "}
            <button
              onClick={toggleVisibility}
              className="text-red-400 hover:text-red-300 transition-colors"
              title="Chiudi"
            >
              <X className="h-4 w-4" />
            </button>
          </div>

          <div className="flex flex-col gap-3 max-h-64 overflow-y-auto">
            {aiPlayers.map((player, playerIndex) => (
              <div key={playerIndex} className="bg-gray-800 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-2">
                  {" "}
                  <span
                    className={cn(
                      "text-sm font-semibold",
                      player.team === 0
                        ? "text-team-0-primary"
                        : "text-team-1-primary"
                    )}
                  >
                    {player.name}
                  </span>
                  <span className="text-gray-400 text-xs">
                    ({player.hand.length} carte)
                  </span>{" "}
                  <span
                    className={cn(
                      "text-xs px-2 py-1 rounded",
                      player.team === 0
                        ? "bg-team-0-primary/20 text-team-0-primary"
                        : "bg-team-1-primary/20 text-team-1-primary"
                    )}
                  >
                    Team {player.team === 0 ? "Giallo" : "Rosso"}
                  </span>
                </div>

                <div className="flex flex-wrap gap-1">
                  {player.hand
                    .sort((a, b) => {
                      // Ordina per seme e poi per forza
                      if (a.suit === b.suit) {
                        return b.order - a.order;
                      }
                      return a.suit.localeCompare(b.suit);
                    })
                    .map((card) => {
                      const isTrump = trumpSuit && card.suit === trumpSuit;
                      const isHighValue = ["A", "2", "3"].includes(card.rank);

                      return (
                        <div
                          key={card.id}
                          className={cn(
                            "relative transition-all duration-200",
                            isTrump && "ring-2 ring-blue-400 ring-opacity-75",
                            isHighValue &&
                              "ring-2 ring-yellow-400 ring-opacity-50"
                          )}
                          title={`${card.rank} di ${card.suit}${
                            isTrump ? " (Briscola)" : ""
                          }${isHighValue ? " (Alto valore)" : ""}`}
                        >
                          <Card
                            card={card}
                            isPlayable={false}
                            isRevealed={true}
                            onClick={() => {}}
                            scale="xs"
                            isTrump={isTrump}
                            className="transform hover:scale-110"
                          />
                          {isTrump && (
                            <div className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center font-bold">
                              B
                            </div>
                          )}
                          {isHighValue && !isTrump && (
                            <div className="absolute -top-1 -right-1 bg-yellow-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center font-bold">
                              ⭐
                            </div>
                          )}
                        </div>
                      );
                    })}
                </div>

                {/* Statistiche rapide */}
                <div className="flex gap-4 mt-2 text-xs text-gray-400">
                  <span>
                    Briscole:{" "}
                    {player.hand.filter((c) => c.suit === trumpSuit).length}
                  </span>
                  <span>
                    Assi: {player.hand.filter((c) => c.rank === "A").length}
                  </span>
                  <span>
                    2-3:{" "}
                    {
                      player.hand.filter((c) => ["2", "3"].includes(c.rank))
                        .length
                    }
                  </span>
                  <span>
                    Punti totali:{" "}
                    {player.hand.reduce((sum, c) => {
                      const values: Record<string, number> = {
                        A: 11,
                        "3": 10,
                        K: 4,
                        H: 3,
                        J: 2,
                      };
                      return sum + (values[c.rank] || 0);
                    }, 0)}
                  </span>
                </div>
              </div>
            ))}
          </div>

          <div className="text-xs text-gray-500 mt-2 border-t border-gray-700 pt-2">
            💡 Tip: Le carte con bordo blu sono briscole, quelle con stella
            gialla sono di alto valore{" "}
          </div>
        </div>
      )}
    </div>
  );
};

export default DebugPlayerHands;

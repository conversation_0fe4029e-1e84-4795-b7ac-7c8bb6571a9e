import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Crown,
  Shield,
  Gem,
  Zap,
  Heart,
  Trophy,
  RotateCcw,
} from "lucide-react";
import PurchaseService, {
  SUBSCRIPTION_PRODUCTS,
} from "@/services/purchaseService";

interface PremiumSectionProps {
  hasAdPass: boolean;
  onAdPassPurchase: () => void;
}

const PremiumSection = ({
  hasAdPass,
  onAdPassPurchase,
}: PremiumSectionProps) => {
  const [selectedPremiumOption, setSelectedPremiumOption] = useState("monthly");
  const [isLoading, setIsLoading] = useState(false);
  const [isPremiumActive, setIsPremiumActive] = useState(false);

  useEffect(() => {
    // Inizializza il servizio di acquisti e controlla lo status premium
    const initializePurchases = async () => {
      await PurchaseService.initialize();
      const premiumStatus = await PurchaseService.checkPremiumStatus();
      setIsPremiumActive(premiumStatus);
    };

    initializePurchases();
  }, []);

  const handlePurchase = async () => {
    setIsLoading(true);
    try {
      const productId =
        selectedPremiumOption === "lifetime"
          ? SUBSCRIPTION_PRODUCTS.LIFETIME
          : SUBSCRIPTION_PRODUCTS.MONTHLY;

      const success = await PurchaseService.purchaseProduct(productId);

      if (success) {
        setIsPremiumActive(true);
        onAdPassPurchase(); // Callback per aggiornare lo stato nell'app
      }
    } catch (error) {
      console.error("Errore durante l'acquisto:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRestore = async () => {
    setIsLoading(true);
    try {
      const restored = await PurchaseService.restorePurchases();
      if (restored) {
        setIsPremiumActive(true);
        onAdPassPurchase(); // Callback per aggiornare lo stato nell'app
      }
    } catch (error) {
      console.error("Errore durante il ripristino:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const actualHasAdPass = hasAdPass || isPremiumActive;

  return (
    <Card className="premium-section relative border-0 shadow-lg bg-gradient-to-br from-purple-900 via-purple-800 to-pink-900 overflow-hidden">
      <CardContent className="relative z-10 p-6">
        <div className="flex items-center justify-center gap-4 mb-4">
          <img
            src="/images/icons/premium-crown 100x100.png"
            alt="Premium Crown"
            className="h-20 w-20 object-contain"
          />
          <div>
            <h2
              className="text-xl md:text-2xl font-bold text-white"
              style={{
                fontFamily: "'DynaPuff', cursive",
                fontWeight: 600,
              }}
            >
              Pass Premium
            </h2>
            <p className="text-purple-200 text-sm">
              Sblocca tutto il potenziale del gioco
            </p>
          </div>
        </div>
        {/* Vantaggi Premium */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
          <div className="text-center p-2 bg-white/10 rounded-lg hover:bg-white/20 transition-all duration-200">
            <Shield className="h-5 w-5 text-yellow-300 mx-auto mb-1 animate-bounce" />
            <div className="text-xs text-white font-medium">Zero Ads</div>
          </div>
          <div className="text-center p-2 bg-white/10 rounded-lg hover:bg-white/20 transition-all duration-200">
            <Gem className="h-5 w-5 text-purple-300 mx-auto mb-1 animate-bounce" />
            <div className="text-xs text-white font-medium">
              Oggetti Esclusivi
            </div>
          </div>
          <div className="text-center p-2 bg-white/10 rounded-lg hover:bg-white/20 transition-all duration-200">
            <Zap className="h-5 w-5 text-blue-300 mx-auto mb-1 animate-bounce" />
            <div className="text-xs text-white font-medium">Bonus XP</div>
          </div>
          <div className="text-center p-2 bg-white/10 rounded-lg hover:bg-white/20 transition-all duration-200">
            <Heart className="h-5 w-5 text-red-300 mx-auto mb-1 animate-bounce" />
            <div className="text-xs text-white font-medium">
              Supporto allo sviluppo
            </div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div
            onClick={() => setSelectedPremiumOption("monthly")}
            className={`p-3 rounded-xl border-2 cursor-pointer transition-all duration-200 relative ${
              selectedPremiumOption === "monthly"
                ? "border-yellow-400 bg-yellow-400/20 shadow-md"
                : "border-white/30 bg-white/10 hover:bg-white/15"
            }`}
          >
            <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-bold">
              -17%
            </div>
            <div className="text-center">
              <div className="text-white font-bold mb-1">Mensile</div>
              <div className="mb-1">
                <span className="line-through text-gray-400 text-sm mr-2">
                  2.99€
                </span>
                <span className="text-yellow-300 text-xl font-bold">2.49€</span>
                <div className="text-purple-200 text-xs">al mese</div>
              </div>
            </div>
          </div>
          <div
            onClick={() => setSelectedPremiumOption("lifetime")}
            className={`p-3 rounded-xl border-2 cursor-pointer transition-all duration-200 relative ${
              selectedPremiumOption === "lifetime"
                ? "border-green-400 bg-green-400/20 shadow-md"
                : "border-white/30 bg-white/10 hover:bg-white/15"
            }`}
          >
            <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs px-2 py-1 rounded-full font-bold animate-pulse">
              🔥 MIGLIORE
            </div>
            <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-bold">
              -20%
            </div>
            <div className="text-center pt-1">
              <div className="text-white font-bold mb-1 flex items-center justify-center gap-1">
                <Trophy className="h-3 w-3 text-yellow-400" />
                Lifetime
              </div>
              <div className="mb-1">
                <span className="line-through text-gray-400 text-sm mr-2">
                  14.99€
                </span>
                <span className="text-green-300 text-xl font-bold">11.99€</span>
              </div>
              <div className="text-green-300 text-xs">una volta sola</div>
            </div>
          </div>
        </div>
        <div className="text-center space-y-3">
          <Button
            onClick={handlePurchase}
            disabled={isLoading || actualHasAdPass}
            className={`px-6 py-3 rounded-xl text-lg font-bold transition-all duration-200 shadow-lg w-full md:w-auto hover:scale-105 transform ${
              actualHasAdPass
                ? "bg-gradient-to-r from-green-500 to-emerald-600"
                : selectedPremiumOption === "lifetime"
                ? "bg-gradient-to-r from-green-500 to-emerald-500"
                : "bg-gradient-to-r from-yellow-400 to-orange-500"
            }`}
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Elaborazione...
              </>
            ) : actualHasAdPass ? (
              <>
                <Trophy className="h-5 w-5 mr-2" />
                Premium Attivo!
              </>
            ) : (
              <>
                <Crown className="h-5 w-5 mr-2" />
                {selectedPremiumOption === "lifetime"
                  ? "Acquista Lifetime"
                  : "Abbonati Ora"}
              </>
            )}
          </Button>

          {!actualHasAdPass && (
            <div className="w-full flex justify-start">
              <button
                onClick={handleRestore}
                disabled={isLoading}
                className="flex items-center gap-1 text-[11px] text-gray-200 opacity-60 hover:opacity-90 transition-opacity duration-200 font-semibold tracking-tight border-none bg-transparent p-0 m-0 shadow-none outline-none focus:outline-none disabled:opacity-30"
                style={{ background: "none", boxShadow: "none" }}
                type="button"
              >
                <RotateCcw className="h-3 w-3 text-gray-300" />
                Ripristina acquisti
              </button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default PremiumSection;

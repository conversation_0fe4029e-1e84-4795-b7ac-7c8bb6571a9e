/**
 * 🎯 Utilità per forzare la maraffa in modalità debug
 * Permette di modificare le carte distribuite per garantire che un giocatore specifico abbia la maraffa
 */

import { Card, Suit, Rank } from "../game/cardUtils";
import { isForcePlayerMaraffaEnabled, isForceCpuMaraffaEnabled, debugForceMaraffa } from "./debugMode";

/**
 * Forza la maraffa per un giocatore specifico modificando le sue carte
 * @param hands - Array delle mani dei giocatori
 * @param targetPlayerIndex - Indice del giocatore che deve avere la maraffa
 * @param suit - Seme per la maraffa (opzionale, se non specificato usa Coins)
 * @returns Le mani modificate con la maraffa forzata
 */
export const forceMaraffaForPlayer = (
  hands: Card[][],
  targetPlayerIndex: number,
  suit: Suit = Suit.Coins
): Card[][] => {
  const modifiedHands = [...hands];
  const targetHand = [...modifiedHands[targetPlayerIndex]];

  // Carte della maraffa necessarie
  const maraffaCards = [
    { suit, rank: Rank.Ace, id: `${suit}_${Rank.Ace}_forced` },
    { suit, rank: Rank.Two, id: `${suit}_${Rank.Two}_forced` },
    { suit, rank: Rank.Three, id: `${suit}_${Rank.Three}_forced` },
  ];

  // Rimuovi eventuali carte dello stesso seme dalla mano del giocatore target
  const filteredHand = targetHand.filter(card => card.suit !== suit);

  // Aggiungi le carte della maraffa
  const newHand = [...filteredHand.slice(0, 7), ...maraffaCards];

  // Assicurati che la mano abbia esattamente 10 carte
  while (newHand.length < 10) {
    // Aggiungi carte casuali di altri semi
    const otherSuits = [Suit.Cups, Suit.Swords, Suit.Clubs].filter(s => s !== suit);
    const randomSuit = otherSuits[Math.floor(Math.random() * otherSuits.length)];
    const randomRank = [Rank.Four, Rank.Five, Rank.Six, Rank.Seven][Math.floor(Math.random() * 4)];
    newHand.push({
      suit: randomSuit,
      rank: randomRank,
      id: `${randomSuit}_${randomRank}_filler_${newHand.length}`
    });
  }

  modifiedHands[targetPlayerIndex] = newHand.slice(0, 10);

  debugForceMaraffa(targetPlayerIndex, suit);
  return modifiedHands;
};

/**
 * Applica la logica di debug per forzare la maraffa se abilitata
 * @param hands - Array delle mani dei giocatori
 * @returns Le mani modificate se il debug è attivo, altrimenti le mani originali
 */
export const applyDebugMaraffaForcing = (hands: Card[][]): Card[][] => {
  // Controlla se il debug per forzare la maraffa al giocatore è attivo
  if (isForcePlayerMaraffaEnabled()) {
    return forceMaraffaForPlayer(hands, 0, Suit.Coins); // Giocatore umano (indice 0)
  }

  // Controlla se il debug per forzare la maraffa alla CPU è attivo
  if (isForceCpuMaraffaEnabled()) {
    // Scegli un giocatore CPU casuale (1, 2, o 3)
    const cpuPlayerIndex = Math.floor(Math.random() * 3) + 1;
    return forceMaraffaForPlayer(hands, cpuPlayerIndex, Suit.Coins);
  }

  // Nessun debug attivo, restituisci le mani originali
  return hands;
};

/**
 * Verifica se almeno una modalità di debug maraffa è attiva
 */
export const isAnyMaraffaDebugEnabled = (): boolean => {
  return isForcePlayerMaraffaEnabled() || isForceCpuMaraffaEnabled();
};

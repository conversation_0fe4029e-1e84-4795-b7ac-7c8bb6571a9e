import React, { useState, useEffect } from "react";
import ActionButton from "./ui/ActionButton";
import { useAudio } from "@/hooks/useAudio";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface GameControlsProps {
  phase: string;
  isCurrentPlayerTurn: boolean;
  onSelectTrump: (suit: string) => void;
  onAnnounceAction: (action: string) => void;
  onDeclareWin: (teamId: number) => void;
  onStartNewRound: () => void; // Manteniamo per compatibilità
  onStartNewGame: () => void;
  onReturnToMenu: () => void;
  message: string;
  currentPlayerTeam: number;
  isTrickStart: boolean;
  gameScore: number[];
  roundScore: number[];
  isMobile: boolean;
  canDeclareVictory: boolean;
  isOnlineMode: boolean;
}

const GameControls = ({
  phase,
  isCurrentPlayerTurn,
  onSelectTrump,
  onAnnounceAction,
  onDeclareWin,
  onStartNewGame,
  onReturnToMenu,
  message,
  currentPlayerTeam,
  isTrickStart,
  gameScore,
  roundScore,
  isMobile,
  canDeclareVictory,
  isOnlineMode,
}: GameControlsProps) => {
  const { playSound } = useAudio();

  // State locale per gestire la visibilità della modale dei controlli
  const [showControls, setShowControls] = useState(false);

  // Effetto per sincronizzare la visibilità della modale con le props
  useEffect(() => {
    if (phase === "play" && isCurrentPlayerTurn && isTrickStart) {
      setShowControls(true);
    } else {
      setShowControls(false);
    }
  }, [phase, isCurrentPlayerTurn, isTrickStart]);

  const handleActionAnnounce = (action: string) => {
    playSound("buttonClick");
    onAnnounceAction(action);
    // Nascondi immediatamente la modale dopo aver cliccato un pulsante
    setShowControls(false);
  };

  const handleDeclareVictory = (teamId: number) => {
    playSound("victory-cheer");
    onDeclareWin(teamId);
  };

  const handleStartNewGame = () => {
    playSound("gameStart");
    onStartNewGame();
  };

  const handleReturnToMenu = () => {
    onReturnToMenu();
  };

  return (
    <div className="absolute top-28 right-4">
      <div className="flex flex-col items-center gap-2 w-28 pointer-events-auto">
        {showControls ? (
          <div className="bg-gray-100 p-2 mt-2 rounded-lg shadow-md w-full">
            <div className="flex flex-col items-center gap-2">
              <div className="text-sm font-semibold text-gray-700">Chiama:</div>
              <ActionButton
                className="text-sm px-2 py-1 w-full bg-red-500 hover:bg-red-600 text-white rounded-lg"
                onClick={() => handleActionAnnounce("busso")}
              >
                👊 Busso
              </ActionButton>
              <ActionButton
                className="text-sm px-2 py-1 w-full bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg"
                onClick={() => handleActionAnnounce("striscio")}
              >
                🐍 Striscio
              </ActionButton>
              <ActionButton
                className="text-sm px-2 py-1 w-full bg-blue-500 hover:bg-blue-600 text-white rounded-lg"
                onClick={() => handleActionAnnounce("volo")}
              >
                🕊️ Volo
              </ActionButton>
            </div>
          </div>
        ) : null}

        {phase === "play" && canDeclareVictory && isCurrentPlayerTurn ? (
          <div className="flex flex-col items-center gap-2 w-full">
            <AlertDialog>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Conferma Dichiarazione</AlertDialogTitle>
                  <AlertDialogDescription>
                    Sei sicuro di voler dichiarare la vittoria per la tua
                    squadra?
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Annulla</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => handleDeclareVictory(currentPlayerTeam)}
                  >
                    🏆 Dichiara Vittoria
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        ) : null}

        {/* Rimosso il pulsante per il nuovo round poiché ora avviene automaticamente */}

        {/* I bottoni per gameOver sono ora gestiti dalla GameOverModal */}

        {/* Only show message on desktop, removed for mobile */}
        {!isMobile && message && (
          <div className="text-center text-sm text-gray-600 mt-2">
            {message}
          </div>
        )}
      </div>
    </div>
  );
};

export default GameControls;

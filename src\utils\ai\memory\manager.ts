import { Card, Suit } from "../../game/cardUtils";
import { GameState } from "../../game/gameLogic";
import { CardMemory } from "../types";
import {
  initializeCardMemory,
  updateCardMemory,
  updateTrumpsRemaining,
} from "../utils/cardUtils";

/**
 * 🎯 SISTEMA MEMORIA AI SEMPLIFICATO
 * Versione ridotta che mantiene solo le funzionalità essenziali
 */
export class MemoryManager {
  private playedCards: Card[] = [];
  private gameRound: number = 0;

  constructor() {
    this.playedCards = [];
  }

  /**
   * 🔄 NUOVO ROUND
   */
  public startNewRound(): void {
    this.playedCards = [];
    this.gameRound++;
  }

  /**
   * 📝 REGISTRA CARTA GIOCATA
   */
  public recordPlayedCard(card: Card, playerIndex: number): void {
    this.playedCards.push(card);
  }

  /**
   * 🃏 OTTIENI CARTE GIOCATE
   */
  public getPlayedCards(): Card[] {
    return [...this.playedCards];
  }

  /**
   * 🔍 VERIFICA SE CARTA È STATA GIOCATA
   */
  public isCardPlayed(card: Card): boolean {
    return this.playedCards.some(
      (played) => played.suit === card.suit && played.rank === card.rank
    );
  }
  /**
   * 🔄 AGGIORNA MEMORIA (COMPATIBILITÀ)
   */
  public updateMemory(gameState: GameState): void {
    // Metodo semplificato per compatibilità
    // La memoria viene aggiornata tramite recordPlayedCard
  }

  /**
   * 🧹 RESET COMPLETO
   */
  public reset(): void {
    this.playedCards = [];
    this.gameRound = 0;
  }
}

// Istanza globale del memory manager
export const globalMemoryManager = new MemoryManager();

import { Check, X } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import ActionButton from "@/components/ui/ActionButton";
import { getPlayerTitle } from "@/services/playerTitlesService";

interface FriendRequest {
  id: string;
  from_user_id: string;
  to_user_id: string;
  created_at: string;
  from_user: {
    username: string;
    avatar_url?: string;
    level?: number;
  };
}

interface FriendRequestsProps {
  requests: FriendRequest[];
  onHandleRequest: (requestId: string, action: "accept" | "decline") => void;
}

const FriendRequests = ({ requests, onHandleRequest }: FriendRequestsProps) => {
  if (requests.length === 0) {
    return null;
  }

  return (
    <div className="space-y-2">
      <h3 className="font-semibold text-romagna-darkWood ml-1">
        Richieste di amicizia ({requests.length})
      </h3>
      <div className="space-y-3">
        {requests.map((request) => (
          <Card
            key={request.id}
            className="border-2 border-amber-800/30 shadow-md bg-amber-50/80 backdrop-blur-sm"
          >
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-amber-100 to-yellow-100 flex items-center justify-center border-2 border-amber-300">
                    <img
                      src={
                        getPlayerTitle(request.from_user.level || 1).rankImage
                      }
                      alt={getPlayerTitle(request.from_user.level || 1).title}
                      className="w-8 h-8 object-contain"
                    />
                  </div>
                  <div>
                    <p className="text-lg font-semibold text-romagna-darkWood">
                      {request.from_user.username}
                    </p>
                    <p className="text-sm text-romagna-darkWood/70">
                      Richiesta di amicizia
                    </p>
                    <p className="text-xs text-romagna-darkWood/50">
                      {new Date(request.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="flex gap-2">
                  <ActionButton
                    onClick={() => onHandleRequest(request.id, "accept")}
                    className="p-2 bg-green-100 hover:bg-green-200 text-green-700 rounded-lg"
                  >
                    <Check className="h-4 w-4" />
                  </ActionButton>
                  <ActionButton
                    onClick={() => onHandleRequest(request.id, "decline")}
                    className="p-2 bg-red-100 hover:bg-red-200 text-red-700 rounded-lg"
                  >
                    <X className="h-4 w-4" />
                  </ActionButton>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default FriendRequests;

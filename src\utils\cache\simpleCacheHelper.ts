/**
 * 🎯 HELPER CACHE SEMPLIFICATO
 * Funzioni utility per gestire cache con TTL in modo semplice
 */

interface CacheItem<T> {
  data: T | null;
  timestamp: number;
}

/**
 * 🔍 VERIFICA SE CACHE È VALIDA
 */
export const isCacheValid = <T>(
  cache: CacheItem<T>,
  ttl: number
): boolean => {
  if (!cache.data || cache.timestamp === 0) return false;
  const age = Date.now() - cache.timestamp;
  return age < ttl;
};

/**
 * 💾 CREA CACHE ENTRY
 */
export const createCacheEntry = <T>(data: T): CacheItem<T> => ({
  data,
  timestamp: Date.now(),
});

/**
 * 🗑️ INVALIDA CACHE
 */
export const invalidateCache = <T>(): CacheItem<T> => ({
  data: null,
  timestamp: 0,
});

/**
 * 📊 OTTIENI ETÀ CACHE IN MINUTI
 */
export const getCacheAge = <T>(cache: CacheItem<T>): number => {
  if (cache.timestamp === 0) return Infinity;
  return Math.round((Date.now() - cache.timestamp) / 60000);
};

/**
 * 🎯 COSTANTI TTL COMUNI
 */
export const CACHE_TTL = {
  SHORT: 2 * 60 * 1000,   // 2 minuti
  MEDIUM: 5 * 60 * 1000,  // 5 minuti  
  LONG: 10 * 60 * 1000,   // 10 minuti
  ERROR: 30 * 1000,       // 30 secondi per errori
} as const;

/**
 * 🔄 HOOK SEMPLIFICATO PER CACHE CON TTL
 */
import { useState, useCallback } from 'react';

export const useSimpleCache = <T>(defaultTtl: number = CACHE_TTL.MEDIUM) => {
  const [cache, setCache] = useState<CacheItem<T>>(invalidateCache<T>());

  const get = useCallback((): T | null => {
    if (isCacheValid(cache, defaultTtl)) {
      return cache.data;
    }
    return null;
  }, [cache, defaultTtl]);

  const set = useCallback((data: T): void => {
    setCache(createCacheEntry(data));
  }, []);

  const invalidate = useCallback((): void => {
    setCache(invalidateCache<T>());
  }, []);

  const isValid = useCallback((): boolean => {
    return isCacheValid(cache, defaultTtl);
  }, [cache, defaultTtl]);

  const getAge = useCallback((): number => {
    return getCacheAge(cache);
  }, [cache]);

  return {
    get,
    set,
    invalidate,
    isValid,
    getAge,
    data: cache.data,
    timestamp: cache.timestamp,
  };
};

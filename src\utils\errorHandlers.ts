/**
 * 🛡️ Error Handlers per gestire warning e errori runtime
 *
 * Risolve problemi comuni come:
 * - runtime.lastError da estensioni browser
 * - Uncaught promise rejections
 * - Console warnings vari
 */

/**
 * Inizializza i gestori di errore globali
 */
export const initErrorHandlers = (): void => {
  // Gestore per errori di runtime delle estensioni
  const handleRuntimeError = (): void => {
    try {
      // Verifica se chrome.runtime esiste e ha lastError
      const chromeObj = (
        window as unknown as {
          chrome?: { runtime?: { lastError?: { message: string } } };
        }
      ).chrome;
      if (chromeObj?.runtime?.lastError) {
        // Consuma l'errore per evitare warning in console
        const error = chromeObj.runtime.lastError;
        console.debug("🔧 Chrome runtime error handled:", error.message);
      }
    } catch (error) {
      // Ignora errori di accesso a chrome.runtime
    }
  };

  // Controlla periodicamente per errori di runtime
  const checkRuntimeErrors = (): void => {
    try {
      handleRuntimeError();
    } catch (error) {
      // Ignora errori di accesso a chrome.runtime
    }
  };

  // Controlla ogni secondo per i primi 10 secondi (quando si verificano più spesso)
  let checkCount = 0;
  const maxChecks = 10;

  const intervalId = setInterval(() => {
    checkRuntimeErrors();
    checkCount++;

    if (checkCount >= maxChecks) {
      clearInterval(intervalId);
    }
  }, 1000);

  // Gestore per promise rejection non gestite
  window.addEventListener("unhandledrejection", (event) => {
    // Filtra errori noti che possono essere ignorati
    const message = event.reason?.message || event.reason || "";

    if (typeof message === "string") {
      // Errori da ignorare
      const ignoredErrors = [
        "chrome-extension",
        "runtime.lastError",
        "Extension context invalidated",
        "message channel closed",
        "listener indicated an asynchronous response",
        "message channel closed before a response was received",
        "asynchronous response by returning true",
        "browser extension",
        "content script",
      ];

      const shouldIgnore = ignoredErrors.some((ignored) =>
        message.toLowerCase().includes(ignored.toLowerCase())
      );

      if (shouldIgnore) {
        console.debug("🔧 Ignored known extension error:", message);
        event.preventDefault();
        return;
      }
    }

    // Log altri errori per debug
    console.warn("⚠️ Unhandled promise rejection:", event.reason);
  });

  // Gestore per errori JavaScript globali
  window.addEventListener("error", (event) => {
    const message = event.message || "";

    // Filtra errori da script esterni (GPT Engineer, estensioni)
    if (
      event.filename &&
      (event.filename.includes("gptengineer.js") ||
        event.filename.includes("chrome-extension") ||
        message.includes("runtime.lastError"))
    ) {
      console.debug("🔧 Filtered external script error:", message);
      event.preventDefault();
      return;
    }

    // Log errori dell'applicazione
    if (message && !message.includes("Script error")) {
      console.warn("⚠️ Application error:", {
        message: event.message,
        filename: event.filename,
        line: event.lineno,
        column: event.colno,
      });
    }
  });

  /**
   * 🔧 Gestione specifica per errori di message channel
   */
  const handleMessageChannelErrors = (): void => {
    // Intercetta errori specifici di message channel che possono verificarsi
    // quando le estensioni del browser perdono il contesto
    const originalAddEventListener = window.addEventListener;
    window.addEventListener = function (type, listener, options) {
      if (typeof listener === "function") {
        const wrappedListener = function (...args: unknown[]) {
          try {
            const result = listener.apply(this, args);
            // Se il listener restituisce una promise, gestisci eventuali rejections
            if (result && typeof result.catch === "function") {
              result.catch((error: Error) => {
                const errorMessage = error?.message || "";
                if (
                  errorMessage.includes("message channel closed") ||
                  errorMessage.includes("asynchronous response")
                ) {
                  console.debug(
                    "🔧 Message channel error handled:",
                    errorMessage
                  );
                  return; // Ignora l'errore
                }
                throw error; // Re-throw se non è un errore di message channel
              });
            }
            return result;
          } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : "";
            if (
              errorMessage.includes("message channel closed") ||
              errorMessage.includes("asynchronous response")
            ) {
              console.debug(
                "🔧 Sync message channel error handled:",
                errorMessage
              );
              return;
            }
            throw error;
          }
        };
        return originalAddEventListener.call(
          this,
          type,
          wrappedListener,
          options
        );
      }
      return originalAddEventListener.call(this, type, listener, options);
    };
  };

  handleMessageChannelErrors();
};

/**
 * Utility per creare promesse che non generano unhandled rejection
 */
export const safePromise = <T>(promise: Promise<T>): Promise<T> => {
  return promise.catch((error) => {
    console.debug("🔧 Safe promise caught error:", error);
    throw error;
  });
};

/**
 * Wrapper per funzioni asincrone che potrebbero generare errori di runtime
 */
export const safeAsync = <T extends unknown[], R>(
  fn: (...args: T) => Promise<R>
) => {
  return async (...args: T): Promise<R | null> => {
    try {
      return await fn(...args);
    } catch (error) {
      console.debug("🔧 Safe async caught error:", error);
      return null;
    }
  };
};

/**
 * Alias per compatibilità con App.tsx
 */
export const setupGlobalErrorHandlers = initErrorHandlers;

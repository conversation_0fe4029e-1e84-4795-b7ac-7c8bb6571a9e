# 🍎 Guida Setup iOS - Maraffa <PERSON>la

## 📋 Prerequisiti Completati

✅ **Configurazione Capacitor iOS** - Aggiunta configurazione iOS in `capacitor.config.ts`
✅ **Supporto AdMob iOS** - Configurato servizio AdMob per rilevare piattaforma iOS
✅ **Safe Areas CSS** - Aggiunte classi CSS per gestire le safe areas di iPhone
✅ **Script NPM iOS** - Aggiunti script per build e apertura iOS
✅ **Variabili Ambiente** - Preparate variabili per AdMob iOS

## 🚀 Prossimi Passaggi (Da Fare su Mac)

### 1. Installazione Dipendenze iOS
```bash
# Installa Capacitor iOS
npm install @capacitor/ios

# Aggiungi piattaforma iOS
npx cap add ios
```

### 2. Configurazione AdMob iOS
1. Vai su [AdMob Console](https://apps.admob.com/)
2. Crea nuova app iOS:
   - Nome: "Marafone Romagnolo"
   - Piattaforma: iOS
   - Bundle ID: `com.eliazavatta.maraffa`
3. Copia l'**App ID iOS** generato
4. Crea unità pubblicitaria Banner iOS
5. Copia l'**Ad Unit ID** generato

### 3. Aggiorna Variabili Ambiente
Crea/aggiorna `.env`:
```env
# AdMob iOS (sostituisci con ID reali)
VITE_ADMOB_IOS_APP_ID=ca-app-pub-XXXXXXXX~XXXXXXXXX
VITE_ADMOB_IOS_BANNER_UNIT_ID=ca-app-pub-XXXXXXXX/XXXXXXXXX
```

### 4. Configurazione Google OAuth iOS
1. Vai su [Google Cloud Console](https://console.cloud.google.com/)
2. Nel progetto esistente, vai su "Credentials"
3. Crea nuovo "OAuth 2.0 Client ID" per iOS:
   - Application type: iOS
   - Bundle ID: `com.eliazavatta.maraffa`
4. Scarica `GoogleService-Info.plist`

### 5. Setup Xcode
```bash
# Build e sincronizza
npm run cap:build:ios

# Apri in Xcode
npm run cap:open:ios
```

### 6. Configurazione Xcode
1. **GoogleService-Info.plist**:
   - Trascina il file nel progetto Xcode
   - Assicurati sia aggiunto al target principale

2. **App Icons**:
   - Crea icone nelle dimensioni richieste:
     - 20x20, 29x29, 40x40, 58x58, 60x60
     - 80x80, 87x87, 120x120, 180x180, 1024x1024
   - Aggiungi in `ios/App/App/Assets.xcassets/AppIcon.appiconset/`

3. **Splash Screen**:
   - Crea immagini splash per diverse risoluzioni
   - Aggiungi in `ios/App/App/Assets.xcassets/Splash.imageset/`

4. **Info.plist**:
   - Aggiungi permessi necessari:
   ```xml
   <key>NSCameraUsageDescription</key>
   <string>L'app ha bisogno dell'accesso alla fotocamera per funzionalità future</string>
   <key>NSMicrophoneUsageDescription</key>
   <string>L'app ha bisogno dell'accesso al microfono per effetti sonori</string>
   <key>CFBundleURLTypes</key>
   <array>
     <dict>
       <key>CFBundleURLName</key>
       <string>com.eliazavatta.maraffa</string>
       <key>CFBundleURLSchemes</key>
       <array>
         <string>com.eliazavatta.maraffa</string>
       </array>
     </dict>
   </array>
   ```

5. **Signing & Capabilities**:
   - Seleziona il tuo Team Developer
   - Configura Bundle Identifier: `com.eliazavatta.maraffa`
   - Abilita capabilities necessarie

### 7. Test e Build
```bash
# Test su simulatore
npm run cap:run:ios

# Build per dispositivo
# (Configurare signing in Xcode prima)
```

## 🔧 Modifiche Già Applicate

### Capacitor Config
```typescript
ios: {
  scheme: "Marafone Romagnolo",
  contentInset: "automatic",
  scrollEnabled: true,
  backgroundColor: "#781d02",
  allowsLinkPreview: false,
  handleApplicationNotifications: false,
}
```

### AdMob Service
- ✅ Rilevamento automatico piattaforma iOS
- ✅ Utilizzo ID AdMob specifici per iOS
- ✅ Fallback ad Android per compatibilità

### CSS Safe Areas
```css
.ios-safe-top { padding-top: max(1rem, env(safe-area-inset-top)); }
.ios-safe-bottom { padding-bottom: max(1rem, env(safe-area-inset-bottom)); }
.ios-safe-left { padding-left: max(1rem, env(safe-area-inset-left)); }
.ios-safe-right { padding-right: max(1rem, env(safe-area-inset-right)); }
```

### Splash Screen iOS
```typescript
SplashScreen: {
  // ... configurazione esistente
  iosLaunchAnimation: "fade",
  iosSpinnerStyle: "large",
}
```

## 📱 Differenze iOS vs Android

### UI/UX
- **Safe Areas**: Gestione automatica notch e home indicator
- **Navigation**: Stile iOS nativo per navigazione
- **Animations**: Ottimizzate per performance iOS

### Performance
- **Memory Management**: Ottimizzazioni specifiche iOS
- **Battery Usage**: Gestione efficiente risorse
- **Background Processing**: Conformità alle linee guida Apple

### Store Submission
- **App Store Guidelines**: Conformità alle regole Apple
- **Review Process**: Preparazione per review Apple (1-7 giorni)
- **Metadata**: Screenshots, descrizioni, keywords per App Store

## ⚠️ Note Importanti

1. **Mac Obbligatorio**: Sviluppo iOS richiede macOS e Xcode
2. **Apple Developer Account**: Necessario per pubblicazione ($99/anno)
3. **Certificati**: Gestione certificati e provisioning profiles
4. **Testing**: Test su dispositivi iOS reali raccomandato
5. **Compliance**: Rispetto delle linee guida App Store

## 🔄 Workflow Consigliato

1. **Sviluppo**: Principalmente su web/Android
2. **Pre-release**: Test su simulatore iOS
3. **Release**: Build e test su dispositivo iOS reale
4. **Deploy**: Caricamento su App Store Connect

## 📞 Supporto

Per problemi specifici iOS:
- **Apple Developer Documentation**: [developer.apple.com](https://developer.apple.com)
- **Capacitor iOS Guide**: [capacitorjs.com/docs/ios](https://capacitorjs.com/docs/ios)
- **Xcode Help**: Menu Help in Xcode

/**
 * 🔊 Audio Context User Gesture Handler
 *
 * Risolve i warning "AudioContext was not allowed to start" implementando
 * la gestione corretta dei user gesture per Web Audio API e Tone.js
 */

import { audioManager } from "./AudioManager";
import * as Tone from "tone";

/**
 * Classe per gestire l'inizializzazione audio dopo user gesture
 */
export class AudioContextManager {
  private static instance: AudioContextManager;
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;

  static getInstance(): AudioContextManager {
    if (!AudioContextManager.instance) {
      AudioContextManager.instance = new AudioContextManager();
    }
    return AudioContextManager.instance;
  }

  private constructor() {
    this.setupUserGestureListeners();
  }
  /**
   * 🎯 Setup listener per user gesture
   */
  private setupUserGestureListeners(): void {
    const initializeAudio = async () => {
      if (this.isInitialized || this.initializationPromise) return;

      try {
        this.initializationPromise = this.initializeAllAudioContexts();
        await this.initializationPromise;
      } catch (error) {
        // Ignora errori di inizializzazione audio per evitare promise rejections
        console.debug("🔧 Audio initialization error (ignored):", error);
      }
    };

    // Eventi per inizializzazione audio
    const events = ["click", "touchstart", "keydown", "pointerdown"];

    events.forEach((eventType) => {
      document.addEventListener(eventType, initializeAudio, {
        once: true,
        passive: true,
      });
    });

    // Specifico per mobile
    document.addEventListener("touchend", initializeAudio, {
      once: true,
      passive: true,
    });
  }
  /**
   * 🔄 Inizializza tutti i contesti audio
   */
  private async initializeAllAudioContexts(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // 1. Inizializza Tone.js con protezione da errori
      await this.safeToneInitialization();

      // 2. Inizializza AudioManager context con protezione
      await this.safeAudioManagerInitialization();

      this.isInitialized = true;
    } catch (error) {
      console.warn("⚠️ Failed to initialize audio contexts:", error);
    }
  }
  /**
   * 🛡️ Inizializzazione sicura di Tone.js
   */
  private async safeToneInitialization(): Promise<void> {
    try {
      if (Tone.context.state === "suspended") {
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error("Tone.js timeout")), 3000)
        );

        // Gestisce entrambe le promise per evitare uncaught rejections
        await Promise.race([
          Tone.start().catch((error) => {
            console.debug("🔧 Tone.start failed:", error);
            throw error;
          }),
          timeoutPromise.catch((error) => {
            console.debug("🔧 Tone.js timeout:", error);
            throw error;
          }),
        ]);
      }
    } catch (error) {
      console.warn("⚠️ Tone.js initialization failed:", error);
    }
  }
  /**
   * 🛡️ Inizializzazione sicura di AudioManager
   */
  private async safeAudioManagerInitialization(): Promise<void> {
    try {
      const manager = audioManager;
      if (manager && typeof manager.resumeAudioContext === "function") {
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error("AudioManager timeout")), 3000)
        );

        // Gestisce entrambe le promise per evitare uncaught rejections
        await Promise.race([
          manager.resumeAudioContext().catch((error) => {
            console.debug("🔧 AudioManager.resumeAudioContext failed:", error);
            throw error;
          }),
          timeoutPromise.catch((error) => {
            console.debug("🔧 AudioManager timeout:", error);
            throw error;
          }),
        ]);
      }
    } catch (error) {
      console.warn("⚠️ AudioManager initialization failed:", error);
    }
  }
  /**
   * 🎮 Forza inizializzazione (per uso programmatico)
   */
  public async forceInitialize(): Promise<void> {
    try {
      if (!this.initializationPromise) {
        this.initializationPromise = this.initializeAllAudioContexts();
      }
      await this.initializationPromise;
    } catch (error) {
      // Gestisce silent failure per evitare uncaught promise rejections
      console.debug("🔧 Force audio initialization failed (ignored):", error);
    }
  }

  /**
   * 📊 Stato dell'inizializzazione
   */
  public isAudioInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * 🔍 Debug info
   */
  public getDebugInfo(): {
    initialized: boolean;
    toneState: string;
    audioManagerState: string;
  } {
    return {
      initialized: this.isInitialized,
      toneState: Tone.context.state,
      audioManagerState: audioManager ? "ready" : "not-ready",
    };
  }
}

// Export singleton instance
export const audioContextManager = AudioContextManager.getInstance();

/**
 * 🎯 Hook helper per componenti React
 */
export const useAudioContextInitialization = () => {
  const manager = AudioContextManager.getInstance();

  return {
    initializeAudio: () => manager.forceInitialize(),
    isInitialized: manager.isAudioInitialized(),
    getDebugInfo: () => manager.getDebugInfo(),
  };
};

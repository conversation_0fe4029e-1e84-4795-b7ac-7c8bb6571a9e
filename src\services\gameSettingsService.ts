export interface GameSettings {
  victoryPoints: "21" | "31" | "41";
  difficulty: "easy" | "medium" | "hard";
  selectedTableMat: string;
}

import {
  loadUnifiedData,
  updateGameSettings as updateUnifiedSettings,
} from "./unifiedStorageService";

/**
 * 🎯 CARICA IMPOSTAZIONI DAL SISTEMA UNIFICATO
 */
export const getGameSettings = (): GameSettings => {
  const data = loadUnifiedData();
  return {
    victoryPoints: data.victoryPoints,
    difficulty: data.difficulty,
    selectedTableMat: data.selectedTableMat,
  };
};

/**
 * 🎯 SALVA IMPOSTAZIONI NEL SISTEMA UNIFICATO
 */
export const saveGameSettings = (settings: GameSettings): void => {
  updateUnifiedSettings(settings);

  // 🔄 NOTIFICA CAMBIAMENTO IMPOSTAZIONI
  window.dispatchEvent(
    new CustomEvent("gameSettingsChanged", {
      detail: settings,
    })
  );
};

/**
 * Aggiorna solo i punti vittoria
 */
export const updateVictoryPoints = (
  victoryPoints: "21" | "31" | "41"
): void => {
  const current = getGameSettings();
  saveGameSettings({ ...current, victoryPoints });
};

/**
 * Aggiorna solo la difficoltà
 */
export const updateDifficulty = (
  difficulty: "easy" | "medium" | "hard"
): void => {
  const current = getGameSettings();
  saveGameSettings({ ...current, difficulty });
};

/**
 * Aggiorna solo il tappetino selezionato
 */
export const updateSelectedTableMat = (selectedTableMat: string): void => {
  const current = getGameSettings();
  saveGameSettings({ ...current, selectedTableMat });
};

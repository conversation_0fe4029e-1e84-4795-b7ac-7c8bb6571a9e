/**
 * 🎯 SERVIZIO CACHE UNIFICATO
 * Sostituisce tutti i sistemi di cache complessi con un unico sistema semplificato
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

class UnifiedCacheService {
  private cache = new Map<string, CacheEntry<any>>();

  // TTL predefiniti (in millisecondi)
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minuti
  private readonly SHORT_TTL = 2 * 60 * 1000; // 2 minuti
  private readonly LONG_TTL = 10 * 60 * 1000; // 10 minuti

  /**
   * 💾 SALVA DATI IN CACHE
   */
  set<T>(key: string, data: T, ttl?: number): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.DEFAULT_TTL,
    };

    this.cache.set(key, entry);
  }

  /**
   * 📖 LEGGI DATI DA CACHE
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);

    if (!entry) {
      return null;
    }

    // Verifica se la cache è scaduta
    const age = Date.now() - entry.timestamp;
    if (age > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  /**
   * ✅ VERIFICA SE CACHE È VALIDA
   */
  isValid(key: string): boolean {
    const entry = this.cache.get(key);

    if (!entry) {
      return false;
    }

    const age = Date.now() - entry.timestamp;
    return age <= entry.ttl;
  }

  /**
   * 🗑️ RIMUOVI SINGOLA CACHE
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * 🧹 PULISCI CACHE SCADUTE
   */
  cleanup(): void {
    const now = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      const age = now - entry.timestamp;
      if (age > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 🔄 RESET COMPLETO
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * 📊 STATISTICHE CACHE
   */
  getStats(): {
    totalEntries: number;
    validEntries: number;
    expiredEntries: number;
  } {
    const now = Date.now();
    let validEntries = 0;
    let expiredEntries = 0;

    for (const entry of this.cache.values()) {
      const age = now - entry.timestamp;
      if (age <= entry.ttl) {
        validEntries++;
      } else {
        expiredEntries++;
      }
    }

    return {
      totalEntries: this.cache.size,
      validEntries,
      expiredEntries,
    };
  }

  /**
   * 🎯 METODI DI CONVENIENZA PER TIPI SPECIFICI
   */

  // Cache per amici
  setFriends(data: any[]): void {
    this.set("friends", data, this.DEFAULT_TTL);
  }

  getFriends(): any[] | null {
    return this.get<any[]>("friends");
  }

  // Cache per leaderboard
  setLeaderboard(data: any[]): void {
    this.set("leaderboard", data, this.LONG_TTL);
  }

  getLeaderboard(): any[] | null {
    return this.get<any[]>("leaderboard");
  }

  // Cache per ricerche
  setSearchResults(query: string, data: any[]): void {
    this.set(`search:${query.toLowerCase()}`, data, this.SHORT_TTL);
  }

  getSearchResults(query: string): any[] | null {
    return this.get<any[]>(`search:${query.toLowerCase()}`);
  }

  // Cache per profilo
  setProfile(userId: string, data: any): void {
    this.set(`profile:${userId}`, data, this.DEFAULT_TTL);
  }

  getProfile(userId: string): any | null {
    return this.get<any>(`profile:${userId}`);
  }

  // Cache per statistiche
  setStats(userId: string, data: any): void {
    this.set(`stats:${userId}`, data, this.DEFAULT_TTL);
  }

  getUserStats(userId: string): any | null {
    return this.get<any>(`stats:${userId}`);
  }
}

// Istanza globale
export const unifiedCache = new UnifiedCacheService();

// Auto-cleanup ogni 10 minuti
setInterval(() => {
  unifiedCache.cleanup();
}, 10 * 60 * 1000);

// Export per compatibilità
export default unifiedCache;

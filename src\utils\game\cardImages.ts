/**
 * Mapping delle immagini delle carte del gioco Marafone Romagnolo
 * Centralizzato per evitare duplicazioni
 */

// Full card images mapping using local images (updated to use new PNG cards)
export const cardImages: Record<string, Record<string, string>> = {
  coins: {
    A: "/images/cards/denari/A.png",
    "2": "/images/cards/denari/2.png",
    "3": "/images/cards/denari/3.png",
    "4": "/images/cards/denari/4.png",
    "5": "/images/cards/denari/5.png",
    "6": "/images/cards/denari/6.png",
    "7": "/images/cards/denari/7.png",
    J: "/images/cards/denari/J.png", // Jack = Fante
    H: "/images/cards/denari/H.png", // Horse = Cavallo
    K: "/images/cards/denari/K.png", // King = Re
  },
  cups: {
    A: "/images/cards/coppe/A.png",
    "2": "/images/cards/coppe/2.png",
    "3": "/images/cards/coppe/3.png",
    "4": "/images/cards/coppe/4.png",
    "5": "/images/cards/coppe/5.png",
    "6": "/images/cards/coppe/6.png",
    "7": "/images/cards/coppe/7.png",
    J: "/images/cards/coppe/J.png", // Jack = Fante
    H: "/images/cards/coppe/H.png", // Horse = Cavallo
    K: "/images/cards/coppe/K.png", // King = Re
  },
  swords: {
    A: "/images/cards/spade/A.png",
    "2": "/images/cards/spade/2.png",
    "3": "/images/cards/spade/3.png",
    "4": "/images/cards/spade/4.png",
    "5": "/images/cards/spade/5.png",
    "6": "/images/cards/spade/6.png",
    "7": "/images/cards/spade/7.png",
    J: "/images/cards/spade/J.png", // Jack = Fante
    H: "/images/cards/spade/H.png", // Horse = Cavallo
    K: "/images/cards/spade/K.png", // King = Re
  },
  clubs: {
    A: "/images/cards/bastoni/A.png",
    "2": "/images/cards/bastoni/2.png",
    "3": "/images/cards/bastoni/3.png",
    "4": "/images/cards/bastoni/4.png",
    "5": "/images/cards/bastoni/5.png",
    "6": "/images/cards/bastoni/6.png",
    "7": "/images/cards/bastoni/7.png",
    J: "/images/cards/bastoni/J.png", // Jack = Fante
    H: "/images/cards/bastoni/H.png", // Horse = Cavallo
    K: "/images/cards/bastoni/K.png", // King = Re
  },
};

// Funzione helper per ottenere l'immagine di una carta
export const getCardImage = (suit: string, rank: string): string => {
  return cardImages[suit]?.[rank] || "";
};

// Utility per gestire la generazione di username dalle email
// Questa funzione rispecchia la logica usata nel database

/**
 * Estrae i primi 18 caratteri della parte locale dell'email (prima della @)
 * @param email - L'indirizzo email da cui estrarre l'username
 * @returns Username troncato a 18 caratteri o 'Pulcino Romagnolo' se email non valida
 */
export const extractUsernameFromEmail = (
  email: string | null | undefined
): string => {
  if (!email || typeof email !== "string") {
    return "Pulcino Romagnolo";
  }

  // Controlla se l'email contiene @
  const atIndex = email.indexOf("@");
  if (atIndex === -1 || atIndex === 0) {
    return "Pulcino Romagnolo";
  }

  // Estrae la parte prima della @ e limita a 18 caratteri
  const localPart = email.substring(0, atIndex);
  return localPart.substring(0, 18);
};

/**
 * Valida e formatta un username per essere conforme alle regole
 * @param username - L'username da validare e formattare
 * @returns Username formattato o null se non valido
 */
export const validateAndFormatUsername = (
  username: string | null | undefined
): string | null => {
  if (!username || typeof username !== "string") {
    return null;
  }

  const trimmed = username.trim();

  // Controlla lunghezza minima e massima
  if (trimmed.length < 2 || trimmed.length > 18) {
    return null;
  }

  // Controlla caratteri validi (lettere, numeri, underscore, trattino)
  const validUsernameRegex = /^[a-zA-Z0-9_-]+$/;
  if (!validUsernameRegex.test(trimmed)) {
    return null;
  }

  return trimmed;
};

/**
 * Genera un username predefinito basato sull'email dell'utente
 * Questa funzione dovrebbe essere usata quando si crea un nuovo profilo
 * @param userEmail - Email dell'utente
 * @param fallbackName - Nome di fallback se disponibile
 * @returns Username generato
 */
export const generateDefaultUsername = (
  userEmail: string | null | undefined,
  fallbackName?: string | null
): string => {
  // Prima prova con l'email
  const emailUsername = extractUsernameFromEmail(userEmail);
  if (emailUsername !== "Pulcino Romagnolo") {
    return emailUsername;
  }

  // Se l'email non è valida, prova con il nome di fallback
  if (fallbackName) {
    const validatedName = validateAndFormatUsername(fallbackName);
    if (validatedName) {
      return validatedName;
    }
  }

  // Ultimo fallback - usa il titolo del livello 1
  const { getPlayerTitle } = require("../services/playerTitlesService");
  return getPlayerTitle(1).title;
};

import { useState, useCallback } from "react";
import { RefreshCcw, Users, Gamepad2, Trophy } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import ActionButton from "@/components/ui/ActionButton";
import FriendsLoadingSkeleton from "./FriendsLoadingSkeleton";
import PlayerProfileModal from "./PlayerProfileModal";
import { getPlayerTitle } from "@/services/playerTitlesService";
import { useNavigate } from "react-router-dom";

interface FriendData {
  id: string;
  username: string;
  level: number;
  xp: number;
  online: boolean;
  avatar_url?: string;
  games_won?: number;
  games_played?: number;
  win_rate?: number;
  created_at?: string;
  updated_at?: string;
}

interface FriendsListProps {
  friends: FriendData[];
  loading: boolean;
  onRefresh: () => void;
  currentUserId?: string;
  onRemoveFriend?: (playerId: string) => Promise<void>;
}

const FriendsList = ({
  friends,
  loading,
  onRefresh,
  currentUserId,
  onRemoveFriend,
}: FriendsListProps) => {
  const navigate = useNavigate();

  // Stati per la modale del profilo
  const [selectedPlayer, setSelectedPlayer] = useState<FriendData | null>(null);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);

  // Funzioni per gestire la modale del profilo
  const handlePlayerClick = useCallback((friend: FriendData) => {
    setSelectedPlayer(friend);
    setIsProfileModalOpen(true);
  }, []);
  const handleCloseProfileModal = useCallback(() => {
    setIsProfileModalOpen(false);
    setSelectedPlayer(null);
  }, []);

  // Converte FriendData a PlayerStats per la modale
  const convertToPlayerStats = useCallback((friend: FriendData) => {
    return {
      id: friend.id,
      username: friend.username,
      level: friend.level,
      xp: friend.xp,
      games_won: friend.games_won || 0,
      games_played: friend.games_played || 0,
      win_rate: friend.win_rate || 0,
      avatar_url: friend.avatar_url,
      created_at: friend.created_at,
      last_active: friend.updated_at,
    };
  }, []);

  return (
    <div>
      <div className="flex items-center justify-between mt-4 mb-2">
        <div>
          <h3
            className="font-semibold text-romagna-darkWood ml-1 "
            style={{
              fontFamily: "'DynaPuff', cursive",
              fontWeight: 500,
            }}
          >
            I tuoi amici
          </h3>
        </div>
        <ActionButton
          onClick={onRefresh}
          className="p-2 bg-amber-100 hover:bg-amber-200 text-amber-700 rounded-lg"
          disabled={loading}
        >
          <RefreshCcw className={`h-4 w-4 ${loading ? "animate-spin" : ""}`} />
        </ActionButton>
      </div>{" "}
      {loading ? (
        <FriendsLoadingSkeleton message="Caricamento amici..." count={3} />
      ) : friends.length === 0 ? (
        <Card className="border-2 border-amber-800/30 shadow-md bg-amber-50/80 backdrop-blur-sm">
          <CardContent className="p-6 text-center">
            <Users className="h-16 w-16 text-amber-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-romagna-darkWood mb-2">
              Nessun amico ancora
            </h3>
            <p className="text-romagna-darkWood/70 mb-4">
              Cerca altri giocatori per iniziare a costruire la tua rete di
              amici!
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-2">
          {" "}
          {friends.map((friend) => (
            <Card
              key={friend.id}
              onClick={() => handlePlayerClick(friend)}
              className="border-2 border-amber-800/30 shadow-md bg-amber-50/80 backdrop-blur-sm cursor-pointer transition-all duration-200 hover:scale-[1.01] hover:shadow-lg hover:bg-amber-100/80 mx-1"
            >
              <CardContent className="p-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    {/* Immagine del rank a sinistra */}
                    <div className="relative">
                      <div className="w-12 h-12 rounded-full bg-gradient-to-br from-amber-100 to-yellow-100 flex items-center justify-center border-2 border-amber-300">
                        <img
                          src={getPlayerTitle(friend.level).rankImage}
                          alt={getPlayerTitle(friend.level).title}
                          className="w-8 h-8 object-contain"
                        />
                      </div>
                      {/* Online status indicator */}
                      {/* <div
                        className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
                          friend.online ? "bg-green-500" : "bg-gray-400"
                        }`}
                      /> */}
                    </div>
                    <div>
                      <div className="text-md font-semibold text-romagna-darkWood">
                        {friend.username}
                      </div>
                      <p className="text-sm text-romagna-darkWood/70">
                        Livello {friend.level} • Vittorie:{" "}
                        {friend.games_won || 0}
                      </p>
                      {/* <p className="text-xs text-romagna-darkWood/50">
                        {friend.online ? "Online" : "Offline"}
                      </p> */}
                    </div>
                  </div>
                  {/* <div className="flex gap-2">
                    <ActionButton
                      onClick={() => {
                      }}
                      className="p-2 bg-green-100 hover:bg-green-200 text-green-700 rounded-lg"
                      disabled={!friend.online}
                    >
                      <Gamepad2 className="h-4 w-4" />
                    </ActionButton>
                    <ActionButton
                      onClick={() => navigate(`/player/${friend.id}`)}
                      className="p-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg"
                    >
                      <Trophy className="h-4 w-4" />
                    </ActionButton>
                  </div> */}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}{" "}
      {/* Modale profilo giocatore */}
      <PlayerProfileModal
        player={selectedPlayer ? convertToPlayerStats(selectedPlayer) : null}
        isOpen={isProfileModalOpen}
        onClose={handleCloseProfileModal}
        currentUserId={currentUserId}
        isFriend={true} // Sono sempre amici in questa lista
        onRemoveFriend={onRemoveFriend}
      />
    </div>
  );
};

export default FriendsList;

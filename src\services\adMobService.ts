import { Capacitor } from "@capacitor/core";
import { env } from "../config/environment";
import PurchaseService from "./purchaseService";
import AnalyticsService from "./analyticsService";

// Import condizionale di AdMob per evitare errori su web
let AdMob: typeof import("@capacitor-community/admob").AdMob | null = null;

// Inizializza l'import solo su piattaforme native
async function loadAdMob() {
  if (Capacitor.isNativePlatform() && !AdMob) {
    try {
      const admobModule = await import("@capacitor-community/admob");
      AdMob = admobModule.AdMob;
    } catch (error) {
      console.warn("AdMob plugin non disponibile:", error);
    }
  }
}

class AdMobService {
  private static instance: AdMobService;
  private isInitialized = false;
  private readonly appId = env.admob.appId;
  private readonly bannerAdUnitId = env.admob.bannerUnitId;

  static getInstance(): AdMobService {
    if (!AdMobService.instance) {
      AdMobService.instance = new AdMobService();
    }
    return AdMobService.instance;
  }

  /**
   * Inizializza AdMob una sola volta per tutta l'app
   */
  async initialize(): Promise<void> {
    if (this.isInitialized || !Capacitor.isNativePlatform()) {
      console.log(
        "AdMob: Inizializzazione saltata (già inizializzato o non nativo)"
      );
      return;
    }

    try {
      console.log("AdMob: Inizializzazione in corso...");

      // Carica il modulo AdMob dinamicamente
      await loadAdMob();

      if (!AdMob) {
        console.warn("AdMob: Plugin non disponibile");
        return;
      }

      await AdMob.initialize({
        testingDevices: [],
        initializeForTesting: false,
      });

      this.isInitialized = true;
      console.log("✅ AdMob inizializzato con successo");
    } catch (error) {
      console.error("❌ Errore nell'inizializzazione di AdMob:", error);
      // Non rilancia l'errore per evitare crash dell'app
      this.isInitialized = false;
    }
  }

  /**
   * Verifica se l'utente ha il pass premium
   * Controlla sia la configurazione che il sistema di acquisti RevenueCat
   */
  hasPremiumPass(): boolean {
    // Controlla prima la configurazione centralizzata (per debug/test)
    if (env.features.passPremium) {
      return true;
    }

    // Controlla il sistema di acquisti RevenueCat
    return PurchaseService.isPremium();
  }

  /**
   * Determina se gli annunci devono essere mostrati
   */
  shouldShowAds(): boolean {
    return (
      !this.hasPremiumPass() &&
      Capacitor.isNativePlatform() &&
      env.features.enableAdMobBanner
    );
  }

  /**
   * Ottiene l'ID dell'unità pubblicitaria del banner per la piattaforma corrente
   */
  getBannerAdUnitId(): string {
    if (Capacitor.getPlatform() === "ios") {
      return env.admob.iosBannerUnitId;
    }
    return this.bannerAdUnitId; // Android/Web
  }

  /**
   * Ottiene l'ID dell'app AdMob per la piattaforma corrente
   */
  getAppId(): string {
    if (Capacitor.getPlatform() === "ios") {
      return env.admob.iosAppId;
    }
    return this.appId; // Android/Web
  }

  /**
   * Verifica se AdMob è stato inizializzato
   */
  isAdMobInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Mostra il banner AdMob
   */
  async showBanner(): Promise<boolean> {
    if (!Capacitor.isNativePlatform() || !this.shouldShowAds()) {
      return false;
    }

    try {
      await loadAdMob();

      if (!AdMob) {
        console.warn("AdMob: Plugin non disponibile per showBanner");
        return false;
      }

      const { BannerAdSize, BannerAdPosition } = await import(
        "@capacitor-community/admob"
      );

      const options = {
        adId: this.bannerAdUnitId,
        adSize: BannerAdSize.BANNER,
        position: BannerAdPosition.BOTTOM_CENTER,
        margin: 0,
        isTesting: false,
      };

      // Mostra il banner con timeout per evitare hang infiniti
      const showPromise = AdMob.showBanner(options);
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error("Timeout showBanner")), 8000);
      });

      await Promise.race([showPromise, timeoutPromise]);
      console.log("✅ Banner AdMob mostrato");

      // 📊 ANALYTICS - Traccia impressione banner
      try {
        await AnalyticsService.trackAdImpression("banner");
      } catch (error) {
        console.warn("⚠️ Errore tracking impressione banner:", error);
      }

      return true;
    } catch (error) {
      console.error("❌ Errore nel mostrare il banner:", error);
      return false;
    }
  }

  /**
   * Nasconde il banner AdMob
   */
  async hideBanner(): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    try {
      await loadAdMob();

      if (!AdMob) {
        console.warn("AdMob: Plugin non disponibile per hideBanner");
        return;
      }

      // Nasconde il banner con timeout per evitare hang infiniti
      const hidePromise = AdMob.hideBanner();
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error("Timeout hideBanner")), 5000);
      });

      await Promise.race([hidePromise, timeoutPromise]);
      console.log("🧹 Banner AdMob nascosto");
    } catch (error) {
      // Non logga errori per timeout di hide, è normale se non c'è banner da nascondere
      if (error.message !== "Timeout hideBanner") {
        console.error("❌ Errore nel nascondere il banner:", error);
      }
    }
  }

  /**
   * Forza la pulizia di tutti i banner AdMob
   */
  async forceCleanup(): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    try {
      console.log("🧹 Pulizia forzata banner AdMob...");

      // Prova a nascondere il banner più volte per essere sicuri
      for (let i = 0; i < 2; i++) {
        try {
          await this.hideBanner();
          await new Promise((resolve) => setTimeout(resolve, 300));
        } catch (hideError) {
          console.log(`Tentativo hide ${i + 1} completato`);
        }
      }

      // Pausa finale per assicurarsi che tutto sia pulito
      await new Promise((resolve) => setTimeout(resolve, 500));
      console.log("✅ Pulizia forzata completata");
    } catch (error) {
      console.error("❌ Errore nella pulizia forzata:", error);
    }
  }
}

export default AdMobService.getInstance();

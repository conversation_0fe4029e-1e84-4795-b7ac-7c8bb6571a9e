import React from "react";
import { Share2, Heart } from "lucide-react";
import { Capacitor } from "@capacitor/core";

interface ShareButtonProps {
  className?: string;
}

const ShareButton: React.FC<ShareButtonProps> = ({ className = "" }) => {
  const handleShare = async () => {
    const shareData = {
      title: "Maraffa Romagnola",
      text: "Scopri il gioco di carte tradizionale romagnolo! 🃏",
      url: "https://play.google.com/store/apps/details?id=com.eliazavatta.maraffa",
    };

    try {
      if (Capacitor.isNativePlatform()) {
        // Su Android usa il plugin Capacitor Share nativo
        const { Share } = await import("@capacitor/share");
        await Share.share(shareData);
        console.log("✅ Condivisione nativa Android completata");
      } else {
        // Su web usa l'API Web Share se disponibile
        if (navigator.share) {
          await navigator.share(shareData);
          console.log("✅ Condivisione web completata");
        } else {
          // Fallback web: copia il link negli appunti
          await navigator.clipboard.writeText(shareData.url);
          console.log("✅ Link copiato negli appunti");

          // Mostra un messaggio temporaneo all'utente
          const originalText =
            document.querySelector("button span")?.textContent;
          const button = document.querySelector("button span");
          if (button) {
            button.textContent = "Link copiato!";
            setTimeout(() => {
              if (button)
                button.textContent = originalText || "Condividi Maraffa";
            }, 2000);
          }
        }
      }
    } catch (error) {
      console.log("Condivisione annullata dall'utente o errore:", error);
      // Non fare nulla se l'utente annulla la condivisione
    }
  };

  return (
    <div
      className={`bg-gradient-to-r from-romagna-rust to-romagna-gold text-white shadow-md ${className}`}
      style={{
        height: "50px",
        minHeight: "50px",
        maxHeight: "50px",
        overflow: "hidden",
        position: "relative",
        zIndex: 30,
      }}
    >
      <button
        onClick={handleShare}
        className="w-full h-full flex items-center justify-center gap-2 hover:from-romagna-rust/90 hover:to-romagna-gold/90 transition-all duration-300 active:scale-95 rustic-button"
        style={{
          background: "inherit",
          border: "none",
          color: "inherit",
          fontFamily: "Playfair Display, serif",
        }}
      >
        <Heart className="w-4 h-4 text-romagna-cream animate-pulse" />
        <span className="text-sm font-semibold tracking-wide">
          Condividi Maraffa
        </span>
        <Share2 className="w-4 h-4" />
      </button>
    </div>
  );
};

export default ShareButton;

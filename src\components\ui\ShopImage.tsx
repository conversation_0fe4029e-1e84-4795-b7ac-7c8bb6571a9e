/**
 * 🖼️ COMPONENTE IMMAGINE ULTRA-SEMPLICE - ZERO PROBLEMI!
 */

import React from "react";
import { cn } from "@/lib/utils";

interface ShopImageProps {
  src: string;
  alt: string;
  className?: string;
  containerClassName?: string;
  priority?: boolean;
}

const ShopImage: React.FC<ShopImageProps> = ({
  src,
  alt,
  className,
  containerClassName,
  priority = false,
}) => {
  return (
    <div className={cn("relative overflow-hidden ", containerClassName)}>
      <img
        src={src}
        alt={alt}
        className={cn(
          "w-full h-full object-cover transition-opacity duration-300",
          className
        )}
        loading={priority ? "eager" : "lazy"}
        onLoad={(e) => {
          // Mostra l'immagine quando caricata
          (e.target as HTMLImageElement).style.opacity = "1";
        }}
        onError={(e) => {
          // Nasconde l'immagine se errore
          (e.target as HTMLImageElement).style.opacity = "0";
        }}
        style={{ opacity: 0 }}
      />

      {/* Placeholder sempre visibile sotto */}
      <div className="absolute inset-0 flex items-center justify-center -z-10">
        <div className="text-center">
          <div className="w-8 h-8 bg-amber-200/50 rounded-lg flex items-center justify-center mb-1">
            <svg
              className="w-4 h-4 text-amber-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>
          <span className="text-xs text-amber-700">Immagine</span>
        </div>
      </div>
    </div>
  );
};

export default ShopImage;

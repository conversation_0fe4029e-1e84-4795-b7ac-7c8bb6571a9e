-- Aggiunge constraint unique sulla colonna username della tabella profiles
-- <PERSON>o assicura che ogni username sia unico a livello di database

-- Rimuove eventuali duplicati esistenti (se ci sono)
-- Prima aggiorna i duplicati aggiungendo un numero progressivo
WITH duplicates AS (
  SELECT id, username, 
         ROW_NUMBER() OVER (PARTITION BY username ORDER BY created_at) as rn
  FROM profiles 
  WHERE username IS NOT NULL
)
UPDATE profiles 
SET username = CONCAT(profiles.username, '_', duplicates.rn::text)
FROM duplicates 
WHERE profiles.id = duplicates.id 
  AND duplicates.rn > 1;

-- Ora aggiungi il constraint unique
ALTER TABLE profiles ADD CONSTRAINT unique_username UNIQUE (username);

-- Crea un indice per migliorare le performance delle query di ricerca username
CREATE INDEX IF NOT EXISTS idx_profiles_username ON profiles (username);

-- Commento: Questo assicura che ogni username sia unico nel database
-- e migliora le performance delle verifiche di disponibilità username

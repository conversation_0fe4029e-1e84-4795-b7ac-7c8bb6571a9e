-- Script per limitare gli username a massimo 18 caratteri
-- Aggiorna tutti gli username esistenti che superano i 18 caratteri

-- 1. Prima controlliamo quanti utenti hanno username troppo lunghi
SELECT 
    id, 
    username, 
    LENGTH(username) as current_length,
    LEFT(username, 18) as new_username
FROM profiles 
WHERE LENGTH(username) > 18
ORDER BY LENGTH(username) DESC;

-- 2. Aggiorna gli username troppo lunghi troncandoli a 18 caratteri
UPDATE profiles 
SET username = LEFT(username, 18)
WHERE LENGTH(username) > 18;

-- 3. Aggiungi un constraint per limitare la lunghezza futura degli username
-- Nota: <PERSON><PERSON> potrebbe fallire se ci sono già constraint esistenti
-- In tal caso, rimuovi prima il constraint esistente

-- Rimuovi constraint esistente se presente (opzionale)
-- ALTER TABLE profiles DROP CONSTRAINT IF EXISTS username_length_check;

-- Aggiungi nuovo constraint per massimo 18 caratteri
ALTER TABLE profiles 
ADD CONSTRAINT username_length_check 
CHECK (LENGTH(username) <= 18 AND LENGTH(username) >= 2);

-- 4. Verifica i risultati
SELECT 
    COUNT(*) as total_users,
    MAX(LENGTH(username)) as max_username_length,
    MIN(LENGTH(username)) as min_username_length
FROM profiles;

-- 5. Mostra alcuni esempi di username aggiornati
SELECT 
    id, 
    username, 
    LENGTH(username) as length,
    created_at
FROM profiles 
ORDER BY LENGTH(username) DESC, created_at DESC
LIMIT 10;

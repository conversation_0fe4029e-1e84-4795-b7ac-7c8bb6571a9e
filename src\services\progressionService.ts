import {
  calculateLevelFromXp,
  getXpRequiredForLevel,
  getXpForNextLevel as getXpForNextLevelNew,
  getProgressToNextLevel as getProgressToNextLevelNew,
  getEstimatedGamesForNextLevel,
  calculateGameXp,
} from "./experienceSystem";

// Helper function to get Supabase client dynamically
const getSupabaseClient = async () => {
  const { supabase } = await import("@/integrations/supabase/client");
  return supabase;
};

// Re-export functions from the new experience system for compatibility
export const getXpForNextLevel = getXpForNextLevelNew;
export const getProgressToNextLevel = getProgressToNextLevelNew;

/**
 * Calculate how many games needed for next level (legacy compatibility)
 */
export const getGamesForNextLevel = (xp: number, level: number): number => {
  return getEstimatedGamesForNextLevel(xp, level, "medium", 0.5);
};

/**
 * Update player statistics after an online game with new XP system
 * @param userId The user ID
 * @param gameParams Game parameters for XP calculation
 * @returns The updated level and XP
 */
export const updatePlayerStats = async (
  userId: string,
  gameParams: {
    isWinner: boolean;
    difficulty?: "easy" | "medium" | "hard";
    maraffeMade?: number;
    isPerfectGame?: boolean;
    isComeback?: boolean;
    isDominantWin?: boolean; // 🎯 NUOVO: Parametro per vittoria dominante
  }
): Promise<{
  level: number;
  xp: number;
  leveledUp: boolean;
} | null> => {
  try {
    const supabase = await getSupabaseClient();

    // Get current stats
    const { data: currentStats, error: statsError } = await supabase
      .from("game_stats")
      .select("games_played, games_won, level, xp")
      .eq("user_id", userId)
      .single();

    if (statsError) {
      console.error("Error fetching stats:", statsError);
      return null;
    }

    // Calculate new values
    const newGamesPlayed = (currentStats.games_played || 0) + 1;
    const newGamesWon =
      (currentStats.games_won || 0) + (gameParams.isWinner ? 1 : 0); // Use new XP system
    const xpResult = calculateGameXp({
      isWinner: gameParams.isWinner,
      difficulty: gameParams.difficulty || "medium", // Default to medium for online games
      maraffeMade: gameParams.maraffeMade,
      isPerfectGame: gameParams.isPerfectGame,
      isComeback: gameParams.isComeback,
      isDominantWin: gameParams.isDominantWin, // 🎯 NUOVO: Passa il parametro vittoria dominante
      // Note: For online games, we don't track daily wins or streaks yet
    });

    const newXp = (currentStats.xp || 0) + xpResult.totalXp;

    // Calculate new level based on XP using new system
    const oldLevel = currentStats.level || 1;
    const newLevel = calculateLevelFromXp(newXp);
    const leveledUp = newLevel > oldLevel;

    // 🔒 PROTEZIONE: Calcola valori solo incrementali (mai decrementa)
    const protectedGamesPlayed = Math.max(
      currentStats.games_played || 0,
      newGamesPlayed
    );
    const protectedGamesWon = Math.max(
      currentStats.games_won || 0,
      newGamesWon
    );
    const protectedLevel = Math.max(currentStats.level || 1, newLevel);
    const protectedXp = Math.max(currentStats.xp || 0, newXp);

    // Log per debug conflitti
    if (
      protectedGamesPlayed !== newGamesPlayed ||
      protectedGamesWon !== newGamesWon ||
      protectedLevel !== newLevel ||
      protectedXp !== newXp
    ) {
      console.log("🔒 Protezione attivata in progressionService:", {
        gamesPlayed: `${newGamesPlayed} → ${protectedGamesPlayed}`,
        gamesWon: `${newGamesWon} → ${protectedGamesWon}`,
        level: `${newLevel} → ${protectedLevel}`,
        xp: `${newXp} → ${protectedXp}`,
      });
    }

    // Update stats in database with protected values
    const { error: updateError } = await supabase
      .from("game_stats")
      .update({
        games_played: protectedGamesPlayed,
        games_won: protectedGamesWon,
        level: protectedLevel,
        xp: protectedXp,
        updated_at: new Date().toISOString(),
      })
      .eq("user_id", userId);

    if (updateError) {
      console.error("Error updating stats:", updateError);
      return null;
    }

    return {
      level: protectedLevel,
      xp: protectedXp,
      leveledUp: protectedLevel > oldLevel,
    };
  } catch (error) {
    console.error("Error in updatePlayerStats:", error);
    return null;
  }
};

/**
 * Calculate player level based on total XP (legacy compatibility)
 */
export const calculateLevel = calculateLevelFromXp;

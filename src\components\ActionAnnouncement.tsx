import React from "react";
import { AnimatePresence } from "framer-motion";
import GameActionBadge, { ActionType } from "@/components/GameActionBadge";

type ActionAnnouncementProps = {
  actionAnnouncement: {
    type: ActionType | null;
    playerIndex: number;
  };
  playerName: string;
};

const ActionAnnouncement: React.FC<ActionAnnouncementProps> = ({
  actionAnnouncement,
  playerName,
}) => {
  return (
    <AnimatePresence>
      {actionAnnouncement.type && (
        <GameActionBadge
          key={`action-${actionAnnouncement.type}-${actionAnnouncement.playerIndex}`}
          type={actionAnnouncement.type}
          playerName={playerName}
        />
      )}
    </AnimatePresence>
  );
};

export default ActionAnnouncement;

@echo off
REM Cambia directory alla root del progetto
cd /d "%~dp0\.."

echo ======================================
echo    Build e Installazione APK
echo    Marafone Romagnolo
echo ======================================
echo.

echo [1/6] Verifica dispositivo collegato...
call adb devices
if %ERRORLEVEL% NEQ 0 (
    echo Errore: ADB non trovato! Assicurati che Android SDK sia installato.
    pause
    exit /b 1
)

echo.
echo Dispositivi collegati:
call adb devices
echo.
echo Dispositivo rilevato! Continuando con la build...

echo.
echo [2/6] Pulizia e preparazione...
if exist "android\app\build\outputs\apk" (
    echo Rimozione vecchi APK...
    rmdir /S /Q "android\app\build\outputs\apk"
)

echo.
echo [3/6] Generazione build ottimizzata...
call npm run build
if %ERRORLEVEL% NEQ 0 (
    echo Errore durante la build! Uscita.
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo [4/6] Sincronizzazione con Android...
call npx cap sync android
if %ERRORLEVEL% NEQ 0 (
    echo Errore durante la sincronizzazione! Uscita.
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo [5/6] Compilazione APK...
cd android
call .\gradlew.bat assembleDebug
if %ERRORLEVEL% NEQ 0 (
    echo Errore durante la compilazione dell'APK! Uscita.
    pause
    cd ..
    exit /b %ERRORLEVEL%
)
cd ..

echo.
echo [6/6] Installazione APK sul dispositivo...

REM Crea il nome del file con timestamp
set TIMESTAMP=%date:~6,4%%date:~3,2%%date:~0,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set APK_FILENAME=maraffa-romagnola_%TIMESTAMP%.apk

REM Assicura che la cartella di output esista
if not exist "apk-output" mkdir apk-output

REM Copia l'APK nella cartella di output con il nuovo nome
copy "android\app\build\outputs\apk\debug\app-debug.apk" "apk-output\%APK_FILENAME%"

echo.
echo APK creato: apk-output\%APK_FILENAME%
echo.

REM Disinstalla la versione precedente se presente
echo Rimozione versione precedente (se presente)...
call adb uninstall it.maraffaromagnola.app 2>nul

echo.
echo Installazione nuova versione...
call adb install "apk-output\%APK_FILENAME%"
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Errore durante l'installazione!
    echo.
    echo Possibili cause:
    echo - Dispositivo non collegato
    echo - Debug USB non attivato
    echo - Spazio insufficiente
    echo - Errore di firma
    echo.
    echo L'APK è comunque disponibile in: apk-output\%APK_FILENAME%
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo ======================================
echo    Installazione completata!
echo ======================================
echo.
echo L'app è stata installata sul dispositivo.
echo APK salvato in: apk-output\%APK_FILENAME%
echo.
echo Vuoi avviare l'app sul dispositivo? (S/N)
set /p launchApp=

if /I "%launchApp%"=="S" (
    echo.
    echo Avvio dell'app...
    call adb shell am start -n it.maraffaromagnola.app/.MainActivity
    if %ERRORLEVEL% NEQ 0 (
        echo Errore nell'avvio automatico. Avvia manualmente l'app dal dispositivo.
    ) else (
        echo App avviata con successo!
    )
)

echo.
echo Operazione completata!
pause

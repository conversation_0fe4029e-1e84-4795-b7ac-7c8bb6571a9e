import { createRoot } from "react-dom/client";
import App from "./App.tsx";
import "./index.css";
import { initCapacitor } from "./capacitor";
import { audioContextManager } from "./utils/audio/AudioContextManager";

// Inizializza Capacitor
initCapacitor().catch(console.error);

// 🎵 Inizializza il sistema audio per risolvere i warning AudioContext
audioContextManager; // Inizializza il singleton

createRoot(document.getElementById("root")!).render(<App />);

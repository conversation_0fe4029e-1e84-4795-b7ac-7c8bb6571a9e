/**
 * 🎯 HOOK UNIFICATO PER STORAGE
 * Sostituisce useUserState e localStatsService
 */

import { useState, useEffect, useCallback } from "react";
import {
  loadUnifiedData,
  saveUnifiedData,
  updateGameStats,
  updateGameSettings,
  updateAudioSettings,
  updateUserProfile,
  resetAllData,
  type UnifiedUserData,
} from "@/services/unifiedStorageService";
import { repairStats } from "@/services/statsManager";

export const useUnifiedStorage = () => {
  const [data, setData] = useState<UnifiedUserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Carica dati iniziali e ascolta aggiornamenti
  useEffect(() => {
    const loadData = async () => {
      try {
        // 🔄 Ripara e sincronizza le statistiche prima di caricare
        repairStats();

        const userData = loadUnifiedData();
        setData(userData);
      } catch (error) {
        console.error("Errore nel caricamento dati:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();

    // 🎯 ASCOLTA AGGIORNAMENTI STATISTICHE (con debounce per evitare loop)
    let debounceTimer: NodeJS.Timeout;
    const handleStatsUpdate = () => {
      // Debounce per evitare aggiornamenti troppo frequenti
      clearTimeout(debounceTimer);
      debounceTimer = setTimeout(() => {
        console.log(
          "🔄 useUnifiedStorage: Ricarico dati dopo aggiornamento statistiche"
        );
        loadData();
      }, 500); // Attendi 500ms prima di ricaricare
    };

    window.addEventListener("statsUpdated", handleStatsUpdate);

    return () => {
      clearTimeout(debounceTimer);
      window.removeEventListener("statsUpdated", handleStatsUpdate);
    };
  }, []);

  // Aggiorna dati e stato locale
  const updateData = useCallback((updates: Partial<UnifiedUserData>) => {
    saveUnifiedData(updates);
    setData((prev) => (prev ? { ...prev, ...updates } : null));
  }, []);

  // Funzioni specifiche
  const recordGameResult = useCallback((won: boolean) => {
    updateGameStats(won);
    const newData = loadUnifiedData();
    setData(newData);
  }, []);

  const updateSettings = useCallback(
    (settings: {
      difficulty?: "easy" | "medium" | "hard";
      victoryPoints?: "21" | "31" | "41";
      selectedTableMat?: string;
    }) => {
      updateGameSettings(settings);
      updateData(settings);
    },
    [updateData]
  );

  const updateAudio = useCallback(
    (settings: {
      audioEnabled?: boolean;
      soundEffectsEnabled?: boolean;
      musicEnabled?: boolean;
      masterVolume?: number;
      soundEffectsVolume?: number;
      musicVolume?: number;
    }) => {
      updateAudioSettings(settings);
      updateData(settings);
    },
    [updateData]
  );

  const updateProfile = useCallback(
    (profile: { username?: string; rank?: string; title?: string }) => {
      updateUserProfile(profile);
      updateData(profile);
    },
    [updateData]
  );

  const resetData = useCallback(() => {
    resetAllData();
    const newData = loadUnifiedData();
    setData(newData);
  }, []);

  return {
    // Stato
    data,
    isLoading,

    // Funzioni
    updateData,
    recordGameResult,
    updateSettings,
    updateAudio,
    updateProfile,
    resetData,

    // Getter di convenienza
    get stats() {
      return data
        ? {
            level: data.level,
            xp: data.xp,
            gamesPlayed: data.gamesPlayed,
            gamesWon: data.gamesWon,
            winRate: data.winRate,
            currentWinStreak: data.currentWinStreak,
            bestWinStreak: data.bestWinStreak,
          }
        : null;
    },

    get settings() {
      return data
        ? {
            difficulty: data.difficulty,
            victoryPoints: data.victoryPoints,
            selectedTableMat: data.selectedTableMat,
          }
        : null;
    },

    get audio() {
      return data
        ? {
            audioEnabled: data.audioEnabled,
            soundEffectsEnabled: data.soundEffectsEnabled,
            musicEnabled: data.musicEnabled,
            masterVolume: data.masterVolume,
            soundEffectsVolume: data.soundEffectsVolume,
            musicVolume: data.musicVolume,
          }
        : null;
    },

    get profile() {
      return data
        ? {
            username: data.username,
            rank: data.rank,
            title: data.title,
            isOfflineMode: data.isOfflineMode,
          }
        : null;
    },
  };
};

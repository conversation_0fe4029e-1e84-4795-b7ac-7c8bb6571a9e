import { useState, useEffect } from "react";

const TERMS_ACCEPTED_KEY = "maraffa_terms_accepted";
const CURRENT_TERMS_VERSION = "2.0";

// Hook per gestire l'accettazione dei termini
export const useTermsAcceptance = () => {
  const [needsAcceptance, setNeedsAcceptance] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Controlla se l'utente ha già accettato i termini
    const checkTermsAcceptance = () => {
      try {
        const acceptedData = localStorage.getItem(TERMS_ACCEPTED_KEY);

        if (!acceptedData) {
          // Prima volta - mostra la modale
          setNeedsAcceptance(true);
        } else {
          const parsed = JSON.parse(acceptedData);

          // Controlla se la versione è aggiornata
          if (parsed.version !== CURRENT_TERMS_VERSION) {
            // Versione termini aggiornata - richiedi nuova accettazione
            setNeedsAcceptance(true);
          } else {
            // Termini già accettati e aggiornati
            setNeedsAcceptance(false);
          }
        }
      } catch (error) {
        console.error("Errore nel controllo accettazione termini:", error);
        // In caso di errore, richiedi accettazione per sicurezza
        setNeedsAcceptance(true);
      }

      setIsLoading(false);
    };

    checkTermsAcceptance();
  }, []);

  const acceptTerms = () => {
    try {
      const acceptanceData = {
        version: CURRENT_TERMS_VERSION,
        timestamp: new Date().toISOString(),
        accepted: true,
      };

      localStorage.setItem(TERMS_ACCEPTED_KEY, JSON.stringify(acceptanceData));
      setNeedsAcceptance(false);
    } catch (error) {
      console.error("Errore nel salvare accettazione termini:", error);
    }
  };

  const declineTerms = () => {
    // L'utente ha rifiutato i termini - l'app dovrebbe chiudersi
    // La logica di chiusura è gestita nella modale stessa
    console.log("Utente ha rifiutato i termini - app dovrebbe chiudersi");

    // Reset dello stato per sicurezza
    try {
      localStorage.removeItem(TERMS_ACCEPTED_KEY);
    } catch (error) {
      console.error("Errore nel reset del localStorage:", error);
    }
  };

  return {
    needsAcceptance,
    isLoading,
    acceptTerms,
    declineTerms,
  };
};

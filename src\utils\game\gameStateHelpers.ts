import {
  Card as CardType,
  Suit,
  suitNames,
  suitImages,
  getWinningCard,
} from "./cardUtils";
import { GameState } from "./gameLogic";

// Costante per le posizioni dei giocatori
export const positions = ["bottom", "right", "top", "left"] as const;

export const getTrumpSuitName = (trumpSuit: Suit | null): string => {
  if (!trumpSuit) return "";
  return suitNames[trumpSuit] || "";
};

export const getTrumpSuitImage = (trumpSuit: Suit | null): string => {
  if (!trumpSuit) return "";
  return suitImages[trumpSuit];
};

export const isCanDeclareVictory = (gameState: GameState): boolean => {
  return gameState.roundScore[gameState.players[0].team] > 60;
};

export const determineTrickWinner = (
  currentTrick: CardType[],
  leadSuit: Suit | null,
  trumpSuit: Suit | null
): number => {
  if (!currentTrick || currentTrick.length === 0) return 0;

  // Trova la carta vincente considerando il seme di briscola e il seme di uscita
  const winningCard = getWinningCard(currentTrick, leadSuit!, trumpSuit!);
  const winningIndex = currentTrick.findIndex(
    (card) => card.id === winningCard.id
  );

  return winningIndex;
};

// Funzione per ottenere la posizione delle carte giocate
export const getPlayedCardPosition = (
  trickIndex: number,
  gameState: GameState,
  isDesktopLayout?: boolean
) => {
  const leadPlayerIndex = gameState.leadPlayer;
  const adjustedIndex = (leadPlayerIndex + trickIndex) % 4; // Posizioni responsive basate sul layout
  // Su mobile usiamo distanze più piccole visto che il tavolo è più piccolo
  const distance = isDesktopLayout ? "2" : "4"; // 4rem su desktop, 1rem su mobile

  const basePositions = [
    {
      className: `absolute bottom-${distance} left-1/2 -translate-x-1/2`,
      zIndex: 25, // Aumentato per apparire sopra il centro della briscola (z-20)
    },
    {
      className: `absolute top-1/2 right-${distance} -translate-y-1/2`,
      zIndex: 25,
    },
    {
      className: `absolute top-${distance} left-1/2 -translate-x-1/2`,
      zIndex: 25,
    },
    {
      className: `absolute top-1/2 left-${distance} -translate-y-1/2`,
      zIndex: 25,
    },
  ];

  // Offset più grandi per desktop per evitare sovrapposizioni
  // Usiamo solo valori supportati da Tailwind (multipli di 4 fino a 96)
  const offsetValues = [8, 16, 24, 32]; // Valori tailwind sicuri
  const offsetIndex = Math.min(trickIndex, offsetValues.length - 1);
  const offset = offsetValues[offsetIndex];

  const offsetClass = [
    `translate-y-${offset}`,
    `-translate-x-${offset}`,
    `-translate-y-${offset}`,
    `translate-x-${offset}`,
  ][adjustedIndex];
  return {
    ...basePositions[adjustedIndex],
    className: `${basePositions[adjustedIndex].className} ${offsetClass}`,
  };
};

// Funzione per ottenere il team del giocatore
export const getPlayerTeam = (index: number, gameState: GameState) =>
  gameState.players[index]?.team;

// Funzione per ottenere punti correnti delle squadre
export const getCurrentTeamPoints = (
  gameState: GameState
): [number, number] => {
  if (!gameState.teams) return [0, 0];

  return [
    gameState.teams[0].currentRoundPoints || 0,
    gameState.teams[1].currentRoundPoints || 0,
  ];
};

// Servizio per gestire i titoli e le icone dei giocatori basati sul livello

export interface PlayerTitle {
  level: number;
  title: string;
  description: string;
  rankImage: string;
}

// Lista completa dei titoli ordinata per livello
// Progressione ottimizzata: inizio veloce, poi sempre più difficile
export const PLAYER_TITLES: PlayerTitle[] = [
  {
    level: 1,
    title: "Pulcino Romagnolo",
    description: "Novizio nelle terre di Romagna",
    rankImage: "/images/rank/pulcino.png",
  },
  {
    level: 3,
    title: "Galletto Curioso",
    description: "Esplora i segreti del Marafone romagnolo",
    rankImage: "/images/rank/galletto_curioso.png",
  },
  {
    level: 7,
    title: "Gallo Coraggioso",
    description: "Non teme le sfide",
    rankImage: "/images/rank/gallo_comandante.png",
  },
  {
    level: 12,
    title: "Gallo Stratega",
    description: "Pianifica come un vero astuto",
    rankImage: "/images/rank/gallo_stratega.png",
  },
  {
    level: 20,
    title: "Gallo Maestro del Bluff",
    description: "Inganna con l'astuzia",
    rankImage: "/images/rank/gallo_bluff.png",
  },
  {
    level: 30,
    title: "Gallo Veggente",
    description: "Prevede le mosse degli avversari",
    rankImage: "/images/rank/gallo_leggenda.png",
  },
  {
    level: 45,
    title: "Gallo Folgorante",
    description: "TI lascia folgorato",
    rankImage: "/images/rank/gallo_folgorante.png",
  },
  {
    level: 65,
    title: "Gallo Eterno",
    description: "Le sue gesta echeggiano da Rimini a Ravenna",
    rankImage: "/images/rank/gallo_eterno.png",
  },
  {
    level: 90,
    title: "Imperatore Gallo",
    description: "Sovrano indiscusso di tutti i bar",
    rankImage: "/images/rank/gallo_imperatore.png",
  },
  {
    level: 120,
    title: "Gallo della Leggenda",
    description: "Immortale nella storia del Marafone",
    rankImage: "/images/rank/gallo_astrale.png",
  },
];

/**
 * Ottiene il titolo appropriato per un determinato livello
 * Restituisce il titolo con livello più alto che non supera il livello del giocatore
 * @param playerLevel Livello attuale del giocatore
 * @returns Oggetto PlayerTitle corrispondente
 */
export const getPlayerTitle = (playerLevel: number): PlayerTitle => {
  // Trova il titolo con il livello più alto che non supera il livello del giocatore
  let currentTitle = PLAYER_TITLES[0]; // Default al primo titolo

  for (const title of PLAYER_TITLES) {
    if (title.level <= playerLevel) {
      currentTitle = title;
    } else {
      break; // Interrompe quando trova un livello superiore al giocatore
    }
  }

  return currentTitle;
};

/**
 * Ottiene il prossimo titolo che il giocatore può raggiungere
 * @param playerLevel Livello attuale del giocatore
 * @returns Oggetto PlayerTitle del prossimo titolo o null se è già al massimo
 */
export const getNextPlayerTitle = (playerLevel: number): PlayerTitle | null => {
  for (const title of PLAYER_TITLES) {
    if (title.level > playerLevel) {
      return title;
    }
  }
  return null; // Il giocatore ha già raggiunto il titolo massimo
};

/**
 * Verifica se il giocatore ha appena sbloccato un nuovo titolo
 * @param oldLevel Livello precedente
 * @param newLevel Nuovo livello
 * @returns Nuovo titolo sbloccato o null se non c'è stato upgrade
 */
export const checkForTitleUpgrade = (
  oldLevel: number,
  newLevel: number
): PlayerTitle | null => {
  const oldTitle = getPlayerTitle(oldLevel);
  const newTitle = getPlayerTitle(newLevel);

  if (oldTitle.level !== newTitle.level) {
    return newTitle;
  }

  return null;
};

/**
 * Ottiene tutti i titoli disponibili ordinati per livello
 * @returns Array di tutti i titoli
 */
export const getAllTitles = (): PlayerTitle[] => {
  return [...PLAYER_TITLES];
};

/**
 * Calcola quanti livelli mancano al prossimo titolo
 * @param playerLevel Livello attuale del giocatore
 * @returns Numero di livelli mancanti o 0 se è già al massimo
 */
export const getLevelsToNextTitle = (playerLevel: number): number => {
  const nextTitle = getNextPlayerTitle(playerLevel);
  if (!nextTitle) return 0;
  return nextTitle.level - playerLevel;
};

import { SoundType } from "./AudioManager";

// Mappatura dei suoni del gioco ai file audio delle carte di Kenney Casino Audio
export const KENNEY_SOUND_MAPPINGS: Record<SoundType, string[]> = {
  // Suoni principali delle carte
  cardPlay: [
    "/sounds/effects/card-place-1.ogg",
    "/sounds/effects/card-place-2.ogg",
    "/sounds/effects/card-place-3.ogg",
    "/sounds/effects/card-place-4.ogg",
  ],
  cardShuffle: ["/sounds/effects/card-shuffle.ogg"],
  cardDeal: [
    "/sounds/effects/card-slide-1.ogg",
    "/sounds/effects/card-slide-2.ogg",
    "/sounds/effects/card-slide-3.ogg",
    "/sounds/effects/card-slide-4.ogg",
  ],
  cardFlip: [
    "/sounds/effects/card-slide-5.ogg",
    "/sounds/effects/card-slide-6.ogg",
  ],
  cardSnap: [
    "/sounds/effects/card-shove-3.ogg",
    "/sounds/effects/card-shove-4.ogg",
  ],
  cardGather: [
    "/sounds/effects/card-slide-7.ogg",
    "/sounds/effects/card-slide-8.ogg",
  ],
  // Suoni specifici per momenti di gioco
  cardFan: ["/sounds/effects/card-fan-1.ogg", "/sounds/effects/card-fan-2.ogg"],
  cardShove: [
    "/sounds/effects/card-shove-1.ogg",
    "/sounds/effects/card-shove-2.ogg",
  ],
  cardPackTakeOut: [
    "/sounds/effects/card-slide-1.ogg",
    "/sounds/effects/card-slide-2.ogg",
    "/sounds/effects/card-slide-3.ogg",
  ],
  cardShuffleHandEnd: ["/sounds/effects/card-shuffle.ogg"],
  // Per gli altri suoni usiamo il nuovo click.wav per i menu e variazioni delle carte per il resto
  buttonClick: ["/sounds/effects/click.wav"],
  menuOpen: ["/sounds/effects/click.wav"],
  menuClose: ["/sounds/effects/click.wav"],
  turnStart: ["/sounds/effects/card-slide-7.ogg"],
  gameStart: ["/sounds/effects/card-shuffle.ogg"],
  gameEnd: ["/sounds/effects/card-shuffle.ogg"],
  roundEnd: ["/sounds/effects/card-shuffle.ogg"],
  victory: ["/sounds/effects/card-place-1.ogg"],
  "victory-cheer": [
    "/sounds/effects/card-place-1.ogg",
    "/sounds/effects/card-place-2.ogg",
  ],
  defeat: ["/sounds/effects/card-shove-3.ogg"],
  "trump-select": ["/sounds/effects/card-slide-8.ogg"],
  error: ["/sounds/effects/card-shove-4.ogg"],
  success: ["/sounds/effects/card-place-1.ogg"],
  warning: ["/sounds/effects/card-slide-8.ogg"],
  notification: ["/sounds/effects/card-slide-7.ogg"],
  maraffa: ["/sounds/effects/maraffa.wav"],
  gameOver: ["/sounds/effects/fine-partita.wav"],
};

// Mappatura per i file musicali di sottofondo
export const MUSIC_MAPPINGS: Record<string, string> = {
  menu: "/sounds/music/guitar-jazz.mp3",
  game: "/sounds/music/guitar-jazz.mp3",
  victory: "/sounds/music/guitar-jazz.mp3",
  intense: "/sounds/music/guitar-jazz.mp3",
  ambient: "/sounds/music/guitar-jazz.mp3",
};

// Parametri per modificare i suoni base per creare varietà
export const SOUND_VARIATIONS: Record<
  SoundType,
  { pitch?: number; volume?: number }
> = {
  // Suoni principali delle carte
  cardPlay: { pitch: 1.0, volume: 0.8 },
  cardShuffle: { pitch: 1.0, volume: 0.9 },
  cardDeal: { pitch: 1.1, volume: 0.7 },
  cardFlip: { pitch: 1.2, volume: 0.8 },
  cardSnap: { pitch: 0.9, volume: 0.9 },
  cardGather: { pitch: 0.8, volume: 0.7 },
  cardFan: { pitch: 0.8, volume: 0.7 },
  cardShove: { pitch: 1.0, volume: 0.8 },
  cardPackTakeOut: { pitch: 1.0, volume: 0.8 },
  cardShuffleHandEnd: { pitch: 0.9, volume: 0.9 },
  // Suoni UI - utilizzano click.wav con pitch diversi per differenziare i menu
  buttonClick: { pitch: 1.0, volume: 0.7 }, // Click normale per pulsanti
  menuOpen: { pitch: 1.2, volume: 0.8 }, // Pitch più alto per apertura menu
  menuClose: { pitch: 0.8, volume: 0.8 }, // Pitch più basso per chiusura menu
  turnStart: { pitch: 1.3, volume: 0.7 },
  gameStart: { pitch: 0.8, volume: 1.0 },
  gameEnd: { pitch: 0.5, volume: 0.9 },
  roundEnd: { pitch: 0.6, volume: 0.8 },

  // Suoni di feedback
  victory: { pitch: 1.5, volume: 1.0 },
  "victory-cheer": { pitch: 1.2, volume: 1.0 },
  defeat: { pitch: 0.7, volume: 0.9 },
  "trump-select": { pitch: 1.4, volume: 0.8 },
  error: { pitch: 0.6, volume: 0.9 },
  success: { pitch: 2.0, volume: 0.8 },
  warning: { pitch: 1.6, volume: 0.7 },
  notification: { pitch: 1.8, volume: 0.6 },
  maraffa: { pitch: 1.0, volume: 1.0 },
  gameOver: { pitch: 1.0, volume: 0.9 },
};

/**
 * Ottiene un file audio casuale per il tipo di suono specificato
 */
export const getRandomSoundFile = (soundType: SoundType): string => {
  const files = KENNEY_SOUND_MAPPINGS[soundType];
  return files[Math.floor(Math.random() * files.length)];
};

/**
 * Ottiene le variazioni per un tipo di suono
 */
export const getSoundVariation = (soundType: SoundType) => {
  return SOUND_VARIATIONS[soundType] || { pitch: 1.0, volume: 0.8 };
};

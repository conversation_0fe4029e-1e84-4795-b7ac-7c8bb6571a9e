import React, { useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Card from "@/components/Card";
import { Card as CardType } from "@/utils/game/cardUtils";
import { GameState } from "@/utils/game/gameLogic";
import { cn } from "@/lib/utils";
import { audioManager } from "@/utils/audio/AudioManager";

type TrickGatherAnimationProps = {
  cards: CardType[];
  gameState: GameState;
  trickWinner: number;
  isVisible: boolean;
  getPlayerTeam: (index: number) => number;
  isMobile: boolean;
  onAnimationComplete?: () => void;
};

const TrickGatherAnimation: React.FC<TrickGatherAnimationProps> = ({
  cards,
  gameState,
  trickWinner,
  isVisible,
  getPlayerTeam,
  isMobile,
  onAnimationComplete,
}) => {
  // Posizioni dei giocatori (0=bottom, 1=right, 2=top, 3=left)
  // Responsive in base al layout
  const getPlayerPosition = (playerIndex: number) => {
    // Su desktop usiamo posizioni più distanti dal centro per il tavolo più grande
    const offset = isMobile ? 25 : 35; // Percentuale di distanza dal centro

    const positions = [
      { x: "50%", y: `${50 + offset}%` }, // bottom - più lontano su desktop
      { x: `${50 + offset}%`, y: "50%" }, // right - più lontano su desktop
      { x: "50%", y: `${50 - offset}%` }, // top - più lontano su desktop
      { x: `${50 - offset}%`, y: "50%" }, // left - più lontano su desktop
    ];
    return positions[playerIndex];
  };
  // Posizioni iniziali delle carte (al centro, disposte a ventaglio dopo il gathering)
  const getInitialCardPosition = (cardIndex: number) => {
    const leadPlayerIndex = gameState.leadPlayer || 0;
    const adjustedIndex = (leadPlayerIndex + cardIndex) % 4;

    // Posizioni leggermente sfalsate per effetto ventaglio al centro
    // Riflettono le posizioni finali dell'animazione di gathering
    const positions = [
      { x: "50%", y: "52%", rotate: -5 }, // bottom card (leggermente sotto il centro)
      { x: "52%", y: "50%", rotate: -10 }, // right card (leggermente a destra del centro)
      { x: "50%", y: "48%", rotate: 5 }, // top card (leggermente sopra il centro)
      { x: "48%", y: "50%", rotate: 10 }, // left card (leggermente a sinistra del centro)
    ];

    return positions[adjustedIndex];
  };

  const winnerPosition = getPlayerPosition(trickWinner);
  const team = getPlayerTeam(trickWinner);

  // 🔊 Riproduci suono card-shove quando le 4 carte vanno verso il giocatore che ha preso
  useEffect(() => {
    if (isVisible && cards.length === 4) {
      // Suono di carte che si muovono dal centro verso il giocatore vincente
      audioManager.playSound("cardShove");
    }
  }, [isVisible, cards.length]);

  // Calcola direzione per uscire completamente dallo schermo
  const getExitPosition = () => {
    const centerX = 50;
    const centerY = 50;
    const targetX = parseFloat(winnerPosition.x);
    const targetY = parseFloat(winnerPosition.y);

    // Calcola la direzione e estendi oltre i bordi dello schermo
    const directionX = targetX - centerX;
    const directionY = targetY - centerY; // Moltiplica per un fattore MOLTO grande per uscire completamente dallo schermo
    // Su desktop serve più spinta perché le posizioni sono più distanti
    const exitFactor = isMobile ? 30 : 45; // Aumentato per desktop

    return {
      x: `${centerX + directionX * exitFactor}%`,
      y: `${centerY + directionY * exitFactor}%`,
    };
  };

  const exitPosition = getExitPosition(); // ANIMAZIONE DI SCORRIMENTO VELOCE DAL CENTRO VERSO IL VINCITORE
  const gatherVariants = {
    initial: (cardIndex: number) => {
      const initialPos = getInitialCardPosition(cardIndex);
      return {
        x: initialPos.x,
        y: initialPos.y,
        scale: 0.9, // Scala ridotta come dopo il gathering
        opacity: 1,
        rotate: initialPos.rotate, // Usa la rotazione del ventaglio
      };
    },
    slideToWinner: {
      x: exitPosition.x, // USCITA COMPLETA DALLO SCHERMO
      y: exitPosition.y, // USCITA COMPLETA DALLO SCHERMO
      scale: 0.6, // Mantiene dimensione visibile durante il movimento
      opacity: 1, // Resta visibile durante tutto il movimento
      rotate: 0, // Raddrizza le carte durante il movimento
      transition: {
        duration: 0.4, // VELOCE come prima
        ease: "easeIn", // Accelera verso la fine
        delay: 0.1, // Partenza immediata dal centro
      },
    },
    fadeOut: {
      opacity: 0,
      scale: 0.3,
      transition: {
        duration: 0.15, // VELOCE come prima
        delay: 0.5, // VELOCE come prima
      },
    },
  };

  // Calcola direzione per effetti visivi
  const getSlideDirection = () => {
    const centerX = 50;
    const centerY = 50;
    const targetX = parseFloat(winnerPosition.x);
    const targetY = parseFloat(winnerPosition.y);

    return {
      deltaX: (targetX - centerX) / 100,
      deltaY: (targetY - centerY) / 100,
      angle: Math.atan2(targetY - centerY, targetX - centerX) * (180 / Math.PI),
    };
  };

  const slideDirection = getSlideDirection();
  return (
    <AnimatePresence>
      {isVisible && (
        <div className="absolute inset-0 pointer-events-none z-50">
          {/* Carte animate con scorrimento */}
          {cards.map((card, index) => {
            const playerIndex = (gameState.leadPlayer + index) % 4;
            const cardTeam = getPlayerTeam(playerIndex);

            return (
              <motion.div
                key={`trick-gather-${card.id}`}
                className="absolute"
                custom={index}
                variants={gatherVariants}
                initial="initial"
                animate={["slideToWinner", "fadeOut"]}
                style={{
                  left: 0,
                  top: 0,
                  transform: "translate(-50%, -50%)",
                  transformOrigin: "center",
                  zIndex: 10 + index,
                }}
                onAnimationComplete={() => {
                  if (index === cards.length - 1 && onAnimationComplete) {
                    onAnimationComplete();
                  }
                }}
              >
                <div
                  className={cn(
                    "p-1 rounded-lg shadow-lg border border-white/30",
                    cardTeam === 0 ? "bg-red-500/60" : "bg-yellow-500/60"
                  )}
                >
                  <Card
                    card={card}
                    isRevealed={true}
                    scale={isMobile ? "sm" : "md"}
                  />
                </div>
              </motion.div>
            );
          })}
        </div>
      )}
    </AnimatePresence>
  );
};

export default TrickGatherAnimation;

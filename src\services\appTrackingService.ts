/**
 * 🍎 iOS App Tracking Transparency (ATT) Service
 * Required for iOS 14.5+ when using analytics or advertising
 */

import { Capacitor } from "@capacitor/core";

// Import condizionale per iOS App Tracking Transparency
let AppTrackingTransparency: any = null;

async function loadATT() {
  if (Capacitor.getPlatform() === "ios") {
    try {
      // You'll need to install: npm install capacitor-app-tracking-transparency
      const attModule = await import("capacitor-app-tracking-transparency");
      AppTrackingTransparency = attModule.AppTrackingTransparency;
      console.log("✅ App Tracking Transparency loaded");
    } catch (error) {
      console.warn("⚠️ App Tracking Transparency not available:", error);
    }
  }
}

export class AppTrackingService {
  private static instance: AppTrackingService;
  private hasRequestedPermission = false;

  static getInstance(): AppTrackingService {
    if (!AppTrackingService.instance) {
      AppTrackingService.instance = new AppTrackingService();
    }
    return AppTrackingService.instance;
  }

  /**
   * Request tracking permission on iOS
   * Must be called before initializing analytics or ads
   */
  async requestTrackingPermission(): Promise<boolean> {
    if (Capacitor.getPlatform() !== "ios" || this.hasRequestedPermission) {
      return true; // Allow on non-iOS or if already requested
    }

    try {
      await loadATT();
      
      if (!AppTrackingTransparency) {
        console.log("📱 ATT not available, proceeding without tracking");
        return false;
      }

      // Check current status
      const status = await AppTrackingTransparency.getTrackingAuthorizationStatus();
      console.log("📱 Current ATT status:", status);

      if (status.status === "authorized") {
        this.hasRequestedPermission = true;
        return true;
      }

      if (status.status === "denied" || status.status === "restricted") {
        this.hasRequestedPermission = true;
        return false;
      }

      // Request permission
      const result = await AppTrackingTransparency.requestTrackingAuthorization();
      this.hasRequestedPermission = true;
      
      console.log("📱 ATT permission result:", result);
      return result.status === "authorized";
    } catch (error) {
      console.error("❌ Error requesting tracking permission:", error);
      this.hasRequestedPermission = true;
      return false;
    }
  }

  /**
   * Check if tracking is authorized
   */
  async isTrackingAuthorized(): Promise<boolean> {
    if (Capacitor.getPlatform() !== "ios") {
      return true; // Allow on non-iOS
    }

    try {
      await loadATT();
      
      if (!AppTrackingTransparency) {
        return false;
      }

      const status = await AppTrackingTransparency.getTrackingAuthorizationStatus();
      return status.status === "authorized";
    } catch (error) {
      console.error("❌ Error checking tracking status:", error);
      return false;
    }
  }
}

export default AppTrackingService.getInstance();

import React from "react";
import { useGameMobileLayout } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";
import { ArrowUp } from "lucide-react";

interface MobileGameLayoutProps {
  children: React.ReactNode;
  className?: string;
  hideChronicleLabel?: boolean;
}

const MobileGameLayout: React.FC<MobileGameLayoutProps> = ({
  children,
  className,
  hideChronicleLabel = false,
}) => {
  const { isMobile, styles } = useGameMobileLayout();

  if (!isMobile) {
    return <div className={className}>{children}</div>;
  }

  // Find and reorganize children based on data attributes
  const childrenArray = React.Children.toArray(children);
  const gameBoard = childrenArray.find(
    (child) =>
      React.isValidElement(child) &&
      child.props["data-component"] === "gameBoard"
  );
  const playerInfo = childrenArray.find(
    (child) =>
      React.isValidElement(child) &&
      child.props["data-component"] === "playerInfo"
  );
  const actionArea = childrenArray.find(
    (child) =>
      React.isValidElement(child) &&
      child.props["data-component"] === "actionArea"
  );
  const cardsArea = childrenArray.find(
    (child) =>
      React.isValidElement(child) &&
      child.props["data-component"] === "cardsArea"
  );
  const otherElements = childrenArray.filter(
    (child) =>
      !React.isValidElement(child) ||
      !["gameBoard", "playerInfo", "actionArea", "cardsArea"].includes(
        child.props["data-component"]
      )
  );

  return (
    <div className={cn("relative", styles.gameContainer, className)}>
      {/* Game board at the top on mobile */}
      <div className={cn(styles.gameBoard)}>
        {gameBoard}
        {!hideChronicleLabel && (
          <div className="absolute top-0 right-2 text-yellow-500 flex items-center">
            <ArrowUp className="animate-bounce mr-1" size={16} />
            <span className="text-xs font-semibold">Campo da gioco</span>
          </div>
        )}
      </div>

      {/* Player info below game board */}
      <div className={cn(styles.playerInfo)}>{playerInfo}</div>

      {/* Action area */}
      <div className={cn(styles.actionArea)}>{actionArea}</div>

      {/* Cards area */}
      <div className={cn(styles.cardsArea)}>{cardsArea}</div>

      {/* Other elements */}
      {otherElements}
    </div>
  );
};

export default MobileGameLayout;

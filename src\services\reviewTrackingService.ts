/**
 * 📝 SERVIZIO TRACKING RECENSIONI PLAY STORE
 * Gestisce quando mostrare la richiesta di recensione e traccia se l'utente ha già lasciato una recensione
 */

import AnalyticsService from "./analyticsService";
import { Capacitor } from "@capacitor/core";

// Import condizionale del plugin Rate App
let RateApp: typeof import("capacitor-rate-app").RateApp | null = null;

// Inizializza l'import solo su piattaforme native
async function loadRateApp() {
  if (Capacitor.isNativePlatform() && !RateApp) {
    try {
      const rateAppModule = await import("capacitor-rate-app");
      RateApp = rateAppModule.RateApp;
      console.log("✅ Rate App plugin caricato");
    } catch (error) {
      console.warn("⚠️ Rate App plugin non disponibile:", error);
    }
  }
}

interface ReviewTrackingData {
  hasReviewed: boolean;
  reviewRequestCount: number;
  lastRequestDate: string | null;
  gamesPlayedSinceLastRequest: number;
  totalGamesPlayed: number;
  firstGameDate: string | null;
}

const STORAGE_KEY = "maraffa_review_tracking";

// Configurazione per quando mostrare la richiesta
const REVIEW_CONFIG = {
  MIN_GAMES_BEFORE_FIRST_REQUEST: 2, // Ridotto da 3 a 2 partite prima della prima richiesta
  MIN_GAMES_BETWEEN_REQUESTS: 3, // Ridotto da 5 a 3 partite tra una richiesta e l'altra
  MAX_REQUESTS: 5, // Aumentato da 3 a 5 richieste totali
  MIN_DAYS_BETWEEN_REQUESTS: 3, // Ridotto da 7 a 3 giorni tra le richieste
  SHOW_PROBABILITY: 0.8, // Aumentato da 30% a 80% di probabilità di mostrare dopo aver soddisfatto i criteri
};

/**
 * Carica i dati di tracking delle recensioni
 */
const loadReviewTrackingData = (): ReviewTrackingData => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.warn("⚠️ Errore caricamento dati tracking recensioni:", error);
  }

  // Dati di default
  return {
    hasReviewed: false,
    reviewRequestCount: 0,
    lastRequestDate: null,
    gamesPlayedSinceLastRequest: 0,
    totalGamesPlayed: 0,
    firstGameDate: null,
  };
};

/**
 * Salva i dati di tracking delle recensioni
 */
const saveReviewTrackingData = (data: ReviewTrackingData): void => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
  } catch (error) {
    console.warn("⚠️ Errore salvataggio dati tracking recensioni:", error);
  }
};

/**
 * Registra una partita completata
 */
export const recordGameCompleted = (): void => {
  const data = loadReviewTrackingData();
  const now = new Date().toISOString();

  // Aggiorna i contatori
  data.totalGamesPlayed += 1;
  data.gamesPlayedSinceLastRequest += 1;

  // Imposta la data della prima partita se non esiste
  if (!data.firstGameDate) {
    data.firstGameDate = now;
  }

  saveReviewTrackingData(data);
  console.log("🎮 Partita registrata per tracking recensioni:", {
    totalGames: data.totalGamesPlayed,
    gamesSinceLastRequest: data.gamesPlayedSinceLastRequest,
  });
};

/**
 * Controlla se dovremmo mostrare la richiesta di recensione
 * SEMPLIFICATO: Solo controlla se non ha già recensito e non ha superato il limite
 */
export const shouldShowReviewRequest = (): boolean => {
  const data = loadReviewTrackingData();

  console.log("🔍 Controllo recensione semplificato:", {
    hasReviewed: data.hasReviewed,
    requestCount: data.reviewRequestCount,
    maxRequests: REVIEW_CONFIG.MAX_REQUESTS,
  });

  // Se ha già lasciato una recensione, non mostrare mai più
  if (data.hasReviewed) {
    console.log("❌ Recensione già lasciata");
    return false;
  }

  // Se ha raggiunto il limite massimo di richieste
  if (data.reviewRequestCount >= REVIEW_CONFIG.MAX_REQUESTS) {
    console.log("❌ Limite massimo richieste raggiunto");
    return false;
  }

  // TUTTO IL RESTO RIMOSSO - mostra sempre se non ha recensito
  console.log("✅ Mostra recensione - criteri soddisfatti");
  return true;
};

/**
 * Registra che la richiesta di recensione è stata mostrata
 */
export const recordReviewRequestShown = (): void => {
  const data = loadReviewTrackingData();

  data.reviewRequestCount += 1;
  data.lastRequestDate = new Date().toISOString();
  data.gamesPlayedSinceLastRequest = 0; // Reset del contatore

  saveReviewTrackingData(data);
  console.log("📝 Richiesta recensione mostrata:", {
    requestCount: data.reviewRequestCount,
    date: data.lastRequestDate,
  });

  // 📊 ANALYTICS - Traccia richiesta recensione mostrata
  try {
    AnalyticsService.trackReviewRequest("shown");
  } catch (error) {
    console.warn("⚠️ Errore tracking richiesta recensione:", error);
  }
};

/**
 * Registra che l'utente ha lasciato una recensione
 */
export const recordReviewGiven = (): void => {
  const data = loadReviewTrackingData();

  data.hasReviewed = true;

  saveReviewTrackingData(data);
  console.log("⭐ Recensione lasciata dall'utente!");

  // 📊 ANALYTICS - Traccia recensione accettata
  try {
    AnalyticsService.trackReviewRequest("accepted");
  } catch (error) {
    console.warn("⚠️ Errore tracking recensione accettata:", error);
  }
};

/**
 * Ottieni statistiche di debug per il tracking delle recensioni
 */
export const getReviewTrackingStats = (): ReviewTrackingData => {
  return loadReviewTrackingData();
};

/**
 * Mostra la modale di recensione nativa di Google Play
 */
export const showNativeReviewModal = async (): Promise<boolean> => {
  try {
    console.log("🎯 Tentativo di mostrare modale recensione...");

    await loadRateApp();

    if (!RateApp) {
      console.warn("⚠️ Rate App plugin non disponibile");
      return false;
    }

    if (Capacitor.isNativePlatform()) {
      console.log(
        "📱 Piattaforma nativa rilevata, mostrando modale Google Play..."
      );

      // Su Android, usa la modale nativa di Google Play
      await RateApp.requestReview();
      console.log("✅ Modale recensione nativa mostrata con successo");

      // Registra che l'utente ha visto la richiesta (non che ha lasciato una recensione)
      // recordReviewGiven(); // Rimosso - questo dovrebbe essere chiamato solo se l'utente effettivamente lascia una recensione

      return true;
    } else {
      console.log("🌐 Piattaforma web rilevata, aprendo Play Store...");

      // Su web, apri il Play Store nel browser
      const packageName = "com.eliazavatta.maraffa";
      const playStoreUrl = `https://play.google.com/store/apps/details?id=${packageName}`;
      window.open(playStoreUrl, "_blank");

      // recordReviewGiven(); // Rimosso anche qui
      return true;
    }
  } catch (error) {
    console.error("❌ Errore mostrando recensione nativa:", error);
    return false;
  }
};

/**
 * Reset dei dati di tracking (solo per debug)
 */
export const resetReviewTracking = (): void => {
  localStorage.removeItem(STORAGE_KEY);
  console.log("🔄 Dati tracking recensioni resettati");
};

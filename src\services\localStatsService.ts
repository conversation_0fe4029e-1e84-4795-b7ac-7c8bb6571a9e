import { getCurrentDateString } from "../utils/dateUtils";
import {
  calculateLevelFromXp,
  getXpRequiredForLevel,
  getXpForNextLevel as getXpForNextLevelNew,
  getProgressToNextLevel as getProgressToNextLevelNew,
  getEstimatedGamesForNextLevel,
  calculateGameXp,
  isFirstWinOfDay,
} from "./experienceSystem";
import {
  getPlayerStats as getUnifiedStats,
  saveUnifiedData,
  updateGameStats,
  resetAllData,
  exportData,
  importData,
} from "./unifiedStorageService";

// Interfacce per le statistiche locali
export interface GameResult {
  id: string;
  date: string;
  result: "Vittoria" | "Sconfitta";
  score: string;
  difficulty: "easy" | "medium" | "hard";
  playerTeam: number;
  xpGained: number;
  xpBreakdown?: string[]; // Dettagli su come è stato calcolato l'XP
}

export interface PlayerStats {
  level: number;
  xp: number;
  totalGames: number;
  gamesWon: number;
  gamesLost: number;
  winRate: number;
  maraffeMade: number;
  achievementsUnlocked: string[];
  lastPlayed: string;
  lastWinDate?: string; // Per tracciare la prima vittoria del giorno
  currentWinStreak: number; // Serie di vittorie consecutive
  bestWinStreak: number; // Migliore serie di vittorie
  recentGames: GameResult[];
  createdAt: string;
  updatedAt: string;
}

const LOCAL_STORAGE_KEY = "maraffa_player_stats";
const MAX_RECENT_GAMES = 5;

// Esporta le funzioni del nuovo sistema con nomi compatibili
export const calculateLevel = calculateLevelFromXp;
export const getXpForNextLevel = getXpForNextLevelNew;
export const getProgressToNextLevel = getProgressToNextLevelNew;

// ⚠️ DEPRECATO: Usa statsManager.ts per nuove implementazioni
// Mantenuto solo per compatibilità con codice esistente

/**
 * @deprecated Usa loadStats da statsManager.ts
 */
export const getPlayerStats = (): PlayerStats => {
  return getUnifiedStats();
};

/**
 * @deprecated Usa saveStats da statsManager.ts
 */
export const savePlayerStats = (stats: PlayerStats): void => {
  saveUnifiedData({
    level: stats.level,
    xp: stats.xp,
    gamesPlayed: stats.totalGames,
    gamesWon: stats.gamesWon,
    winRate: stats.winRate,
    currentWinStreak: stats.currentWinStreak || 0,
    bestWinStreak: stats.bestWinStreak || 0,
    lastPlayed: stats.lastPlayed,
    updatedAt: getCurrentDateString(),
  });
};

/**
 * @deprecated Use updateStatsAfterGame from profileService.ts for online database updates
 */
export const updateStatsAfterGame = (gameData: {
  isWinner: boolean;
  difficulty: "easy" | "medium" | "hard";
  playerTeam: number;
  finalScore: [number, number];
  maraffeMade?: number;
  isPerfectGame?: boolean;
  isComeback?: boolean;
  isDominantWin?: boolean;
  isAbandoned?: boolean;
}): {
  stats: PlayerStats;
  leveledUp: boolean;
  xpGained: number;
  xpBreakdown: string[];
} => {
  console.warn(
    "⚠️ DEPRECATED: Use updateStatsAfterGame from profileService.ts for online database updates"
  );

  // Delega al nuovo sistema (ora rinominato)
  const {
    updateStatsAfterGameLocal: newUpdateStats,
  } = require("./statsManager");
  const result = newUpdateStats({
    isWinner: gameData.isWinner,
    difficulty: gameData.difficulty,
    maraffeMade: gameData.maraffeMade,
    isPerfectGame: gameData.isPerfectGame,
    isComeback: gameData.isComeback,
    isDominantWin: gameData.isDominantWin,
    isAbandoned: gameData.isAbandoned,
  });

  // Convert UnifiedStats to PlayerStats for compatibility
  return {
    stats: {
      level: result.stats.level,
      xp: result.stats.xp,
      totalGames: result.stats.gamesPlayed,
      gamesWon: result.stats.gamesWon,
      gamesLost: result.stats.gamesPlayed - result.stats.gamesWon,
      winRate: result.stats.winRate,
      currentWinStreak: result.stats.currentWinStreak,
      bestWinStreak: result.stats.bestWinStreak,
      lastPlayed: result.stats.lastPlayed,
      updatedAt: result.stats.updatedAt,
      maraffeMade: 0,
      achievementsUnlocked: [],
      recentGames: [],
      createdAt: result.stats.updatedAt,
    },
    leveledUp: result.leveledUp,
    xpGained: result.xpGained,
    xpBreakdown: result.xpBreakdown,
  };
};

/**
 * 🎯 FUNZIONE SEMPLIFICATA PER ABBANDONI
 */
export const updateStatsAfterAbandonment = (): {
  stats: PlayerStats;
  leveledUp: boolean;
  xpGained: number;
  xpBreakdown: string[];
} => {
  // Gli abbandoni sono gestiti come sconfitte dal sistema unificato
  return updateStatsAfterGame({
    isWinner: false,
    difficulty: "easy",
    playerTeam: 0,
    finalScore: [0, 31],
    isAbandoned: true,
  });
};
// Tutto il codice rimanente rimosso - delegato al sistema unificato

/**
 * 🎯 RESET DELEGATO AL SISTEMA UNIFICATO
 */
export const resetPlayerStats = (): PlayerStats => {
  resetAllData();
  return getPlayerStats();
};

/**
 * 🎯 EXPORT DELEGATO AL SISTEMA UNIFICATO
 */
export const exportStats = (): string => {
  return exportData();
};

/**
 * 🎯 IMPORT DELEGATO AL SISTEMA UNIFICATO
 */
export const importStats = (jsonData: string): boolean => {
  return importData(jsonData);
};

import { useNavigate } from "react-router-dom";
import { Users } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import UserSearch from "./UserSearch";
import FriendsList from "./FriendsList";
import FriendRequests from "./FriendRequests";
import OptimizedScrollContainer from "@/components/ui/OptimizedScrollContainer";

interface FriendData {
  id: string;
  username: string;
  level: number;
  xp: number;
  online: boolean;
  avatar_url?: string;
}

interface FriendRequest {
  id: string;
  from_user_id: string;
  to_user_id: string;
  created_at: string;
  from_user: {
    username: string;
    avatar_url?: string;
    level?: number;
  };
}

interface FriendsTabProps {
  isLoggedIn: boolean;
  authUser: { id: string } | null;
  friends: FriendData[];
  friendRequests: FriendRequest[];
  loading: boolean;
  onRefreshFriends: () => void;
  onHandleFriendRequest: (
    requestId: string,
    action: "accept" | "decline"
  ) => void;
  onRemoveFriend?: (playerId: string) => Promise<void>;
  onAddFriend?: (playerId: string) => Promise<void>;
  hasPendingRequestFor?: (playerId: string) => boolean;
}

const FriendsTab = ({
  isLoggedIn,
  authUser,
  friends,
  friendRequests,
  loading,
  onRefreshFriends,
  onHandleFriendRequest,
  onRemoveFriend,
  onAddFriend,
  hasPendingRequestFor,
}: FriendsTabProps) => {
  const navigate = useNavigate();

  if (!isLoggedIn) {
    return (
      <div className="flex items-center justify-center p-8">
        <Card className="border-2 border-amber-800/30 shadow-md bg-amber-50/80 backdrop-blur-sm max-w-md">
          <CardContent className="p-6 text-center">
            <Users className="h-16 w-16 text-amber-600 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-romagna-darkWood mb-2">
              Accesso Richiesto
            </h2>
            <p className="text-romagna-darkWood/70 mb-4">
              Devi essere registrato e autenticato per gestire la tua lista
              amici.
            </p>
            <Button
              onClick={() => navigate("/account")}
              className="bg-romagna-rust hover:bg-romagna-terracotta text-white"
            >
              Vai al Profilo
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }
  return (
    <OptimizedScrollContainer className="h-full overflow-y-auto">
      {/* Barra di ricerca */}
      <UserSearch
        authUser={authUser}
        friends={friends}
        hasPendingRequestFor={hasPendingRequestFor}
        onAddFriend={onAddFriend}
      />
      {/* Lista Amici */}
      <FriendsList
        friends={friends}
        loading={loading}
        onRefresh={onRefreshFriends}
        currentUserId={authUser?.id}
        onRemoveFriend={onRemoveFriend}
      />
      {/* Richieste di amicizia */}
      <FriendRequests
        requests={friendRequests}
        onHandleRequest={onHandleFriendRequest}
      />
    </OptimizedScrollContainer>
  );
};

export default FriendsTab;

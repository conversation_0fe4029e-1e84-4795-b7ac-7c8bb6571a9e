// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_21
      targetCompatibility JavaVersion.VERSION_21
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-community-admob')
    implementation project(':capacitor-community-firebase-analytics')
    implementation project(':capacitor-app')
    implementation project(':capacitor-share')
    implementation project(':capacitor-splash-screen')
    implementation project(':capacitor-status-bar')
    implementation project(':codetrix-studio-capacitor-google-auth')
    implementation project(':revenuecat-purchases-capacitor')
    implementation project(':capacitor-rate-app')

}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}

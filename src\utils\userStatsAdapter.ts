
// Import UserStats interface from auth-context
import { UserStats } from '@/context/auth-context';
import { GameStats } from '@/types/database';

// Adapter to handle both snake_case from API and camelCase for UI compatibility
export const adaptUserStats = (stats: GameStats | null) => {
  if (!stats) return { userStats: null, isLoggedIn: false };
  
  const userStats: UserStats = {
    id: stats.id,
    user_id: stats.user_id,
    games_played: stats.games_played,
    games_won: stats.games_won,
    level: stats.level,
    xp: stats.xp,
    created_at: stats.created_at,
    updated_at: stats.updated_at,
    gamesPlayed: stats.games_played,
    gamesWon: stats.games_won
  };
  
  return {
    userStats,
    isLoggedIn: true
  };
};

// Type for the adapted stats with both naming conventions
export type AdaptedUserStats = ReturnType<typeof adaptUserStats>;

import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

/**
 * Hook per gestire il tasto indietro hardware su smartphone
 * @param targetRoute - La route di destinazione quando viene premuto il tasto indietro
 * @param isEnabled - Se true, l'hook è attivo (default: true)
 */
export function useHardwareBackButton(
  targetRoute: string = "/",
  isEnabled: boolean = true
) {
  const navigate = useNavigate();

  useEffect(() => {
    if (!isEnabled) return;

    const handleBackButton = (event: PopStateEvent) => {
      // Previeni il comportamento di default del browser
      event.preventDefault();

      // Naviga alla route specificata
      navigate(targetRoute, { replace: true });
    };

    // Aggiungi l'event listener per il tasto indietro
    window.addEventListener("popstate", handleBackButton);

    // Aggiungi uno stato alla cronologia per intercettare il tasto indietro
    window.history.pushState(null, "", window.location.href);

    // Cleanup
    return () => {
      window.removeEventListener("popstate", handleBackButton);
    };
  }, [navigate, targetRoute, isEnabled]);
}

import React, { ReactNode, useEffect, useState, useRef } from "react";
import { Capacitor } from "@capacitor/core";
import { useAndroidScrollOptimization } from "@/hooks/useAndroidScrollOptimization";

interface PageContainerProps {
  children: ReactNode;
  className?: string;
  footerHeight?: number;
  forceScroll?: boolean;
}

/**
 * Componente che gestisce il layout con footer.
 * Se il contenuto è più piccolo dello spazio disponibile, non mostra scrollbar.
 * Se il contenuto è più grande, abilita lo scroll.
 * Il footer è sempre posizionato in fondo e non influenza il comportamento di scroll.
 */
const PageContainer: React.FC<PageContainerProps> = ({
  children,
  className = "",
  footerHeight = 67, // Valore predefinito per l'altezza del footer
  forceScroll = false, // Se true, forza lo scroll anche quando non necessario
}) => {
  const [needsScroll, setNeedsScroll] = useState(forceScroll);
  const [isScrolling, setIsScrolling] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const isAndroid = Capacitor.getPlatform() === "android";

  // Usa l'hook di ottimizzazione per Android
  const { shouldOptimize } = useAndroidScrollOptimization({
    debounceTime: 16, // 60fps
  });

  // Verifica se il contenuto necessita di scroll
  useEffect(() => {
    if (forceScroll) {
      setNeedsScroll(true);
      return;
    }

    const checkContentHeight = () => {
      if (contentRef.current) {
        const contentHeight = contentRef.current.scrollHeight;
        const containerHeight = window.innerHeight - footerHeight;
        setNeedsScroll(contentHeight > containerHeight);
      }
    };

    // Controlla subito
    checkContentHeight();

    // Controlla al resize della finestra
    window.addEventListener("resize", checkContentHeight);

    // Cleanup
    return () => {
      window.removeEventListener("resize", checkContentHeight);
    };
  }, [footerHeight, forceScroll, children]);

  // Gestione dello stato di scroll per ottimizzazioni Android
  useEffect(() => {
    if (!shouldOptimize || !contentRef.current) return;

    const container = contentRef.current;
    let scrollTimeout: NodeJS.Timeout;

    const handleScroll = () => {
      setIsScrolling(true);
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        setIsScrolling(false);
      }, 150);
    };

    container.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      container.removeEventListener("scroll", handleScroll);
      clearTimeout(scrollTimeout);
    };
  }, [shouldOptimize]);

  // Stili ottimizzati per Android
  const containerStyles = shouldOptimize
    ? {
        WebkitOverflowScrolling: "touch",
        overflowScrolling: "touch",
        transform: "translateZ(0)",
        willChange: "scroll-position",
      }
    : {};

  return (
    <div
      ref={contentRef}
      className={`${className} ${
        shouldOptimize ? "android-optimized-scroll scroll-optimized" : ""
      } ${isScrolling ? "scrolling" : ""}`}
      style={{
        height: `calc(100vh - ${footerHeight}px)`,
        overflowY: needsScroll ? "auto" : "hidden",
        position: "relative",
        ...containerStyles,
      }}
    >
      {children}
    </div>
  );
};

export default PageContainer;

/**
 * 🌐 HOOK PER MONITORAGGIO CONNESSIONE E SESSIONE
 * Monitora la salute della connessione e gestisce il recovery automatico
 */

import { useEffect, useRef, useState } from "react";
import { useAuth } from "@/context/auth-context";

interface ConnectionMonitorOptions {
  onConnectionLost?: () => void;
  onConnectionRestored?: () => void;
  onSessionRecovered?: () => void;
  checkInterval?: number; // in millisecondi
  maxRetries?: number;
}

interface ConnectionStatus {
  isOnline: boolean;
  isSessionValid: boolean;
  lastCheck: Date | null;
  retryCount: number;
  connectionQuality: "excellent" | "good" | "poor" | "offline";
}

export const useConnectionMonitor = (
  options: ConnectionMonitorOptions = {}
) => {
  const {
    onConnectionLost,
    onConnectionRestored,
    onSessionRecovered,
    checkInterval = 60000, // 60 secondi (ridotto conflitto con altri timer)
    maxRetries = 3,
  } = options;

  const { session, isLoggedIn, isOnlineFeatureEnabled } = useAuth();
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    isOnline: navigator.onLine,
    isSessionValid: !!session?.access_token,
    lastCheck: null,
    retryCount: 0,
    connectionQuality: "excellent",
  });

  const lastConnectionState = useRef<boolean>(navigator.onLine);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Test di connettività avanzato
  const testConnection = async (): Promise<{
    isOnline: boolean;
    responseTime: number;
    quality: "excellent" | "good" | "poor" | "offline";
  }> => {
    try {
      const startTime = Date.now();

      // Test multipli per valutare la qualità
      const tests = [
        fetch("https://www.google.com/favicon.ico", {
          method: "HEAD",
          mode: "no-cors",
          signal: AbortSignal.timeout(5000),
        }),
        fetch("https://httpbin.org/status/200", {
          method: "HEAD",
          signal: AbortSignal.timeout(5000),
        }),
      ];

      const results = await Promise.allSettled(tests);
      const responseTime = Date.now() - startTime;

      const successfulTests = results.filter(
        (result) => result.status === "fulfilled"
      ).length;

      if (successfulTests === 0) {
        return { isOnline: false, responseTime, quality: "offline" };
      }

      // Valuta qualità basata su tempo di risposta e successi
      let quality: "excellent" | "good" | "poor" | "offline";
      if (responseTime < 1000 && successfulTests === tests.length) {
        quality = "excellent";
      } else if (responseTime < 3000 && successfulTests >= tests.length / 2) {
        quality = "good";
      } else {
        quality = "poor";
      }

      return { isOnline: true, responseTime, quality };
    } catch (error) {
      console.warn("⚠️ Test connessione fallito:", error);
      return { isOnline: false, responseTime: 0, quality: "offline" };
    }
  };

  // Test sessione
  const testSession = async (): Promise<boolean> => {
    if (!isOnlineFeatureEnabled || !isLoggedIn) {
      return true; // Non applicabile
    }

    try {
      const { sessionManager } = await import("@/integrations/supabase/client");
      const validSession = await sessionManager.ensureValidSession();
      return !!validSession?.access_token;
    } catch (error) {
      console.warn("⚠️ Test sessione fallito:", error);
      return false;
    }
  };

  // Controllo completo
  const performHealthCheck = async (): Promise<void> => {
    try {
      const [connectionResult, sessionValid] = await Promise.all([
        testConnection(),
        testSession(),
      ]);

      const newStatus: ConnectionStatus = {
        isOnline: connectionResult.isOnline,
        isSessionValid: sessionValid,
        lastCheck: new Date(),
        retryCount: connectionResult.isOnline
          ? 0
          : connectionStatus.retryCount + 1,
        connectionQuality: connectionResult.quality,
      };

      // Notifica cambiamenti di stato
      if (!lastConnectionState.current && connectionResult.isOnline) {
        console.log("✅ Connessione ripristinata");
        onConnectionRestored?.();

        // Se anche la sessione è valida, notifica recovery completo
        if (sessionValid) {
          onSessionRecovered?.();
        }
      } else if (lastConnectionState.current && !connectionResult.isOnline) {
        console.warn("⚠️ Connessione persa");
        onConnectionLost?.();
      }

      lastConnectionState.current = connectionResult.isOnline;
      setConnectionStatus(newStatus);

      // Programma retry se necessario
      if (!connectionResult.isOnline && newStatus.retryCount < maxRetries) {
        const retryDelay = Math.min(
          1000 * Math.pow(2, newStatus.retryCount),
          30000
        );
        console.log(
          `🔄 Retry connessione in ${retryDelay}ms (tentativo ${
            newStatus.retryCount + 1
          }/${maxRetries})`
        );

        retryTimeoutRef.current = setTimeout(() => {
          performHealthCheck();
        }, retryDelay);
      }
    } catch (error) {
      console.error("❌ Errore health check:", error);
    }
  };

  // Gestione eventi browser
  useEffect(() => {
    const handleOnline = () => {
      console.log("🌐 Browser online event");
      performHealthCheck();
    };

    const handleOffline = () => {
      console.log("🌐 Browser offline event");
      setConnectionStatus((prev) => ({
        ...prev,
        isOnline: false,
        connectionQuality: "offline",
        lastCheck: new Date(),
      }));
      onConnectionLost?.();
    };

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, [onConnectionLost]);

  // Controllo periodico
  useEffect(() => {
    if (!isOnlineFeatureEnabled) {
      return;
    }

    // Controllo iniziale
    performHealthCheck();

    // Controllo periodico
    const interval = setInterval(performHealthCheck, checkInterval);

    return () => {
      clearInterval(interval);
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, [isOnlineFeatureEnabled, checkInterval, maxRetries]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  return {
    connectionStatus,
    performHealthCheck,
    isHealthy: connectionStatus.isOnline && connectionStatus.isSessionValid,
    needsAttention:
      connectionStatus.connectionQuality === "poor" ||
      !connectionStatus.isSessionValid,
  };
};

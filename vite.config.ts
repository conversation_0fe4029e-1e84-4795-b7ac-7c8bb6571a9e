import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [react(), mode === "development" && componentTagger()].filter(
    Boolean
  ),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor libraries
          vendor: ["react", "react-dom"],

          // Audio libraries
          audio: ["howler", "tone"],

          // UI libraries
          ui: [
            "lucide-react",
            "@radix-ui/react-dialog",
            "@radix-ui/react-switch",
            "@radix-ui/react-accordion",
            "@radix-ui/react-alert-dialog",
            "@radix-ui/react-checkbox",
            "@radix-ui/react-label",
            "@radix-ui/react-separator",
            "@radix-ui/react-slot",
            "@radix-ui/react-tooltip",
            "@radix-ui/react-slider",
            "@radix-ui/react-scroll-area",
            "@radix-ui/react-progress",
            "swiper",
          ],

          // Game logic
          game: [
            "./src/utils/game/gameLogic.ts",
            "./src/utils/game/cardUtils.ts",
            "./src/utils/ai/index.ts",
          ],

          // Audio management
          audioManager: ["./src/utils/audio/AudioManager.ts"],

          // Supabase
          supabase: ["@supabase/supabase-js"],
        },
      },
    },
  },
}));

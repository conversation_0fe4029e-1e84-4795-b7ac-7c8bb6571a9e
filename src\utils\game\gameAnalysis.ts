// File per l'analisi delle partite e il rilevamento di eventi speciali
// Usato per calcolare bonus XP nel sistema di esperienza

import { GameState } from "./gameLogic";

/**
 * Determina se una partita è stata una "partita perfetta"
 * Una partita perfetta si verifica quando una squadra vince senza
 * che gli avversari abbiano ottenuto alcun punto
 */
export const isPerfectGame = (
  gameState: GameState,
  winnerTeam: 0 | 1
): boolean => {
  const loserTeam = winnerTeam === 0 ? 1 : 0;
  return gameState.gameScore[loserTeam] === 0;
};

/**
 * Determina se una partita è stata un "comeback"
 * Un comeback si verifica quando una squadra vince dopo essere stata
 * significativamente indietro (differenza >= 10 punti) durante la partita
 */
export const isComeback = (
  gameState: GameState,
  winnerTeam: 0 | 1
): boolean => {
  // Un comeback si verifica se:
  // 1. La squadra vincitrice è stata indietro di almeno 10 punti
  // 2. La massima differenza raggiunta è >= 10 punti
  // 3. La squadra perdente aveva un vantaggio significativo

  const loserTeam = winnerTeam === 0 ? 1 : 0;
  const finalWinnerScore = gameState.gameScore[winnerTeam];
  const finalLoserScore = gameState.gameScore[loserTeam];

  // Se la partita non è stata combattuta abbastanza, non è un comeback
  if (gameState.maxScoreDifference < 10) {
    return false;
  }

  // Se la vittoria è stata troppo netta, probabilmente non è un comeback
  const finalDifference = finalWinnerScore - finalLoserScore;
  if (finalDifference > 15) {
    return false;
  }

  // Analizza lo storico dei punteggi per determinare se c'è stato un sorpasso
  // La logica è che deve esserci stato un momento in cui il perdente era avanti
  // di almeno 10 punti e poi il vincitore ha recuperato
  return gameState.maxScoreDifference >= 10 && finalDifference <= 10;
};

/**
 * Calcola il numero totale di maraffe realizzate dal giocatore (team 0)
 */
export const getPlayerMaraffeMade = (gameState: GameState): number => {
  return gameState.maraffeMade[0]; // Il giocatore è sempre nel team 0
};

/**
 * Determina se la partita è stata dominante (vittoria con grande distacco)
 */
export const isDominantWin = (
  gameState: GameState,
  winnerTeam: 0 | 1
): boolean => {
  const loserTeam = winnerTeam === 0 ? 1 : 0;
  const difference =
    gameState.gameScore[winnerTeam] - gameState.gameScore[loserTeam];
  return difference > 15; // Vittoria con più di 15 punti di differenza
};

/**
 * Calcola statistiche riassuntive della partita per il debug
 */
export const getGameSummary = (gameState: GameState) => {
  const winnerTeam = gameState.gameScore[0] > gameState.gameScore[1] ? 0 : 1;
  const loserTeam = winnerTeam === 0 ? 1 : 0;

  return {
    winnerTeam,
    finalScore: [gameState.gameScore[0], gameState.gameScore[1]] as [
      number,
      number
    ],
    scoreDifference:
      gameState.gameScore[winnerTeam] - gameState.gameScore[loserTeam],
    maxScoreDifference: gameState.maxScoreDifference,
    maraffeMade: gameState.maraffeMade,
    isPerfect: isPerfectGame(gameState, winnerTeam),
    isComeback: isComeback(gameState, winnerTeam),
    isDominant: isDominantWin(gameState, winnerTeam),
  };
};

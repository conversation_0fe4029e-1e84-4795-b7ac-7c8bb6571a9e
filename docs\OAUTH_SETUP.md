# Configurazione OAuth per Login Social

## Setup necessario per l'autenticazione social

### 1. Configurazione Google OAuth

1. Vai su [Google Cloud Console](https://console.cloud.google.com/)
2. Crea un nuovo progetto o seleziona quello esistente
3. Vai su **APIs & Services > Credentials**
4. Clicca su **Create Credentials > OAuth 2.0 Client IDs**
5. Configura le origini autorizzate:
   - **Authorized JavaScript origins**: `http://localhost:5173`, `https://yourdomain.com`
   - **Authorized redirect URIs**:
     - `https://bzqlnxftjcfcrfopcjlf.supabase.co/auth/v1/callback`
     - `http://localhost:5173/auth/callback` (per sviluppo)

### 2. Configurazione Facebook OAuth

1. Vai su [Facebook for Developers](https://developers.facebook.com/)
2. Crea una nuova app o seleziona quella esistente
3. Vai su **Settings > Basic**
4. Aggiungi dominio: `bzqlnxftjcfcrfopcjlf.supabase.co`
5. In **Facebook Login > Settings**:
   - **Valid OAuth Redirect URIs**: `https://bzqlnxftjcfcrfopcjlf.supabase.co/auth/v1/callback`

### 3. Configurazione Database Supabase

**IMPORTANTE**: Prima di testare l'autenticazione, configura le Row Level Security policies:

1. Vai su **Supabase Dashboard > SQL Editor**
2. Esegui lo script `docs/SUPABASE_RLS_SETUP.sql` che configura:

   - Policies RLS per la tabella profiles
   - Trigger automatico per creare profili utente
   - Permessi corretti per leggere/scrivere i propri dati

3. Verifica che la tabella `profiles` abbia la struttura corretta:

```sql
-- Struttura richiesta per la tabella profiles
CREATE TABLE profiles (
  id UUID REFERENCES auth.users PRIMARY KEY,
  username TEXT NOT NULL,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 4. Configurazione Supabase Dashboard

1. Vai su [Supabase Dashboard](https://app.supabase.com/)
2. Seleziona il tuo progetto
3. Vai su **Authentication > Providers**
4. Abilita **Google**:
   - Inserisci **Client ID** e **Client Secret** da Google Console
5. Abilita **Facebook**:
   - Inserisci **App ID** e **App Secret** da Facebook Developers

### 4. Funzionalità implementate

✅ **Login con Google e Facebook**

- Autenticazione tramite OAuth 2.0
- Gestione automatica della sessione
- Recupero dati profilo (nome, email, avatar)

✅ **Sincronizzazione profilo**

- Salvataggio del profilo utente nel database
- Gestione delle informazioni di base

🔄 **Prossime funzionalità**

- Sincronizzazione statistiche di gioco
- Partite multiplayer online
- Classifiche globali
- Sistema di amicizie
- Sfide tra giocatori

### 5. Sicurezza e Privacy

- **OAuth sicuro**: Le credenziali sono gestite dai provider (Google/Facebook)
- **Dati minimi**: Vengono richiesti solo nome, email e avatar
- **Sessioni sicure**: Gestite automaticamente da Supabase
- **No password**: Nessuna password da ricordare o gestire

### 6. Testing

Per testare in locale:

1. Configura gli URL di redirect per localhost
2. Avvia l'app con `npm run dev`
3. Vai su Account e prova il login social
4. Verifica che i dati del profilo vengano salvati correttamente

### 7. Deployment

Prima del deployment:

1. Aggiorna gli URL di redirect con il dominio di produzione
2. Configura le variabili d'ambiente se necessario
3. Testa le funzionalità OAuth in ambiente di produzione

### 8. Troubleshooting

**Errore "redirect_uri_mismatch"**:

- Verifica che gli URL di redirect siano configurati correttamente in Google/Facebook

**Errore "invalid_client"**:

- Controlla che Client ID e Client Secret siano corretti in Supabase

**Login non funziona su mobile**:

- Configura gli URL schemes per Capacitor (se necessario)

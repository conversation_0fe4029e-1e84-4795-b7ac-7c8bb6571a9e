/**
 * 🧠 Sistema Avanzato di Valutazione Forza delle Carte
 *
 * Implementa la memoria perfetta delle carte giocate per valutare
 * la forza relativa di ogni carta nella mano basandosi su:
 * - Carte già giocate e rimaste nel gioco
 * - Probabilità che la carta possa essere superata
 * - Valutazione del rischio di taglio con briscole
 * - Posizione dominante nel seme
 */

import { Card, Suit } from "../../game/cardUtils";
import { GameState } from "../../game/gameLogic";
import { CardMemory } from "../types";
import { getCardOrder, getCardValue } from "../utils/cardUtils";

/**
 * Informazioni dettagliate sulla forza di una carta
 */
export interface CardStrengthInfo {
  card: Card;
  isDominant: boolean; // È la carta più forte del seme rimasta?
  dominantRank: number; // Posizione nella gerarchia del seme (1 = più forte)
  canBeBeatInSuit: boolean; // Può essere superata da carte dello stesso seme?
  strongerCardsRemaining: number; // Quante carte più forti rimangono nel seme
  trumpCutRisk: number; // Probabilità di taglio con briscola (0-1)
  overallSafetyScore: number; // Punteggio sicurezza finale (0-10)
  strategicValue: number; // Valore strategico considerando il contesto
  playRecommendation: "safe" | "risky" | "dangerous"; // Raccomandazione di gioco
  reasoning: string[]; // Spiegazione del ragionamento
}

/**
 * Risultato dell'analisi della mano completa
 */
export interface HandStrengthAnalysis {
  dominantCards: Card[]; // Carte dominanti nei loro semi
  safeCards: Card[]; // Carte sicure da giocare
  riskyCards: Card[]; // Carte con rischio moderato
  dangerousCards: Card[]; // Carte ad alto rischio
  trumpControl: {
    strongTrumps: Card[]; // Briscole forti
    weakTrumps: Card[]; // Briscole deboli
    trumpDominance: number; // Controllo delle briscole (0-1)
  };
  bestOpeningCard: Card | null; // Migliore carta per aprire
  overallHandStrength: number; // Forza complessiva della mano (0-10)
}

/**
 * Analizza la forza di una singola carta basandosi sulla memoria perfetta
 */
export const evaluateCardStrength = (
  card: Card,
  memory: CardMemory,
  gameState: GameState
): CardStrengthInfo => {
  const reasoning: string[] = [];

  // 1. ANALISI DOMINANZA NEL SEME
  const { isDominant, dominantRank, strongerCardsRemaining } =
    analyzeSuitDominance(card, memory, reasoning);

  // 2. ANALISI RISCHIO TAGLIO BRISCOLA
  const trumpCutRisk = analyzeTrumpCutRisk(card, memory, gameState, reasoning);

  // 3. CALCOLO SICUREZZA COMPLESSIVA
  const overallSafetyScore = calculateOverallSafety(
    isDominant,
    strongerCardsRemaining,
    trumpCutRisk,
    reasoning
  );

  // 4. VALUTAZIONE STRATEGICA CONTESTUALE
  const strategicValue = evaluateStrategicContext(
    card,
    gameState,
    isDominant,
    overallSafetyScore,
    reasoning
  );

  // 5. RACCOMANDAZIONE FINALE
  const playRecommendation = determinePlayRecommendation(
    overallSafetyScore,
    strategicValue,
    gameState.trickNumber
  );

  return {
    card,
    isDominant,
    dominantRank,
    canBeBeatInSuit: strongerCardsRemaining > 0,
    strongerCardsRemaining,
    trumpCutRisk,
    overallSafetyScore,
    strategicValue,
    playRecommendation,
    reasoning,
  };
};

/**
 * Analizza la dominanza di una carta nel suo seme
 */
const analyzeSuitDominance = (
  card: Card,
  memory: CardMemory,
  reasoning: string[]
): {
  isDominant: boolean;
  dominantRank: number;
  strongerCardsRemaining: number;
} => {
  // Ottieni tutte le carte rimanenti del seme
  const remainingInSuit = memory.remainingCards.filter(
    (c) => c.suit === card.suit
  );

  // Ordina per forza (dal più forte al più debole)
  const sortedBySuit = remainingInSuit.sort(
    (a, b) => getCardOrder(b) - getCardOrder(a)
  );

  // Trova la posizione della carta
  const cardIndex = sortedBySuit.findIndex(
    (c) => c.rank === card.rank && c.suit === card.suit
  );

  const isDominant = cardIndex === 0;
  const dominantRank = cardIndex + 1;
  const strongerCardsRemaining = cardIndex;

  if (isDominant) {
    reasoning.push(
      `🎯 DOMINANTE: ${card.rank} di ${card.suit} è la carta più forte rimasta nel seme`
    );
  } else if (dominantRank <= 3) {
    reasoning.push(
      `⚡ FORTE: ${card.rank} di ${card.suit} è la ${dominantRank}ª carta più forte del seme`
    );
  } else {
    reasoning.push(
      `📉 DEBOLE: ${card.rank} di ${card.suit} è solo la ${dominantRank}ª carta del seme`
    );
  }

  // Analisi delle carte superiori giocate
  const playedInSuit = memory.playedBySuit[card.suit] || [];
  const playedStrongerCards = playedInSuit.filter(
    (c) => getCardOrder(c) > getCardOrder(card)
  );

  if (playedStrongerCards.length > 0) {
    reasoning.push(
      `📊 Carte superiori già giocate: ${
        playedStrongerCards.length
      } (${playedStrongerCards.map((c) => c.rank).join(", ")})`
    );
  }

  return { isDominant, dominantRank, strongerCardsRemaining };
};

/**
 * Calcola il rischio di taglio con briscola
 */
const analyzeTrumpCutRisk = (
  card: Card,
  memory: CardMemory,
  gameState: GameState,
  reasoning: string[]
): number => {
  // Se la carta è già una briscola, non può essere tagliata
  if (gameState.trumpSuit && card.suit === gameState.trumpSuit) {
    reasoning.push(`🛡️ BRISCOLA: Immune al taglio`);
    return 0;
  }

  const trumpsRemaining = memory.trumpsRemaining.length;

  if (trumpsRemaining === 0) {
    reasoning.push(`✅ SICURA: Nessuna briscola rimasta nel gioco`);
    return 0;
  }

  // Analisi giocatori che potrebbero non avere il seme
  const suitPlayedBy = memory.playedBySuit[card.suit] || [];
  const totalSuitCards = 10; // Carte per seme in un mazzo standard
  const suitCardsPlayed = suitPlayedBy.length;
  const suitCardsRemaining = totalSuitCards - suitCardsPlayed;

  // Stima probabilità che qualcuno non abbia più il seme
  let cutRiskFactor = 0;

  if (suitCardsRemaining <= 6) {
    cutRiskFactor = 0.3; // Rischio moderato
    reasoning.push(
      `⚠️ RISCHIO MODERATO: Solo ${suitCardsRemaining} carte del seme rimaste`
    );
  } else if (suitCardsRemaining <= 3) {
    cutRiskFactor = 0.6; // Rischio alto
    reasoning.push(
      `⚠️ RISCHIO ALTO: Solo ${suitCardsRemaining} carte del seme rimaste`
    );
  } else {
    cutRiskFactor = 0.1; // Rischio basso
    reasoning.push(
      `✅ RISCHIO BASSO: ${suitCardsRemaining} carte del seme ancora in gioco`
    );
  }

  // Aumenta rischio basandosi sul numero di briscole rimaste
  const trumpRiskMultiplier = Math.min(1, trumpsRemaining / 8);
  const finalRisk = cutRiskFactor * trumpRiskMultiplier;

  reasoning.push(
    `🃏 Briscole rimaste: ${trumpsRemaining} - Rischio taglio: ${(
      finalRisk * 100
    ).toFixed(1)}%`
  );

  return finalRisk;
};

/**
 * Calcola il punteggio di sicurezza complessivo
 */
const calculateOverallSafety = (
  isDominant: boolean,
  strongerCardsRemaining: number,
  trumpCutRisk: number,
  reasoning: string[]
): number => {
  let safetyScore = 0;

  // Base safety dalla dominanza
  if (isDominant) {
    safetyScore = 10;
  } else {
    safetyScore = Math.max(0, 10 - strongerCardsRemaining * 1.5);
  }

  // Penalità per rischio taglio
  const trumpPenalty = trumpCutRisk * 4;
  safetyScore = Math.max(0, safetyScore - trumpPenalty);

  const safetyLevel =
    safetyScore >= 8 ? "ALTA" : safetyScore >= 5 ? "MEDIA" : "BASSA";

  reasoning.push(`🛡️ SICUREZZA ${safetyLevel}: ${safetyScore.toFixed(1)}/10`);

  return safetyScore;
};

/**
 * Valuta il valore strategico contestuale
 */
const evaluateStrategicContext = (
  card: Card,
  gameState: GameState,
  isDominant: boolean,
  safetyScore: number,
  reasoning: string[]
): number => {
  let strategicValue = getCardValue(card) * 2; // Base dal valore in punti

  // Bonus per carte dominanti
  if (isDominant) {
    strategicValue += 3;
    reasoning.push(`⭐ BONUS DOMINANZA: +3 punti strategici`);
  }

  // Bonus sicurezza
  if (safetyScore >= 8) {
    strategicValue += 2;
    reasoning.push(`🛡️ BONUS SICUREZZA: +2 punti strategici`);
  }
  // Considerazioni fase di gioco
  const gamePhase =
    gameState.trickNumber <= 3
      ? "early"
      : gameState.trickNumber <= 7
      ? "mid"
      : "late";
  switch (gamePhase) {
    case "early":
      if (card.rank === "3" && card.suit !== gameState.trumpSuit) {
        strategicValue += 8; // I 3 non di briscola sono OTTIMI per aprire e prendere punti
        reasoning.push(
          `🎯 EARLY GAME: 3 non di briscola per controllo e punti (+8)`
        );
      } else if (card.rank === "2") {
        strategicValue += 4; // I 2 restano strategici da conservare
        reasoning.push(`🎯 EARLY GAME: 2 strategico da conservare (+4)`);
      }
      break;

    case "mid":
      if (card.rank === "3" && card.suit !== gameState.trumpSuit) {
        strategicValue += 6; // I 3 non di briscola restano molto utili in mid game
        reasoning.push(
          `⚡ MID GAME: 3 non di briscola per prendere punti (+6)`
        );
      } else if (isDominant && getCardValue(card) > 0) {
        strategicValue += 2; // Carte dominanti con punti sono preziose
        reasoning.push(`⚡ MID GAME: Carta dominante con punti (+2)`);
      }
      break;

    case "late":
      if (card.rank === "3" && card.suit !== gameState.trumpSuit) {
        strategicValue += 4; // Anche in late game, i 3 sono utili
        reasoning.push(
          `🏁 LATE GAME: 3 non di briscola per controllo finale (+4)`
        );
      } else if (safetyScore >= 7) {
        strategicValue += 3; // Late game, sicurezza è cruciale
        reasoning.push(`🏁 LATE GAME: Sicurezza cruciale (+3)`);
      }
      break;
  }

  reasoning.push(`📊 VALORE STRATEGICO: ${strategicValue.toFixed(1)}/10`);

  return Math.min(10, strategicValue);
};

/**
 * Determina la raccomandazione di gioco
 */
const determinePlayRecommendation = (
  safetyScore: number,
  strategicValue: number,
  trickNumber: number
): "safe" | "risky" | "dangerous" => {
  const combinedScore = (safetyScore + strategicValue) / 2;

  if (combinedScore >= 7) return "safe";
  if (combinedScore >= 4) return "risky";
  return "dangerous";
};

/**
 * Analizza la forza dell'intera mano
 */
export const analyzeHandStrength = (
  hand: Card[],
  memory: CardMemory,
  gameState: GameState
): HandStrengthAnalysis => {
  // Valuta ogni carta individualmente
  const cardEvaluations = hand.map((card) =>
    evaluateCardStrength(card, memory, gameState)
  );
  // Categorizza le carte
  const dominantCards = cardEvaluations
    .filter((cardEval) => cardEval.isDominant)
    .map((cardEval) => cardEval.card);

  const safeCards = cardEvaluations
    .filter((cardEval) => cardEval.playRecommendation === "safe")
    .map((cardEval) => cardEval.card);

  const riskyCards = cardEvaluations
    .filter((cardEval) => cardEval.playRecommendation === "risky")
    .map((cardEval) => cardEval.card);

  const dangerousCards = cardEvaluations
    .filter((cardEval) => cardEval.playRecommendation === "dangerous")
    .map((cardEval) => cardEval.card);
  // Analisi controllo briscole
  const trumpCards = hand.filter(
    (card) => gameState.trumpSuit && card.suit === gameState.trumpSuit
  );

  const trumpEvaluations = trumpCards.map((trump) =>
    evaluateCardStrength(trump, memory, gameState)
  );

  const strongTrumps = trumpEvaluations
    .filter((trumpEval) => trumpEval.dominantRank <= 3)
    .map((trumpEval) => trumpEval.card);

  const weakTrumps = trumpEvaluations
    .filter((trumpEval) => trumpEval.dominantRank > 3)
    .map((trumpEval) => trumpEval.card);

  const trumpDominance =
    trumpCards.length > 0
      ? strongTrumps.length / memory.trumpsRemaining.length
      : 0;

  // Determina migliore carta di apertura
  const bestOpeningCard = determineBestOpeningCard(
    cardEvaluations,
    gameState.trumpSuit
  );

  // Calcola forza complessiva della mano
  const overallHandStrength = calculateOverallHandStrength(cardEvaluations);

  return {
    dominantCards,
    safeCards,
    riskyCards,
    dangerousCards,
    trumpControl: {
      strongTrumps,
      weakTrumps,
      trumpDominance,
    },
    bestOpeningCard,
    overallHandStrength,
  };
};

/**
 * 🎯 STRATEGIA DI APERTURA OTTIMIZZATA PER PRIME MANI
 * - PRIME MANI: Priorità assoluta al 3 non-briscola per sfruttare la cooperazione del compagno
 * - Nelle prime 3 mani il compagno ha 100% di riconoscimento del 3 non-briscola
 * - Se il 3 è già uscito, usa il 2 o la carta più alta del seme più ricco
 */
const determineBestOpeningCard = (
  evaluations: CardStrengthInfo[],
  trumpSuit: string | null = null
): Card | null => {
  if (evaluations.length === 0) return null;

  // 🔥 PRIORITÀ ASSOLUTA: 3 non di briscola per apertura strategica
  // Nelle prime mani questo è ANCORA PIÙ CRITICO per sfruttare la cooperazione perfetta
  const threeNonTrump = evaluations.filter(
    (cardEval) =>
      cardEval.card.rank === "3" &&
      cardEval.playRecommendation === "safe" &&
      cardEval.card.suit !== trumpSuit // 🎯 CORRETTO: Verifica che non sia briscola
  );

  if (threeNonTrump.length > 0) {
    // Se ci sono più 3 non-briscola, scegli quello del seme più ricco
    const bestThree = threeNonTrump.reduce((best, current) => {
      // Preferisci il 3 con maggior sicurezza e potenziale di controllo del seme
      return current.overallSafetyScore > best.overallSafetyScore
        ? current
        : best;
    });
    console.log(
      `[APERTURA STRATEGICA] 🎯 Uso 3 di ${bestThree.card.suit} per forzare e controllare il gioco - PERFETTO per cooperazione compagno!`
    );
    return bestThree.card;
  }

  // 🎯 PRIORITÀ SECONDARIA: Se il 3 è già uscito, cerca il 2 non-briscola
  const twoNonTrump = evaluations.filter(
    (cardEval) =>
      cardEval.card.rank === "2" &&
      cardEval.playRecommendation === "safe" &&
      cardEval.card.suit !== trumpSuit // 🎯 CORRETTO: Non è briscola
  );

  if (twoNonTrump.length > 0) {
    const bestTwo = twoNonTrump.reduce((best, current) => {
      return current.overallSafetyScore > best.overallSafetyScore
        ? current
        : best;
    });

    console.log(
      `[APERTURA STRATEGICA] ⚡ Uso 2 di ${bestTwo.card.suit} per controllo (3 non disponibile)`
    );
    return bestTwo.card;
  }

  // 🎯 PRIORITÀ TERZIARIA: Carta più alta di un seme ricco (Asso, Re, Cavallo)
  const highCardsRichSuit = evaluations.filter(
    (cardEval) =>
      ["A", "K", "H"].includes(cardEval.card.rank) &&
      cardEval.playRecommendation === "safe" &&
      cardEval.dominantRank <= 2 // È dominante nel suo seme
  );

  if (highCardsRichSuit.length > 0) {
    // Preferisci carte di alto valore ma non l'Asso (per non sprecarlo)
    const bestHighCard = highCardsRichSuit
      .filter((cardEval) => cardEval.card.rank !== "A") // Evita Asso se possibile
      .reduce((best, current) => {
        return current.overallSafetyScore > best.overallSafetyScore
          ? current
          : best;
      }, highCardsRichSuit[0]); // Fallback all'Asso se necessario

    console.log(
      `[APERTURA STRATEGICA] 👑 Uso ${bestHighCard.card.rank} di ${bestHighCard.card.suit} per aprire semi ricco`
    );
    return bestHighCard.card;
  }

  // 🎯 FALLBACK: Carta sicura con valore moderato
  const safesWithValue = evaluations.filter(
    (cardEval) =>
      cardEval.playRecommendation === "safe" &&
      getCardValue(cardEval.card) > 0 &&
      getCardValue(cardEval.card) < 4 // Non sprecare assi
  );

  if (safesWithValue.length > 0) {
    console.log(
      `[APERTURA STRATEGICA] 🛡️ Apertura sicura con valore: ${safesWithValue[0].card.rank}`
    );
    return safesWithValue[0].card;
  }

  // Ultimo fallback: carta più sicura disponibile
  const sortedBySafety = [...evaluations].sort(
    (a, b) => b.overallSafetyScore - a.overallSafetyScore
  );

  console.log(
    `[APERTURA STRATEGICA] ⚠️ Fallback sicurezza: ${sortedBySafety[0].card.rank}`
  );
  return sortedBySafety[0].card;
};

/**
 * Calcola la forza complessiva della mano
 */
const calculateOverallHandStrength = (
  evaluations: CardStrengthInfo[]
): number => {
  if (evaluations.length === 0) return 0;

  const avgSafety =
    evaluations.reduce(
      (sum, cardEval) => sum + cardEval.overallSafetyScore,
      0
    ) / evaluations.length;

  const avgStrategic =
    evaluations.reduce((sum, cardEval) => sum + cardEval.strategicValue, 0) /
    evaluations.length;

  const dominantCount = evaluations.filter(
    (cardEval) => cardEval.isDominant
  ).length;
  const dominantBonus = Math.min(3, dominantCount);

  return Math.min(10, (avgSafety + avgStrategic) / 2 + dominantBonus);
};

/**
 * Ottieni la migliore carta da giocare basandosi sulla valutazione di forza
 */
export const selectBestCardByStrength = (
  hand: Card[],
  memory: CardMemory,
  gameState: GameState,
  strategy: "conservative" | "balanced" | "aggressive" = "balanced"
): {
  selectedCard: Card;
  evaluation: CardStrengthInfo;
  handAnalysis: HandStrengthAnalysis;
  reasoning: string[];
} => {
  const handAnalysis = analyzeHandStrength(hand, memory, gameState);
  const cardEvaluations = hand.map((card) =>
    evaluateCardStrength(card, memory, gameState)
  );

  let selectedEvaluation: CardStrengthInfo;
  const reasoning: string[] = [];
  // Strategia di selezione basata sul parametro
  switch (strategy) {
    case "conservative": {
      // Priorità assoluta alla sicurezza
      const safestCards = cardEvaluations
        .filter((cardEval) => cardEval.overallSafetyScore >= 7)
        .sort((a, b) => b.overallSafetyScore - a.overallSafetyScore);

      if (safestCards.length > 0) {
        selectedEvaluation = safestCards[0];
        reasoning.push(
          `CONSERVATIVO: Scelta della carta più sicura disponibile`
        );
      } else {
        selectedEvaluation = cardEvaluations.sort(
          (a, b) => b.overallSafetyScore - a.overallSafetyScore
        )[0];
        reasoning.push(`CONSERVATIVO: Migliore opzione sicura disponibile`);
      }
      break;
    }

    case "aggressive": {
      // Priorità alle carte dominanti con alto valore strategico
      const dominantCards = cardEvaluations
        .filter((cardEval) => cardEval.isDominant)
        .sort((a, b) => b.strategicValue - a.strategicValue);

      if (dominantCards.length > 0) {
        selectedEvaluation = dominantCards[0];
        reasoning.push(`AGGRESSIVO: Gioca carta dominante per controllo`);
      } else {
        selectedEvaluation = cardEvaluations.sort(
          (a, b) => b.strategicValue - a.strategicValue
        )[0];
        reasoning.push(`AGGRESSIVO: Carta con maggior valore strategico`);
      }
      break;
    }

    case "balanced":
    default: {
      // Bilanciamento tra sicurezza e valore strategico
      const balancedScore = (cardEval: CardStrengthInfo) =>
        (cardEval.overallSafetyScore + cardEval.strategicValue) / 2;

      selectedEvaluation = cardEvaluations.sort(
        (a, b) => balancedScore(b) - balancedScore(a)
      )[0];

      reasoning.push(`BILANCIATO: Ottimo compromesso sicurezza/strategia`);
      break;
    }
  }

  const selectedCard = selectedEvaluation.card;

  reasoning.push(
    `Carta selezionata: ${selectedCard.rank} di ${selectedCard.suit}`
  );
  reasoning.push(
    `Sicurezza: ${selectedEvaluation.overallSafetyScore.toFixed(1)}/10`
  );
  reasoning.push(
    `Valore strategico: ${selectedEvaluation.strategicValue.toFixed(1)}/10`
  );
  reasoning.push(
    `Raccomandazione: ${selectedEvaluation.playRecommendation.toUpperCase()}`
  );

  return {
    selectedCard,
    evaluation: selectedEvaluation,
    handAnalysis,
    reasoning,
  };
};

/**
 * Identifica carte "trappola" - carte che sembrano sicure ma non lo sono
 */
export const identifyTrapCards = (
  hand: Card[],
  memory: CardMemory,
  gameState: GameState
): {
  trapCards: Card[];
  warnings: string[];
} => {
  const trapCards: Card[] = [];
  const warnings: string[] = [];

  for (const card of hand) {
    const evaluation = evaluateCardStrength(card, memory, gameState);

    // Carta che sembra dominante ma ha alto rischio taglio
    if (evaluation.isDominant && evaluation.trumpCutRisk > 0.5) {
      trapCards.push(card);
      warnings.push(
        `⚠️ TRAPPOLA: ${card.rank} di ${
          card.suit
        } è dominante ma alto rischio taglio (${(
          evaluation.trumpCutRisk * 100
        ).toFixed(1)}%)`
      );
    }

    // Carta con valore alto ma bassa sicurezza
    if (getCardValue(card) >= 3 && evaluation.overallSafetyScore < 4) {
      trapCards.push(card);
      warnings.push(
        `⚠️ TRAPPOLA: ${card.rank} di ${
          card.suit
        } ha alto valore ma bassa sicurezza (${evaluation.overallSafetyScore.toFixed(
          1
        )}/10)`
      );
    }
  }

  return { trapCards, warnings };
};

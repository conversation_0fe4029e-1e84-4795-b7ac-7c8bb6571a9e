import { Card, GameState, Player } from "../types/game";
import {
  getWinningCard,
  Suit,
  Card as CardType,
} from "../utils/game/cardUtils";

// Update points after each trick
export const updatePointsAfterTrick = (gameState: GameState): GameState => {
  // If there was a trick winner, let's add points
  if (gameState.trickWinner !== null) {
    // Calculate points for the trick
    const trickPoints = calculatePointsForTrick(gameState.playedCards);

    // Update team points based on the winner
    const winningTeam = gameState.players[gameState.trickWinner].team;
    const updatedTeamPoints = { ...gameState.teamPoints };

    updatedTeamPoints[winningTeam] =
      (updatedTeamPoints[winningTeam] || 0) + trickPoints;

    return {
      ...gameState,
      teamPoints: updatedTeamPoints,
    };
  }

  return gameState;
};

// Calculate points for a trick based on Maraffa rules - DEPRECATED: usa calculateScore in gameLogic.ts
export const calculatePointsForTrick = (cards: Card[]): number => {
  // TEMPORARY: mantieni il vecchio sistema per compatibilità
  // TODO: aggiorna questo file per usare il nuovo sistema preciso
  return cards.reduce((sum, card) => {
    switch (card.rank) {
      case "A":
        return sum + 1.0; // Asso vale 1 punto
      case "3":
      case "2":
      case "K":
      case "H":
      case "J":
        return sum + 0.3; // Figure valgono 0.3 punti
      default:
        return sum; // Carte dal 4 al 7 non valgono nulla
    }
  }, 0);
};

// Function to determine the winner of a trick
export const calculateTrickWinner = (
  playedCards: Card[],
  briscola?: string
): number | null => {
  if (playedCards.length === 0) return null;

  // Adapt the Card object from game.ts to match the Card interface expected by cardUtils
  const adaptedCards = playedCards.map(
    (card) =>
      ({
        id: card.id,
        displayName: card.displayName || String(card.value), // Add a displayName property
        order: card.order || 0, // Will be updated below
        suit: (card.suit || "coins") as Suit, // Cast to Suit type
        rank: (card.rank || "7") as string, // Use string instead of any
        value: card.value,
        points: 0, // Add missing points property
      } as CardType)
  );

  // Set proper order based on rank
  adaptedCards.forEach((card) => {
    if (card.rank === "A") card.order = 8;
    else if (card.rank === "3") card.order = 10;
    else if (card.rank === "2") card.order = 9;
    else if (card.rank === "K") card.order = 7;
    else if (card.rank === "H") card.order = 6;
    else if (card.rank === "J") card.order = 5;
    else if (card.rank === "7") card.order = 4;
    else if (card.rank === "6") card.order = 3;
    else if (card.rank === "5") card.order = 2;
    else if (card.rank === "4") card.order = 1;
  });

  try {
    // We need to ensure the cards have the right structure for getWinningCard
    const winningSuit = adaptedCards[0].suit;
    const winningCard = getWinningCard(
      adaptedCards,
      winningSuit,
      (briscola || winningSuit) as Suit
    );

    // Find the index of the winning card in the played cards
    const winnerIndex = adaptedCards.findIndex(
      (card) => card.id === winningCard.id
    );
    return winnerIndex >= 0 ? winnerIndex : 0;
  } catch (error) {
    console.error("Error calculating trick winner:", error);
    return 0; // Default to first player in case of error
  }
};

// Function to calculate hand points (used at end of hand)
export const calculateHandPoints = (cards: Card[]): number => {
  let rawPoints = 0;

  // Calculate according to Maraffa rules
  cards.forEach((card) => {
    if (card.rank === "A")
      rawPoints += 3; // Will be divided by 3 later for 1 point
    else if (["3", "2", "K", "H", "J"].includes(card.rank as string))
      rawPoints += 1; // 1/3 point each
  });

  // Integer division
  return Math.floor(rawPoints / 3);
};

// Calculate the final score at the end of a hand
export const calculateFinalHandScore = (gameState: GameState): GameState => {
  // Check if either team has a Maraffa (Asso, Due, Tre of the same suit in briscola)
  const checkForMaraffa = (team: number, cards: Card[]): number => {
    if (!gameState.briscola) return 0;

    // Get all briscola cards
    const briscolaCards = cards.filter(
      (card) => card.suit === gameState.briscola
    );

    // Check if team has A, 2, 3 of briscola suit
    const hasA = briscolaCards.some((card) => card.rank === "A");
    const has2 = briscolaCards.some((card) => card.rank === "2");
    const has3 = briscolaCards.some((card) => card.rank === "3");

    return hasA && has2 && has3 ? 3 : 0; // 3 bonus points for Maraffa
  };

  // Calculate Maraffa bonus for each team
  let teamACards: Card[] = [];
  let teamBCards: Card[] = [];

  // Collect cards won by each team
  gameState.trickHistory.forEach((trick) => {
    const winnerTeam = gameState.players[trick.winner || 0].team;
    if (winnerTeam === 0) {
      teamACards = [...teamACards, ...trick.cards];
    } else {
      teamBCards = [...teamBCards, ...trick.cards];
    }
  });

  const teamAMaraffaBonus = checkForMaraffa(0, teamACards);
  const teamBMaraffaBonus = checkForMaraffa(1, teamBCards);

  // Update team points including bonuses
  const updatedTeamPoints = { ...gameState.teamPoints };

  // Add Maraffa bonus
  if (teamAMaraffaBonus > 0) {
    updatedTeamPoints[0] = (updatedTeamPoints[0] || 0) + teamAMaraffaBonus;
  }

  if (teamBMaraffaBonus > 0) {
    updatedTeamPoints[1] = (updatedTeamPoints[1] || 0) + teamBMaraffaBonus;
  }

  // Add 1 point for last trick if it's the last trick
  if (gameState.isLastTrick && gameState.trickWinner !== null) {
    const lastTrickWinnerTeam = gameState.players[gameState.trickWinner].team;
    updatedTeamPoints[lastTrickWinnerTeam] =
      (updatedTeamPoints[lastTrickWinnerTeam] || 0) + 1;
  }

  return {
    ...gameState,
    teamPoints: updatedTeamPoints,
  };
};

// Function to check if game is over
export const isGameOver = (
  gameState: GameState,
  victoryPoints: number = 31
): boolean => {
  // Game is over if one team reaches the winning score
  const winningScore = victoryPoints;

  return Object.values(gameState.teamPoints).some(
    (points) => points >= winningScore
  );
};

// Determine the winner of the game
export const determineWinner = (
  gameState: GameState,
  victoryPoints: number = 31
): 0 | 1 | null => {
  if (gameState.teamPoints[0] >= victoryPoints) return 0;
  if (gameState.teamPoints[1] >= victoryPoints) return 1;

  return null;
};

// Function to play a card
export const playCard = (
  gameState: GameState,
  playerIndex: number,
  cardIndex: number
): GameState => {
  // Get the player and the card they're playing
  const player = gameState.players[playerIndex];
  const card = player.hand[cardIndex];

  // Create updated player hands
  const updatedPlayers = [...gameState.players];
  updatedPlayers[playerIndex] = {
    ...player,
    hand: player.hand.filter((_, i) => i !== cardIndex),
  };

  // Add the card to played cards
  const updatedPlayedCards = [
    ...gameState.playedCards,
    { ...card, playedBy: playerIndex },
  ];

  // Check if this will be the last trick (all players will have no cards after this trick)
  const isLastTrick = updatedPlayers.every(
    (p) => p.hand.length === 0 || p.hand.length === 1
  );

  let updatedGameState: GameState = {
    ...gameState,
    players: updatedPlayers,
    playedCards: updatedPlayedCards,
    currentPlayer: (playerIndex + 1) % gameState.players.length,
    isLastTrick: isLastTrick,
  };

  // If all players have played a card, determine the trick winner
  if (updatedPlayedCards.length === gameState.players.length) {
    const trickWinner = calculateTrickWinner(
      updatedPlayedCards,
      gameState.briscola
    );

    // Create a trick record to store in the trick history
    const trick = {
      cards: updatedPlayedCards,
      winner: trickWinner,
      points: calculatePointsForTrick(updatedPlayedCards),
    };

    updatedGameState = {
      ...updatedGameState,
      trickWinner,
      trickHistory: [...gameState.trickHistory, trick],
      playedCards: [],
      currentPlayer: trickWinner || 0,
    };

    // Update points for this trick
    updatedGameState = updatePointsAfterTrick(updatedGameState);

    // Check if the hand is over (players have no more cards)
    const isHandOver = updatedGameState.players.every(
      (player) => player.hand.length === 0
    );

    if (isHandOver) {
      // Calculate final hand score and prepare for a new hand
      updatedGameState = calculateFinalHandScore(updatedGameState);

      // Check if the game is over using the configurable victory points
      if (isGameOver(updatedGameState, updatedGameState.victoryPoints || 31)) {
        const winner = determineWinner(
          updatedGameState,
          updatedGameState.victoryPoints || 31
        );
        updatedGameState = {
          ...updatedGameState,
          gameOver: true,
          winner,
        };
      }
    }
  }

  return updatedGameState;
};

// Mock online game functions for offline use
export const mockCreateGameSession = async (
  userId: string,
  username: string
) => {
  console.log(
    "Creating game session with userId:",
    userId,
    "and username:",
    username
  );
  return "mock-session-id";
};

export const mockGetAvailableGameSessions = async () => {
  console.log("Getting available game sessions - placeholder");
  // Return mock game sessions for display
  return [
    {
      id: "mock-session-1",
      status: "waiting" as const,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      created_by: "user-123",
      players: [
        {
          id: "0",
          user_id: "user-123",
          username: "Marco",
          position: "north" as const,
          team: 0 as const,
          connected: true,
        },
      ],
      current_state: null,
      winner_team: null,
    },
    {
      id: "mock-session-2",
      status: "waiting" as const,
      created_at: new Date(Date.now() - 120000).toISOString(),
      updated_at: new Date(Date.now() - 120000).toISOString(),
      created_by: "user-456",
      players: [
        {
          id: "0",
          user_id: "user-456",
          username: "Giulia",
          position: "north" as const,
          team: 0 as const,
          connected: true,
        },
        {
          id: "1",
          user_id: "user-789",
          username: "Lorenzo",
          position: "east" as const,
          team: 1 as const,
          connected: true,
        },
      ],
      current_state: null,
      winner_team: null,
    },
  ];
};

export const mockJoinGameSession = async (
  sessionId: string,
  userId: string,
  username: string
) => {
  console.log(
    "Joining game session with sessionId:",
    sessionId,
    "userId:",
    userId,
    "and username:",
    username
  );
  return true;
};

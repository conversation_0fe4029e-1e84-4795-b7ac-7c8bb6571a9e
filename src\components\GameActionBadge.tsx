import React from "react";
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";

export type ActionType = "busso" | "striscio" | "volo" | "maraffa" | null;

interface GameActionBadgeProps {
  type: ActionType;
  playerName: string;
}

const GameActionBadge = ({ type, playerName }: GameActionBadgeProps) => {
  const badgeStyles = {
    busso: "bg-red-500 border-red-600 text-white hover:bg-red-600",
    striscio: "bg-yellow-500 border-yellow-600 text-white hover:bg-yellow-600",
    volo: "bg-blue-500 border-blue-600 text-white hover:bg-blue-600",
    maraffa: "bg-green-500 border-green-600 text-white hover:bg-green-600",
  };

  const badgeEmojis = {
    busso: "👊",
    striscio: "🐍",
    volo: "🕊️",
    maraffa: "♣️",
  };

  const badgeLabels = {
    busso: "Busso!",
    striscio: "<PERSON><PERSON>cio!",
    volo: "Volo!",
    maraffa: "Maraffa!",
  };

  return (
    <motion.div
      initial={{ scale: 0.8, opacity: 0, y: -20 }}
      animate={{ scale: 1, opacity: 1, y: 0 }}
      exit={{
        scale: 0.6,
        opacity: 0,
        y: -30,
        transition: {
          duration: 0.8,
          ease: "easeIn",
        },
      }}
      transition={{ type: "spring", damping: 12 }}
      className="absolute z-50 transform -translate-x-1/2 -translate-y-1/2 left-1/2 top-1/2 pointer-events-none"
    >
      <div className="flex flex-col items-center space-y-1">
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{
            scale: 0.7,
            opacity: 0,
            rotate: 5,
            transition: {
              duration: 0.5,
              ease: "easeIn",
            },
          }}
          transition={{ delay: 0.1, duration: 0.4 }}
        >
          <Badge
            className={`px-4 py-2 text-lg font-bold border-2 shadow-lg ${badgeStyles[type]}`}
            variant="outline"
          >
            <motion.span
              animate={{
                scale: [1, 1.2, 1],
              }}
              exit={{
                scale: 0.5,
                opacity: 0,
                transition: {
                  duration: 0.3,
                  ease: "easeIn",
                },
              }}
              transition={{
                repeat: 2,
                duration: 0.7,
              }}
              className="mr-2"
            >
              {badgeEmojis[type]}
            </motion.span>
            <motion.span
              animate={{
                scale: [1, 1.1, 1, 1.1, 1],
              }}
              exit={{
                scale: 0.6,
                opacity: 0,
                x: -10,
                transition: {
                  delay: 0.1,
                  duration: 0.4,
                  ease: "easeIn",
                },
              }}
              transition={{
                duration: 1.4,
                ease: "easeInOut",
              }}
            >
              {badgeLabels[type]}
            </motion.span>
          </Badge>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{
            opacity: 0,
            y: -15,
            scale: 0.8,
            transition: {
              delay: 0.1,
              duration: 0.6,
              ease: "easeInOut",
            },
          }}
          transition={{
            delay: 0.3,
            duration: 0.5,
            ease: "easeInOut",
          }}
          className="text-xs text-white bg-black/60 backdrop-blur-sm px-2 py-1 rounded-full shadow-md"
        >
          {playerName}
        </motion.div>
      </div>
    </motion.div>
  );
};

export default GameActionBadge;

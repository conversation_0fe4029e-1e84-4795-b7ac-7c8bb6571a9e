import { GameState } from "../../game/gameLogic";
import { Card, Rank } from "../../game/cardUtils";
import { CardMemory, TrickEvaluation } from "../types";
import { getCardValue, getCardStrengthScore } from "./cardUtils";
// Performance modules removed for simplification

export const evaluateCardStrategicValue = (
  card: Card,
  memory: CardMemory,
  state: GameState,
  playerIndex: number
): number => {
  let value = getCardValue(card) * 10; // Valore base in punti

  // Bonus per le briscole
  if (card.suit === state.trumpSuit) {
    value += 15;

    // Bonus extra per briscole alte
    if ([Rank.Ace, Rank.Three, Rank.Two].includes(card.rank as Rank)) {
      value += 20;
    }
  }

  // Penalità se è l'ultima carta alta di un seme
  const highCardsInSuit =
    memory.highCardsRemaining[card.suit?.toString() || ""] || [];
  if (highCardsInSuit.length === 1 && highCardsInSuit[0].rank === card.rank) {
    value += 25; // Molto preziosa se è l'ultima carta alta del seme
  }

  // Bonus se poche carte di questo seme sono state giocate
  const playedInSuit =
    memory.playedBySuit[card.suit?.toString() || ""]?.length || 0;
  if (playedInSuit <= 2) {
    value += 10;
  }

  return value;
};

export const evaluateWinProbability = (
  card: Card,
  currentTrick: Card[],
  state: GameState,
  memory: CardMemory,
  playerIndex: number
): number => {
  // Simplified without caching
  let probability = 0.5; // Base neutrale

  const remainingPlayers = 4 - currentTrick.length - 1;
  const isCardTrump = card.suit === state.trumpSuit;
  const currentTrickHasTrump = currentTrick.some(
    (c) => c.suit === state.trumpSuit
  );

  // Se gioco una briscola
  if (isCardTrump) {
    const cardStrength = getCardStrengthScore(card);
    const trumpsStillOut = memory.trumpsRemaining.filter(
      (c) =>
        c.suit === state.trumpSuit && getCardStrengthScore(c) > cardStrength
    ).length;

    // Probabilità basata su quante briscole più forti sono ancora in gioco
    if (trumpsStillOut === 0) {
      probability = 0.95; // Briscola più forte
    } else if (trumpsStillOut <= 2) {
      probability = 0.8 - trumpsStillOut * 0.2;
    } else {
      probability = 0.4 - trumpsStillOut * 0.05;
    }
  } else {
    // Se gioco seme normale
    if (currentTrickHasTrump) {
      probability = 0.1; // Molto difficile vincere contro briscola
    } else {
      const cardStrength = getCardStrengthScore(card);
      const strongerCardsInSuit =
        memory.highCardsRemaining[card.suit?.toString() || ""]?.filter(
          (c) => getCardStrengthScore(c) > cardStrength
        ).length || 0;

      if (strongerCardsInSuit === 0) {
        probability = 0.9; // Carta più forte del seme
      } else if (strongerCardsInSuit <= 2) {
        probability = 0.7 - strongerCardsInSuit * 0.15;
      } else {
        probability = 0.3;
      }

      // Riduci probabilità se altri giocatori possono giocare briscole
      const opponentTrumpProbability = calculateOpponentTrumpProbability(
        state,
        memory,
        playerIndex
      );
      probability *= 1 - opponentTrumpProbability * 0.7;
    }
  }
  // Aggiusta per numero di giocatori rimanenti
  probability = Math.pow(probability, remainingPlayers);

  const result = Math.max(0.05, Math.min(0.95, probability));
  return result;
};

export const evaluateEnhancedTrickWorthiness = (
  currentTrick: Card[],
  potentialCard: Card,
  state: GameState,
  memory: CardMemory,
  playerIndex: number,
  isLastTrick: boolean = false
): TrickEvaluation => {
  // Simplified without caching
  // Calcola valore totale della presa
  let trickValue = currentTrick.reduce(
    (sum, card) => sum + getCardValue(card),
    0
  );
  trickValue += getCardValue(potentialCard);

  // Bonus significativo per ultima presa
  if (isLastTrick) {
    trickValue += 1;
  }

  // Calcola probabilità di vincita
  const winProbability = evaluateWinProbability(
    potentialCard,
    currentTrick,
    state,
    memory,
    playerIndex
  );

  // Calcola "costo" della carta giocata
  const cardCost =
    evaluateCardStrategicValue(potentialCard, memory, state, playerIndex) / 10;

  // Valore atteso = (probabilità di vincita * valore presa) - costo carta
  let expectedValue = winProbability * trickValue - cardCost * 0.3;

  // Bonus strategici aggiuntivi
  const trickNumber = state.trickNumber ?? 1;

  // Incentiva la presa dell'ultima mano più fortemente
  if (isLastTrick) {
    expectedValue += 2; // Bonus extra per ultima presa
  }

  // Nelle prime prese, sii più conservativo con le briscole alte
  if (trickNumber <= 3 && potentialCard.suit === state.trumpSuit) {
    const cardStrength = getCardStrengthScore(potentialCard);
    if (cardStrength >= 8) {
      // Asso, 2, 3 di briscola
      expectedValue -= 1; // Penalità per uso precoce di briscole alte
    }
  }
  // Decisione se tentare la presa (soglia più intelligente)
  const shouldAttempt =
    expectedValue > 0.3 ||
    (isLastTrick && trickValue >= 1) ||
    (trickValue >= 3 && winProbability > 0.3) ||
    (trickNumber >= 8 && trickValue >= 1); // Fine partita: più aggressivo

  const result = {
    worthiness: expectedValue,
    shouldAttempt,
  };

  return result;
};

export const evaluateHandQuality = (
  cards: Card[],
  trumpSuit: string | null | undefined,
  memory: CardMemory
): number => {
  let quality = 0;

  // Conta le briscole
  const trumpCards = cards.filter((card) => card.suit === trumpSuit);
  quality += trumpCards.length * 5; // Ogni briscola vale 5 punti base

  // Bonus per briscole alte
  const highTrumps = trumpCards.filter(
    (card) =>
      [Rank.Ace, Rank.Three, Rank.Two].includes(card.rank as Rank) ||
      ["A", "3", "2"].includes(card.rank as string)
  );
  quality += highTrumps.length * 15;

  // Conta le carte di valore in ogni seme
  const suits = ["Coins", "Cups", "Swords", "Clubs"];
  suits.forEach((suit) => {
    const suitCards = cards.filter((card) => card.suit === suit);
    const valueCards = suitCards.filter((card) => getCardValue(card) > 0);
    quality += valueCards.length * 8;

    // Bonus se hai molte carte dello stesso seme (controllo)
    if (suitCards.length >= 4) {
      quality += 10;
    }
  });

  // Penalità per mani sbilanciate (troppe carte di un seme non briscola)
  const nonTrumpCounts = suits
    .filter((suit) => suit !== trumpSuit)
    .map((suit) => cards.filter((card) => card.suit === suit).length);

  const maxNonTrump = Math.max(...nonTrumpCounts);
  if (maxNonTrump >= 6) {
    quality -= 5; // Penalità per squilibrio
  }

  return quality;
};

export const getAggressivenessLevel = (
  handQuality: number,
  trickNumber: number,
  cardsRemaining: number
): number => {
  let aggressiveness = 0.5; // Base neutrale

  // Mani di alta qualità permettono più aggressività
  if (handQuality >= 80) {
    aggressiveness += 0.3;
  } else if (handQuality >= 60) {
    aggressiveness += 0.2;
  } else if (handQuality <= 30) {
    aggressiveness -= 0.2;
  }

  // All'inizio del gioco, sii più conservativo
  if (trickNumber <= 2) {
    aggressiveness -= 0.1;
  }

  // Nelle fasi finali, sii più aggressivo
  if (cardsRemaining <= 3) {
    aggressiveness += 0.2;
  }

  return Math.max(0.1, Math.min(0.9, aggressiveness));
};

const calculateOpponentTrumpProbability = (
  state: GameState,
  memory: CardMemory,
  playerIndex: number
): number => {
  const myTrumps =
    state.players[playerIndex]?.hand?.filter((c) => c.suit === state.trumpSuit)
      .length || 0;

  const trumpsPlayed =
    memory.playedBySuit[state.trumpSuit?.toString() || ""]?.length || 0;
  const trumpsUnaccounted = 10 - trumpsPlayed - myTrumps;

  // Stima conservativa: assumiamo che almeno uno degli avversari abbia briscole
  if (trumpsUnaccounted >= 3) return 0.8;
  if (trumpsUnaccounted >= 1) return 0.5;
  return 0.1;
};

# 🔥 Guida Configurazione Abbonamento Premium

Questa guida ti aiuterà a configurare il sistema di abbonamenti premium per Marafone Romagnolo usando RevenueCat e Google Play Console.

## 📋 Prerequisiti

- Account Google Play Console attivo
- App pubblicata su Google Play Store (almeno in versione di test)
- Account RevenueCat (gratuito fino a $2.5M di fatturato)

## 🚀 Passaggio 1: Configurazione Google Play Console

### 1.1 Accedi a Google Play Console
1. Vai su [Google Play Console](https://play.google.com/console)
2. Seleziona la tua app "Marafone Romagnolo"

### 1.2 Configura i Prodotti In-App
1. Nel menu laterale, vai su **Monetizzazione** → **Prodotti**
2. Clicca su **Crea prodotto**

#### Prodotto 1: Abbonamento Mensile
- **ID prodotto**: `maraffa_premium_monthly`
- **Nome**: `Premium Mensile`
- **Descrizione**: `Rimuove tutte le pubblicità per un mese`
- **Prezzo**: `€0.99`
- **Periodo di fatturazione**: `1 mese`
- **Periodo di prova gratuita**: `3 giorni` (opzionale)

#### Prodotto 2: Acquisto Lifetime
- **ID prodotto**: `maraffa_premium_lifetime`
- **Nome**: `Premium Lifetime`
- **Descrizione**: `Rimuove tutte le pubblicità per sempre`
- **Prezzo**: `€8.99`
- **Tipo**: `Prodotto gestito` (non abbonamento)

### 1.3 Configura i Gruppi di Abbonamento
1. Vai su **Monetizzazione** → **Abbonamenti**
2. Crea un nuovo gruppo chiamato `Premium`
3. Aggiungi l'abbonamento mensile al gruppo

### 1.4 Attiva i Prodotti
1. Salva tutti i prodotti
2. Attivali per la distribuzione

## 🔧 Passaggio 2: Configurazione RevenueCat

### 2.1 Crea Account RevenueCat
1. Vai su [RevenueCat](https://www.revenuecat.com/)
2. Registrati gratuitamente
3. Crea un nuovo progetto chiamato "Marafone Romagnolo"

### 2.2 Configura l'App Android
1. Nel dashboard RevenueCat, clicca su **Apps**
2. Clicca **+ New** per aggiungere una nuova app
3. Seleziona **Android**
4. Inserisci:
   - **App name**: `Marafone Romagnolo`
   - **Bundle ID**: `com.eliazavatta.maraffa`

### 2.3 Collega Google Play Console
1. Nella sezione **Service Credentials**, clicca **Upload**
2. Carica il file JSON delle credenziali di servizio di Google Play
   
   **Come ottenere il file JSON:**
   - Vai su [Google Cloud Console](https://console.cloud.google.com/)
   - Seleziona il progetto collegato alla tua app
   - Vai su **IAM & Admin** → **Service Accounts**
   - Crea un nuovo account di servizio o usa quello esistente
   - Scarica la chiave JSON
   - Assicurati che l'account abbia i permessi per Google Play Developer API

### 2.4 Configura i Prodotti in RevenueCat
1. Vai su **Products**
2. Clicca **+ New**
3. Aggiungi i prodotti:
   - `maraffa_premium_monthly`
   - `maraffa_premium_lifetime`

### 2.5 Configura gli Entitlements
1. Vai su **Entitlements**
2. Crea un entitlement chiamato `premium`
3. Collega entrambi i prodotti a questo entitlement

### 2.6 Copia la API Key
1. Vai su **API Keys**
2. Copia la **Public API Key** per Android
3. Sostituisci `your_revenuecat_api_key_here` nel file `src/services/purchaseService.ts`

## 🔨 Passaggio 3: Aggiornamenti Codice

### 3.1 Aggiorna PurchaseService
Modifica il file `src/services/purchaseService.ts`:

```typescript
await Purchases.configure({
  apiKey: "rcpk_xxxxxxxxxxxxxxxxxxxxxxxx", // La tua API key qui
  appUserID: null,
});
```

### 3.2 Integra con AdMobService
Il sistema è già configurato per nascondere le pubblicità quando `isPremium()` restituisce `true`.

## 🧪 Passaggio 4: Test

### 4.1 Test con Account di Prova
1. In Google Play Console, vai su **Setup** → **License testing**
2. Aggiungi il tuo account Google come tester
3. Compila e installa l'app in modalità debug
4. Testa gli acquisti (saranno gratuiti per gli account di test)

### 4.2 Test RevenueCat
1. Nel dashboard RevenueCat, vai su **Customer lookup**
2. Cerca il tuo User ID per verificare gli acquisti

## 📱 Passaggio 5: Pubblicazione

### 5.1 Build di Produzione
```bash
npm run build
npx cap sync
npx cap build android
```

### 5.2 Upload su Google Play
1. Genera l'APK/AAB firmato
2. Carica su Google Play Console
3. Pubblica come versione di test interna prima della produzione

## 🎯 Prezzi Configurati

- **Mensile**: €0.99/mese (sconto 66% da €2.99)
- **Lifetime**: €8.99 una tantum (sconto 40% da €14.99)

## 🔍 Monitoraggio

### RevenueCat Dashboard
- Monitora le metriche di conversione
- Analizza i ricavi
- Gestisci i rimborsi

### Google Play Console
- Controlla le statistiche di acquisto
- Gestisci le recensioni relative ai prezzi
- Monitora i chargeback

## ⚠️ Note Importanti

1. **Test sempre prima della produzione**: Usa account di test per verificare tutto
2. **Backup delle chiavi**: Salva in modo sicuro tutte le API key
3. **Conformità GDPR**: RevenueCat gestisce automaticamente la privacy
4. **Rimborsi**: Gestiti automaticamente da Google Play e sincronizzati con RevenueCat

## 🆘 Risoluzione Problemi

### Errore "Prodotto non trovato"
- Verifica che i prodotti siano attivi in Google Play Console
- Controlla che gli ID prodotto corrispondano esattamente

### Errore "Entitlement non attivo"
- Verifica la configurazione degli entitlements in RevenueCat
- Controlla che i prodotti siano collegati correttamente

### Test non funzionanti
- Assicurati di usare un account di test configurato
- Verifica che l'app sia firmata correttamente

## 📞 Supporto

- **RevenueCat**: [Documentazione](https://docs.revenuecat.com/)
- **Google Play**: [Centro assistenza](https://support.google.com/googleplay/android-developer/)

---

✅ **Una volta completata la configurazione, il sistema di abbonamenti sarà completamente funzionale!**

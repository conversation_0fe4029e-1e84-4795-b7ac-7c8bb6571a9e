-- Script SQL per configurare le Row Level Security Policies per Supabase
-- Eseguire questo script nel SQL Editor del Dashboard Supabase

-- 1. Abilita RLS sulla tabella profiles (se non già abilitato)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- 2. Elimina eventuali policy esistenti (opzionale, se necessario riconfigurare)
-- DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
-- DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
-- DROP POLICY IF EXISTS "Users can update own profile" ON profiles;

-- 3. Policy per permettere agli utenti di leggere il proprio profilo
CREATE POLICY "Users can view own profile" ON profiles
FOR SELECT USING (auth.uid() = id);

-- 4. Policy per permettere agli utenti di inserire il proprio profilo
CREATE POLICY "Users can insert own profile" ON profiles
FOR INSERT WITH CHECK (auth.uid() = id);

-- 5. Policy per permettere agli utenti di aggiornare il proprio profilo
CREATE POLICY "Users can update own profile" ON profiles
FOR UPDATE USING (auth.uid() = id);

-- 6. Policy per permettere agli utenti di eliminare il proprio profilo (opzionale)
CREATE POLICY "Users can delete own profile" ON profiles
FOR DELETE USING (auth.uid() = id);

-- 7. Verifica che le policy siano state create correttamente
SELECT schemaname, tablename, policyname, cmd, qual
FROM pg_policies 
WHERE tablename = 'profiles';

-- 8. Trigger per aggiornare automaticamente updated_at (opzionale ma consigliato)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON profiles 
    FOR EACH ROW 
    EXECUTE PROCEDURE update_updated_at_column();

-- 9. Funzione per creare automaticamente un profilo quando un utente si registra
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, username, avatar_url, created_at, updated_at)
  VALUES (
    new.id,
    -- Estrae i primi 18 caratteri della parte email prima della @ se presente, altrimenti usa il nome o 'Giocatore'
    CASE 
      WHEN new.email IS NOT NULL AND new.email LIKE '%@%' THEN 
        LEFT(SPLIT_PART(new.email, '@', 1), 18)
      ELSE 
        LEFT(COALESCE(new.raw_user_meta_data->>'full_name', new.raw_user_meta_data->>'name', 'Giocatore'), 18)
    END,
    new.raw_user_meta_data->>'avatar_url',
    now(),
    now()
  );
  RETURN new;
END;
$$ language plpgsql security definer;

-- 10. Trigger per eseguire automaticamente la funzione quando un utente si registra
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- ====================================================================
-- CONFIGURAZIONE RLS PER TABELLA FRIENDSHIPS (amicizie confermate)
-- ====================================================================

-- 11. Abilita RLS sulla tabella friendships
ALTER TABLE friendships ENABLE ROW LEVEL SECURITY;

-- 12. Policy per permettere agli utenti di leggere le proprie relazioni di amicizia
CREATE POLICY "Users can view own friendships" ON friendships
FOR SELECT USING (
  auth.uid() = user_id OR auth.uid() = friend_id
);

-- 13. Policy per permettere agli utenti di inserire amicizie confermate
CREATE POLICY "Users can insert own friendships" ON friendships
FOR INSERT WITH CHECK (
  auth.uid() = user_id
);

-- 14. Policy per permettere agli utenti di aggiornare le proprie relazioni di amicizia
CREATE POLICY "Users can update own friendships" ON friendships
FOR UPDATE USING (
  auth.uid() = user_id OR auth.uid() = friend_id
);

-- 15. Policy per permettere agli utenti di eliminare le proprie relazioni di amicizia
-- CRITICO: Permette la rimozione bidirezionale delle amicizie
CREATE POLICY "Users can delete own friendships" ON friendships
FOR DELETE USING (
  auth.uid() = user_id OR auth.uid() = friend_id
);

-- ====================================================================
-- CONFIGURAZIONE RLS PER TABELLA FRIEND_REQUESTS (richieste di amicizia)
-- ====================================================================

-- 16. Abilita RLS sulla tabella friend_requests
ALTER TABLE friend_requests ENABLE ROW LEVEL SECURITY;

-- 17. Policy per permettere agli utenti di leggere le proprie richieste di amicizia
CREATE POLICY "Users can view own friend_requests" ON friend_requests
FOR SELECT USING (
  auth.uid() = sender_id OR auth.uid() = receiver_id
);

-- 18. Policy per permettere agli utenti di inserire richieste di amicizia
CREATE POLICY "Users can insert own friend_requests" ON friend_requests
FOR INSERT WITH CHECK (
  auth.uid() = sender_id
);

-- 19. Policy per permettere agli utenti di aggiornare le proprie richieste di amicizia
CREATE POLICY "Users can update own friend_requests" ON friend_requests
FOR UPDATE USING (
  auth.uid() = sender_id OR auth.uid() = receiver_id
);

-- 20. Policy per permettere agli utenti di eliminare le proprie richieste di amicizia
CREATE POLICY "Users can delete own friend_requests" ON friend_requests
FOR DELETE USING (
  auth.uid() = sender_id OR auth.uid() = receiver_id
);

-- 21. Verifica policy friendships e friend_requests
SELECT schemaname, tablename, policyname, cmd, qual, with_check
FROM pg_policies 
WHERE tablename IN ('friendships', 'friend_requests')
ORDER BY tablename, policyname;

-- Note:
-- - Assicurati che la tabella 'profiles' abbia le colonne: id (uuid), username (text), avatar_url (text), created_at (timestamp), updated_at (timestamp)
-- - L'id dovrebbe essere collegato a auth.users.id come foreign key
-- - La tabella 'friendships' deve avere: user_id (uuid), friend_id (uuid), created_at (timestamp)
-- - La tabella 'friend_requests' deve avere: sender_id (uuid), receiver_id (uuid), status (text), created_at (timestamp)
-- - Le policy di DELETE per friendships sono critiche per la rimozione bidirezionale degli amici
-- - Se la struttura delle tabelle è diversa, adatta le query di conseguenza

/**
 * Export centrale per tutte le strategie dell'AI
 */

// Strategia unificata
export { getUnifiedAIMove } from "./unifiedStrategy";

// Strategie specializzate
export * from "./specialized/cooperativeStrategy";
export * from "./specialized/trumpStrategy";

// Base strategy removed for simplification

// Funzione principale di selezione strategia (ora unificata)
import { getUnifiedAIMove } from "./unifiedStrategy";
import { AIDifficulty } from "../types";
import { Card } from "../../game/cardUtils";
import { GameState } from "../../game/gameLogic";

export const selectCardByDifficulty = (
  cards: Card[],
  state: GameState,
  playerIndex: number,
  difficulty: AIDifficulty
): Card => {
  return getUnifiedAIMove(cards, state, playerIndex, difficulty);
};

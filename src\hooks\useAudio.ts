import { useEffect, useRef, useCallback } from "react";
import { AudioManager, SoundType } from "@/utils/audio/AudioManager";

interface AudioConfig {
  masterVolume: number;
  musicVolume: number;
  soundEffectsVolume: number;
  isEnabled: boolean;
  soundEffectsEnabled: boolean;
  musicEnabled: boolean;
}

interface UseAudioReturn {
  playSound: (
    sound: SoundType,
    options?: { volume?: number; pitch?: number; playerId?: number }
  ) => void;
  playMusic: (trackName: string) => void;
  stopMusic: () => void;
  isTrackPlaying: (trackName: string) => boolean;
  getCurrentTrackName: () => string | null;
  setMusicVolume: (volume: number) => void;
  setSoundVolume: (volume: number) => void;
  setMusicEnabled: (enabled: boolean) => void;
  setEffectsEnabled: (enabled: boolean) => void;
  getConfig: () => AudioConfig;
  toggleMute: () => void;
  isMuted: boolean;
}

export const useAudio = (): UseAudioReturn => {
  const audioManagerRef = useRef<AudioManager | null>(null);

  useEffect(() => {
    if (!audioManagerRef.current) {
      audioManagerRef.current = AudioManager.getInstance();
    }

    return () => {
      // Cleanup non necessario, AudioManager è singleton
    };
  }, []);
  const playSound = useCallback(
    (
      sound: SoundType,
      options?: { volume?: number; pitch?: number; playerId?: number }
    ) => {
      audioManagerRef.current?.playSound(sound, options);
    },
    []
  );

  const playMusic = useCallback((trackName: string) => {
    audioManagerRef.current?.playMusic(trackName);
  }, []);
  const stopMusic = useCallback(() => {
    audioManagerRef.current?.stopMusic();
  }, []);

  const isTrackPlaying = useCallback((trackName: string) => {
    return audioManagerRef.current?.isTrackPlaying(trackName) ?? false;
  }, []);

  const getCurrentTrackName = useCallback(() => {
    return audioManagerRef.current?.getCurrentTrackName() ?? null;
  }, []);

  const setMusicVolume = useCallback((volume: number) => {
    audioManagerRef.current?.setMusicVolume(volume);
  }, []);

  const setSoundVolume = useCallback((volume: number) => {
    audioManagerRef.current?.setSoundEffectsVolume(volume);
  }, []);
  const setMusicEnabled = useCallback((enabled: boolean) => {
    audioManagerRef.current?.setMusicEnabled(enabled);
  }, []);

  const setEffectsEnabled = useCallback((enabled: boolean) => {
    audioManagerRef.current?.setSoundEffectsEnabled(enabled);
  }, []);
  const getConfig = useCallback((): AudioConfig => {
    const manager = audioManagerRef.current;
    if (!manager) {
      return {
        masterVolume: 0.7,
        musicVolume: 0.2, // ✅ Volume al 20% per coerenza con AudioManager
        soundEffectsVolume: 0.8,
        isEnabled: true,
        soundEffectsEnabled: true,
        musicEnabled: false, // ✅ Musica disabilitata di default
      };
    }

    return {
      masterVolume: manager.getMasterVolume(),
      musicVolume: manager.getMusicVolume(),
      soundEffectsVolume: manager.getSoundEffectsVolume(),
      isEnabled: manager.isAudioEnabled(),
      soundEffectsEnabled: manager.isSoundEffectsEnabled(),
      musicEnabled: manager.isMusicEnabled(),
    };
  }, []);

  const toggleMute = useCallback(() => {
    const manager = audioManagerRef.current;
    if (manager) {
      manager.setEnabled(!manager.isAudioEnabled());
    }
  }, []);

  const isMuted = !audioManagerRef.current?.isAudioEnabled();
  return {
    playSound,
    playMusic,
    stopMusic,
    isTrackPlaying,
    getCurrentTrackName,
    setMusicVolume,
    setSoundVolume,
    setMusicEnabled,
    setEffectsEnabled,
    getConfig,
    toggleMute,
    isMuted,
  };
};

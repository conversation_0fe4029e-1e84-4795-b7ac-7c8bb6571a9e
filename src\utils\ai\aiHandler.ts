import { playCard, announceAction } from "@/utils/game/gameLogic";
import { GameState } from "@/utils/game/gameLogic";
import { getValidCards, Card as CardType } from "@/utils/game/cardUtils";
import { getAIMove, AIDifficulty } from "./aiCore";
import { audioManager } from "../audio/AudioManager";
import {
  handleAIStrategicAnnouncement,
  integrateAnnouncementsInAIDecision,
  processReceivedAnnouncement,
} from "./handlers/strategicAIHandler";
import { ActionType } from "@/components/GameActionBadge";

export const handleAIMove = (
  gameState: GameState,
  difficulty: "easy" | "medium" | "hard",
  setGameState: (state: GameState) => void,
  setLastPlayedCard: (card: CardType | null) => void,
  setShowingLastTrick: (value: boolean) => void,
  setActionAnnouncement?: (announcement: {
    type: ActionType;
    playerIndex: number;
  }) => void
): void => {
  const currentPlayer = gameState.players[gameState.currentPlayer];

  // Verifica che currentPlayer e hand esistano prima di procedere
  if (!currentPlayer || !currentPlayer.hand) {
    console.error("Errore: giocatore corrente o mano non definiti");
    return;
  }

  const validCards = getValidCards(currentPlayer.hand, gameState.leadSuit);
  if (validCards.length > 0) {
    // Procedi direttamente con la selezione della carta
    proceedWithCardSelection(gameState);
  }

  // Funzione per procedere con la selezione della carta
  function proceedWithCardSelection(currentState: GameState) {
    const aiDifficulty =
      difficulty === "easy"
        ? AIDifficulty.EASY
        : difficulty === "medium"
        ? AIDifficulty.MEDIUM
        : AIDifficulty.HARD;

    // 🗣️ STEP 1: Integra le dichiarazioni strategiche nella decisione AI
    const strategicDecision = integrateAnnouncementsInAIDecision(
      validCards,
      currentState,
      currentState.currentPlayer,
      difficulty
    );

    console.log(`[AI STRATEGIC HANDLER] ${strategicDecision.strategicInfo}`);

    // 🗣️ STEP 2: Modifica temporaneamente la mano del giocatore per influenzare l'AI
    const originalHand = [...currentPlayer.hand];
    if (strategicDecision.useAnnouncement) {
      // Filtra solo le carte valide strategicamente nella mano temporanea
      const strategicCards = strategicDecision.cards;
      currentPlayer.hand = currentPlayer.hand.filter((card) =>
        strategicCards.some((sc) => sc.id === card.id)
      );
      console.log(
        `[AI STRATEGIC] Filtering hand from ${originalHand.length} to ${currentPlayer.hand.length} cards`
      );
    }

    // Passa l'intero gameState all'AI
    const cardToPlay = getAIMove(
      currentState,
      currentState.currentPlayer,
      aiDifficulty
    );

    // Ripristina la mano originale
    currentPlayer.hand = originalHand;
    if (cardToPlay) {
      // 🗣️ STEP 2: Valuta se fare una dichiarazione strategica basata sulla carta SCELTA
      // Solo il primo giocatore del turno può fare dichiarazioni
      if (currentState.currentTrick.length === 0) {
        const announcementDecision = handleAIStrategicAnnouncement(
          currentState,
          currentState.currentPlayer,
          cardToPlay,
          difficulty
        );

        // 🗣️ STEP 3: Esegui la dichiarazione se necessaria
        if (announcementDecision.shouldAnnounce) {
          console.log(
            `[AI ANNOUNCEMENT] ${
              currentPlayer.name
            }: ${announcementDecision.announcement?.toUpperCase()}`
          );
          console.log(
            `[AI ANNOUNCEMENT] Ragione: ${announcementDecision.reason}`
          );

          const updatedState = announceAction(
            currentState,
            announcementDecision.announcement!
          );

          // Mostra il badge della dichiarazione se la funzione è disponibile
          if (setActionAnnouncement) {
            setActionAnnouncement({
              type: announcementDecision.announcement! as ActionType,
              playerIndex: currentState.currentPlayer,
            });

            // Nascondi l'annuncio dopo 2.5 secondi
            setTimeout(() => {
              setActionAnnouncement({ type: null, playerIndex: -1 });
            }, 2500);
          }

          // Aggiorna la memoria AI
          processReceivedAnnouncement(
            updatedState,
            currentState.currentPlayer,
            announcementDecision.announcement,
            cardToPlay.suit
          );

          // Breve pausa per mostrare la dichiarazione
          setTimeout(() => {
            executeCardPlay(updatedState, cardToPlay);
          }, 1000);
          return;
        }
      }

      // Se non c'è dichiarazione, gioca immediatamente
      executeCardPlay(currentState, cardToPlay);
    } else {
      // Se non è stata trovata alcuna carta da giocare (improbabile ma per sicurezza)
      console.error(
        "L'AI non è riuscita a scegliere una carta valida in modalità",
        difficulty
      );
      // Gioca la prima carta valida per evitare blocchi
      if (validCards.length > 0) {
        // 🔊 Suono di emergenza per carta giocata dall'AI con playerId specifico
        audioManager.playSound("cardSnap", {
          playerId: currentState.currentPlayer,
        });

        const newState = playCard(currentState, validCards[0].id);
        setLastPlayedCard(validCards[0]);
        setGameState(newState);
      }
    }
  }

  // Funzione per eseguire la giocata della carta
  function executeCardPlay(currentState: GameState, cardToPlay: CardType) {
    // 🔊 Suono cardPlay specifico per il giocatore AI corrente
    audioManager.playSound("cardPlay", {
      playerId: currentState.currentPlayer,
    });

    const newState = playCard(currentState, cardToPlay.id);
    setLastPlayedCard(cardToPlay); // Mostra l'ultima presa completata quando necessario
    if (
      newState.currentTrick.length === 0 &&
      currentState.currentTrick.length === 3
    ) {
      // Suono per fine presa quando l'AI completa una presa
      setTimeout(() => audioManager.playSound("cardGather"), 500);

      setShowingLastTrick(true);
      setTimeout(() => {
        setShowingLastTrick(false);
        setLastPlayedCard(null);
        setGameState(newState);
      }, 2800); // Tempo sufficiente per animazioni veloci: 500ms + 800ms + 300ms + 1500ms = 3100ms + margine
    } else {
      setGameState(newState);
    }
  }
};

var Ae=Object.defineProperty;var pe=(e,r,n)=>r in e?Ae(e,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[r]=n;var j=(e,r,n)=>pe(e,typeof r!="symbol"?r+"":r,n);import{a as Y}from"./audioManager-oMWoQbcF.js";var b=(e=>(e.Coins="coins",e.Cups="cups",e.Swords="swords",e.Clubs="clubs",e))(b||{}),y=(e=>(e.Ace="A",e.Two="2",e.Three="3",e.Four="4",e.Five="5",e.Six="6",e.Seven="7",e.Jack="J",e.Horse="H",e.<PERSON>="K",e))(y||{});const fe={coins:"Denari",cups:"Coppe",swords:"Spade",clubs:"Bastoni"},Ce={A:"asso",3:"tre",2:"due",K:"re",H:"cavallo",J:"fante",7:"setta",6:"sei",5:"cinque",4:"quattro"},ne=()=>{const e=[];return Object.values(b).forEach(r=>{Object.values(y).forEach(n=>{let o=0,t=0;switch(n){case"3":o=10,t=3;break;case"2":o=9,t=3;break;case"A":o=8,t=10;break;case"K":o=7,t=3;break;case"H":o=6,t=3;break;case"J":o=5,t=3;break;case"7":o=4,t=0;break;case"6":o=3,t=0;break;case"5":o=2,t=0;break;case"4":o=1,t=0;break}const s=`${Ce[n]} di ${fe[r]}`,a=Oe();e.push({id:a,suit:r,rank:n,displayName:s,order:o,value:t})})}),e},se=e=>{const r=[...e];for(let n=r.length-1;n>0;n--){const o=Math.floor(Math.random()*(n+1));[r[n],r[o]]=[r[o],r[n]]}return r},ae=(e,r,n)=>{const o=Array(r).fill([]).map(()=>[]);for(let t=0;t<n;t++)for(let s=0;s<r;s++)if(e.length>0){const a=e.pop();o[s]=[...o[s],a]}return o},he=(e,r)=>{if(!r)return e;const n=e.filter(o=>o.suit===r);return n.length>0?n:e},Te=(e,r,n)=>{if(e.length===0)throw new Error("Cannot determine winner of empty trick");const o=e.filter(s=>s.suit===n);if(o.length>0)return o.reduce((s,a)=>a.order>s.order?a:s,o[0]);const t=e.filter(s=>s.suit===r);return t.length>0?t.reduce((s,a)=>a.order>s.order?a:s,t[0]):e.reduce((s,a)=>a.order>s.order?a:s,e[0])},Oe=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const r=Math.random()*16|0;return(e==="x"?r:r&3|8).toString(16)}),sr={coins:"/images/semi/denari.png",cups:"/images/semi/coppe.png",swords:"/images/semi/spade.png",clubs:"/images/semi/bastoni.png"},ar=(e,r,n)=>{if(!r.trumpSuit||e.suit!==r.trumpSuit||e.rank!==y.Ace||r.lastTrumpSelector!==n||r.trickNumber!==1)return!1;const o=r.players[n].hand;return ue(o,r.trumpSuit)},ue=(e,r)=>[y.Ace,y.Two,y.Three].every(o=>e.some(t=>t.suit===r&&t.rank===o)),Q=(e,r)=>["A","2","3"].every(o=>e.some(t=>t.suit===r&&t.rank===o)),ce=e=>{const r=[b.Coins,b.Cups,b.Swords,b.Clubs];for(const n of r)if(ue(e,n))return n;return null},J=e=>{if(e.gamePhase!=="selectTrump")return{hasMaraffa:!1,maraffaSuit:null,playerIndex:-1};const r=e.players[e.currentPlayer],n=ce(r.hand);return{hasMaraffa:n!==null,maraffaSuit:n,playerIndex:e.currentPlayer}},Se=e=>{const r=J(e);return!r.hasMaraffa||!r.maraffaSuit?e:q(e,r.maraffaSuit)},le=(e=31)=>{const r=se(ne()),n=[{id:0,name:"Tu",hand:[],team:0,position:"south"},{id:1,name:"Player 2",hand:[],team:1,position:"east"},{id:2,name:"Player 3",hand:[],team:0,position:"north"},{id:3,name:"Player 4",hand:[],team:1,position:"west"}],o=ae(r,4,10);n.forEach((l,u)=>{l.hand=o[u]});const t=[{id:0,players:[n[0],n[2]],score:0,tricksWon:[],currentRoundPoints:0,currentRoundFigures:0},{id:1,players:[n[1],n[3]],score:0,tricksWon:[],currentRoundPoints:0,currentRoundFigures:0}],s=n.findIndex(l=>l.hand.some(u=>u.suit===b.Coins&&u.rank===y.Four)),a=s!==-1?s:0,c=J({players:n,currentPlayer:a}),g={deck:r,players:n,teams:t,currentPlayer:a,trumpSuit:null,leadSuit:null,currentTrick:[],trickNumber:1,gamePhase:"selectTrump",playerWithFourOfCoins:s,roundScore:[0,0],gameScore:[0,0],lastTrickWinner:null,announcedAction:null,message:"Il giocatore con il 4 di Denari deve scegliere la briscola",leadPlayer:a,currentRoundScoreHistory:{team0:[],team1:[]},victoryPoints:e,lastTrumpSelector:null,maraffeMade:[0,0],maxScoreDifference:0,automaticMaraffa:c.hasMaraffa?c:null};return c.hasMaraffa&&c.maraffaSuit&&a!==0?q(g,c.maraffaSuit):g},q=(e,r)=>{if(e.gamePhase!=="selectTrump")return e;const n=e.players[e.currentPlayer],o=[...e.teams],t=[...e.maraffeMade];let s=!1;return Q(n.hand,r)?(s=!0,console.log(`� MARAFFA RILEVATA! Team ${n.team} ha A+2+3 di ${r}. I punti verranno assegnati quando l'Asso sarà giocato come prima carta.`)):console.log(`❌ NESSUNA MARAFFA: Team ${n.team} non ha A+2+3 di ${r}`),{...e,trumpSuit:r,gamePhase:"play",teams:o,maraffeMade:t,automaticMaraffa:null,message:`La briscola è ${r===b.Coins?"Denari":r===b.Cups?"Coppe":r===b.Swords?"Spade":"Bastoni"}. ${e.players[e.currentPlayer].name} inizia.${s?" Maraffa rilevata! I punti saranno assegnati quando l'Asso verrà giocato.":""}`,leadPlayer:e.currentPlayer,lastTrumpSelector:e.currentPlayer}},de=(e,r)=>{if(e.gamePhase!=="play"||e.currentTrick.length!==0)return e;const n=e.players[e.currentPlayer];return console.log(`[STRATEGIC ANNOUNCEMENT] ${n.name} dichiara: ${r.toUpperCase()}`),{...e,announcedAction:r,message:`${n.name} dichiara: "${r.toUpperCase()}"`}},te=e=>{const r=e.filter(s=>s.rank===y.Ace).length,n=e.filter(s=>[y.Three,y.Two,y.King,y.Horse,y.Jack].includes(s.rank)).length,o=r,t=n;return(o>0||t>0)&&console.log(`    🧮 Calcolo: ${r} assi = ${o} punti, ${n} figure`),{acePoints:o,figureCount:t,totalPoints:o+Math.floor(t/3)}},Ie=(e,r)=>{const n=e.players[r].team,o=[...e.teams];if(e.currentTrick.length!==4)return console.warn(`⚠️ ERRORE: Tentativo di calcolare punteggio con trick incompleto (${e.currentTrick.length} carte)`),e;const t=te(e.currentTrick);console.log(`🎯 PRESA ${e.trickNumber}:`),console.log("   Carte giocate:",e.currentTrick.map(a=>`${a.rank} di ${a.suit}`)),console.log(`   Punti da assi: ${t.acePoints}`),console.log(`   Figure trovate: ${t.figureCount}`),console.log(`   Team vincitore: ${n}`);const s={team0:[...e.currentRoundScoreHistory.team0],team1:[...e.currentRoundScoreHistory.team1]};if(n===0){const a=o[0].currentRoundPoints,i=o[0].currentRoundFigures;o[0].currentRoundPoints+=t.acePoints;const c=i+t.figureCount,g=Math.floor(c/3);o[0].currentRoundPoints+=g,o[0].currentRoundFigures=c%3,console.log(`   Team 0: ${a} + ${t.acePoints} (assi) + ${g} (da ${c} figure) = ${o[0].currentRoundPoints}`),console.log(`   Team 0: Figure rimanenti: ${o[0].currentRoundFigures}`),s.team0.push(t.acePoints+g)}else{const a=o[1].currentRoundPoints,i=o[1].currentRoundFigures;o[1].currentRoundPoints+=t.acePoints;const c=i+t.figureCount,g=Math.floor(c/3);o[1].currentRoundPoints+=g,o[1].currentRoundFigures=c%3,console.log(`   Team 1: ${a} + ${t.acePoints} (assi) + ${g} (da ${c} figure) = ${o[1].currentRoundPoints}`),console.log(`   Team 1: Figure rimanenti: ${o[1].currentRoundFigures}`),s.team1.push(t.acePoints+g)}if(o[n].tricksWon.push(...e.currentTrick),e.trickNumber===10){const a=o[n].currentRoundPoints;o[n].currentRoundPoints+=1,console.log(`🏆 ULTIMA PRESA - Team ${n}: ${a} + 1 (bonus) = ${o[n].currentRoundPoints}`),n===0?s.team0.push(1):s.team1.push(1),console.log(`📊 FINE MANO - Totali: Team 0 = ${o[0].currentRoundPoints}, Team 1 = ${o[1].currentRoundPoints}`),console.log(`📊 SOMMA TOTALE: ${o[0].currentRoundPoints+o[1].currentRoundPoints} punti`),console.log(`📊 Figure rimaste: Team 0 = ${o[0].currentRoundFigures}, Team 1 = ${o[1].currentRoundFigures}`)}return{...e,teams:o,currentRoundScoreHistory:s}},X=(e,r)=>{if(e.gamePhase!=="play")return e;const n=e.players[e.currentPlayer],o=n.hand.findIndex(c=>c.id===r);if(o===-1)return e;const t=n.hand[o];if(e.trumpSuit&&e.trickNumber===1&&e.currentTrick.length===0&&Q(n.hand,e.trumpSuit)&&e.lastTrumpSelector===e.currentPlayer&&(t.suit!==e.trumpSuit||t.rank!=="A"))return{...e,message:"🎯 MARAFFA! Devi giocare l'Asso della briscola come prima carta."};if(e.currentTrick.length===0){const c=[...n.hand];c.splice(o,1);const g=[...e.players];g[e.currentPlayer]={...n,hand:c};let l=0;const u=[...e.maraffeMade],d=[...e.teams];if(e.trumpSuit&&e.trickNumber===1&&e.currentTrick.length===0&&t.suit===e.trumpSuit&&t.rank==="A"&&e.lastTrumpSelector===e.currentPlayer&&Q(n.hand,e.trumpSuit)){const T=n.hand.some(I=>I.suit===e.trumpSuit&&I.rank==="A"),k=n.hand.some(I=>I.suit===e.trumpSuit&&I.rank==="2"),P=n.hand.some(I=>I.suit===e.trumpSuit&&I.rank==="3");T&&k&&P?(l=3,u[n.team]+=1,d[n.team].currentRoundPoints+=l,console.log(`🎯🎯🎯 MARAFFA COMPLETATA! ${n.name} (Team ${n.team}) ha giocato l'Asso di briscola come prima carta. +${l} punti bonus! Round points aggiornati: ${d[n.team].currentRoundPoints}`)):console.log(`❌ MARAFFA FALSA: ${n.name} non ha tutte le carte della Maraffa!`)}const m=[e.roundScore[0]+(n.team===0?l:0),e.roundScore[1]+(n.team===1?l:0)],f=Math.abs(m[0]-m[1]),C=Math.max(e.maxScoreDifference,f);return{...e,players:g,teams:d,currentTrick:[t],leadSuit:t.suit,leadPlayer:e.currentPlayer,currentPlayer:(e.currentPlayer+1)%4,roundScore:m,maraffeMade:u,maxScoreDifference:C,announcedAction:null,message:l>0?`${n.name} ha giocato ${t.displayName} e ottenuto il bonus Maraffa (+3 punti)!`:`${n.name} ha giocato ${t.displayName}`}}if(e.leadSuit&&t.suit!==e.leadSuit&&n.hand.some(c=>c.suit===e.leadSuit))return{...e,message:`Devi giocare una carta di ${e.leadSuit===b.Coins?"Denari":e.leadSuit===b.Cups?"Coppe":e.leadSuit===b.Swords?"Spade":"Bastoni"} se ne hai una.`};const s=[...n.hand];s.splice(o,1);const a=[...e.players];a[e.currentPlayer]={...n,hand:s};const i=[...e.currentTrick,t];if(i.length===4){const c=Te(i,e.leadSuit,e.trumpSuit),g=i.findIndex(C=>C.id===c.id),l=(e.leadPlayer+g)%4;e.players[l].team;const u=[...e.teams],d=Ie({...e,players:a,teams:u,currentTrick:i},l),m=d.teams[0].currentRoundPoints,f=d.teams[1].currentRoundPoints;if(e.trickNumber===10){const C=[e.gameScore[0]+m,e.gameScore[1]+f],T=(C[0]>=e.victoryPoints||C[1]>=e.victoryPoints)&&C[0]!==C[1];return{...d,currentTrick:[],leadSuit:null,currentPlayer:l,leadPlayer:l,trickNumber:1,gamePhase:T?"gameOver":"roundOver",lastTrickWinner:l,roundScore:[m,f],gameScore:C,message:T?`Gioco finito! ${C[0]>C[1]?"Squadra 1":"Squadra 2"} ha vinto!`:C[0]>=e.victoryPoints&&C[1]>=e.victoryPoints&&C[0]===C[1]?`Entrambe le squadre hanno raggiunto ${e.victoryPoints} punti ma sono in pareggio (${C[0]}-${C[1]}). La partita continua fino al primo vincitore!`:`${e.players[l].name} ha vinto l'ultima presa. Punteggio mano: ${m}-${f}`}}return{...d,currentTrick:[],leadSuit:null,currentPlayer:l,leadPlayer:l,trickNumber:e.trickNumber+1,lastTrickWinner:l,message:`${e.players[l].name} ha vinto la presa. Punteggio attuale: Squadra 1 (${m.toFixed(1)}) - Squadra 2 (${f.toFixed(1)})`}}return{...e,players:a,currentTrick:i,currentPlayer:(e.currentPlayer+1)%4,message:`${n.name} ha giocato ${t.displayName}`}},ke=e=>{if(e.gamePhase!=="scoring"&&e.gamePhase!=="roundOver")return e;const r=se(ne()),n=[...e.players],o=ae(r,4,10);n.forEach((g,l)=>{g.hand=o[l]});const t=[{...e.teams[0],tricksWon:[],currentRoundPoints:0,currentRoundFigures:0},{...e.teams[1],tricksWon:[],currentRoundPoints:0,currentRoundFigures:0}],s=n.findIndex(g=>g.hand.some(l=>l.suit===b.Coins&&l.rank===y.Four));let a=0;e.lastTrumpSelector===null?a=s!==-1?s:0:a=(e.lastTrumpSelector+1)%4;const i={...e,players:n,currentPlayer:a},c=J(i);if(c.hasMaraffa&&c.maraffaSuit&&a!==0){const g={...e,deck:r,players:n,teams:t,currentPlayer:a,leadPlayer:a,trumpSuit:null,leadSuit:null,currentTrick:[],trickNumber:1,gamePhase:"selectTrump",playerWithFourOfCoins:s,roundScore:[0,0],lastTrickWinner:null,announcedAction:null,currentRoundScoreHistory:{team0:[],team1:[]},automaticMaraffa:c};return q(g,c.maraffaSuit)}return{...e,deck:r,players:n,teams:t,currentPlayer:a,leadPlayer:a,trumpSuit:null,leadSuit:null,currentTrick:[],trickNumber:1,gamePhase:"selectTrump",playerWithFourOfCoins:s,roundScore:[0,0],lastTrickWinner:null,announcedAction:null,currentRoundScoreHistory:{team0:[],team1:[]},automaticMaraffa:c.hasMaraffa?c:null,message:c.hasMaraffa&&a===0?`${n[a].name} ha la Maraffa! Puoi scegliere il seme per la briscola.`:e.lastTrumpSelector===null?`${n[a].name} ha il 4 di Denari e deve scegliere la briscola`:`${n[a].name} deve scegliere la briscola`}},ye=(e,r)=>{if(e.gamePhase!=="play")return e;const n=e.teams[r],o=e.gameScore[r],t=te(n.tricksWon),s=t.acePoints+Math.floor(t.figureCount/3);return o+s>=e.victoryPoints?(console.log("🎯 gameLogic: Partita finita - vittoria squadra",r+1),{...e,gamePhase:"gameOver",gameScore:[r===0?o+s:e.gameScore[0],r===1?o+s:e.gameScore[1]],message:`Squadra ${r+1} ha dichiarato vittoria e ha raggiunto i punti necessari.`}):(console.log("🎯 gameLogic: Partita finita - dichiarazione fallita squadra",r+1),{...e,gamePhase:"gameOver",roundScore:r===0?[0,11]:[11,0],gameScore:[e.gameScore[0]+(r===0?0:11),e.gameScore[1]+(r===1?0:11)],message:`Squadra ${r+1} ha dichiarato vittoria ma non ha abbastanza punti. Squadra ${r===0?2:1} vince 11-0.`})},Re=(e=31)=>le(e),ur=Object.freeze(Object.defineProperty({__proto__:null,announceAction:de,applyAutomaticMaraffa:Se,calculateScore:te,checkForAutomaticMaraffa:J,declareGameWin:ye,findAutomaticMaraffa:ce,initializeGameState:le,playCard:X,selectTrump:q,startNewGame:Re,startNewRound:ke},Symbol.toStringTag,{value:"Module"}));var G=(e=>(e[e.EASY=0]="EASY",e[e.MEDIUM=1]="MEDIUM",e[e.HARD=2]="HARD",e))(G||{});const ee=()=>ne(),W=e=>({3:10,2:9,A:8,K:7,H:6,J:5,7:4,6:3,5:2,4:1})[e.rank||""]||0,B=e=>{switch(e.rank){case y.Ace:return 1;case y.Three:case y.Two:case y.King:case y.Horse:case y.Jack:return .3;default:return 0}},Z=e=>e.rank===y.Ace||e.rank===y.King||e.rank===y.Horse||e.rank===y.Jack,H=e=>e.filter(Z),oe=(e,r,n)=>{const o=n.trumpSuit,t=n.leadSuit;if(e.suit===o&&e.rank===y.Three)return!0;if(e.suit===o){const s=[];e.rank!==y.Three&&s.push(y.Three),e.rank!==y.Two&&e.rank!==y.Three&&s.push(y.Two),e.rank!==y.Ace&&e.rank!==y.Two&&e.rank!==y.Three&&s.push(y.Ace);const a=r.playedBySuit[o]||[];return s.every(c=>a.some(g=>g.rank===c))}if(t&&e.suit===t){const s=[],a=W(e),i=[y.Three,y.Two,y.Ace,y.King,y.Horse,y.Jack,"7","6","5","4"];for(const d of i)W({rank:d,suit:e.suit})>a&&s.push(d);const c=r.playedBySuit[t]||[],g=s.every(d=>c.some(m=>m.rank===d)),u=(r.playedBySuit[o]||[]).length>=10;return g&&u}return!1},K=e=>({3:10,2:9,A:8,K:7,H:6,J:5,7:4,6:3,5:2,4:1})[e.rank]||0,_=(e,r,n,o)=>{if(!r||r.length===0)return!0;const t=o&&e.suit===o,s=r.filter(a=>o&&a.suit===o);if(t){if(s.length===0)return!0;{const a=s.reduce((i,c)=>W(c)>W(i)?c:i);return W(e)>W(a)}}else{if(s.length>0||!n||e.suit!==n)return!1;const a=r.filter(c=>n&&c.suit===n);if(a.length===0)return!0;const i=a.reduce((c,g)=>W(g)>W(c)?g:c);return W(e)>W(i)}},$e=(e,r,n,o)=>n&&e.suit===n?Ee(e,r):Pe(e,r,n),Ee=(e,r,n)=>{const o=r.trumpsRemaining,t=W(e);return o.filter(a=>W(a)>t).length===0},Pe=(e,r,n)=>{const o=r.remainingCards.filter(a=>a.suit===e.suit),t=W(e);return o.filter(a=>W(a)>t).length>0?!1:n&&e.suit!==n?r.trumpsRemaining.length===0:!0},ie=(e,r,n,o)=>{if(e.length===0)return 0;let t=0,s=e[0];for(let a=1;a<e.length;a++){const i=e[a];Ne(i,s,r,n)&&(t=a,s=i)}return t},Ne=(e,r,n,o)=>o&&e.suit===o&&r.suit===o?W(e)>W(r):o&&e.suit===o&&r.suit!==o?!0:o&&e.suit!==o&&r.suit===o?!1:n&&e.suit===n&&r.suit===n?W(e)>W(r):n&&e.suit===n&&r.suit!==n?!0:(n&&e.suit!==n&&r.suit===n,!1),Ve=(e,r,n)=>{if(!r)return!1;const o=["3","2","A","K","H","J","7","6","5","4"],t=W(e),s=o.filter(c=>{const g={rank:c,suit:e.suit};return W(g)>t}),a=r.playedBySuit[e.suit]||[];return s.every(c=>a.some(g=>g.rank===c))?(console.log(`[DOMINANZA] 🏆 ${e.rank} di ${e.suit} è DOMINANTE! Tutte le carte più forti sono state giocate`),console.log(`[DOMINANZA] Carte più forti giocate: ${s.join(", ")}`),!0):!1},ge=e=>e.trumpSuit?[...e.teams[0].tricksWon,...e.teams[1].tricksWon,...e.currentTrick||[]].filter(n=>n&&n.suit===e.trumpSuit).length:0,Le=(e,r,n)=>{if(!r.trumpSuit)return{shouldPlayTrump:!1,recommendedCard:null,reason:"Nessuna briscola selezionata"};const o=e.filter(i=>i.suit===r.trumpSuit);if(o.length<4)return{shouldPlayTrump:!1,recommendedCard:null,reason:`Solo ${o.length} briscole, strategia non applicabile`};const t=ge(r),a=10-t-o.length;if(console.log(`[STRATEGIA MOLTE BRISCOLE] 🎯 Ho ${o.length} briscole, giocate: ${t}, rimaste agli avversari: ~${a}`),a>2){const i=o.filter(g=>!["3","2","A"].includes(g.rank));let c;if(i.length>0)c=i[0];else{const g=new CardEvaluator;c=o.sort((u,d)=>g.getCardOrder(u)-g.getCardOrder(d))[0]}return{shouldPlayTrump:!0,recommendedCard:c,reason:`Strategia molte briscole: gioco ${c.rank} per far finire le briscole agli avversari (~${a} rimaste)`}}return{shouldPlayTrump:!1,recommendedCard:null,reason:`Avversari hanno poche briscole (~${a}), non serve continuare la strategia`}},ve=(e,r,n)=>{if(!r.trumpSuit)return{shouldConserve:!1,cardToAvoid:null,reason:"Nessuna briscola selezionata"};const o=e.filter(m=>m.suit===r.trumpSuit),t=r.trickNumber||1,s=t>=8;if(t===10)return{shouldConserve:!1,cardToAvoid:null,reason:"È l'ultimo turno, gioca tutto"};if(o.filter(m=>["3","2","A"].includes(m.rank)).length===0)return{shouldConserve:!1,cardToAvoid:null,reason:"Non ho briscole alte da conservare"};ge(r);const c=[...r.teams[0].tricksWon,...r.teams[1].tricksWon,...r.currentTrick||[]],g=c.some(m=>m&&m.suit===r.trumpSuit&&m.rank==="3"),l=c.some(m=>m&&m.suit===r.trumpSuit&&m.rank==="2"),u=c.some(m=>m&&m.suit===r.trumpSuit&&m.rank==="A");let d=null;return!g&&o.some(m=>m.rank==="3")?d=o.find(m=>m.rank==="3")||null:!l&&o.some(m=>m.rank==="2")?d=o.find(m=>m.rank==="2")||null:!u&&o.some(m=>m.rank==="A")&&(d=o.find(m=>m.rank==="A")||null),d?s?{shouldConserve:!0,cardToAvoid:d,reason:`Conservo ${d.rank} di briscola per l'ultimo turno (turno ${t}/10)`}:{shouldConserve:!1,cardToAvoid:null,reason:"Troppo presto per conservare carte forti"}:{shouldConserve:!1,cardToAvoid:null,reason:"Tutte le briscole forti sono già uscite"}},Me=(e,r)=>{const n=[],o={};return e.forEach(t=>{o[t.suit]||(o[t.suit]=[]),o[t.suit].push(t)}),Object.keys(o).forEach(t=>{const s=o[t],a=s.some(c=>c.rank==="2"),i=s.some(c=>c.rank==="3");a&&i?r.playedCards.some(g=>g.suit===t&&g.rank==="3")?(console.log(`[PRIORITÀ 3 SU 2] ✅ Il 3 di ${t} è già uscito - ora il 2 è sicuro`),n.push(...s)):(console.log(`[PRIORITÀ 3 SU 2] 🎯 Ho sia 2 che 3 di ${t}, il 3 non è uscito - preferisco il 3`),s.forEach(g=>{g.rank!=="2"&&n.push(g)})):n.push(...s)}),n},me=e=>{const r=ee(),n=[],o={},t={};[b.Coins,b.Cups,b.Swords,b.Clubs].forEach(u=>{o[u]=[]});for(let u=0;u<4;u++)t[u]=[];[...e.teams[0].tricksWon,...e.teams[1].tricksWon].forEach(u=>{u&&(n.push(u),u.suit&&o[u.suit].push(u))}),e.currentTrick&&e.currentTrick.length>0&&e.currentTrick.forEach((u,d)=>{if(u){n.push(u),u.suit&&o[u.suit].push(u);const m=((e.leadPlayer??0)+d)%4;t[m].push(u)}}),console.log(`[MEMORY ANALYSIS] 📊 Carte giocate totali: ${n.length}`),console.log("[MEMORY ANALYSIS] 📊 Dettaglio per seme:",Object.keys(o).map(u=>`${u}: ${o[u].length}`).join(", ")),e.teams&&e.teams.forEach(u=>{u.tricksWon&&Array.isArray(u.tricksWon)&&u.tricksWon.forEach(d=>{Array.isArray(d)&&d.forEach((m,f)=>{if(m&&m.suit&&m.rank){n.push(m),o[m.suit.toString()].push(m);const T=((e.leadPlayer??0)+f)%4;t[T].push(m)}})})}),e.currentTrick&&Array.isArray(e.currentTrick)&&e.currentTrick.forEach((u,d)=>{if(u&&u.suit&&u.rank){n.push(u),o[u.suit.toString()].push(u);const m=((e.leadPlayer??0)+d)%4;t[m].push(u)}});const a=r.filter(u=>!n.some(d=>u.suit===d.suit&&u.rank===d.rank)),i={};[b.Coins,b.Cups,b.Swords,b.Clubs].forEach(u=>{i[u]=a.filter(d=>d.suit===u).length});const c=a.filter(u=>u.suit===e.trumpSuit),g={};return[b.Coins,b.Cups,b.Swords,b.Clubs].forEach(u=>{g[u]=a.filter(d=>d.suit===u&&["A","3","2","K","H","J"].includes(d.rank))}),{playedCards:n,playedBySuit:o,playedByPlayer:t,remainingCards:a,suitDistribution:i,trumpsRemaining:c,highCardsRemaining:g}},re=e=>{const r={playedCards:[],playedBySuit:{},playedByPlayer:{},remainingCards:ee(),suitDistribution:{},trumpsRemaining:[],highCardsRemaining:{}};[b.Coins,b.Cups,b.Swords,b.Clubs].forEach(n=>{r.playedBySuit[n]=[],r.suitDistribution[n]=10,r.highCardsRemaining[n]=ee().filter(o=>o.suit===n&&["A","3","2","K","H","J"].includes(o.rank))});for(let n=0;n<4;n++)r.playedByPlayer[n]=[];return e.currentTrick&&e.currentTrick.length>0&&e.currentTrick.forEach((n,o)=>{r.playedCards.push(n),n.suit&&r.playedBySuit[n.suit.toString()].push(n);const t=((e.leadPlayer??0)+o)%4;r.playedByPlayer[t].push(n)}),r.trumpsRemaining=r.remainingCards.filter(n=>n.suit===e.trumpSuit),r};let D=class{getCardValue(r){switch(r.rank){case y.Ace:return 1;case y.Three:case y.Two:case y.King:case y.Horse:case y.Jack:return .3;default:return 0}}getCardOrder(r){return{3:10,2:9,A:8,K:7,H:6,J:5,7:4,6:3,5:2,4:1}[r.rank]||0}getCardStrengthScore(r,n=!1){const o=this.getCardOrder(r),t=this.getCardValue(r),s=n?5:0;return o+t*2+s}isStrategicCard(r){return r.rank===y.Two}isThreeForOpening(r,n){return r.rank===y.Three&&r.suit!==n}isGoodForTeammate(r){return r.rank===y.Ace||r.rank===y.King||r.rank===y.Horse||r.rank===y.Jack}canWinCurrentTrick(r,n,o,t){if(!n||n.length===0)return!0;const s=r.suit===t;for(const a of n){const i=a.suit===t;if(!(s&&!i)){if(!s&&i)return!1;if(s&&i){if(this.getCardOrder(r)<=this.getCardOrder(a))return!1}else if(r.suit===o&&a.suit===o){if(this.getCardOrder(r)<=this.getCardOrder(a))return!1}else if(r.suit!==o&&a.suit===o)return!1}}return!0}isObviousWinSituation(r,n,o,t,s=0){if(!this.canWinCurrentTrick(r,n,o,t))return!1;if(r.rank==="3"&&r.suit===o&&r.suit!==t||r.rank==="2"&&r.suit===o&&r.suit!==t&&!n.some(g=>g.rank==="3"&&g.suit===o))return!0;const a=r.suit===t;if(!a&&(r.rank==="3"||r.rank==="2")&&(n.length<=1||s>=1||n.length>=2&&!n.some(l=>l.rank==="A"||l.rank==="3"||l.rank==="2")))return!0;if(a&&(r.rank==="3"||r.rank==="2")&&s>=2){const c=n.filter(l=>l.suit===t);return c.length===0?!0:c.filter(l=>this.getCardOrder(l)>this.getCardOrder(r)).length===0}if(r.suit===t&&(r.rank==="3"||r.rank==="2")){const c=n.filter(l=>l.suit===t);return c.length===0?!0:c.filter(l=>this.getCardOrder(l)>this.getCardOrder(r)).length===0}return!1}findLowestValidCard(r,n,o){return this.findBestDiscardCard(r,n,o)}selectOptimalTrumpSuit(r){const n=this.findMaraffa(r);if(n)return n;const o={};Object.values(b).forEach(s=>o[s]=0),r.forEach(s=>o[s.suit]++);const t=Object.entries(o).find(([s,a])=>a>=4);return t?t[0]:this.chooseSuitWithHighestCards(r)}findMaraffa(r){const n={};Object.values(b).forEach(o=>n[o]=[]),r.forEach(o=>n[o.suit].push(o));for(const[o,t]of Object.entries(n)){const s=t.map(a=>a.rank);if(s.includes(y.Ace)&&s.includes(y.Two)&&s.includes(y.Three))return o}return null}chooseSuitWithHighestCards(r){const n={};return Object.values(b).forEach(o=>n[o]=0),r.forEach(o=>{n[o.suit]+=this.getCardStrengthScore(o)}),Object.entries(n).reduce((o,[t,s])=>s>n[o]?t:o,Object.keys(n)[0])}shouldLoadTrick(r,n,o,t,s,a){if(!(r.length===3))return{shouldLoad:!1,reason:"Non ultimo giocatore",loadingCards:[]};const c=this.analyzeCurrentWinner(r,n,o,t,a),g=a.players[s].team;if(!(c.winnerTeam===g&&c.winnerIndex!==(s-t+4)%4))return{shouldLoad:!1,reason:"Compagno non sta vincendo",loadingCards:[]};const u=r.reduce((I,V)=>I+this.getCardValue(V),0),d=a.trickNumber??1,m=d===10,f=d>=8;if(!(u>=1||f||m||u>=.5))return{shouldLoad:!1,reason:`Trucco non abbastanza prezioso (${u} punti)`,loadingCards:[]};const k=a.players[s].hand,P=this.getLoadingCards(k);return{shouldLoad:!0,reason:`Caricamento trucco: compagno vincente, ${u} punti attuali`,loadingCards:P}}getCooperativePlayStrategy(r,n,o,t,s,a,i){if(n.length===0){if(console.log("[STRATEGIA] 🎯 PRIMO A GIOCARE - Strategia intelligente"),(i.trickNumber??1)<=3){const S=r.filter(E=>E.rank==="3"&&E.suit!==t);if(S.length>0){const E=S[0];return console.log(`[STRATEGIA] 🎯 PRIMO (prime mani): Controllo con 3 di ${E.suit}!`),{strategy:"compete",recommendedCard:E,reason:`🎯 CONTROLLO PRIME MANI: 3 di ${E.suit} come primo giocatore per controllare il tavolo`}}}const w=r.filter(S=>this.getCardValue(S)===0&&S.suit!==t&&S.rank!=="2");if(w.length>0){w.sort((E,p)=>this.getCardOrder(p)-this.getCardOrder(E));const S=w[0];return{strategy:"compete",recommendedCard:S,reason:`🎯 APERTURA SICURA: ${S.rank} di ${S.suit} - conservo carte di valore per momenti giusti`}}const L=[...r].sort((S,E)=>this.getCardValue(S)-this.getCardValue(E)),O=L.filter(S=>!(S.rank==="3"&&S.suit===t)),R=O.length>0?O[0]:L[0];return{strategy:"compete",recommendedCard:R,reason:`🎯 APERTURA ULTIMA RISORSA: ${R.rank} di ${R.suit} - evito 3 di briscola`}}const c=this.selectOptimalCardWithSafetyCheck(r,n,o,t,i,a);if(c)return console.log(`[SAFE ACE PRIORITY] ${c.reason}`),{strategy:c.strategy,recommendedCard:c.recommendedCard,reason:c.reason};if(n.some(N=>N.rank==="A")){const N=r.filter(v=>this.canWinCurrentTrick(v,n,o,t));if(N.length>0){N.sort((w,L)=>{const O=this.isStrategicCard(w),R=this.isStrategicCard(L);if(!O&&R)return-1;if(O&&!R)return 1;if(O&&R){if(w.rank==="2"&&L.rank==="3")return-1;if(w.rank==="3"&&L.rank==="2")return 1}return this.getCardStrengthScore(w)-this.getCardStrengthScore(L)});const v=N[0];return{strategy:"compete",recommendedCard:v,reason:`🔥 ASSO SUL TAVOLO (1 punto)! Prendo con ${v.rank} - 1 punto vale QUALSIASI carta!`}}}const l=n.reduce((N,v)=>N+this.getCardValue(v),0);if(l>=1){const N=r.filter(v=>this.canWinCurrentTrick(v,n,o,t));if(N.length>0)return N.sort((v,w)=>this.getCardStrengthScore(v)-this.getCardStrengthScore(w)),{strategy:"compete",recommendedCard:N[0],reason:`💰 ${l.toFixed(1)} PUNTI SUL TAVOLO - Vale qualsiasi carta per vincere!`}}if(l>=.6){const N=re(i),v=this.shouldUseStrategicCardsForPoints(r,n,o,t,l,N,n.length===3);if(v.shouldUse&&v.recommendedCard)return console.log(`[STRATEGIA] ${v.reason}`),{strategy:"compete",recommendedCard:v.recommendedCard,reason:v.reason};const w=r.filter(L=>this.canWinCurrentTrick(L,n,o,t));if(w.length>0){const L=w.filter(O=>!this.isStrategicCard(O));if(L.length>0)return L.sort((O,R)=>this.getCardStrengthScore(O)-this.getCardStrengthScore(R)),{strategy:"compete",recommendedCard:L[0],reason:`💰 ${l.toFixed(1)} punti - Prendo con ${L[0].rank}, conservo 2 e 3 per situazioni migliori!`}}console.log(`[STRATEGIA] ${v.reason}`)}const u=this.analyzeCurrentWinner(n,o,t,s,i),d=i.players[a].team,m=u.winnerTeam===d,f=u.winnerTeam!==d&&u.winnerTeam!==-1;if(n.length===3){console.log("[STRATEGIA] 🎯 ULTIMO GIOCATORE - Analisi priorità assoluta");const N=r.filter(L=>this.canWinCurrentTrick(L,n,o,t)&&this.getCardValue(L)>0&&!(L.suit===t&&this.isStrategicCard(L)));if(N.length>0){N.sort((O,R)=>{const S=this.getCardValue(O),E=this.getCardValue(R);return S!==E?E-S:this.getCardOrder(O)-this.getCardOrder(R)});const L=N[0];return console.log(`[STRATEGIA] 🔥 ULTIMO GIOCATORE - Prendo con ${L.rank} (${this.getCardValue(L)} punti)`),{strategy:"compete",recommendedCard:L,reason:`🔥 ULTIMO GIOCATORE: Prendo sempre con carte di valore! ${L.rank} = ${this.getCardValue(L)} punti garantiti`}}if(l>0){const L=r.filter(O=>this.canWinCurrentTrick(O,n,o,t)&&O.suit!==t);if(L.length>0)return L.sort((O,R)=>this.getCardStrengthScore(O)-this.getCardStrengthScore(R)),console.log(`[STRATEGIA] 💰 ULTIMO GIOCATORE - Prendo ${l.toFixed(1)} punti con carta non-briscola`),{strategy:"compete",recommendedCard:L[0],reason:`💰 ULTIMO GIOCATORE: ${l.toFixed(1)} punti sul tavolo - Prendo con ${L[0].rank} non-briscola`}}const v=re(i),w=this.shouldUseStrategicCardsForPoints(r,n,o,t,l,v,!0);if(w.shouldUse&&w.recommendedCard)return console.log(`[STRATEGIA] ${w.reason}`),{strategy:"compete",recommendedCard:w.recommendedCard,reason:w.reason};console.log(`[STRATEGIA] ${w.reason}`)}if(m){console.log("[STRATEGIA] 🎯 TEAM VINCENTE - DEVE DARE PUNTI SUBITO!");const N=r.filter(L=>this.getCardValue(L)>0);if(N.length>0){N.sort((O,R)=>{const S=this.getCardValue(O),E=this.getCardValue(R);return O.rank==="A"&&R.rank!=="A"?-1:R.rank==="A"&&O.rank!=="A"?1:S!==E?E-S:this.getCardOrder(R)-this.getCardOrder(O)});const L=N[0];return console.log(`[STRATEGIA] 🔥 TEAM VINCENTE: Gioco SUBITO ${L.rank} (${this.getCardValue(L)} punti) - NON accumulo!`),{strategy:"give_points",recommendedCard:L,reason:`🔥 STRATEGIA CORRETTA: Team vince = gioco SUBITO ${L.rank} (${this.getCardValue(L)} pt)! Non accumulo carte con punti!`}}const v=r.filter(L=>this.getCardValue(L)===0&&!this.isStrategicCard(L));if(v.length>0)return v.sort((L,O)=>this.getCardOrder(L)-this.getCardOrder(O)),{strategy:"give_points",recommendedCard:v[0],reason:`🤝 TEAM vincente: Nessuna carta con punti, scarto sicuro ${v[0].rank}`};const w=r.filter(L=>this.isStrategicCard(L));if(w.length>0){const L=w.filter(R=>!(R.rank==="3"&&R.suit===t)),O=L.length>0?L[0]:w[0];return{strategy:"give_points",recommendedCard:O,reason:`🤝 TEAM vincente: Ultima opzione ${O.rank} - conservo 3 di briscola se possibile`}}return{strategy:"give_points",recommendedCard:r[0],reason:"🤝 TEAM vincente: Fallback"}}if(f){const N=r.filter(w=>this.getCardValue(w)===0);if(N.length>0)return N.sort((w,L)=>this.getCardOrder(w)-this.getCardOrder(L)),{strategy:"discard_low",recommendedCard:N[0],reason:`🤝 COLLABORATIVO: Avversari vincenti, scarto ${N[0].rank} (0 punti)!`};const v=[...r].sort((w,L)=>this.getCardValue(w)-this.getCardValue(L));return{strategy:"discard_low",recommendedCard:v[0],reason:`🤝 COLLABORATIVO: Costretto a dare ${v[0].rank} (${this.getCardValue(v[0])} punti) agli avversari`}}const T=i.trickNumber??1,k=r.filter(N=>this.getCardValue(N)>0),P=k.length>=4,I=T>=4&&T<=6;if(P&&I){console.log(`[STRATEGIA] ⚠️ CONTROLLO ACCUMULO: ${k.length} carte con punti a metà partita`);const N=k.filter(v=>v.rank==="K"||v.rank==="H"||v.rank==="J");if(N.length>=3)return N.sort((v,w)=>this.getCardValue(v)-this.getCardValue(w)),{strategy:"support_passive",recommendedCard:N[0],reason:`⚠️ CONTROLLO ESTREMO: Troppe figure (${N.length}) - gioco ${N[0].rank} per bilanciare mano`}}const V=r.filter(N=>this.canWinCurrentTrick(N,n,o,t));if(V.length>0&&l>=1)return V.sort((N,v)=>this.getCardStrengthScore(N)-this.getCardStrengthScore(v)),{strategy:"compete",recommendedCard:V[0],reason:`🤝 COMPETIZIONE LOGICA: Situazione neutra, prendo ${l.toFixed(1)} punti per il team!`};const U=r.filter(N=>this.getCardValue(N)===0);return U.length>0?{strategy:"support_passive",recommendedCard:U[0],reason:`🤝 COLLABORATIVO: Supporto passivo con ${U[0].rank}`}:{strategy:"support_passive",recommendedCard:r[0],reason:"🤝 COLLABORATIVO: Fallback"}}getDiscardOrderScore(r){return{4:1,5:2,6:3,7:4,J:5,H:6,K:7,2:8,3:9,A:10}[r.rank]||0}findBestDiscardCard(r,n,o){if(!n){const i=r.filter(g=>g.suit!==o);return i.length>0?(i.sort((g,l)=>this.getDiscardOrderScore(g)-this.getDiscardOrderScore(l)),i[0]):[...r].sort((g,l)=>this.getDiscardOrderScore(g)-this.getDiscardOrderScore(l))[0]}const t=r.filter(i=>i.suit===n);if(t.length>0)return t.sort((i,c)=>this.getDiscardOrderScore(i)-this.getDiscardOrderScore(c)),t[0];const s=r.filter(i=>i.suit!==o);return s.length>0?(s.sort((i,c)=>this.getDiscardOrderScore(i)-this.getDiscardOrderScore(c)),s[0]):[...r].sort((i,c)=>this.getDiscardOrderScore(i)-this.getDiscardOrderScore(c))[0]}getLoadingCards(r){const n=r.filter(t=>this.getCardValue(t)>0&&!this.isStrategicCard(t)),o=r.filter(t=>this.isStrategicCard(t));return n.sort((t,s)=>this.getCardValue(s)-this.getCardValue(t)),o.sort((t,s)=>this.getCardValue(s)-this.getCardValue(t)),[...n,...o]}getBestLoadingCard(r,n,o){if(!this.shouldLoadTrick(n,o.leadSuit,o.trumpSuit,o.leadPlayer??0,o.currentPlayer,o).shouldLoad)return null;const s=r.filter(c=>this.getCardValue(c)>0&&!this.isStrategicCard(c));if(s.length>0)return s.sort((c,g)=>this.getCardValue(g)-this.getCardValue(c)),s[0];const a=r.filter(c=>this.getCardValue(c)===0);if(a.length>0)return a[0];const i=r.filter(c=>this.isStrategicCard(c));return i.length>0?(console.warn("[LOADING] ⚠️ Costretto a caricare con carta strategica - situazione subottimale!"),i[0]):null}canPlayAceSafely(r,n,o,t,s,a){if(n.length>0)if(console.log(`[SAFE ACE CHECK] 🚨 Controllo draconiano per asso ${r.suit}`),this.canWinCurrentTrick(r,n,o,t))console.log(`[SAFE ACE CHECK] ✅ ASSO ${r.suit} sicuro perché può vincere`);else{const l=s.players[a].team;let u=0,d=-1,m=!1;for(let k=0;k<n.length;k++){const P=n[k],I=this.getCardOrder(P),V=P.suit===t;V&&!m?(u=k,d=I,m=!0):(V&&m&&I>d||!m&&P.suit===o&&I>d)&&(u=k,d=I)}const f=((s.leadPlayer||0)+u)%4;if(!(s.players[f].team===l))return console.log(`[SAFE ACE CHECK] 🚫 ASSO ${r.suit} NON SICURO - non vince e team non sta vincendo!`),!1;console.log(`[SAFE ACE CHECK] ✅ ASSO ${r.suit} sicuro per supporto team`)}return r.suit===t||!this.canWinCurrentTrick(r,n,o,t)&&n.length>0?!1:4-n.length-1===0||(s.trickNumber??1)===1&&r.suit===o?!0:r.suit===o?!n.some(l=>l.suit===o&&(l.rank==="2"||l.rank==="3")):!1}getSafeAces(r,n,o,t,s,a){return r.filter(i=>i.rank===y.Ace&&this.canPlayAceSafely(i,n,o,t,s,a))}selectOptimalCardWithSafetyCheck(r,n,o,t,s,a){const i=this.getSafeAces(r,n,o,t,s,a);if(i.length>0){console.log(`[SAFE ACES] 🔥 Identificati ${i.length} assi sicuri`);const c=i.filter(u=>this.canWinCurrentTrick(u,n,o,t));if(c.length>0)return{recommendedCard:c[0],reason:`🔥 ASSO SICURO: ${c[0].rank} di ${c[0].suit} può vincere senza rischi`,strategy:"safe_ace_play"};const g=n.length===3,l=n.reduce((u,d)=>u+this.getCardValue(d),0);if(g&&l>0)return{recommendedCard:i[0],reason:`🎯 ASSO SICURO ultimo giocatore: 1 punto garantito + ${l.toFixed(1)} punti sul tavolo`,strategy:"safe_ace_loading"}}return null}analyzeCurrentWinner(r,n,o,t,s){if(r.length===0)return{winnerIndex:-1,winnerTeam:-1,winnerCard:null};let a=0,i=r[0],c=this.calculateCardTrickValue(i,n,o);for(let u=1;u<r.length;u++){const d=r[u],m=this.calculateCardTrickValue(d,n,o);m>c&&(c=m,a=u,i=d)}const g=(t+a)%4,l=s.players[g].team;return{winnerIndex:a,winnerTeam:l,winnerCard:i}}calculateCardTrickValue(r,n,o){return r.suit===o?1e3+this.getCardOrder(r):r.suit===n?this.getCardOrder(r):0}analyzeTeammateCollaborativeStatus(r,n,o,t,s,a){if(r.length===0)return{teammateExists:!1,teammateIsWinning:!1,teammateCanBeBeaten:!1,teammateCard:null,shouldLoadTrick:!1,shouldProtectTeammate:!1,collaborativeAction:"neutral",reason:"Primo a giocare - nessun compagno da analizzare"};const i=a.players[s].team;let c=null,g=-1;for(let P=0;P<r.length;P++){const I=(t+P)%4;if(a.players[I].team===i&&I!==s){c=r[P],g=P;break}}if(!c)return{teammateExists:!1,teammateIsWinning:!1,teammateCanBeBeaten:!1,teammateCard:null,shouldLoadTrick:!1,shouldProtectTeammate:!1,collaborativeAction:"neutral",reason:"Nessun compagno nel trick corrente"};const l=this.analyzeCurrentWinner(r,n,o,t,a),u=l.winnerTeam===i&&l.winnerIndex===g,d=4-r.length-1;let m=!1;u&&d>0&&(m=!this.isCardUnbeatable(c,r,n,o,a));let f,C,T=!1,k=!1;if(u){const P=r.reduce((U,N)=>U+this.getCardValue(N),0),I=r.length===3,V=P>=1;I&&(V||a.trickNumber===10)?(f="support",T=!0,C=`Compagno vincente - valorizzare presa (${P.toFixed(1)} punti)`):m?(f="protect",k=!0,C="Compagno vincente ma vulnerabile - proteggere"):(f="support",C="Compagno sicuramente vincente - supporto passivo")}else this.canWinCurrentTrick(a.players[s].hand[0],r,n,o)?(f="compete",C="Compagno non sta vincendo - posso competere per il team"):(f="neutral",C="Compagno non vincente e non posso aiutare");return{teammateExists:!0,teammateIsWinning:u,teammateCanBeBeaten:m,teammateCard:c,shouldLoadTrick:T,shouldProtectTeammate:k,collaborativeAction:f,reason:C}}isCardUnbeatable(r,n,o,t,s){return r.suit===t&&r.rank==="3"?!0:r.suit===t&&r.rank==="2"?!n.some(i=>i.suit===t&&i.rank==="3"):r.suit===o&&r.rank==="3"?!n.some(i=>i.suit===t):!1}shouldUseStrategicCardsForPoints(r,n,o,t,s,a,i=!1){const c=r.filter(f=>this.isStrategicCard(f)&&this.canWinCurrentTrick(f,n,o,t));if(c.length===0)return{shouldUse:!1,recommendedCard:null,reason:"Nessuna carta strategica può vincere"};if(n.some(f=>f.rank==="A"))return c.sort((f,C)=>f.rank==="2"&&C.rank==="3"?-1:f.rank==="3"&&C.rank==="2"?1:this.getCardStrengthScore(f)-this.getCardStrengthScore(C)),{shouldUse:!0,recommendedCard:c[0],reason:"🔥 ASSO SUL TAVOLO! Vale qualsiasi carta strategica per 1 punto"};const l=c.map(f=>{let C=!1;return a&&a.playedBySuit&&a.playedBySuit[f.suit]&&(C=a.playedBySuit[f.suit].some(T=>T.rank==="A")),{card:f,aceAlreadyPlayed:C,effectiveThreshold:C?.6:Number.MAX_SAFE_INTEGER}}),u=l.filter(f=>s>=f.effectiveThreshold);if(u.length>0){u.sort((T,k)=>{if(T.aceAlreadyPlayed&&!k.aceAlreadyPlayed)return-1;if(!T.aceAlreadyPlayed&&k.aceAlreadyPlayed)return 1;const P=T.card.suit===t,I=k.card.suit===t;return!P&&I?-1:P&&!I?1:T.card.rank==="2"&&k.card.rank==="3"?-1:T.card.rank==="3"&&k.card.rank==="2"?1:this.getCardStrengthScore(T.card)-this.getCardStrengthScore(k.card)});const f=u[0],C=f.aceAlreadyPlayed?`🎯 Asso di ${f.card.suit} già uscito! ${s.toFixed(1)} punti ≥ 0.6 - Uso ${f.card.rank}`:`🔥 ${s.toFixed(1)} punti con Asso sul tavolo - Uso ${f.card.rank}`;return{shouldUse:!0,recommendedCard:f.card,reason:C}}return{shouldUse:!1,recommendedCard:null,reason:l.some(f=>!f.aceAlreadyPlayed)?`💎 Conservo ${c[0].rank} per prendere l'Asso di ${c[0].suit} (${s.toFixed(1)} < soglia Asso)`:`💡 ${s.toFixed(1)} punti < 0.6 - Conservo carte strategiche anche se Assi usciti`}}shouldBeAggressiveForOpponentPrevention(r,n,o,t,s,a){const i=r.reduce((f,C)=>f+this.getCardValue(C),0);if(i<1)return{shouldBeAggressive:!1,trickValue:i,opponentIsWinning:!1,aggressivenessLevel:"none",reason:"Nessun punto sul tavolo - non serve aggressività"};const c=this.analyzeCurrentWinner(r,n,o,t,a);if(c.winnerIndex===-1)return{shouldBeAggressive:!1,trickValue:i,opponentIsWinning:!1,aggressivenessLevel:"none",reason:"Primo a giocare - nessun avversario da contrastare"};const g=a.players[s].team;if(!(c.winnerTeam!==g))return{shouldBeAggressive:!1,trickValue:i,opponentIsWinning:!1,aggressivenessLevel:"none",reason:"Il nostro team sta già vincendo - non serve aggressività"};let u,d;return r.some(f=>f.rank==="A")?(u="critical",d="CRITICO: Avversario sta prendendo un ASSO (1 punto) - IMPEDIRE ASSOLUTAMENTE!"):i>=3?(u="critical",d=`CRITICO: Avversario vince ${i.toFixed(1)} punti - IMPEDIRE A TUTTI I COSTI!`):i>=2?(u="high",d=`ALTO: Avversario vince ${i.toFixed(1)} punti - molto importante impedire`):i>=1.5?(u="medium",d=`MEDIO: Avversario vince ${i.toFixed(1)} punti - importante impedire`):i>=1?(u="medium",d=`MEDIO: Avversario vince ${i.toFixed(1)} punti - importante impedire`):(u="none",d="Troppo pochi punti per giustificare aggressività"),{shouldBeAggressive:u!=="none",trickValue:i,opponentIsWinning:!0,aggressivenessLevel:u,reason:d}}findBestCardToPreventOpponent(r,n,o,t,s){if(o){const i=r.filter(c=>c.suit===o);if(i.length>0){const c=i.filter(g=>this.canWinCurrentTrick(g,n,o,t));if(c.length>0){c.sort((l,u)=>this.getCardOrder(l)-this.getCardOrder(u));const g=c[0];return{recommendedCard:g,strategy:"same_suit",reason:`Uso ${g.rank} del seme per vincere con minimo spreco`}}return{recommendedCard:null,strategy:"cannot_win",reason:"Nessuna carta del seme può vincere"}}}const a=r.filter(i=>i.suit===t);if(a.length>0&&(s==="critical"||s==="high"&&a.some(c=>this.getCardValue(c)===0)||s==="medium"&&a.some(c=>this.getCardValue(c)===0&&this.getCardOrder(c)<=6))){const c=a.filter(g=>this.canWinCurrentTrick(g,n,o,t));if(c.length>0){c.sort((u,d)=>{const m=this.getCardValue(u),f=this.getCardValue(d);return m!==f?m-f:this.getCardOrder(u)-this.getCardOrder(d)});const g=c[0],l=this.getCardValue(g);if((g.rank==="3"||g.rank==="2")&&s!=="critical"){const u=c.filter(d=>d.rank!=="3"&&d.rank!=="2");if(u.length>0){const d=u[0];return{recommendedCard:d,strategy:"trump_cut",reason:`Taglio con ${d.rank} di briscola (evito carte strategiche)`}}}return{recommendedCard:g,strategy:"trump_cut",reason:`Taglio con ${g.rank} di briscola (valore: ${l}, livello: ${s})`}}}return{recommendedCard:null,strategy:"cannot_win",reason:"Impossibile vincere la presa con le carte disponibili"}}getTeammateSupport(r,n,o,t,s,a){if(n.length===0)return{shouldSupport:!1,recommendedCard:null,reason:"Primo giocatore - nessun compagno da supportare",supportType:"NO_SUPPORT"};const i=this.analyzeCurrentWinner(n,o,t,0,s),c=s.players[a].team;if(!(i.winnerTeam===c&&i.winnerIndex!==a))return{shouldSupport:!1,recommendedCard:null,reason:"Compagno non sta vincendo",supportType:"NO_SUPPORT"};const l=n[i.winnerIndex];if(o?r.some(d=>d.suit===o):!1){const d=r.filter(I=>I.suit===o),f=["3","2","A","K","H"].includes(l.rank)||l.suit===t&&l.rank==="J",C=d.filter(I=>!this.canWinCurrentTrick(I,n,o,t)),T=C.filter(I=>this.getCardValue(I)>0);if(T.length>0){T.sort((U,N)=>{const v={A:1,K:2,H:3,J:4,2:5,3:6};return(v[U.rank]||999)-(v[N.rank]||999)});const I=this.getCardValue(T[0]),V=f?"imbattibile":"vincente";return{shouldSupport:!0,recommendedCard:T[0],reason:`Compagno ha ${l.rank} (${V}) - REGALO ${T[0].rank} (${I} punti) SENZA superarlo`,supportType:"GIVE_POINTS"}}const k=C.filter(I=>this.getCardValue(I)===0);if(k.length>0)return k.sort((I,V)=>this.getDiscardOrderScore(I)-this.getDiscardOrderScore(V)),{shouldSupport:!0,recommendedCard:k[0],reason:`Compagno sta vincendo - SCARTO carta senza punti: ${k[0].rank}`,supportType:"BE_STRATEGIC"};const P=d.filter(I=>this.getCardValue(I)>0);return P.sort((I,V)=>this.getDiscardOrderScore(I)-this.getDiscardOrderScore(V)),{shouldSupport:!0,recommendedCard:P[0],reason:`Compagno sta vincendo - SCARTO carta meno dolorosa: ${P[0].rank}`,supportType:"BE_STRATEGIC"}}else{if(!(l.suit===t)){console.log(`[TEAMMATE SUPPORT] 🚫 Compagno vince con ${l.rank} NON-briscola - NO taglio!`);const f=r.filter(C=>C.suit!==t);if(f.length>0){const C=f.filter(P=>this.getCardValue(P)>0);if(C.length>0)return C.sort((P,I)=>{const V={A:1,K:2,H:3,J:4,2:5,3:6};return(V[P.rank]||999)-(V[I.rank]||999)}),{shouldSupport:!0,recommendedCard:C[0],reason:`Compagno ha ${l.rank} NON-briscola - REGALO ${C[0].rank} (${this.getCardValue(C[0])} punti) SENZA tagliare`,supportType:"GIVE_POINTS"};const T=f.filter(P=>this.getCardValue(P)===0);if(T.length>0)return T.sort((P,I)=>this.getDiscardOrderScore(P)-this.getDiscardOrderScore(I)),{shouldSupport:!0,recommendedCard:T[0],reason:`Compagno vince con NON-briscola - NON taglio, uso carta senza punti: ${T[0].rank}`,supportType:"BE_STRATEGIC"};const k=f.filter(P=>this.getCardValue(P)>0);return k.sort((P,I)=>this.getDiscardOrderScore(P)-this.getDiscardOrderScore(I)),{shouldSupport:!0,recommendedCard:k[0],reason:`Compagno vince con NON-briscola - sacrifico ${k[0].rank} SENZA tagliare`,supportType:"BE_STRATEGIC"}}else{console.log("[TEAMMATE SUPPORT] ⚠️ Solo briscole disponibili ma compagno vince con NON-briscola!");const C=r.filter(T=>T.suit===t);return C.sort((T,k)=>{const P=this.getCardValue(T)>0,I=this.getCardValue(k)>0;return!P&&I?-1:P&&!I?1:this.getCardOrder(T)-this.getCardOrder(k)}),{shouldSupport:!0,recommendedCard:C[0],reason:`⚠️ SOLO briscole disponibili - uso la PIÙ DEBOLE: ${C[0].rank} (compagno ha ${l.rank} NON-briscola)`,supportType:"BE_STRATEGIC"}}}const m=r.filter(f=>f.suit!==t);if(m.length>0){const C=["3","2","A","K","H"].includes(l.rank)||l.suit===t&&l.rank==="J",T=m.filter(I=>this.getCardValue(I)>0);if(T.length>0){T.sort((V,U)=>{const N={A:1,K:2,H:3,J:4,2:5,3:6};return(N[V.rank]||999)-(N[U.rank]||999)});const I=C?"imbattibile":"vincente";return{shouldSupport:!0,recommendedCard:T[0],reason:`Compagno ha ${l.rank} (${I}) - REGALO ${T[0].rank} non-briscola (${this.getCardValue(T[0])} punti) SENZA tagliare`,supportType:"GIVE_POINTS"}}const k=m.filter(I=>this.getCardValue(I)===0);if(k.length>0)return k.sort((I,V)=>this.getDiscardOrderScore(I)-this.getDiscardOrderScore(V)),{shouldSupport:!0,recommendedCard:k[0],reason:`Compagno sta vincendo - NON taglio, uso carta senza punti: ${k[0].rank}`,supportType:"BE_STRATEGIC"};const P=m.filter(I=>this.getCardValue(I)>0);return P.sort((I,V)=>this.getDiscardOrderScore(I)-this.getDiscardOrderScore(V)),{shouldSupport:!0,recommendedCard:P[0],reason:`Compagno sta vincendo - non spreco briscole, sacrifico ${P[0].rank}`,supportType:"BE_STRATEGIC"}}else{const f=r.filter(T=>T.suit===t),C=f.filter(T=>!this.canWinCurrentTrick(T,n,o,t));return C.length>0?(C.sort((T,k)=>{const P=this.getCardValue(T)>0,I=this.getCardValue(k)>0;return!P&&I?-1:P&&!I?1:this.getDiscardOrderScore(T)-this.getDiscardOrderScore(k)}),{shouldSupport:!0,recommendedCard:C[0],reason:`Compagno già vincente - uso briscola DEBOLE SENZA superarlo: ${C[0].rank}`,supportType:"BE_STRATEGIC"}):(f.sort((T,k)=>{const P=this.getCardValue(T)>0,I=this.getCardValue(k)>0;return!P&&I?-1:P&&!I?1:this.getDiscardOrderScore(T)-this.getDiscardOrderScore(k)}),{shouldSupport:!0,recommendedCard:f[0],reason:`⚠️ SOLO briscole che superano compagno - uso la PIÙ DEBOLE: ${f[0].rank} (male minore)`,supportType:"BE_STRATEGIC"})}}}isHighUnsuperableCard(r,n){return!!(["3","2","A","K","H"].includes(r.rank)||r.suit===n&&r.rank==="J")}shouldNotWasteAce(r,n,o,t,s){if(r.rank!=="A")return{shouldAvoid:!1,reason:"Non è un asso"};if(this.canWinCurrentTrick(r,o,t.leadSuit,t.trumpSuit))return{shouldAvoid:!1,reason:"L'asso può prendere"};if(this.analyzeTeammatePosition(t,s).teammateIsWinning)return{shouldAvoid:!1,reason:"Il team sta prendendo"};const i=n.filter(c=>c!==r&&this.getCardValue(c)===0);return i.length>0?{shouldAvoid:!0,reason:"Asso sprecato: non prende e regala punti agli avversari",alternative:i[0]}:{shouldAvoid:!1,reason:"Nessuna alternativa disponibile"}}handleNoLeadSuit(r,n,o,t){if(r.some(c=>c.suit===o.leadSuit))return{reason:"Ha il seme di uscita"};const a=this.analyzeTeammatePosition(o,t),i=n.reduce((c,g)=>c+this.getCardValue(g),0);if(a.teammateIsWinning){const c=r.filter(g=>["A","K","Q","J"].includes(g.rank));if(c.length>0)return c.sort((g,l)=>{const u={A:4,K:3,Q:2,J:1};return(u[l.rank]||0)-(u[g.rank]||0)}),{recommendedCard:c[0],reason:`Compagno prende: do ${c[0].rank} di ${c[0].suit}`}}else{if(i>=1){const l=r.filter(u=>u.suit===o.trumpSuit).filter(u=>this.canWinCurrentTrick(u,n,o.leadSuit,o.trumpSuit));if(l.length>0)return l.sort((u,d)=>this.getCardOrder(u)-this.getCardOrder(d)),{recommendedCard:l[0],reason:`Taglio per ${i} punti con ${l[0].rank} di ${l[0].suit}`}}const c=r.filter(g=>["4","5","6","7"].includes(g.rank));if(c.length>0)return{recommendedCard:c[0],reason:`Scarto ${c[0].rank} (non spreco 2 e 3)`}}return{reason:"Nessuna strategia applicabile"}}handleLastPlayer(r,n,o,t){if(!n||n.length!==3)return{reason:"Non è l'ultimo giocatore"};const s=r.filter(a=>this.canWinCurrentTrick(a,n,o.leadSuit,o.trumpSuit));if(s.length>0){const a=s.filter(i=>["A","K","Q","J"].includes(i.rank));if(a.length>0)return a.sort((i,c)=>{const g={A:4,K:3,Q:2,J:1};return(g[c.rank]||0)-(g[i.rank]||0)}),{recommendedCard:a[0],reason:`Ultimo giocatore: prendo con ${a[0].rank} di ${a[0].suit}`}}return{reason:"Non posso prendere con carte di valore"}}handleAceByTeamStatus(r,n,o,t){if(!n||n.length===0)return{reason:"Nessuna presa in corso"};const a=r.filter(g=>["A","K","Q","J"].includes(g.rank)&&g.suit!==o.trumpSuit).filter(g=>this.canWinCurrentTrick(g,n,o.leadSuit,o.trumpSuit));if(a.length>0)return a.sort((g,l)=>{const u={A:4,K:3,Q:2,J:1};return(u[l.rank]||0)-(u[g.rank]||0)}),{recommendedCard:a[0],reason:`Prendo sempre con carte di valore non-briscola: ${a[0].rank} di ${a[0].suit}`};const i=this.analyzeTeammatePosition(o,t),c=r.filter(g=>g.rank==="A");if(i.teammateIsWinning){if(c.length>0)return{recommendedCard:c[0],reason:`Team prende: butto asso ${c[0].rank} di ${c[0].suit}`}}else{const g=r.some(l=>l.rank!=="A");if(c.length>0&&g)return{filteredCards:r.filter(u=>u.rank!=="A"),reason:"Team NON prende: conservo asso, uso alternative"}}return{reason:"Gestione asso non applicabile"}}analyzeTeammatePosition(r,n){if(!r.currentTrick||r.currentTrick.length===0)return{teammateIsWinning:!1};let o=0,t=-1,s=-1,a=!1;for(let l=0;l<r.currentTrick.length;l++){const u=r.currentTrick[l];if(u.suit===r.trumpSuit){a=!0;const d=this.getCardOrder(u);d>t&&(t=d,o=l)}else if(u.suit===r.leadSuit&&!a){const d=this.getCardOrder(u);d>s&&(s=d,o=l)}}const i=((r.leadPlayer??0)+o)%4,c=r.players[i].team,g=r.players[n].team;return{teammateIsWinning:c===g&&i!==n}}applyGameRules(r,n,o){const t=n.currentTrick||[],s=this.handleLastPlayer(r,t,n,o);if(s.recommendedCard)return s;const a=this.handleAceByTeamStatus(r,t,n,o);if(a.recommendedCard)return a;a.filteredCards&&(r=a.filteredCards);const i=this.handleNoLeadSuit(r,t,n,o);if(i.recommendedCard)return i;const c=r.filter(g=>g.rank==="A");for(const g of c){const l=this.shouldNotWasteAce(g,r,t,n,o);if(l.shouldAvoid&&l.alternative)return{recommendedCard:l.alternative,reason:l.reason}}return{reason:"Nessuna regola applicabile, usa logica normale"}}};const we=(e,r,n,o,t,s=null)=>{const a=B(e),i=n.reduce((c,g)=>c+B(g),0);if(i>=1){if(_(e,n,o.leadSuit,o.trumpSuit))return{isWaste:!1,reason:`REGOLA ASSOLUTA: ${i.toFixed(1)} punti sul tavolo e posso vincere - MAI SPRECO!`,severity:"low",alternativesExist:!1};if(n.length>0&&o.leadPlayer!==void 0){const g=ie(n,o.leadSuit,o.trumpSuit,o.leadPlayer),l=(o.leadPlayer+g)%4,u=o.players[t].team;if(o.players[l].team===u&&l!==t)return{isWaste:!1,reason:`REGOLA ASSOLUTA BIS: ${i.toFixed(1)} punti e compagno vince - aiutare MAI è spreco!`,severity:"low",alternativesExist:!1}}}if(a===0)return{isWaste:!1,reason:"Carta senza valore - sempre sicura da giocare",severity:"low",alternativesExist:!1};if(e.rank==="3"||e.rank==="2"||e.rank==="A"){const c=_(e,n,o.leadSuit,o.trumpSuit);if(c){if(new D().isObviousWinSituation(e,n,o.leadSuit,o.trumpSuit,i))return{isWaste:!1,reason:`Situazione di vincita ovvia con ${e.rank} - sempre giustificato`,severity:"low",alternativesExist:!1};const d=e.suit===o.trumpSuit,m=e.rank==="2",f=e.rank==="3"&&!d;if(m){if(d){if(i>=2)return{isWaste:!1,reason:`Briscola strategica (${e.rank}) giustificata con ${i} punti sul tavolo`,severity:"low",alternativesExist:!1}}else if(i>=2)return{isWaste:!1,reason:`2 strategico giustificato con ${i} punti sul tavolo`,severity:"low",alternativesExist:!1}}else if(f){if(i>=1)return{isWaste:!1,reason:`3 non di briscola OTTIMO per prendere ${i} punti`,severity:"low",alternativesExist:!1};if(n.length<=1)return{isWaste:!1,reason:`3 non di briscola per controllo e apertura (${n.length+1}° a giocare)`,severity:"low",alternativesExist:!1}}if(n.length>=2&&!n.some(T=>T.rank==="A"||T.rank==="3"||T.rank==="2"))return{isWaste:!1,reason:`Carta strategica (${e.rank}) per anticipare possibili assi avversari`,severity:"low",alternativesExist:!1}}let g=!1;if(n.length>0&&o.leadPlayer!==void 0){const l=ie(n,o.leadSuit,o.trumpSuit,o.leadPlayer),u=(o.leadPlayer+l)%4,d=o.players[t].team;g=o.players[u].team===d&&u!==t}if(!c&&!g)return{isWaste:!0,reason:`CRITICO: ${e.rank} andrebbe agli avversari!`,severity:"critical",alternativesExist:r.length>1}}if(s&&Ve(e,s)&&!_(e,n,o.leadSuit,o.trumpSuit)){let g=!1;if(o.leadSuit&&e.suit===o.leadSuit?g=r.filter(u=>u.suit===o.leadSuit).filter(u=>u.id!==e.id).length>0:g=r.filter(l=>l.id!==e.id).length>0,g)return{isWaste:!0,reason:"DOMINANZA: Carta dominante sprecata, ho alternative",severity:"high",alternativesExist:!0}}return i===0?{isWaste:!0,reason:`PRESA SENZA VALORE: Non do ${a} punti per 0 punti`,severity:"medium",alternativesExist:r.some(c=>B(c)===0)}:e.rank==="A"&&i<3?{isWaste:!0,reason:`ASSO: Richiede almeno 3 punti, presa vale ${i}`,severity:"high",alternativesExist:r.some(c=>c.rank!=="A")}:e.rank==="K"&&i<2?{isWaste:!0,reason:`RE: Richiede almeno 2 punti, presa vale ${i}`,severity:"medium",alternativesExist:r.some(c=>c.rank!=="K")}:a>0&&i<a*.8?{isWaste:!0,reason:`VALORE: Carta vale ${a}, presa solo ${i}`,severity:"low",alternativesExist:r.some(c=>B(c)<a)}:{isWaste:!1,reason:"Carta appropriata per questa situazione",severity:"low",alternativesExist:!1}},be=(e,r,n,o)=>{let t=null;try{t=me(n)}catch(u){console.warn("Errore nell'analisi memoria:",u)}const s=e.map(u=>we(u,e,r,n,o,t)),a=e.filter((u,d)=>!s[d].isWaste);let i=a;if(a.length===0){console.log("[SISTEMA ANTI-SPRECO] ⚠️ Tutte le carte sono spreco, scelgo male minore");const u=Math.min(...s.map(d=>({low:1,medium:2,high:3,critical:4})[d.severity]));i=e.filter((d,m)=>({low:1,medium:2,high:3,critical:4})[s[m].severity]===u)}let c=null,g="",l=0;if(i.length>0)if(r.length===0)c=i[0],g="Carta strategica per aprire",l=.7;else{const u=i.filter(d=>_(d,r,n.leadSuit,n.trumpSuit));u.length>0?(u.sort((d,m)=>B(d)-B(m)),c=u[0],g="Carta più debole che può vincere",l=.85):(i.sort((d,m)=>B(d)-B(m)),c=i[0],g="Carta di minor valore (non posso vincere)",l=.6)}return{optimalCards:i,wasteAnalysis:s,recommendation:{bestCard:c,reason:g,confidence:l}}},x=(e,r,n,o)=>{var i,c,g;let t=B(e)*10;e.suit===n.trumpSuit&&(t+=15,[y.Ace,y.Three,y.Two].includes(e.rank)&&(t+=20));const s=r.highCardsRemaining[((i=e.suit)==null?void 0:i.toString())||""]||[];return s.length===1&&s[0].rank===e.rank&&(t+=25),(((g=r.playedBySuit[((c=e.suit)==null?void 0:c.toString())||""])==null?void 0:g.length)||0)<=2&&(t+=10),t},Ue=(e,r)=>{if(!e.currentTrick||e.currentTrick.length===0)return{teammateIsWinning:!1,teammatePosition:-1,winningCard:null,shouldSupport:!1};const n=e.players[r].team,o=e.leadPlayer??0;let t=-1;for(let u=0;u<e.currentTrick.length;u++){const d=(o+u)%4;if(e.players[d].team===n&&d!==r){t=u;break}}let s=0,a=e.currentTrick[0],i=0;a.suit===e.trumpSuit?i=1e3+K(a):a.suit===e.leadSuit&&(i=K(a));for(let u=1;u<e.currentTrick.length;u++){const d=e.currentTrick[u];let m=0;d.suit===e.trumpSuit?m=1e3+K(d):d.suit===e.leadSuit&&(m=K(d)),m>i&&(i=m,s=u,a=d)}const c=(o+s)%4,l=(c>=0?e.players[c].team:-1)===n&&c!==r;return{teammateIsWinning:l,teammatePosition:t,winningCard:a,shouldSupport:l&&t>=0}},Fe=(e,r,n)=>{const o=e.suit;if(e.rank==="A"){const t=r.some(a=>a.suit===o&&a.rank==="2"),s=r.some(a=>a.suit===o&&a.rank==="3");if(t&&s)return console.log(`[ASSO SICURO] 🎯 Ho la maraffa in ${o} - Asso sicuro!`),!0}else if(e.rank==="K"){const t=r.some(i=>i.suit===o&&i.rank==="A"),s=r.some(i=>i.suit===o&&i.rank==="2"),a=r.some(i=>i.suit===o&&i.rank==="3");if(t&&s&&a)return console.log(`[RE SICURO] 🎯 Ho tutte le carte superiori in ${o} - Re sicuro!`),!0}if(e.rank==="A"){const t=n.some(a=>a.suit===o&&a.rank==="2"),s=n.some(a=>a.suit===o&&a.rank==="3");return t&&s?(console.log(`[ASSO SICURO] 🎯 2 e 3 di ${o} già usciti - Asso sicuro!`),!0):(console.log(`[ASSO NON SICURO] ⚠️ Asso di ${o} non sicuro - 2 e 3 ancora in gioco`),!1)}else if(e.rank==="K"){const t=n.some(i=>i.suit===o&&i.rank==="A"),s=n.some(i=>i.suit===o&&i.rank==="2"),a=n.some(i=>i.suit===o&&i.rank==="3");return t&&s&&a?(console.log(`[RE SICURO] 🎯 A, 2 e 3 di ${o} già usciti - Re sicuro!`),!0):(console.log(`[RE NON SICURO] ⚠️ Re di ${o} non sicuro - A, 2 o 3 ancora in gioco`),!1)}return!1},We=(e,r,n,o=[])=>{var c;const t=((c=r.currentTrick)==null?void 0:c.reduce((g,l)=>g+n.getCardValue(l),0))||0;if(t>=1){const g=e.filter(l=>{var u,d;return n.canWinCurrentTrick(l,r.currentTrick||[],((d=(u=r.currentTrick)==null?void 0:u[0])==null?void 0:d.suit)||null,r.trumpSuit)});if(g.length>0){g.sort((d,m)=>{const f=n.getCardValue(d),C=n.getCardValue(m);return f===0&&C>0?-1:C===0&&f>0?1:n.getCardOrder(d)-n.getCardOrder(m)});const l=g[0],u=t+n.getCardValue(l);return console.log(`[COOPERATIVA] 🚨 REGOLA ASSOLUTA: ${t.toFixed(1)} punti sul tavolo - PRENDO con ${l.rank} di ${l.suit}!`),{canWin:!0,bestCard:l,pointsSecured:u}}}const s=e.filter(g=>{var l,u;return n.canWinCurrentTrick(g,r.currentTrick||[],((u=(l=r.currentTrick)==null?void 0:l[0])==null?void 0:u.suit)||null,r.trumpSuit)});if(s.length===0)return{canWin:!1,bestCard:null,pointsSecured:0};const a=s.filter(g=>g.suit!==r.trumpSuit&&n.getCardValue(g)>0);if(a.length>0)if(!r.currentTrick||r.currentTrick.length===0){const l=a.filter(u=>u.rank==="A"||u.rank==="K"?Fe(u,e,o):!0);if(l.length>0){l.sort((m,f)=>n.getCardValue(f)-n.getCardValue(m));const u=l[0],d=t+n.getCardValue(u);return console.log(`[CARTA VERIFICATA] ✅ Gioco ${u.rank} di ${u.suit} come prima carta - SICURO!`),{canWin:!0,bestCard:u,pointsSecured:d}}else return console.log("[CARTE BLOCCATE] ❌ Nessuna carta di punti sicura come prima carta - salto strategia punti"),{canWin:!1,bestCard:null,pointsSecured:0}}else{a.sort((d,m)=>n.getCardValue(m)-n.getCardValue(d));const l=a[0],u=t+n.getCardValue(l);return{canWin:!0,bestCard:l,pointsSecured:u}}const i=s.filter(g=>g.suit===r.trumpSuit);if(i.length>0){i.sort((u,d)=>n.getCardValue(u)-n.getCardValue(d));const g=i[0],l=t+n.getCardValue(g);if(t>=2||r.trickNumber===10)return{canWin:!0,bestCard:g,pointsSecured:l}}return{canWin:!1,bestCard:null,pointsSecured:0}},Be=(e,r,n,o)=>{var m,f,C,T,k,P,I,V,U,N,v,w,L;console.log(`[COLLABORATIVA AVANZATA] 🤝 Avvio strategia TEAM-FIRST per giocatore ${r}`);const t=new D,s=Ue(e,r),a=((m=e.currentTrick)==null?void 0:m.length)===3,i=((f=e.currentTrick)==null?void 0:f.reduce((O,R)=>O+t.getCardValue(R),0))||0,g=(e.trickNumber??1)>=8;if(console.log(`[COLLABORATIVA AVANZATA] 📊 Posizione: ${a?"ULTIMO":"NON ULTIMO"}, Presa: ${i.toFixed(1)} punti`),s.teammateIsWinning&&e.currentTrick&&e.currentTrick.length>0){const O=(C=e.currentTrick[0])==null?void 0:C.suit,R=O?n.some(p=>p.suit===O):!1;console.log(`[COLLABORATIVA AVANZATA] 🚨 RILEVATO COMPAGNO VINCENTE: ${(T=s.winningCard)==null?void 0:T.rank} di ${(k=s.winningCard)==null?void 0:k.suit}`),console.log(`[COLLABORATIVA AVANZATA] 📊 Presa attuale: ${i.toFixed(1)} punti, Posso seguire seme: ${R?"SÌ":"NO"}`);const S=n.filter(p=>p.suit===e.trumpSuit),E=n.filter(p=>p.suit!==e.trumpSuit);if(R){const p=n.filter(A=>A.suit===O);if(p.length>0){console.log(`[COLLABORATIVA AVANZATA] ✅ SEGUO SEME: ${p.length} carte di ${O} disponibili`);const A=p.filter(h=>Z(h));if(A.length>0){A.sort(($,M)=>{const F={A:1,K:2,H:3,J:4};return(F[$.rank]||999)-(F[M.rank]||999)});const h=A[0];return console.log(`[COLLABORATIVA AVANZATA] 🎯 REGALO PUNTI SEGUENDO SEME: ${h.rank} di ${h.suit} (${t.getCardValue(h)} punti)`),{strategy:"support",recommendedCard:h,reason:`🎁 SUPPORTO OTTIMALE: Compagno vincente → regalo ${h.rank} del seme richiesto (${t.getCardValue(h)} punti)`}}return p.sort((h,$)=>t.getDiscardOrderScore(h)-t.getDiscardOrderScore($)),console.log(`[COLLABORATIVA AVANZATA] 🎯 SCARTO SEGUENDO SEME: ${p[0].rank} di ${p[0].suit}`),{strategy:"support",recommendedCard:p[0],reason:`🎯 SUPPORTO PULITO: Compagno vincente → scarto ${p[0].rank} seguendo il seme`}}}if(!R&&E.length>0){console.log(`[COLLABORATIVA AVANZATA] 🛡️ DIVIETO BRISCOLE: ${E.length} carte non-briscola disponibili, ${S.length} briscole VIETATE`);const p=E.filter(h=>Z(h));if(p.length>0){p.sort(($,M)=>{const F={A:1,K:2,H:3,J:4};return(F[$.rank]||999)-(F[M.rank]||999)});const h=p[0];return console.log(`[COLLABORATIVA AVANZATA] 🎯 ANTI-SPRECO PUNTI: ${h.rank} di ${h.suit} (${t.getCardValue(h)} punti) invece di sprecare briscole`),{strategy:"support",recommendedCard:h,reason:`🛡️ ANTI-SPRECO CRITICO: Compagno vincente → regalo ${h.rank} non-briscola (${t.getCardValue(h)} punti) invece di sprecare briscole`}}const A=E.filter(h=>t.getCardValue(h)===0);if(A.length>0)return A.sort((h,$)=>t.getDiscardOrderScore(h)-t.getDiscardOrderScore($)),console.log(`[COLLABORATIVA AVANZATA] 🎯 ANTI-SPRECO SCARTO: ${A[0].rank} di ${A[0].suit} invece di sprecare briscole`),{strategy:"support",recommendedCard:A[0],reason:`🛡️ ANTI-SPRECO TOTALE: Compagno vincente → scarto ${A[0].rank} non-briscola invece di sprecare briscole`};if(E.length>0)return E.sort((h,$)=>t.getDiscardOrderScore(h)-t.getDiscardOrderScore($)),console.log(`[COLLABORATIVA AVANZATA] ⚠️ SACRIFICIO MINIMO: ${E[0].rank} di ${E[0].suit} per evitare briscole`),{strategy:"support",recommendedCard:E[0],reason:`⚠️ SACRIFICIO MINIMO: Compagno vincente → sacrifico ${E[0].rank} non-briscola per conservare le briscole`}}if(S.length>0&&E.length===0){if(console.log("[COLLABORATIVA AVANZATA] ⚠️ CASO ESTREMO: Solo briscole disponibili, compagno sta vincendo"),i>=1){const p=S.filter(A=>t.getCardValue(A)===0);if(p.length>0)return p.sort((A,h)=>t.getCardStrengthScore(A)-t.getCardStrengthScore(h)),console.log(`[COLLABORATIVA AVANZATA] 🎯 BRISCOLA DEBOLE GIUSTIFICATA: ${p[0].rank} di ${p[0].suit} per ${i.toFixed(1)} punti`),{strategy:"support",recommendedCard:p[0],reason:`🎯 BRISCOLA GIUSTIFICATA: Solo briscole disponibili, uso la più debole ${p[0].rank} per ${i.toFixed(1)} punti in presa`}}return S.sort((p,A)=>t.getDiscardOrderScore(p)-t.getDiscardOrderScore(A)),console.log(`[COLLABORATIVA AVANZATA] 😞 BRISCOLA FORZATA: ${S[0].rank} di ${S[0].suit} (nessuna alternativa)`),{strategy:"support",recommendedCard:S[0],reason:`😞 BRISCOLA FORZATA: Compagno vincente ma nessuna alternativa disponibile, uso la briscola più debole ${S[0].rank}`}}}const l=We(n,e,t,o.playedCards);if(l.canWin&&l.bestCard)return console.log("[COLLABORATIVA AVANZATA] 🎯 PRIORITÀ ASSOLUTA 0: POSSO VINCERE CON CARTE DI PUNTI!"),console.log(`[COLLABORATIVA AVANZATA] 💰 PUNTI ASSICURATI AL TEAM: ${l.pointsSecured.toFixed(1)} punti con ${l.bestCard.rank} di ${l.bestCard.suit}`),{strategy:"compete",recommendedCard:l.bestCard,reason:`🎯 PUNTI ASSICURATI: ${l.pointsSecured.toFixed(1)} punti per il team con ${l.bestCard.rank} di ${l.bestCard.suit}! ${l.bestCard.suit===e.trumpSuit?"Briscola strategica":"Carta di punti perfetta"}`};if(s.teammateIsWinning&&s.winningCard){const O=s.winningCard,R=(I=(P=e.currentTrick)==null?void 0:P[0])==null?void 0:I.suit,S=O.rank==="3"||O.rank==="2"||O.rank==="A",E=R?n.some(p=>p.suit===R):!0;if(S&&!E){console.log(`[COLLABORATIVA AVANZATA] 🚨 REGOLA SUPREMA: Compagno vince con ${O.rank} forte e non posso seguire ${R} - DEVO DARE PUNTI!`);const p=n.filter(A=>A.rank==="A"||A.rank==="K"||A.rank==="H"||A.rank==="J");if(p.length>0){p.sort((h,$)=>{const M={A:4,K:3,H:2,J:1};return(M[$.rank]||0)-(M[h.rank]||0)});const A=p[0];return console.log(`[COLLABORATIVA AVANZATA] 🎯 PUNTI AL COMPAGNO: ${A.rank} di ${A.suit} (valore: ${t.getCardValue(A)}) per carta forte ${O.rank}`),{strategy:"support",recommendedCard:A,reason:`🚨 REGOLA SUPREMA: Compagno vince con ${O.rank} forte, non posso seguire ${R} → SEMPRE dare punti! (${A.rank})`}}}}if(s.teammateIsWinning&&i>=1){console.log(`[COLLABORATIVA AVANZATA] 🚨 REGOLA ASSOLUTA BIS: Compagno vince ${i.toFixed(1)} punti - AIUTO SEMPRE!`);const O=n.filter(R=>Z(R));if(O.length>0){O.sort((S,E)=>S.rank==="A"&&E.rank!=="A"?-1:E.rank==="A"&&S.rank!=="A"?1:t.getCardValue(E)-t.getCardValue(S));const R=O[0];return console.log(`[COLLABORATIVA AVANZATA] 🤝 AIUTO COMPAGNO: ${R.rank} di ${R.suit} (valore: ${t.getCardValue(R)})`),{strategy:"support",recommendedCard:R,reason:`REGOLA ASSOLUTA: Aiuto compagno che vince ${i.toFixed(1)} punti con ${R.rank}`}}}if(a&&s.teammateIsWinning){const O=e.trickNumber<=3;if(console.log(`[COLLABORATIVA AVANZATA] 🎯 PRIORITÀ ASSOLUTA 1 ATTIVATA: Ultimo giocatore + Team vincente! ${O?"(PRIME MANI - COOPERAZIONE CRITICA!)":""}`),s.winningCard&&s.winningCard.rank==="3"&&s.winningCard.suit!==e.trumpSuit){const A=n.find(h=>h.rank==="A"&&h.suit===s.winningCard.suit);if(A)return console.log(`[COLLABORATIVA AVANZATA] 🏆 ASSO PRIORITARIO ULTIMO: Compagno ha giocato 3 di ${s.winningCard.suit}, gioco Asso di ${A.suit}! ${O?"(STRATEGIA PRIME MANI PERFETTA!)":""}`),{strategy:"support",recommendedCard:A,reason:`🏆 RICONOSCIMENTO 3 (ULTIMO): Compagno ha giocato 3 di ${s.winningCard.suit} per prendere - gioco Asso per massimizzare punti! ${O?"(STRATEGIA PRIME MANI!)":""}`}}if(s.winningCard&&t.getCardValue(s.winningCard)>0){console.log(`[COLLABORATIVA AVANZATA] 🔥 COMPAGNO PRENDE CON PUNTI: ${s.winningCard.rank} (${t.getCardValue(s.winningCard)} punti)`);const A=n.filter($=>$.rank==="A");if(A.length>0){const $=A[0];return console.log(`[COLLABORATIVA AVANZATA] 🏆 ASSO PRIORITARIO: Compagno prende con punti, gioco Asso di ${$.suit}!`),{strategy:"support",recommendedCard:$,reason:`🏆 ASSO PRIORITARIO: Compagno prende con carta di punti (${s.winningCard.rank}) = SEMPRE giocare l'Asso! (${t.getCardValue($)} punto)`}}const h=["J","H","K"];for(const $ of h){const M=n.find(F=>F.rank===$);if(M)return console.log(`[COLLABORATIVA AVANZATA] 💎 CARTA ALTERNATIVA: Nessun asso, gioco ${M.rank} di ${M.suit}`),{strategy:"support",recommendedCard:M,reason:`💎 SUPPORTO PRIORITARIO: Compagno prende con punti, gioco ${M.rank} (${t.getCardValue(M)} punti)`}}}const S=H(n).sort((A,h)=>t.getCardValue(h)-t.getCardValue(A));if(S.length>0){const A=S[0];return console.log(`[COLLABORATIVA AVANZATA] 🔥 VALORIZZAZIONE MASSIMA: ${A.rank} di ${A.suit} (${t.getCardValue(A)} punti)`),{strategy:"support",recommendedCard:A,reason:`🎯 TEAM-FIRST P1: Ultimo giocatore + team vincente = SEMPRE valorizzare! Gioco ${A.rank} (${t.getCardValue(A)} punti)`}}const E=n.filter(A=>A.rank==="A");if(E.length>0){const h=E.find($=>{var M,F;return $.suit===((F=(M=e.currentTrick)==null?void 0:M[0])==null?void 0:F.suit)})||E.sort(($,M)=>t.getCardValue(M)-t.getCardValue($))[0];return console.log(`[COLLABORATIVA AVANZATA] 🏆 ASSO MASSIMIZZAZIONE: Team vincente + ultimo giocatore = gioco Asso di ${h.suit} per massimizzare punti!`),{strategy:"support",recommendedCard:h,reason:`🏆 MASSIMIZZAZIONE TEAM: Team vincente + ultimo giocatore = SEMPRE giocare l'Asso! (${t.getCardValue(h)} punti)`}}const p=n.sort((A,h)=>t.getCardValue(h)-t.getCardValue(A))[0];return{strategy:"support",recommendedCard:p,reason:`🎯 MASSIMIZZAZIONE PUNTI: Ultimo giocatore + team vincente = gioco carta di maggior valore (${t.getCardValue(p)} punti)`}}if(((V=e.currentTrick)==null?void 0:V.length)===2&&s.teammateIsWinning&&s.winningCard){const O=s.winningCard;if(oe(O,o,e)||t.isObviousWinSituation(O,e.currentTrick||[],((N=(U=e.currentTrick)==null?void 0:U[0])==null?void 0:N.suit)||null,e.trumpSuit,i)){const S=e.trickNumber<=3;if(console.log(`[COLLABORATIVA AVANZATA] 🚀 PRIORITÀ ASSOLUTA 2 ATTIVATA: Terzo giocatore + compagno imbattibile! ${S?"(PRIME MANI - COOPERAZIONE CRITICA!)":""}`),O.rank==="3"&&O.suit!==e.trumpSuit){const p=n.find(A=>A.rank==="A"&&A.suit===O.suit);if(p)return console.log(`[COLLABORATIVA AVANZATA] 🏆 ASSO PRIORITARIO TERZO: Compagno ha giocato 3 di ${O.suit}, gioco Asso di ${p.suit}! ${S?"(STRATEGIA PRIME MANI PERFETTA!)":""}`),{strategy:"support",recommendedCard:p,reason:`🏆 RICONOSCIMENTO 3 (TERZO): Compagno ha giocato 3 di ${O.suit} per prendere - gioco Asso per massimizzare punti! ${S?"(STRATEGIA PRIME MANI!)":""}`}}const E=H(n).sort((p,A)=>t.getCardValue(A)-t.getCardValue(p));if(E.length>0){const p=E[0];return console.log(`[COLLABORATIVA AVANZATA] 🚀 VALORIZZAZIONE TERZO GIOCATORE: ${p.rank} di ${p.suit} (${t.getCardValue(p)} punti) - Compagno ha ${O.rank} imbattibile!`),{strategy:"support",recommendedCard:p,reason:`🚀 TEAM-FIRST P2: Terzo giocatore + compagno imbattibile (${O.rank}) = SEMPRE valorizzare! Gioco ${p.rank} (${t.getCardValue(p)} punti)`}}}}if(((v=e.currentTrick)==null?void 0:v.length)===1&&s.teammateIsWinning&&s.winningCard){const O=s.winningCard;if(O.rank==="3"&&O.suit!==e.trumpSuit||O.rank==="2"&&O.suit!==e.trumpSuit||O.suit===e.trumpSuit&&(O.rank==="3"||O.rank==="2")){const S=e.trickNumber<=3;if(console.log(`[COLLABORATIVA AVANZATA] ⚡ PRIORITÀ ASSOLUTA 3 ATTIVATA: Secondo giocatore + compagno primo con carta super forte! ${S?"(PRIME MANI - COOPERAZIONE CRITICA!)":""}`),O.rank==="3"&&O.suit!==e.trumpSuit){const p=n.find(A=>A.rank==="A"&&A.suit===O.suit);if(p)return console.log(`[COLLABORATIVA AVANZATA] 🏆 ASSO PRIORITARIO: Compagno ha giocato 3 di ${O.suit}, gioco Asso di ${p.suit}! ${S?"(STRATEGIA PRIME MANI PERFETTA!)":""}`),{strategy:"support",recommendedCard:p,reason:`🏆 RICONOSCIMENTO 3: Compagno ha giocato 3 di ${O.suit} per prendere - gioco Asso per massimizzare punti! ${S?"(STRATEGIA PRIME MANI!)":""}`}}const E=H(n).filter(p=>{var A,h;return!t.canWinCurrentTrick(p,e.currentTrick||[],((h=(A=e.currentTrick)==null?void 0:A[0])==null?void 0:h.suit)||null,e.trumpSuit)}).sort((p,A)=>t.getCardValue(A)-t.getCardValue(p));if(E.length>0){const p=E[0];return console.log(`[COLLABORATIVA AVANZATA] ⚡ VALORIZZAZIONE SECONDO GIOCATORE: ${p.rank} di ${p.suit} (${t.getCardValue(p)} punti) - Compagno primo con ${O.rank} super forte!`),{strategy:"support",recommendedCard:p,reason:`⚡ TEAM-FIRST P3: Secondo giocatore + compagno primo con carta super forte (${O.rank}) = valorizzare senza competere! Gioco ${p.rank} (${t.getCardValue(p)} punti)`}}}}if(!s.teammateIsWinning&&i>0){if(console.log(`[COLLABORATIVA AVANZATA] 🛡️ AVVERSARI VINCENTI: ${i.toFixed(1)} punti in gioco!`),a){const R=e.trickNumber<=3;console.log(`[COLLABORATIVA AVANZATA] 🔥 ULTIMO GIOCATORE + AVVERSARI VINCENTI! ${R?"(PRIME MANI - RIBALTAMENTO CRITICO!)":""}`);const S=n.filter(E=>E.rank==="A");if(S.length>0){const E=S.filter(p=>{var A,h;return t.canWinCurrentTrick(p,e.currentTrick||[],((h=(A=e.currentTrick)==null?void 0:A[0])==null?void 0:h.suit)||null,e.trumpSuit)});if(E.length>0){const A=E.find(h=>{var $,M;return h.suit===((M=($=e.currentTrick)==null?void 0:$[0])==null?void 0:M.suit)})||E.sort((h,$)=>t.getCardValue($)-t.getCardValue(h))[0];return console.log(`[COLLABORATIVA AVANZATA] 🏆 RIBALTAMENTO ASSO: ${A.rank} di ${A.suit} ribalta ${i.toFixed(1)} punti! ${R?"(STRATEGIA PRIME MANI PERFETTA!)":""}`),{strategy:"compete",recommendedCard:A,reason:`🏆 RIBALTAMENTO CRITICO: Ultimo giocatore con Asso di ${A.suit} ribalta ${i.toFixed(1)} punti agli avversari! ${R?"(PRIME MANI!)":""}`}}}}if(i>=1){console.log(`[COLLABORATIVA AVANZATA] 🚨 RECUPERO AGGRESSIVO: ${i.toFixed(1)} punti da recuperare!`);const R=n.filter(S=>{var E,p;return t.canWinCurrentTrick(S,e.currentTrick||[],((p=(E=e.currentTrick)==null?void 0:E[0])==null?void 0:p.suit)||null,e.trumpSuit)});if(R.length>0){const S=R.filter(A=>A.suit!==e.trumpSuit);if(S.length>0){S.sort((h,$)=>{const M=x(h,o,e),F=x($,o,e);return M-F});const A=S[0];return console.log(`[COLLABORATIVA AVANZATA] 🎯 RECUPERO NON-BRISCOLA: ${A.rank} di ${A.suit} per ${i.toFixed(1)} punti`),{strategy:"compete",recommendedCard:A,reason:`🎯 RECUPERO OTTIMALE: Non-briscola ${A.rank} recupera ${i.toFixed(1)} punti senza sprecare briscole!`}}if(i>=1){const A=R.filter(h=>h.suit===e.trumpSuit&&(h.rank==="7"||h.rank==="6"||h.rank==="5"||h.rank==="4"));if(A.length>0){A.sort(($,M)=>t.getCardStrengthScore($)-t.getCardStrengthScore(M));const h=A[0];return console.log(`[COLLABORATIVA AVANZATA] 🎯 RECUPERO OTTIMALE: Briscola bassa ${h.rank} di ${h.suit} per ${i.toFixed(1)} punti!`),{strategy:"compete",recommendedCard:h,reason:`🎯 RECUPERO OTTIMALE: Briscola bassa ${h.rank} recupera ${i.toFixed(1)} punti dagli avversari!`}}}const p=R.sort((A,h)=>{const $=x(A,o,e),M=x(h,o,e);return $-M})[0];return console.log(`[COLLABORATIVA AVANZATA] 🚨 RECUPERO CON: ${p.rank} di ${p.suit} - Salvo ${i.toFixed(1)} punti!`),{strategy:"compete",recommendedCard:p,reason:`🚨 RECUPERO AGGRESSIVO: Avversari vincenti con ${i.toFixed(1)} punti, recupero con ${p.rank}!`}}}if(i===0){console.log("[COLLABORATIVA AVANZATA] 🛡️ PRESA SENZA VALORE: 0 punti - evito briscole a tutti i costi");const R=n.filter(S=>S.suit!==e.trumpSuit);if(R.length>0){const S=(L=(w=e.currentTrick)==null?void 0:w[0])==null?void 0:L.suit;if(S?R.some(p=>p.suit===S):!1){const p=R.filter(A=>A.suit===S);return p.sort((A,h)=>t.getDiscardOrderScore(A)-t.getDiscardOrderScore(h)),console.log(`[COLLABORATIVA AVANZATA] 🎯 PRESA 0 PUNTI - SEGUO SEME: ${p[0].rank} di ${p[0].suit}`),{strategy:"support",recommendedCard:p[0],reason:`🛡️ CONSERVAZIONE: Presa da 0 punti → seguo seme con ${p[0].rank} per non sprecare briscole`}}else{const p=R.filter(A=>t.getCardValue(A)===0);return p.length>0?(p.sort((A,h)=>t.getDiscardOrderScore(A)-t.getDiscardOrderScore(h)),console.log(`[COLLABORATIVA AVANZATA] 🎯 PRESA 0 PUNTI - SCARTO PULITO: ${p[0].rank} di ${p[0].suit}`),{strategy:"support",recommendedCard:p[0],reason:`🛡️ SCARTO OTTIMALE: Presa da 0 punti → scarto ${p[0].rank} non-briscola senza valore`}):(R.sort((A,h)=>t.getDiscardOrderScore(A)-t.getDiscardOrderScore(h)),console.log(`[COLLABORATIVA AVANZATA] ⚠️ PRESA 0 PUNTI - SACRIFICIO MINIMO: ${R[0].rank} di ${R[0].suit}`),{strategy:"support",recommendedCard:R[0],reason:`⚠️ SACRIFICIO MINIMO: Presa da 0 punti → sacrifico ${R[0].rank} non-briscola per conservare briscole`})}}else{console.log("[COLLABORATIVA AVANZATA] 😞 CASO ESTREMO: Solo briscole per presa da 0 punti - uso la più debole");const S=n.filter(p=>p.suit===e.trumpSuit),E=S.filter(p=>t.getCardValue(p)===0);return E.length>0?(E.sort((p,A)=>t.getCardStrengthScore(p)-t.getCardStrengthScore(A)),console.log(`[COLLABORATIVA AVANZATA] 😞 BRISCOLA DEBOLE FORZATA: ${E[0].rank} di ${E[0].suit}`),{strategy:"support",recommendedCard:E[0],reason:`😞 FORZATURA: Presa da 0 punti ma solo briscole disponibili → uso la più debole ${E[0].rank}`}):(S.sort((p,A)=>t.getDiscardOrderScore(p)-t.getDiscardOrderScore(A)),console.log(`[COLLABORATIVA AVANZATA] 😢 SPRECO FORZATO: ${S[0].rank} di ${S[0].suit} per presa da 0 punti`),{strategy:"support",recommendedCard:S[0],reason:`😢 SPRECO INEVITABILE: Presa da 0 punti, solo briscole con valore disponibili → uso ${S[0].rank}`})}}const O=n.filter(R=>t.getCardValue(R)===0);if(O.length>0){const R=O.reduce((S,E)=>t.getCardOrder(E)<t.getCardOrder(S)?E:S);return{strategy:"support",recommendedCard:R,reason:`🛡️ PROTEZIONE TEAM: Avversari vincenti con ${i.toFixed(1)} punti, gioco carta senza punti (${R.rank})`}}}if(!s.shouldSupport)return console.log("[COLLABORATIVA AVANZATA] ⚡ Nessun supporto necessario - strategia neutra"),{strategy:"neutral"};if(s.teammateIsWinning){console.log("[COLLABORATIVA AVANZATA] 🏆 COMPAGNO STA VINCENDO - Modalità supporto attivo!");const O=s.winningCard;if(!O)return{strategy:"support"};if(oe(O,o,e)){console.log("[COLLABORATIVA AVANZATA] 🔥 COMPAGNO IMBATTIBILE - Preparazione valorizzazione!");const p=n.filter(A=>{var h,$;return!t.canWinCurrentTrick(A,e.currentTrick||[],(($=(h=e.currentTrick)==null?void 0:h[0])==null?void 0:$.suit)||null,e.trumpSuit)});if(p.length>0){const A=p[0];return{strategy:"support",recommendedCard:A,reason:`🤝 SUPPORTO SICURO: Compagno imbattibile, non interferisco con ${A.rank}`}}}const S=n.filter(p=>O.suit===e.trumpSuit?p.suit===e.trumpSuit?t.getCardOrder(p)<=t.getCardOrder(O):!0:p.suit===e.trumpSuit?!1:p.suit===O.suit?t.getCardOrder(p)<=t.getCardOrder(O):!0);if(S.length===0)return{strategy:"support",recommendedCard:n.reduce((A,h)=>{const $=h.suit===e.trumpSuit?1e3+t.getCardOrder(h):t.getCardOrder(h),M=A.suit===e.trumpSuit?1e3+t.getCardOrder(A):t.getCardOrder(A);return $<M?h:A}),reason:"Giocando la carta meno competitiva per non danneggiare il compagno"};if(a&&(i>=1||g)){console.log(`[COLLABORATIVA AVANZATA] 🔥 ULTIMO GIOCATORE + COMPAGNO VINCENTE + PRESA PREZIOSA (${i.toFixed(1)} punti)`);const p=H(S);if(p.length>0){const $=p.reduce((M,F)=>t.getCardValue(F)>t.getCardValue(M)?F:M);return console.log(`[COLLABORATIVA AVANZATA] 🏆 VALORIZZAZIONE FIGURE: ${$.rank} di ${$.suit} (${t.getCardValue($)} punti)`),{strategy:"support",recommendedCard:$,reason:`🏆 VALORIZZAZIONE FIGURE: Ultimo giocatore + team vincente, gioco ${$.rank} (${t.getCardValue($)} punti)`}}const A=S.filter($=>$.suit===e.trumpSuit&&($.rank==="7"||$.rank==="6"||$.rank==="5"||$.rank==="4"));if(A.length>0&&i>=1){const $=A.filter(M=>{var F,z;return t.canWinCurrentTrick(M,e.currentTrick||[],((z=(F=e.currentTrick)==null?void 0:F[0])==null?void 0:z.suit)||null,e.trumpSuit)});if($.length>0){$.sort((F,z)=>t.getCardStrengthScore(F)-t.getCardStrengthScore(z));const M=$[0];return console.log(`[COLLABORATIVA AVANZATA] 🚨 CORREZIONE CRITICA: Prendo con briscola bassa ${M.rank} di ${M.suit} per salvare ${i.toFixed(1)} punti!`),{strategy:"support",recommendedCard:M,reason:`🚨 SALVATAGGIO PUNTI: Ultimo giocatore, uso briscola bassa ${M.rank} per salvare ${i.toFixed(1)} punti del team!`}}}const h=S.filter($=>Z($));if(h.length>0){h.sort((M,F)=>t.getCardValue(F)-t.getCardValue(M));const $=h[0];return console.log(`[COLLABORATIVA AVANZATA] 💰 FALLBACK PUNTI: ${$.rank} di ${$.suit} (${t.getCardValue($)} punti)`),{strategy:"support",recommendedCard:$,reason:`💰 PUNTI FALLBACK: Ultimo giocatore, valorizzo con ${$.rank} (${t.getCardValue($)} punti)`}}}if(i>=1){console.log(`[COLLABORATIVA AVANZATA] 🎯 NON-ULTIMO + COMPAGNO VINCENTE + PRESA PREZIOSA (${i.toFixed(1)} punti)`);const p=S.filter(A=>t.getCardValue(A)>0&&(A.rank==="A"||A.rank==="K"||A.rank==="H"||A.rank==="J"));if(p.length>0){p.sort((h,$)=>t.getCardValue($)-t.getCardValue(h));const A=p[0];return console.log(`[COLLABORATIVA AVANZATA] 🏆 VALORIZZAZIONE NON-ULTIMO: ${A.rank} di ${A.suit} (${t.getCardValue(A)} punti)`),{strategy:"support",recommendedCard:A,reason:`🏆 VALORIZZAZIONE FIGURE: Compagno vincente + presa preziosa, gioco ${A.rank} (${t.getCardValue(A)} punti)`}}}return{strategy:"support",recommendedCard:S.reduce((p,A)=>{const h=x(A,o,e),$=x(p,o,e);return h<$?A:p}),reason:"Supportando il compagno con carta di scarto"}}return{strategy:"compete"}},De={[G.EASY]:{errorRate:.4,cooperationRate:.6,strategicRate:.3,memoryUsage:.4},[G.MEDIUM]:{errorRate:.1,cooperationRate:.9,strategicRate:.6,memoryUsage:.7},[G.HARD]:{errorRate:0,cooperationRate:1,strategicRate:1,memoryUsage:1}},Ge=(e,r,n,o)=>{var g,l;const t=De[o],s=((l=(g=r.players)==null?void 0:g[n])==null?void 0:l.team)||"UNKNOWN",a=`CPU${n} (Team ${s}) [${o}]`;if(console.log(`
🤖 [UNIFIED AI] ${a} - Thinking...`),!r.currentTrick||r.currentTrick.length===0){const u=r.trickNumber??1;if(u===1&&r.trumpSuit&&r.lastTrumpSelector===n){const f=e.some(k=>k.suit===r.trumpSuit&&k.rank==="A"),C=e.some(k=>k.suit===r.trumpSuit&&k.rank==="2"),T=e.some(k=>k.suit===r.trumpSuit&&k.rank==="3");if(f&&C&&T){const k=e.find(P=>P.suit===r.trumpSuit&&P.rank==="A");if(k)return console.log(`[UNIFIED AI] 🎯🎯🎯 MARAFFA OBBLIGATORIA! Gioco Asso di ${k.suit} per 3 punti bonus!`),k}}const m=u<=3?.8:t.strategicRate;if(Math.random()<m){console.log(`[UNIFIED AI] 🎯 Tentativo apertura strategica (${(m*100).toFixed(0)}%)`);const f=e.filter(C=>C.rank==="3"&&C.suit!==r.trumpSuit);if(f.length>0){const C=f[0];return console.log(`[UNIFIED AI] ✅ Apertura con 3 di ${C.suit}`),C}}}if(Math.random()<t.cooperationRate){const u=re(r),d=Be(r,n,e,u);if(d.strategy==="support"&&d.recommendedCard)return console.log(`[UNIFIED AI] 🤝 SUPPORTO TEAM (${(t.cooperationRate*100).toFixed(0)}%): ${d.reason}`),d.recommendedCard;if(d.strategy==="compete"&&d.recommendedCard)if(!r.currentTrick||r.currentTrick.length===0){const C=new D().getCardValue(d.recommendedCard),T=["4","5","6","7"].includes(d.recommendedCard.rank),k=d.recommendedCard.suit===r.trumpSuit;if(C>0&&!T&&!k)console.log(`[UNIFIED AI] ⚠️ BLOCCO strategia cooperativa - carta con punti (${d.recommendedCard.rank} di ${d.recommendedCard.suit}) non appropriata per primo turno`);else return console.log(`[UNIFIED AI] 🚨 RECUPERO AGGRESSIVO (${(t.cooperationRate*100).toFixed(0)}%): ${d.reason}`),d.recommendedCard}else return console.log(`[UNIFIED AI] 🚨 RECUPERO AGGRESSIVO (${(t.cooperationRate*100).toFixed(0)}%): ${d.reason}`),d.recommendedCard}if(r.currentTrick&&r.currentTrick.length>0&&r.currentTrick.some(d=>d&&d.rank==="A")){console.log("[UNIFIED AI] 🔥 ASSO SUL TAVOLO! Cerco di prenderlo...");const d=new D,m=e.filter(f=>{const C=[...r.currentTrick.filter(Boolean),f];return d.canWinCurrentTrick(f,C,r.leadSuit,r.trumpSuit)});if(m.length>0)if(Math.random()>t.errorRate){const f=m[0];return console.log(`[UNIFIED AI] 🔥 PRENDO ASSO CON ${f.rank} di ${f.suit}!`),f}else console.log(`[UNIFIED AI] 😵 ERRORE: Non riesco a prendere l'asso (${(t.errorRate*100).toFixed(0)}% errore)`)}if(Math.random()<t.memoryUsage){console.log(`[UNIFIED AI] 🧠 Uso analisi memoria (${(t.memoryUsage*100).toFixed(0)}%)`);const u=be(e,r.currentTrick||[],r,n);if(u.optimalCards.length>0&&Math.random()>t.errorRate){const d=u.optimalCards[0];if((!r.currentTrick||r.currentTrick.length===0)&&d.suit===r.trumpSuit){const f=e.filter(C=>C.suit===r.trumpSuit);if(f.length<4)console.log(`[UNIFIED AI] ⚠️ Memoria suggerisce briscola ${d.rank}, ma ho solo ${f.length} briscole - ignoro suggerimento`);else return console.log(`[UNIFIED AI] ✅ Carta ottimale dalla memoria: ${d.rank} di ${d.suit}`),d}else return console.log(`[UNIFIED AI] ✅ Carta ottimale dalla memoria: ${d.rank} di ${d.suit}`),d}}if(Math.random()<t.errorRate){console.log(`[UNIFIED AI] 😵 ERRORE CASUALE (${(t.errorRate*100).toFixed(0)}% probabilità)`);const u=e[Math.floor(Math.random()*e.length)];return console.log(`[UNIFIED AI] 🎲 Gioco carta casuale: ${u.rank} di ${u.suit}`),u}if(console.log("[UNIFIED AI] 🎯 Logica ottimale"),!r.currentTrick||r.currentTrick.length===0){console.log("[UNIFIED AI] 🎯 PRIMO DEL TURNO - Strategia apertura intelligente");const u=new D,d=me(r);e.filter(V=>V.suit===r.trumpSuit);const m=ve(e,r);let f=e;m.shouldConserve&&m.cardToAvoid&&(f=e.filter(V=>{var U,N;return!(V.suit===((U=m.cardToAvoid)==null?void 0:U.suit)&&V.rank===((N=m.cardToAvoid)==null?void 0:N.rank))}),console.log(`[UNIFIED AI] 🎯 ${m.reason}`));const C=f.filter(V=>{if((V.rank==="A"||V.rank==="K")&&V.suit!==r.trumpSuit)return!1;if(V.suit===r.trumpSuit){const U=e.filter(N=>N.suit===r.trumpSuit);if(U.length<4)return console.log(`[UNIFIED AI] ⚠️ Evito briscola ${V.rank} come apertura - ho solo ${U.length} briscole`),!1}return $e(V,d,r.trumpSuit)});if(C.length>0){const V=Me(C,d);if(V.length>0){const U=V[0];return console.log(`[UNIFIED AI] ✅ CARTA SICURA: ${U.rank} di ${U.suit}`),U}}const T=Le(f,r);if(T.shouldPlayTrump&&T.recommendedCard)return console.log(`[UNIFIED AI] 🎯 ${T.reason}`),T.recommendedCard;const k=f.filter(V=>{const U=u.getCardValue(V)===0,N=V.suit!==r.trumpSuit,v=["4","5","6","7"].includes(V.rank);return U&&N&&v});if(k.length>0){const V=k.reduce((U,N)=>u.getCardOrder(N)>u.getCardOrder(U)?N:U);return console.log(`[UNIFIED AI] ✅ CARTA LISCIA: ${V.rank} di ${V.suit} (nessun punto da perdere)`),V}const P=f.filter(V=>!((V.rank==="A"||V.rank==="K")&&V.suit!==r.trumpSuit)),I=(P.length>0?P:f).reduce((V,U)=>u.getCardValue(U)<u.getCardValue(V)?U:V);return console.log(`[UNIFIED AI] ⚠️ FALLBACK: ${I.rank} di ${I.suit} (minor rischio, evito assi/re non-briscola)`),I}const i=new D,c=e.reduce((u,d)=>i.getCardValue(d)<i.getCardValue(u)?d:u);return console.log(`[UNIFIED AI] 📉 Carta di minor valore: ${c.rank} di ${c.suit}`),c};class xe{constructor(){j(this,"playedCards",[]);j(this,"gameRound",0);this.playedCards=[]}startNewRound(){this.playedCards=[],this.gameRound++}recordPlayedCard(r,n){this.playedCards.push(r)}getPlayedCards(){return[...this.playedCards]}isCardPlayed(r){return this.playedCards.some(n=>n.suit===r.suit&&n.rank===r.rank)}updateMemory(r){}reset(){this.playedCards=[],this.gameRound=0}}const Ze=new xe,ze=(e,r,n)=>{var c,g;const o=((g=(c=e.players)==null?void 0:c[r])==null?void 0:g.team)||"UNKNOWN",t=`CPU${r} (Team ${o}) [${n}]`;if(console.log(`
🤖 ===== ${t} STA PENSANDO... =====`),!e||!e.players||!e.players[r]||!e.players[r].hand)return console.log(`❌ ${t} - ERRORE: dati giocatore non validi`),null;let a=[...e.players[r].hand];if(!e.currentTrick||e.currentTrick.length===0){if((e.trickNumber??1)===1&&e.trumpSuit&&e.lastTrumpSelector===r){const d=a.some(C=>C.suit===e.trumpSuit&&C.rank==="A"),m=a.some(C=>C.suit===e.trumpSuit&&C.rank==="2"),f=a.some(C=>C.suit===e.trumpSuit&&C.rank==="3");if(d&&m&&f){const C=a.find(T=>T.suit===e.trumpSuit&&T.rank==="A");if(C)return console.log(`🎯🎯🎯 ${t} - MARAFFA OBBLIGATORIA! Gioco Asso di ${C.suit} per 3 punti bonus!`),C}}console.log(`🎯 ${t} - PRIMO DEL TURNO - CONTROLLO 3 NON-BRISCOLA!`);const u=a.filter(d=>d.rank==="3"&&d.suit!==e.trumpSuit);if(u.length>0){const d=u[0];return console.log(`🎯 ${t} - STRATEGIA APERTURA: GIOCO 3 di ${d.suit}!`),d}}if(e.leadSuit&&e.currentTrick&&e.currentTrick.length>0){const l=a.filter(u=>u.suit===e.leadSuit);l.length>0&&(a=l,console.log(`🎴 ${t} - Devo seguire il seme: ${e.leadSuit} (${l.length} carte)`))}if(a.length===0)return console.log(`❌ ${t} - ERRORE: nessuna carta disponibile`),null;if(e.currentTrick&&e.currentTrick.length>0&&e.currentTrick.some(u=>u&&u.rank==="A")){console.log(`🔥 ${t} - ASSO SUL TAVOLO! Cerco di prenderlo...`);const u=new D,d=a.filter(m=>{const f=[...e.currentTrick,m];return u.canWinCurrentTrick(m,f,e.leadSuit,e.trumpSuit)});if(d.length>0){const m=d[0];return console.log(`🔥 ${t} - PRENDO ASSO CON ${m.rank} di ${m.suit}!`),m}}try{Ze.updateMemory(e)}catch(l){console.log(`⚠️ ${t} - Errore aggiornamento memoria:`,l)}console.log(`🎯 ${t} - STRATEGIA UNIFICATA [${n}]`);const i=Ge(a,e,r,n);return console.log(`✅ ${t} - DECISIONE FINALE: ${(i==null?void 0:i.rank)||"NULL"} di ${(i==null?void 0:i.suit)||"NULL"}`),i},He=(e,r,n)=>{if(r.currentTrick.length>0||r.trickNumber===1||r.trickNumber>5)return null;const o=n.suit,t=e.hand.filter(l=>l.suit===o),s=r.trumpSuit?e.hand.filter(l=>l.suit===r.trumpSuit):[];if(t.length===1&&s.length>0)return"volo";const a=t.some(l=>l.rank===y.Two),i=t.some(l=>l.rank===y.Three),c=t.some(l=>l.rank===y.Ace);return a&&n.suit===o&&n.rank!==y.Two&&!i&&!c&&t.length>=2?(console.log(`[BUSSO] 🎯 Gioco ${n.rank} di ${n.suit} - ho il 2 dello stesso seme!`),"busso"):t.length===2?"striscio":null},Ke=(e,r,n,o)=>{const t=He(e,r,n);if(!t)return null;const s={easy:.4,medium:.7,hard:.9};if(Math.random()>s[o])return null;switch(t){case"busso":return _e(e,r,n)?"busso":null;case"volo":return Math.random()<.8?"volo":null;case"striscio":return Math.random()<.6?"striscio":null;default:return null}},_e=(e,r,n)=>{if(!e.hand.filter(c=>c.suit===n.suit).some(c=>c.rank===y.Two)||r.trickNumber>5)return!1;const s=r.players.findIndex(c=>c.team===e.team&&c.id!==e.id);if(s===-1)return!1;const a=r.currentPlayer;return(s-a+4)%4<=2},Je=(e,r)=>{if(console.log(`[STRATEGIC ANNOUNCEMENTS] 📢 ${r.players[e.playerIndex].name} dichiara: ${e.type.toUpperCase()}`),typeof window<"u"&&window.aiMemory){const n=window.aiMemory;n.strategicAnnouncements||(n.strategicAnnouncements=[]),n.strategicAnnouncements.push({...e,timestamp:Date.now()});const o=r.trickNumber;n.strategicAnnouncements=n.strategicAnnouncements.filter(t=>o-t.trickNumber<=3)}},qe=(e,r)=>{if(typeof window>"u"||!window.aiMemory)return[];const n=window.aiMemory;if(!n.strategicAnnouncements)return[];const o=e.trickNumber;return n.strategicAnnouncements.filter(t=>{if(o-t.trickNumber>2)return!1;const s=e.players[t.playerIndex],a=e.players[r];return s.team===a.team?!0:t.type==="volo"})},je=(e,r,n)=>{const o=qe(r,n),t=r.players[n],s=o.filter(i=>r.players[i.playerIndex].team===t.team&&i.playerIndex!==n);if(s.length===0)return{recommendedCards:e,strategy:"Nessuna dichiarazione del compagno"};const a=s[s.length-1];switch(a.type){case"busso":return Ye(e,r,a);case"striscio":return Qe(e,r,a);case"volo":return Xe(e,r,a);default:return{recommendedCards:e,strategy:"Dichiarazione non riconosciuta"}}},Ye=(e,r,n)=>{const o=n.suit,t=e.filter(i=>i.suit===o);if(t.length>0){const c=t.sort((g,l)=>{const u=d=>({3:10,2:9,A:8,K:7,H:6,J:5,7:4,6:3,5:2,4:1})[d.rank]||0;return u(l)-u(g)})[0];return{recommendedCards:[c],strategy:`BUSSO: Prendo con ${c.rank} di ${o} (carta più forte del seme)`}}const s=e.filter(i=>i.suit===r.trumpSuit);if(s.length>0){const i=s.sort((c,g)=>{const l=u=>({3:10,2:9,A:8,K:7,H:6,J:5,7:4,6:3,5:2,4:1})[u.rank]||0;return l(c)-l(g)});return{recommendedCards:[i[0]],strategy:`BUSSO: Prendo con briscola ${i[0].rank} (non ho carte del seme ${o})`}}const a=e.filter(i=>i.suit===r.trumpSuit?!0:i.suit===o?i.rank===y.Three||i.rank===y.Two||i.rank===y.Ace:!1);return a.length>0?{recommendedCards:a,strategy:`BUSSO: Prendo la mano per collaborare con il compagno (seme: ${o})`}:{recommendedCards:e,strategy:"BUSSO: Non posso prendere la mano"}},Qe=(e,r,n)=>({recommendedCards:e,strategy:`STRISCIO: Il compagno ha ancora carte in ${n.suit}`}),Xe=(e,r,n)=>{const o=n.suit,t=e.filter(s=>s.suit===o);if(t.length>0&&r.leadSuit===o){const s=t.filter(a=>a.rank===y.Ace||a.rank===y.King||a.rank===y.Horse||a.rank===y.Jack);if(s.length>0)return{recommendedCards:s,strategy:`VOLO: Do punti al compagno che tagliarà (seme: ${o})`}}return{recommendedCards:e,strategy:`VOLO: Il compagno può tagliare ${o}`}},er=(e,r,n,o)=>{if(e.currentTrick.length>0)return{shouldAnnounce:!1,announcement:null,reason:"Non è il primo del turno"};const t=e.players[r],s=Ke(t,e,n,o);return s?(console.log(`[AI STRATEGIC] Player ${r} (${t.name}) considera dichiarazione: ${s}`),{shouldAnnounce:!0,announcement:s,reason:`Dichiarazione strategica: ${s} (${n.rank} di ${n.suit})`}):{shouldAnnounce:!1,announcement:null,reason:"Nessuna dichiarazione strategica appropriata"}},rr=(e,r,n)=>{const o=je(e,r,n);let t=.5;return o.strategy.includes("BUSSO")?t=.9:o.strategy.includes("VOLO")?t=.8:o.strategy.includes("STRISCIO")&&(t=.6),console.log(`[AI ANNOUNCEMENT STRATEGY] ${o.strategy} (Confidenza: ${(t*100).toFixed(0)}%)`),{filteredCards:o.recommendedCards,strategy:o.strategy,confidence:t}},nr=(e,r,n,o)=>{if(!n)return;const t={type:n,playerIndex:r,suit:o,trickNumber:e.trickNumber};Je(t,e);const s=e.players[r];console.log(`[AI MEMORY UPDATE] Processed announcement: ${s.name} declares ${n.toUpperCase()} on ${o}`)},tr=(e,r,n,o)=>{if((!r.currentTrick||r.currentTrick.length===0)&&e.filter(i=>i.rank==="3"&&i.suit!==r.trumpSuit).length>0)return console.log("[STRATEGIC HANDLER] 🎯🎯🎯 PRIMO DEL TURNO CON 3 NON-BRISCOLA - BLOCCO FILTRI! 🎯🎯🎯"),{cards:e,strategicInfo:"PRIORITÀ ASSOLUTA: 3 non-briscola disponibile - no filtri strategici",useAnnouncement:!1};const s=rr(e,r,n);return s.confidence>=.7?{cards:s.filteredCards,strategicInfo:`Strategia basata su dichiarazione: ${s.strategy}`,useAnnouncement:!0}:{cards:e,strategicInfo:"Strategia normale - no dichiarazioni influenti",useAnnouncement:!1}},lr=(e,r,n,o,t,s)=>{const a=e.players[e.currentPlayer];if(!a||!a.hand){console.error("Errore: giocatore corrente o mano non definiti");return}const i=he(a.hand,e.leadSuit);i.length>0&&c(e);function c(l){var C;const u=r==="easy"?G.EASY:r==="medium"?G.MEDIUM:G.HARD,d=tr(i,l,l.currentPlayer);console.log(`[AI STRATEGIC HANDLER] ${d.strategicInfo}`);const m=[...a.hand];if(d.useAnnouncement){const T=d.cards;a.hand=a.hand.filter(k=>T.some(P=>P.id===k.id)),console.log(`[AI STRATEGIC] Filtering hand from ${m.length} to ${a.hand.length} cards`)}const f=ze(l,l.currentPlayer,u);if(a.hand=m,f){if(l.currentTrick.length===0){const T=er(l,l.currentPlayer,f,r);if(T.shouldAnnounce){console.log(`[AI ANNOUNCEMENT] ${a.name}: ${(C=T.announcement)==null?void 0:C.toUpperCase()}`),console.log(`[AI ANNOUNCEMENT] Ragione: ${T.reason}`);const k=de(l,T.announcement);s&&(s({type:T.announcement,playerIndex:l.currentPlayer}),setTimeout(()=>{s({type:null,playerIndex:-1})},2500)),nr(k,l.currentPlayer,T.announcement,f.suit),setTimeout(()=>{g(k,f)},1e3);return}}g(l,f)}else if(console.error("L'AI non è riuscita a scegliere una carta valida in modalità",r),i.length>0){Y.playSound("cardSnap",{playerId:l.currentPlayer});const T=X(l,i[0].id);o(i[0]),n(T)}}function g(l,u){Y.playSound("cardPlay",{playerId:l.currentPlayer});const d=X(l,u.id);o(u),d.currentTrick.length===0&&l.currentTrick.length===3?(setTimeout(()=>Y.playSound("cardGather"),500),t(!0),setTimeout(()=>{t(!1),o(null),n(d)},2800)):n(d)}};export{y as R,b as S,de as a,ke as b,Re as c,ye as d,fe as e,sr as f,he as g,lr as h,Te as i,ar as j,le as k,ur as l,X as p,q as s};

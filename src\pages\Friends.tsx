import { useState, useEffect, useCallback, useMemo, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { useHardwareBackButton } from "@/hooks/useHardwareBackButton";
import { useAuth } from "@/context/auth-context";
import { useCustomToast } from "@/hooks/useCustomToast";
import { Users, Trophy } from "lucide-react";
import { useAudio } from "@/hooks/useAudio";
import { Capacitor } from "@capacitor/core";
import { AdMob } from "@capacitor-community/admob";
import PageContainer from "@/components/layout/PageContainer";
import FriendsTab from "@/components/friends/FriendsTab";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Leaderboard from "@/components/friends/Leaderboard";

interface FriendData {
  id: string;
  username: string;
  level: number;
  xp: number;
  online: boolean;
  avatar_url?: string;
  lastSeen?: string;
  games_played?: number;
  games_won?: number;
  win_rate?: number;
  created_at?: string;
  updated_at?: string;
}

interface FriendRequest {
  id: string;
  from_user_id: string;
  to_user_id: string;
  created_at: string;
  from_user: {
    username: string;
    avatar_url?: string;
    level?: number;
  };
}

const Friends = () => {
  const navigate = useNavigate();
  const { playSound } = useAudio();
  const {
    user: authUser,
    isLoggedIn,
    isLoading: authLoading,
    getSupabaseClient,
    getFriends,
    getFriendRequests,
    getSentFriendRequests,
    acceptFriendRequest,
    rejectFriendRequest,
    sendFriendRequest,
    removeFriend,
    invalidateFriendsCache,
    getCachedFriends,
    getCachedFriendRequests,
    getCachedSentFriendRequests,
  } = useAuth();
  const { showToast } = useCustomToast();

  // Stati locali
  const [friends, setFriends] = useState<FriendData[]>([]);
  const [friendRequests, setFriendRequests] = useState<FriendRequest[]>([]);
  const [sentRequests, setSentRequests] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false);
  const [activeTab, setActiveTab] = useState<"friends" | "leaderboard">(
    "friends"
  );

  // useRef per evitare re-render e loop infiniti
  const loadingRef = useRef(false);
  const dataLoadedRef = useRef(false);
  const lastLoadedUserIdRef = useRef<string | null>(null);

  // Amici memorizzati con useMemo
  const memoizedFriends = useMemo(() => {
    return [...friends].sort((a, b) => {
      if (a.online !== b.online) {
        return a.online ? -1 : 1;
      }
      return a.username.localeCompare(b.username);
    });
  }, [friends]);

  // Richieste memorizzate con useMemo
  const memoizedFriendRequests = useMemo(() => {
    return [...friendRequests].sort((a, b) => {
      return (
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
    });
  }, [friendRequests]);

  // 🎯 FUNZIONE CHIAVE: Popola gli stati dalla cache diretta o fa fetch iniziale
  const populateFromCache = useCallback(async () => {
    console.log("🔍 Accesso diretto alla cache auth-context...");

    const cachedFriendsResult = getCachedFriends();
    const cachedRequestsResult = getCachedFriendRequests();
    const cachedSentRequestsResult = getCachedSentFriendRequests();

    console.log(
      `📋 Cache amici: ${
        cachedFriendsResult.isValid ? "valida" : "non valida"
      }, dati: ${cachedFriendsResult.data?.length || 0}`
    );
    console.log(
      `📋 Cache richieste ricevute: ${
        cachedRequestsResult.isValid ? "valida" : "non valida"
      }, dati: ${cachedRequestsResult.data?.length || 0}`
    );
    console.log(
      `📋 Cache richieste inviate: ${
        cachedSentRequestsResult.isValid ? "valida" : "non valida"
      }, dati: ${cachedSentRequestsResult.data?.length || 0}`
    );

    // Se almeno una delle cache è valida, usa i dati dalla cache
    if (
      cachedFriendsResult.isValid ||
      cachedRequestsResult.isValid ||
      cachedSentRequestsResult.isValid
    ) {
      const formattedFriends: FriendData[] = (
        cachedFriendsResult.data || []
      ).map((friendship) => ({
        id: friendship.friend.id,
        username: friendship.friend.username,
        level: friendship.friend.level || 1,
        xp: friendship.friend.xp || 0,
        online: false,
        avatar_url: friendship.friend.avatar_url,
        games_played: friendship.friend.games_played || 0,
        games_won: friendship.friend.games_won || 0,
        win_rate: friendship.friend.win_rate || 0,
        created_at: friendship.friend.created_at,
        updated_at: friendship.friend.updated_at,
      }));

      const formattedRequests: FriendRequest[] = (
        cachedRequestsResult.data || []
      ).map((request) => ({
        id: request.id,
        from_user_id: request.sender_id,
        to_user_id: request.receiver_id,
        created_at: request.created_at,
        from_user: {
          username: request.sender.username || "Unknown",
          avatar_url: request.sender.avatar_url,
          level: 1,
        },
      }));

      // Estrai gli ID delle richieste inviate dalla cache
      const sentRequestIds = (cachedSentRequestsResult.data || []).map(
        (request) => request.receiver_id
      );

      setFriends(formattedFriends);
      setFriendRequests(formattedRequests);
      setSentRequests(sentRequestIds);

      console.log(
        `✅ Stati popolati dalla cache: ${formattedFriends.length} amici, ${formattedRequests.length} richieste ricevute, ${sentRequestIds.length} richieste inviate`
      );
      return true;
    }

    // 🚀 CACHE VUOTA: Fai fetch iniziale dal server
    console.log("📡 Cache vuota - eseguo fetch iniziale dal server...");

    if (loadingRef.current) {
      console.log("⏳ Fetch già in corso, skip");
      return false;
    }

    try {
      setLoading(true);
      loadingRef.current = true;

      // Fetch iniziale dal server
      const friendsData = await getFriends(false); // Non forza, ma se cache vuota farà fetch
      const requestsData = await getFriendRequests();
      const sentRequestsData = await getSentFriendRequests();

      const formattedFriends: FriendData[] = friendsData.map((friendship) => ({
        id: friendship.friend.id,
        username: friendship.friend.username,
        level: friendship.friend.level || 1,
        xp: friendship.friend.xp || 0,
        online: false,
        avatar_url: friendship.friend.avatar_url,
        games_won: friendship.friend.games_won || 0,
        games_played: friendship.friend.games_played || 0,
        win_rate: friendship.friend.win_rate || 0,
        created_at: friendship.friend.created_at,
        updated_at: friendship.friend.updated_at,
      }));

      const formattedRequests: FriendRequest[] = requestsData.map(
        (request) => ({
          id: request.id,
          from_user_id: request.sender_id,
          to_user_id: request.receiver_id,
          created_at: request.created_at,
          from_user: {
            username: request.sender.username || "Unknown",
            avatar_url: request.sender.avatar_url,
            level: 1,
          },
        })
      );

      // Estrai gli ID delle richieste inviate
      const sentRequestIds = sentRequestsData.map(
        (request) => request.receiver_id
      );

      setFriends(formattedFriends);
      setFriendRequests(formattedRequests);
      setSentRequests(sentRequestIds);

      console.log(
        `✅ Fetch iniziale completato: ${formattedFriends.length} amici, ${formattedRequests.length} richieste ricevute, ${sentRequestIds.length} richieste inviate`
      );
      return true;
    } catch (error) {
      console.error("❌ Errore fetch iniziale:", error);
      return false;
    } finally {
      setLoading(false);
      loadingRef.current = false;
    }
  }, [
    getCachedFriends,
    getCachedFriendRequests,
    getCachedSentFriendRequests,
    getFriends,
    getFriendRequests,
    getSentFriendRequests,
  ]);

  // Effetto principale: gestione del caricamento dati
  useEffect(() => {
    // Se non autenticato, resetta tutto
    if (!isLoggedIn || !authUser?.id) {
      setFriends([]);
      setFriendRequests([]);
      setSentRequests([]);
      setDataLoaded(false);
      dataLoadedRef.current = false;
      lastLoadedUserIdRef.current = null;
      setLoading(false);
      loadingRef.current = false;
      return;
    }

    // Se stiamo ancora caricando l'auth, attendi
    if (authLoading) {
      return;
    }

    // Se stiamo già caricando, skip
    if (loadingRef.current) {
      console.log("⏳ Caricamento già in corso, skip");
      return;
    }

    console.log(`🔍 Controllo dati per utente ${authUser.id}...`);

    // 🎯 CHIAVE: Usa cache diretta o fa fetch iniziale se cache vuota
    const handleDataLoad = async () => {
      try {
        await populateFromCache();

        // Segna come caricato per questo utente
        setDataLoaded(true);
        dataLoadedRef.current = true;
        lastLoadedUserIdRef.current = authUser.id;

        console.log("✅ Inizializzazione completata");
      } catch (error) {
        console.error("❌ Errore inizializzazione:", error);
        // Anche in caso di errore, segna come caricato per evitare loop infiniti
        setDataLoaded(true);
        dataLoadedRef.current = true;
        lastLoadedUserIdRef.current = authUser.id;
      }
    };

    handleDataLoad();
  }, [isLoggedIn, authUser?.id, authLoading, populateFromCache]);

  // Funzione per il refresh manuale - UNICA funzione che fa fetch dal server
  const handleRefreshFriends = useCallback(async () => {
    if (!authUser?.id) return;

    if (loadingRef.current) {
      console.warn("⚠️ Refresh già in corso, skip");
      return;
    }

    console.log("🔄 Refresh manuale - FORCE REFRESH dal server");

    setLoading(true);
    loadingRef.current = true;

    const refreshTimeout = setTimeout(() => {
      console.error("🚨 Timeout refresh");
      setLoading(false);
      loadingRef.current = false;
      showToast("Ricaricamento troppo lento, riprova", "error");
    }, 20000); // Aumentato a 20 secondi per dare più tempo alle query ottimizzate

    try {
      // Invalida la cache prima del refresh
      invalidateFriendsCache();

      // Force refresh dal server
      const forceFriends = await getFriends(true);
      const forceRequests = await getFriendRequests();
      const forceSentRequests = await getSentFriendRequests();

      // Trasforma i dati
      const formattedFriends: FriendData[] = forceFriends.map((friendship) => ({
        id: friendship.friend.id,
        username: friendship.friend.username,
        level: friendship.friend.level || 1,
        xp: 0,
        online: false,
        avatar_url: friendship.friend.avatar_url,
      }));

      const formattedRequests: FriendRequest[] = forceRequests.map(
        (request) => ({
          id: request.id,
          from_user_id: request.sender_id,
          to_user_id: request.receiver_id,
          created_at: request.created_at,
          from_user: {
            username: request.sender.username || "Unknown",
            avatar_url: request.sender.avatar_url,
            level: 1,
          },
        })
      );

      // Estrai gli ID delle richieste inviate
      const sentRequestIds = forceSentRequests.map(
        (request) => request.receiver_id
      );

      // Aggiorna gli stati
      setFriends(formattedFriends);
      setFriendRequests(formattedRequests);
      setSentRequests(sentRequestIds);

      console.log(
        `✅ Refresh completato: ${formattedFriends.length} amici, ${formattedRequests.length} richieste ricevute, ${sentRequestIds.length} richieste inviate`
      );
      clearTimeout(refreshTimeout);
    } catch (error) {
      console.error("❌ Errore refresh:", error);
      clearTimeout(refreshTimeout);
      showToast("Errore durante l'aggiornamento degli amici", "error");
    } finally {
      clearTimeout(refreshTimeout);
      setLoading(false);
      loadingRef.current = false;
    }
  }, [
    authUser?.id,
    invalidateFriendsCache,
    getFriends,
    getFriendRequests,
    getSentFriendRequests,
    showToast,
  ]);

  // Gestione richieste di amicizia - ORA USA AUTH-CONTEXT
  const handleFriendRequest = useCallback(
    async (requestId: string, action: "accept" | "decline") => {
      if (!authUser) return;

      try {
        const requestToHandle = memoizedFriendRequests.find(
          (r) => r.id === requestId
        );
        if (!requestToHandle) {
          console.error("Richiesta non trovata:", requestId);
          showToast("Richiesta non trovata", "error");
          return;
        }

        console.log(
          `${
            action === "accept" ? "✅ Accettazione" : "❌ Rifiuto"
          } richiesta da ${requestToHandle.from_user_id}`
        );

        let success = false;
        if (action === "accept") {
          success = await acceptFriendRequest(requestId);
          if (success) {
            // Se accettata con successo, aggiungi il nuovo amico alla lista locale
            const newFriend: FriendData = {
              id: requestToHandle.from_user_id,
              username: requestToHandle.from_user.username,
              level: requestToHandle.from_user.level || 1,
              xp: 0,
              online: false,
              avatar_url: requestToHandle.from_user.avatar_url,
            };
            setFriends((prev) => [...prev, newFriend]);
            showToast("Nuovo amico aggiunto!", "success");
          } else {
            showToast("Errore nell'accettare la richiesta", "error");
            return;
          }
        } else if (action === "decline") {
          success = await rejectFriendRequest(requestId);
          if (success) {
            showToast("Richiesta rifiutata", "success");
          } else {
            showToast("Errore nel rifiutare la richiesta", "error");
            return;
          }
        }

        // Se l'operazione è andata a buon fine, rimuovi la richiesta dalla lista locale
        if (success) {
          setFriendRequests((prev) => prev.filter((r) => r.id !== requestId));
        }
      } catch (error) {
        console.error("Errore gestione richiesta:", error);
        showToast("Impossibile gestire la richiesta", "error");
      }
    },
    [
      authUser,
      memoizedFriendRequests,
      acceptFriendRequest,
      rejectFriendRequest,
      showToast,
    ]
  );

  // Funzioni per gestire amici dalla leaderboard - ORA USA AUTH-CONTEXT
  const handleAddFriendFromLeaderboard = useCallback(
    async (playerId: string) => {
      if (!authUser) {
        console.error("❌ AuthUser non disponibile");
        return;
      }

      console.log(`👥 Tentativo invio richiesta amicizia a ${playerId}`);
      console.log(`📋 Stato sessione:`, {
        userId: authUser.id,
        isLoggedIn,
        authLoading,
      });

      try {
        console.log(`✅ Utilizzo sendFriendRequest dell'auth-context`);

        const success = await sendFriendRequest(playerId);

        if (success) {
          console.log(`✅ Richiesta inviata nel DB, aggiorno sentRequests`);

          setSentRequests((prev) => {
            const newArray = [...prev, playerId];
            console.log(`📋 sentRequests aggiornato:`, { prev, newArray });
            return newArray;
          });

          showToast("Richiesta di amicizia inviata!", "success");
          playSound("success");

          console.log(`🎉 Richiesta amicizia completata con successo`);
        } else {
          // Il sendFriendRequest restituisce false se sono già amici o c'è già una richiesta
          console.log(
            `⚠️ Richiesta non inviata - potrebbero essere già amici o richiesta esistente`
          );
          showToast(
            "Richiesta non inviata - potrebbero essere già amici o esiste già una richiesta",
            "info"
          );
        }
      } catch (error) {
        console.error("❌ Errore completo aggiunta amico:", error);

        // Log dettagliato dell'errore
        if (error instanceof Error) {
          console.error("❌ Messaggio errore:", error.message);
          console.error("❌ Stack errore:", error.stack);
        }

        showToast("Errore nell'inviare la richiesta di amicizia", "error");
        playSound("error");
      }
    },
    [authUser, sendFriendRequest, showToast, playSound, isLoggedIn, authLoading]
  );

  const handleRemoveFriendFromLeaderboard = useCallback(
    async (playerId: string) => {
      if (!authUser) {
        console.error("❌ AuthUser non disponibile per rimozione");
        return;
      }

      console.log(
        `🗑️ Tentativo rimozione amicizia tra ${authUser.id} e ${playerId}`
      );

      try {
        console.log(`✅ Utilizzo removeFriend dell'auth-context`);

        const success = await removeFriend(playerId);

        if (success) {
          console.log(`✅ Amicizia rimossa con successo per ${playerId}`);

          // Aggiorna lo stato locale
          setFriends((prev) => {
            const newFriends = prev.filter((friend) => friend.id !== playerId);
            console.log(
              `📋 Lista amici aggiornata: ${prev.length} -> ${newFriends.length}`
            );
            return newFriends;
          });

          showToast("Amico rimosso dalla lista", "success");
          playSound("success");
        } else {
          console.warn(
            "⚠️ Rimozione non riuscita - possibile problema policy RLS o relazione inesistente"
          );
          showToast("Errore nella rimozione dell'amico", "error");
          playSound("error");
        }
      } catch (error) {
        console.error("❌ Errore completo rimozione amico:", error);

        // Log dettagliato dell'errore
        if (error instanceof Error) {
          console.error("❌ Messaggio errore:", error.message);
          console.error("❌ Stack errore:", error.stack);
        }

        showToast("Errore nella rimozione dell'amico", "error");
        playSound("error");
      }
    },
    [authUser, removeFriend, showToast, playSound]
  );

  const hasPendingRequestFor = useCallback(
    (playerId: string) => {
      // Controlla se abbiamo già inviato una richiesta a questo giocatore
      const hasSentRequest = sentRequests.includes(playerId);

      console.log(`🔍 hasPendingRequestFor(${playerId}):`, {
        hasSentRequest,
        sentRequestsArray: sentRequests,
      });

      return hasSentRequest;
    },
    [sentRequests]
  );

  // Nuova funzione per controllare se abbiamo ricevuto una richiesta da un giocatore
  const hasReceivedRequestFrom = useCallback(
    (playerId: string) => {
      const hasReceivedRequest = memoizedFriendRequests.some(
        (request) => request.from_user_id === playerId
      );

      console.log(`🔍 hasReceivedRequestFrom(${playerId}):`, {
        hasReceivedRequest,
        friendRequestsArray: memoizedFriendRequests.map((r) => ({
          id: r.id,
          from: r.from_user_id,
        })),
      });

      return hasReceivedRequest;
    },
    [memoizedFriendRequests]
  );

  // Funzioni per accettare/rifiutare richieste direttamente dal profilo
  const handleAcceptFriendRequestFromProfile = useCallback(
    async (playerId: string) => {
      if (!authUser) return;

      console.log(`✅ Accetto richiesta amicizia da ${playerId}`);

      // Trova la richiesta specifica
      const requestToAccept = memoizedFriendRequests.find(
        (r) => r.from_user_id === playerId
      );

      if (!requestToAccept) {
        console.error("Richiesta non trovata per playerId:", playerId);
        showToast("Richiesta non trovata", "error");
        return;
      }

      await handleFriendRequest(requestToAccept.id, "accept");
    },
    [authUser, memoizedFriendRequests, handleFriendRequest, showToast]
  );

  const handleRejectFriendRequestFromProfile = useCallback(
    async (playerId: string) => {
      if (!authUser) return;

      console.log(`❌ Rifiuto richiesta amicizia da ${playerId}`);

      // Trova la richiesta specifica
      const requestToReject = memoizedFriendRequests.find(
        (r) => r.from_user_id === playerId
      );

      if (!requestToReject) {
        console.error("Richiesta non trovata per playerId:", playerId);
        showToast("Richiesta non trovata", "error");
        return;
      }

      await handleFriendRequest(requestToReject.id, "decline");
    },
    [authUser, memoizedFriendRequests, handleFriendRequest, showToast]
  );

  // Hardware back button
  useHardwareBackButton();

  // Loading state
  if (authLoading) {
    return (
      <div className="min-h-screen  flex flex-col items-center justify-center">
        <div className="text-xl text-romagna-darkWood">Caricamento...</div>
      </div>
    );
  }

  return (
    <PageContainer>
      <div className="h-full  flex flex-col overflow-hidden">
        {/* Header */}
        <div
          className="flex-shrink-0 p-4 flex items-center justify-between"
          style={{ paddingTop: `calc(1rem + env(safe-area-inset-top, 0px))` }}
        >
          <div className="flex items-center gap-4">
            <h1
              className="text-2xl font-bold text-romagna-darkWood font-serif"
              style={{
                fontFamily: "'DynaPuff', cursive",
                fontWeight: 600,
              }}
            >
              Amici
            </h1>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex-shrink-0 px-4 mb-4">
          <div className="flex bg-white/50 rounded-lg p-1 border border-amber-200">
            <button
              onClick={() => setActiveTab("friends")}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === "friends"
                  ? "bg-amber-500 text-white shadow-sm"
                  : "text-amber-700 hover:bg-amber-100"
              }`}
            >
              <Users className="h-4 w-4 inline mr-2" />
              Amici{" "}
              {isLoggedIn && memoizedFriendRequests.length > 0 && (
                <span className="ml-1 inline-flex items-center justify-center bg-red-500 text-white text-xs rounded-full h-5 w-5">
                  {memoizedFriendRequests.length}
                </span>
              )}
            </button>
            <button
              onClick={async () => {
                setActiveTab("leaderboard");

                // 🎬 MOSTRA PUBBLICITÀ QUANDO SI CLICCA SU CLASSIFICHE
                if (Capacitor.isNativePlatform()) {
                  try {
                    console.log("🎬 Tentativo pubblicità per classifiche...");

                    // Tentativo 1: Interstitial Ad
                    try {
                      console.log(
                        "🎬 Preparazione interstitial per classifiche: ca-app-pub-3013811216506035/1024211513"
                      );
                      await AdMob.prepareInterstitial({
                        adId: "ca-app-pub-3013811216506035/1024211513",
                        isTesting: false,
                      });

                      console.log(
                        "🎬 Mostrando interstitial per classifiche..."
                      );
                      await AdMob.showInterstitial();
                      console.log(
                        "✅ Pubblicità classifiche mostrata con successo"
                      );
                    } catch (interstitialError) {
                      console.warn(
                        "⚠️ Interstitial classifiche fallito, provo reward video:",
                        interstitialError
                      );

                      // Tentativo 2: Reward Video Ad (senza premio)
                      try {
                        console.log(
                          "🎬 Fallback: Preparazione reward video per classifiche..."
                        );
                        await AdMob.prepareRewardVideoAd({
                          adId: "ca-app-pub-3013811216506035/8135187414",
                          isTesting: false,
                        });

                        console.log(
                          "🎬 Mostrando reward video per classifiche..."
                        );
                        await AdMob.showRewardVideoAd();
                        console.log(
                          "✅ Pubblicità reward video classifiche mostrata con successo (fallback)"
                        );
                      } catch (rewardError) {
                        console.error(
                          "❌ Anche reward video classifiche fallito:",
                          rewardError
                        );
                      }
                    }
                  } catch (error) {
                    console.warn(
                      "⚠️ Errore generale pubblicità classifiche:",
                      error
                    );
                  }
                }
              }}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === "leaderboard"
                  ? "bg-amber-500 text-white shadow-sm"
                  : "text-amber-700 hover:bg-amber-100"
              }`}
            >
              <Trophy className="h-4 w-4 inline mr-2" />
              Classifiche
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 px-4 overflow-hidden">
          <div className="max-w-3xl mx-auto h-full">
            {activeTab === "friends" && (
              <div className="h-full">
                <FriendsTab
                  isLoggedIn={isLoggedIn}
                  authUser={authUser}
                  friends={memoizedFriends}
                  friendRequests={memoizedFriendRequests}
                  loading={loading}
                  onRefreshFriends={handleRefreshFriends}
                  onHandleFriendRequest={handleFriendRequest}
                  onRemoveFriend={handleRemoveFriendFromLeaderboard}
                  onAddFriend={handleAddFriendFromLeaderboard}
                  hasPendingRequestFor={hasPendingRequestFor}
                />
              </div>
            )}

            {activeTab === "leaderboard" && (
              <div className="h-full">
                {isLoggedIn && authUser ? (
                  <Leaderboard
                    authUser={authUser}
                    onAddFriend={handleAddFriendFromLeaderboard}
                    onRemoveFriend={handleRemoveFriendFromLeaderboard}
                    friendsList={memoizedFriends.map((friend) => friend.id)}
                    hasPendingRequestFor={hasPendingRequestFor}
                    hasReceivedRequestFrom={hasReceivedRequestFrom}
                    onAcceptFriendRequest={handleAcceptFriendRequestFromProfile}
                    onRejectFriendRequest={handleRejectFriendRequestFromProfile}
                  />
                ) : (
                  <div className="flex items-center justify-center p-8">
                    <Card className="border-2 border-amber-800/30 shadow-md bg-amber-50/80 backdrop-blur-sm max-w-md">
                      <CardContent className="p-6 text-center">
                        <Users className="h-16 w-16 text-amber-600 mx-auto mb-4" />
                        <h2 className="text-xl font-bold text-romagna-darkWood mb-2">
                          Accesso Richiesto
                        </h2>
                        <p className="text-romagna-darkWood/70 mb-4">
                          Devi essere registrato e autenticato per gestire la
                          tua lista amici.
                        </p>
                        <Button
                          onClick={() => navigate("/account")}
                          className="bg-romagna-rust hover:bg-romagna-terracotta text-white"
                        >
                          Vai al Profilo
                        </Button>
                      </CardContent>
                    </Card>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </PageContainer>
  );
};

export default Friends;

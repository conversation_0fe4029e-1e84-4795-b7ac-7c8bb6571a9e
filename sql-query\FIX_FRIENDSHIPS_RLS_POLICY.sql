-- Script per correggere la policy RLS della tabella friendships
-- <PERSON><PERSON> script risolve il problema del 403 Forbidden sul secondo inserimento
-- quando si accetta una richiesta d'amicizia

-- Rimuovi la policy esistente
DROP POLICY IF EXISTS "Users can insert own friendships" ON friendships;

-- Crea una nuova policy che permette l'inserimento quando l'utente corrente
-- è sia user_id CHE friend_id (per permettere entrambi i record dell'amicizia)
CREATE POLICY "Users can insert own friendships" ON friendships
FOR INSERT WITH CHECK (
  auth.uid() = user_id OR auth.uid() = friend_id
);

-- Verifica che la policy sia stata aggiornata correttamente
SELECT schemaname, tablename, policyname, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'friendships' AND policyname = 'Users can insert own friendships';

-- Test per verificare che entrambi gli inserimenti funzionino
-- (Sostituisci gli UUID con valori reali per testare)
-- 
-- Esempio di test (NON ESEGUIRE SE NON HAI GLI UUID CORRETTI):
-- INSERT INTO friendships (user_id, friend_id) VALUES 
-- ('user1-uuid', 'user2-uuid'),
-- ('user2-uuid', 'user1-uuid');

import React from "react";
import ActionButton from "./ui/ActionButton";
import { useAudio } from "@/hooks/useAudio";

interface TrumpSelectionModalProps {
  isVisible: boolean;
  onSelectTrump: (suit: string) => void;
}

const TrumpSelectionModal: React.FC<TrumpSelectionModalProps> = ({
  isVisible,
  onSelectTrump,
}) => {
  const { playSound } = useAudio();

  const handleTrumpSelect = (suit: string) => {
    onSelectTrump(suit);
  };

  if (!isVisible) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50  pointer-events-auto"
      data-trump-selector
    >
      <div className="bg-gradient-to-br from-green-50 to-amber-50 p-4 rounded-xl shadow-2xl mb-20 max-w-[90vw] border-2 border-amber-200 relative">
        <div className="flex flex-col items-center gap-3">
          <div
            className="text-xl font-bold text-amber-800 text-center flex items-center justify-center gap-2"
            style={{
              fontFamily: "'DynaPuff', cursive",
              fontWeight: 600,
            }}
          >
            <img
              src="/images/icons/play.png"
              alt="Seleziona"
              className="inline-block w-10 h-10 align-middle"
              style={{ marginRight: 6 }}
            />
            Seleziona Briscola
          </div>

          <div className="grid grid-cols-2 gap-4 w-full max-w-xs relative">
            {" "}
            <div className="relative">
              <img
                src="/images/semi/denari.png"
                alt="Denari"
                className="w-10 h-10 object-contain drop-shadow-lg absolute -top-2 -left-2 z-10 transform -rotate-12 cursor-pointer"
                onClick={() => handleTrumpSelect("coins")}
              />
              <ActionButton
                className="text-sm px-3 py-3 bg-gradient-to-r from-yellow-400 to-yellow-600 hover:from-yellow-500 hover:to-yellow-700 text-white rounded-lg shadow-md transform hover:scale-105 transition-all duration-200 flex items-center justify-center border border-yellow-300 w-full"
                onClick={() => handleTrumpSelect("coins")}
              >
                <span className="font-semibold ml-1 text-sm">Denari</span>
              </ActionButton>
            </div>{" "}
            <div className="relative">
              <img
                src="/images/semi/coppe.png"
                alt="Coppe"
                className="w-10 h-10 object-contain drop-shadow-lg absolute -top-2 -left-2 z-10 transform -rotate-12 cursor-pointer"
                onClick={() => handleTrumpSelect("cups")}
              />
              <ActionButton
                className="text-sm px-3 py-3 bg-gradient-to-r from-red-400 to-red-600 hover:from-red-500 hover:to-red-700 text-white rounded-lg shadow-md transform hover:scale-105 transition-all duration-200 flex items-center justify-center border border-red-300 w-full"
                onClick={() => handleTrumpSelect("cups")}
              >
                <span className="font-semibold text-sm">Coppe</span>
              </ActionButton>
            </div>{" "}
            <div className="relative">
              <img
                src="/images/semi/spade.png"
                alt="Spade"
                className="w-10 h-10 object-contain drop-shadow-lg absolute -top-2 -left-2 z-10 transform -rotate-12 cursor-pointer"
                onClick={() => handleTrumpSelect("swords")}
              />
              <ActionButton
                className="text-sm px-3 py-3 bg-gradient-to-r from-blue-400 to-blue-600 hover:from-blue-500 hover:to-blue-700 text-white rounded-lg shadow-md transform hover:scale-105 transition-all duration-200 flex items-center justify-center border border-blue-300 w-full"
                onClick={() => handleTrumpSelect("swords")}
              >
                <span className="font-semibold text-sm">Spade</span>
              </ActionButton>
            </div>{" "}
            <div className="relative">
              <img
                src="/images/semi/bastoni.png"
                alt="Bastoni"
                className="w-10 h-10 object-contain drop-shadow-lg absolute -top-2 -left-2 z-10 transform -rotate-12 cursor-pointer"
                onClick={() => handleTrumpSelect("clubs")}
              />
              <ActionButton
                className="text-sm px-3 py-3 bg-gradient-to-r from-green-500 to-green-700 hover:from-green-600 hover:to-green-800 text-white rounded-lg shadow-md transform hover:scale-105 transition-all duration-200 flex items-center justify-center border border-green-400 w-full"
                onClick={() => handleTrumpSelect("clubs")}
              >
                <span className="font-semibold text-sm">Bastoni</span>
              </ActionButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrumpSelectionModal;

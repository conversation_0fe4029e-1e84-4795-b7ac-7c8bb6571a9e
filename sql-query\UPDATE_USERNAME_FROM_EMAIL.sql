-- Script per aggiornare la logica di generazione username e aggiornare quelli esistenti
-- PARTE 1: Aggiorna la funzione handle_new_user per usare email

-- Aggiorna la funzione per estrarre username dalla email (primi 18 caratteri prima della @)
CREATE OR REPLACE FUNCTION public.handle_new_user()
R<PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, username, avatar_url, created_at, updated_at)
  VALUES (
    new.id,
    -- Estrae i primi 18 caratteri della parte email prima della @ se presente, altrimenti usa il nome o 'Giocatore'
    CASE 
      WHEN new.email IS NOT NULL AND new.email LIKE '%@%' THEN 
        LEFT(SPLIT_PART(new.email, '@', 1), 18)
      ELSE 
        LEFT(COALESCE(new.raw_user_meta_data->>'full_name', new.raw_user_meta_data->>'name', 'Giocatore'), 18)
    END,
    new.raw_user_meta_data->>'avatar_url',
    now(),
    now()
  );
  RETURN new;
END;
$$ language plpgsql security definer;

-- PARTE 2: Aggiorna gli username esistenti per usare email quando disponibile

-- Prima mostra uno preview delle modifiche che verranno applicate
SELECT 
    p.id,
    p.username as current_username,
    au.email,
    CASE 
        WHEN au.email IS NOT NULL AND au.email LIKE '%@%' THEN 
            LEFT(SPLIT_PART(au.email, '@', 1), 18)
        ELSE 
            LEFT(p.username, 18)
    END as new_username,
    LENGTH(p.username) as current_length,
    CASE 
        WHEN au.email IS NOT NULL AND au.email LIKE '%@%' THEN 
            LENGTH(LEFT(SPLIT_PART(au.email, '@', 1), 18))
        ELSE 
            LENGTH(LEFT(p.username, 18))
    END as new_length
FROM profiles p
JOIN auth.users au ON p.id = au.id
WHERE LENGTH(p.username) > 18 
   OR (au.email IS NOT NULL AND au.email LIKE '%@%' AND p.username != LEFT(SPLIT_PART(au.email, '@', 1), 18))
ORDER BY LENGTH(p.username) DESC, p.created_at;

-- Aggiorna tutti gli username per riflettere la nuova logica
UPDATE profiles 
SET 
    username = CASE 
        WHEN au.email IS NOT NULL AND au.email LIKE '%@%' THEN 
            LEFT(SPLIT_PART(au.email, '@', 1), 18)
        ELSE 
            LEFT(profiles.username, 18)
    END,
    updated_at = now()
FROM auth.users au 
WHERE profiles.id = au.id
AND (
    LENGTH(profiles.username) > 18 
    OR (au.email IS NOT NULL AND au.email LIKE '%@%' AND profiles.username != LEFT(SPLIT_PART(au.email, '@', 1), 18))
);

-- PARTE 3: Aggiungi/aggiorna il constraint per limitare la lunghezza

-- Rimuovi constraint esistente se presente
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS username_length_check;

-- Aggiungi nuovo constraint per massimo 18 caratteri
ALTER TABLE profiles 
ADD CONSTRAINT username_length_check 
CHECK (LENGTH(username) <= 18 AND LENGTH(username) >= 2);

-- PARTE 4: Verifica i risultati finali

-- Controlla statistiche finali
SELECT 
    COUNT(*) as total_users,
    MAX(LENGTH(username)) as max_username_length,
    MIN(LENGTH(username)) as min_username_length,
    AVG(LENGTH(username))::numeric(5,2) as avg_username_length
FROM profiles;

-- Mostra alcuni esempi di username aggiornati
SELECT 
    p.id, 
    p.username, 
    LENGTH(p.username) as length,
    au.email,
    p.created_at,
    p.updated_at
FROM profiles p
JOIN auth.users au ON p.id = au.id
ORDER BY LENGTH(p.username) DESC, p.created_at DESC
LIMIT 15;

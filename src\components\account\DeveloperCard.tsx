import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Heart, Mail } from "lucide-react";

const DeveloperCard = () => {
  return (
    <Card
      id="elia-zavatta-section"
      className="border-2 border-amber-800/30 shadow-md bg-amber-50/80 backdrop-blur-sm"
    >
      <CardHeader className="pb-2">
        <CardTitle
          className="text-xl text-amber-900 flex items-center gap-3"
          style={{
            fontFamily: "'DynaPuff', cursive",
            fontWeight: 500,
          }}
        >
          <img
            src="/images/icons/elia-gallo (100x100).png"
            alt="Elia Zavatta"
            className="w-16 h-16 rounded-full shadow-md border-2"
          />
          Elia Zavatta - Sviluppatore
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Messaggio personale */}
        <div className="bg-amber-100/50 rounded-lg p-4 border border-amber-200 ">
          <p className="text-amber-800 text-sm leading-relaxed">
            Ciao! Sono Elia, uno sviluppatore romagnolo. Ho creato questa app da
            solo per{" "}
            <span className="font-semibold text-amber-900">preservare</span> e{" "}
            <span className="font-semibold text-amber-900">condividere</span> il
            gioco del Marafone, una parte fondamentale della{" "}
            <span className="font-semibold text-amber-900">
              cultura romagnola
            </span>
            .<br />
            Il mio obiettivo è portare questa{" "}
            <span className="font-semibold text-amber-900">
              tradizione nel mondo digitale
            </span>
            , rendendola accessibile a tutti!
          </p>
          {/* Griglia compatta delle azioni principali */}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-4">
            {/* Supporto */}
            <Button
              className="p-2 py-2 bg-gradient-to-r from-pink-600 to-red-600 hover:from-pink-700 hover:to-red-700 text-white shadow-md flex items-center justify-center gap-2 min-h-[40px] transition-all duration-300 hover:scale-105 hover:shadow-lg transform"
              onClick={() =>
                window.open("https://www.paypal.me/eliazavatta", "_blank")
              }
            >
              <Heart className="h-5 w-5 transition-transform duration-300 hover:scale-110" />
              <div className="text-center leading-tight">
                <div className="font-semibold text-sm">Dona</div>
                <div className="text-xs opacity-90 mt-0">
                  Supporta il progetto
                </div>
              </div>
            </Button>
            {/* Contattami */}
            <Button
              className="p-2 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-md flex items-center justify-center gap-2 min-h-[40px] transition-all duration-300 hover:scale-105 hover:shadow-lg transform"
              onClick={() =>
                window.open("mailto:<EMAIL>", "_blank")
              }
            >
              <Mail className="h-5 w-5 transition-transform duration-300 hover:scale-110" />
              <div className="text-center leading-tight">
                <div className="font-semibold text-sm">Contattami</div>
                <div className="text-xs opacity-90 mt-0">
                  Suggerimenti & Partnership
                </div>
              </div>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DeveloperCard;

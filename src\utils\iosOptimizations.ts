/**
 * 🍎 iOS Performance Optimizations
 * Handles iOS-specific performance and memory management
 */

import { Capacitor } from "@capacitor/core";

export class IOSOptimizations {
  private static instance: IOSOptimizations;
  private memoryWarningListener: (() => void) | null = null;

  static getInstance(): IOSOptimizations {
    if (!IOSOptimizations.instance) {
      IOSOptimizations.instance = new IOSOptimizations();
    }
    return IOSOptimizations.instance;
  }

  /**
   * Initialize iOS-specific optimizations
   */
  async initialize(): Promise<void> {
    if (Capacitor.getPlatform() !== "ios") {
      return;
    }

    console.log("🍎 Initializing iOS optimizations...");

    // Set up memory warning handling
    this.setupMemoryWarningHandler();

    // Optimize for iOS performance
    this.optimizeForIOS();

    // Set up background/foreground handling
    this.setupAppStateHandling();
  }

  /**
   * Handle iOS memory warnings
   */
  private setupMemoryWarningHandler(): void {
    // Listen for memory warnings (if available)
    if ('onmemorywarning' in window) {
      this.memoryWarningListener = () => {
        console.warn("🍎 iOS Memory warning received - cleaning up...");
        this.handleMemoryWarning();
      };
      window.addEventListener('memorywarning', this.memoryWarningListener);
    }
  }

  /**
   * Clean up resources on memory warning
   */
  private handleMemoryWarning(): void {
    // Clear image caches
    this.clearImageCaches();
    
    // Pause audio
    this.pauseAudio();
    
    // Clear animation caches
    this.clearAnimationCaches();
    
    // Force garbage collection (if available)
    if ('gc' in window && typeof (window as any).gc === 'function') {
      (window as any).gc();
    }
  }

  /**
   * iOS-specific performance optimizations
   */
  private optimizeForIOS(): void {
    // Disable hover effects on iOS (they can cause issues)
    document.documentElement.classList.add('ios-device');
    
    // Optimize touch handling
    document.addEventListener('touchstart', () => {}, { passive: true });
    document.addEventListener('touchmove', () => {}, { passive: true });
    
    // Prevent zoom on input focus
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      viewport.setAttribute('content', 
        'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
      );
    }
  }

  /**
   * Handle app background/foreground states
   */
  private setupAppStateHandling(): void {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // App went to background
        this.handleAppBackground();
      } else {
        // App came to foreground
        this.handleAppForeground();
      }
    });
  }

  /**
   * Handle app going to background
   */
  private handleAppBackground(): void {
    console.log("🍎 App going to background - pausing resources...");
    
    // Pause audio
    this.pauseAudio();
    
    // Pause animations
    this.pauseAnimations();
    
    // Clear unnecessary caches
    this.clearTemporaryCaches();
  }

  /**
   * Handle app coming to foreground
   */
  private handleAppForeground(): void {
    console.log("🍎 App coming to foreground - resuming resources...");
    
    // Resume audio if it was playing
    this.resumeAudio();
    
    // Resume animations
    this.resumeAnimations();
  }

  /**
   * Clear image caches to free memory
   */
  private clearImageCaches(): void {
    // Clear any cached images that aren't currently visible
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      if (!this.isElementVisible(img)) {
        img.src = '';
      }
    });
  }

  /**
   * Pause audio playback
   */
  private pauseAudio(): void {
    // This will be handled by AudioManager
    try {
      const audioElements = document.querySelectorAll('audio');
      audioElements.forEach(audio => {
        if (!audio.paused) {
          audio.pause();
        }
      });
    } catch (error) {
      console.warn("Error pausing audio:", error);
    }
  }

  /**
   * Resume audio playback
   */
  private resumeAudio(): void {
    // This will be handled by AudioManager
    // Only resume if user was previously playing audio
  }

  /**
   * Pause animations to save battery
   */
  private pauseAnimations(): void {
    document.documentElement.style.setProperty('--animation-play-state', 'paused');
  }

  /**
   * Resume animations
   */
  private resumeAnimations(): void {
    document.documentElement.style.setProperty('--animation-play-state', 'running');
  }

  /**
   * Clear animation caches
   */
  private clearAnimationCaches(): void {
    // Clear any cached animation data
    const animatedElements = document.querySelectorAll('[data-framer-motion]');
    animatedElements.forEach(element => {
      // Reset transform if not visible
      if (!this.isElementVisible(element as HTMLElement)) {
        (element as HTMLElement).style.transform = '';
      }
    });
  }

  /**
   * Clear temporary caches
   */
  private clearTemporaryCaches(): void {
    // Clear any temporary data that can be regenerated
    try {
      // Clear session storage of non-essential data
      const keysToKeep = ['gameState', 'userPreferences', 'authToken'];
      Object.keys(sessionStorage).forEach(key => {
        if (!keysToKeep.includes(key)) {
          sessionStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.warn("Error clearing temporary caches:", error);
    }
  }

  /**
   * Check if element is visible in viewport
   */
  private isElementVisible(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  }

  /**
   * Clean up listeners
   */
  cleanup(): void {
    if (this.memoryWarningListener) {
      window.removeEventListener('memorywarning', this.memoryWarningListener);
      this.memoryWarningListener = null;
    }
  }
}

export default IOSOptimizations.getInstance();

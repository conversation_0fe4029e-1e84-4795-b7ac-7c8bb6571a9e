import React, { useState, useEffect } from "react";
import { UserPlus, Search, MessageCircle, Trophy, Users } from "lucide-react";
import { useAuth } from "@/context/auth-context";
import { useToast } from "@/hooks/use-toast";

interface Friend {
  id: string;
  username: string;
  avatar?: string;
  isOnline: boolean;
  lastSeen: string;
  gamesPlayed: number;
  winRate: number;
}

const FriendsContent: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [friends, setFriends] = useState<Friend[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  useEffect(() => {
    // Mock data per ora - sostituire con veri dati dal backend
    const mockFriends: Friend[] = [
      {
        id: "1",
        username: "GiocatorePro",
        isOnline: true,
        lastSeen: "<PERSON><PERSON>",
        gamesPlayed: 156,
        winRate: 78.5,
      },
      {
        id: "2",
        username: "<PERSON>ffaL<PERSON>",
        isOnline: false,
        lastSeen: "2 ore fa",
        gamesPlayed: 89,
        winRate: 65.2,
      },
      {
        id: "3",
        username: "RomagnaBoss",
        isOnline: true,
        lastSeen: "Ora",
        gamesPlayed: 234,
        winRate: 82.1,
      },
    ];

    setFriends(mockFriends);
  }, []);

  const handleInviteFriend = (friendId: string) => {
    toast({
      title: "Invito inviato!",
      description: "Il tuo amico riceverà una notifica per giocare.",
    });
  };

  const handleSendMessage = (friendId: string) => {
    toast({
      title: "Chat non disponibile",
      description: "Funzionalità in arrivo prossimamente.",
    });
  };

  const filteredFriends = friends.filter((friend) =>
    friend.username.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Search Header */}
      <div className="bg-white p-4 border-b border-gray-200">
        <div className="relative">
          <Search
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            size={20}
          />
          <input
            type="text"
            placeholder="Cerca amici..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          />
        </div>
      </div>

      {/* Add Friend Button */}
      <div className="p-4">
        <button className="w-full flex items-center justify-center space-x-2 bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors">
          <UserPlus size={20} />
          <span className="font-medium">Aggiungi Amico</span>
        </button>
      </div>

      {/* Friends List */}
      <div className="flex-1 overflow-y-auto px-4 pb-4">
        {filteredFriends.length === 0 ? (
          <div className="text-center py-12">
            <Users className="mx-auto mb-4 text-gray-400" size={48} />
            <h3 className="text-lg font-medium text-gray-600 mb-2">
              {searchTerm ? "Nessun amico trovato" : "Nessun amico aggiunto"}
            </h3>
            <p className="text-gray-500">
              {searchTerm
                ? "Prova a cercare con un altro nome"
                : "Inizia ad aggiungere amici per giocare insieme!"}
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredFriends.map((friend) => (
              <div
                key={friend.id}
                className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center">
                        <span className="text-white font-bold text-lg">
                          {friend.username.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div
                        className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
                          friend.isOnline ? "bg-green-400" : "bg-gray-400"
                        }`}
                      />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">
                        {friend.username}
                      </h4>
                      <p className="text-sm text-gray-500">{friend.lastSeen}</p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleSendMessage(friend.id)}
                      className="p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-colors"
                    >
                      <MessageCircle size={18} />
                    </button>
                    <button
                      onClick={() => handleInviteFriend(friend.id)}
                      className="bg-purple-600 text-white px-3 py-2 rounded-lg hover:bg-purple-700 transition-colors text-sm font-medium"
                    >
                      Invita
                    </button>
                  </div>
                </div>
                <div className="mt-3 flex items-center space-x-4 text-sm text-gray-600">
                  <div className="flex items-center space-x-1">
                    <Trophy size={14} />
                    <span>{friend.gamesPlayed} partite</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full" />
                    <span>{friend.winRate}% vittorie</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default FriendsContent;

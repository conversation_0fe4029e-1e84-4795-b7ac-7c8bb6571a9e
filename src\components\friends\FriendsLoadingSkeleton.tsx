import React from "react";
import { Card, CardContent } from "@/components/ui/card";

interface FriendsLoadingSkeletonProps {
  message?: string;
  count?: number;
}

const FriendsLoadingSkeleton: React.FC<FriendsLoadingSkeletonProps> = ({
  message = "Caricamento amici...",
  count = 3,
}) => {
  return (
    <>
      {/* CSS inline per garantire che l'animazione funzioni */}
      <style>
        {`
          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
          @-webkit-keyframes spin {
            from { -webkit-transform: rotate(0deg); }
            to { -webkit-transform: rotate(360deg); }
          }
        `}
      </style>

      <div className="space-y-3">
        {/* Intestazione con spinner */}
        <Card className="border-2 border-amber-800/30 shadow-md bg-amber-50/80 backdrop-blur-sm">
          <CardContent className="p-6 text-center">
            {/* Spinner principale più robusto per Android */}
            <div className="relative mx-auto mb-4 w-12 h-12">
              {/* Anello esterno */}
              <div className="absolute inset-0 border-4 border-amber-200 rounded-full"></div>
              {/* Anello animato con CSS inline */}
              <div
                className="absolute inset-0 border-4 border-transparent border-t-amber-600 rounded-full"
                style={{
                  animation: "spin 1s linear infinite",
                  WebkitAnimation: "spin 1s linear infinite",
                  filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.3))",
                }}
              ></div>
              {/* Icona centrale */}
              <div className="absolute inset-0 flex items-center justify-center p-2">
                <img
                  src="/images/icons/amici 100x100.png"
                  alt="Amici"
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
            <p className="text-romagna-darkWood font-medium">{message}</p>
          </CardContent>
        </Card>

        {/* Skeleton cards per preview */}
        {Array.from({ length: count }).map((_, index) => (
          <Card
            key={`skeleton-${index}`}
            className="border-2 border-amber-800/30 shadow-md bg-amber-50/80 backdrop-blur-sm opacity-50"
          >
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {/* Avatar skeleton */}
                  <div className="w-12 h-12 rounded-full bg-amber-200 animate-pulse"></div>
                  <div>
                    {/* Nome skeleton */}
                    <div className="w-24 h-4 bg-amber-200 rounded animate-pulse mb-2"></div>
                    {/* Info skeleton */}
                    <div className="w-16 h-3 bg-amber-200 rounded animate-pulse"></div>
                  </div>
                </div>
                {/* Pulsante skeleton */}
                <div className="w-8 h-8 bg-amber-200 rounded animate-pulse"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </>
  );
};

export default FriendsLoadingSkeleton;

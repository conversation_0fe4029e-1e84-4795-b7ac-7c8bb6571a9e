// Test script per verificare il funzionamento del sistema di statistiche
import {
  updateStatsAfterGame,
  updateStatsAfterAbandonment,
} from "./profileService";

// Simulazione di una partita vinta
const testVictoryGame = async () => {
  console.log("🎯 Test partita vinta...");

  const result = await updateStatsAfterGame({
    isWinner: true,
    difficulty: "medium",
    playerTeam: 0,
    finalScore: [11, 7],
    maraffeMade: 2,
    isPerfectGame: false,
    isComeback: false,
    isDominantWin: false,
    isAbandoned: false,
  });

  console.log("✅ Risultato partita vinta:", {
    xpGained: result.xpGained,
    leveledUp: result.leveledUp,
    newLevel: result.stats.level,
    totalGames: result.stats.totalGames,
    gamesWon: result.stats.gamesWon,
    xpBreakdown: result.xpBreakdown,
  });

  return result;
};

// Simulazione di una partita abbandonata
const testAbandonedGame = async () => {
  console.log("🎯 Test partita abbandonata...");

  const result = await updateStatsAfterAbandonment({
    difficulty: "medium",
    playerTeam: 0,
    currentScore: [5, 3],
  });

  console.log("✅ Risultato partita abbandonata:", {
    xpGained: result.xpGained,
    leveledUp: result.leveledUp,
    newLevel: result.stats.level,
    totalGames: result.stats.totalGames,
    gamesWon: result.stats.gamesWon,
    xpBreakdown: result.xpBreakdown,
  });

  return result;
};

// Simulazione di una partita persa
const testDefeatGame = async () => {
  console.log("🎯 Test partita persa...");

  const result = await updateStatsAfterGame({
    isWinner: false,
    difficulty: "medium",
    playerTeam: 0,
    finalScore: [7, 11],
    maraffeMade: 0,
    isPerfectGame: false,
    isComeback: false,
    isDominantWin: false,
    isAbandoned: false,
  });

  console.log("✅ Risultato partita persa:", {
    xpGained: result.xpGained,
    leveledUp: result.leveledUp,
    newLevel: result.stats.level,
    totalGames: result.stats.totalGames,
    gamesWon: result.stats.gamesWon,
    xpBreakdown: result.xpBreakdown,
  });

  return result;
};

// Esegue tutti i test
export const runStatsTests = async () => {
  console.log("🚀 Inizio test sistema statistiche...");

  try {
    await testVictoryGame();
    await testAbandonedGame();
    await testDefeatGame();

    console.log("✅ Tutti i test completati con successo!");
  } catch (error) {
    console.error("❌ Errore nei test:", error);
  }
};

// Esponi le funzioni di test globalmente per il debug
if (typeof window !== "undefined" && process.env.NODE_ENV === "development") {
  // @ts-expect-error - Debug functions for development
  window.testStats = {
    testVictoryGame,
    testAbandonedGame,
    testDefeatGame,
    runStatsTests,
  };
  console.log("🧪 Test statistiche disponibili in window.testStats");
}

/**
 * 🔄 HOOK PER MONITORAGGIO SESSIONE
 * Monitora lo stato della sessione e notifica problemi
 */

import { useEffect, useRef } from "react";
import { useAuth } from "@/context/auth-context";

interface SessionMonitorOptions {
  onSessionExpiring?: () => void;
  onSessionLost?: () => void;
  onSessionRestored?: () => void;
  checkInterval?: number; // in millisecondi
  warningThreshold?: number; // minuti prima della scadenza per avvisare
}

export const useSessionMonitor = (options: SessionMonitorOptions = {}) => {
  const {
    onSessionExpiring,
    onSessionLost,
    onSessionRestored,
    checkInterval = 60000, // 1 minuto
    warningThreshold = 5, // 5 minuti
  } = options;

  const { session, isLoggedIn, isOnlineFeatureEnabled } = useAuth();
  const lastSessionState = useRef<boolean>(false);
  const hasWarned = useRef<boolean>(false);

  useEffect(() => {
    if (!isOnlineFeatureEnabled || !isLoggedIn) {
      return;
    }

    const checkSession = () => {
      const currentSessionState = !!session?.access_token;

      // Controlla se la sessione è stata persa
      if (lastSessionState.current && !currentSessionState) {
        console.warn("⚠️ Sessione persa rilevata");
        onSessionLost?.();
        hasWarned.current = false; // Reset warning per la prossima sessione
      }

      // Controlla se la sessione è stata ripristinata
      if (!lastSessionState.current && currentSessionState) {
        // console.log("✅ Sessione ripristinata");
        onSessionRestored?.();
        hasWarned.current = false; // Reset warning
      }

      // Controlla se la sessione sta per scadere
      if (currentSessionState && session?.expires_at) {
        const expiresAt = session.expires_at * 1000;
        const now = Date.now();
        const minutesUntilExpiry = Math.round((expiresAt - now) / 60000);

        if (
          minutesUntilExpiry <= warningThreshold &&
          minutesUntilExpiry > 0 &&
          !hasWarned.current
        ) {
          console.warn(`⚠️ Sessione scade tra ${minutesUntilExpiry} minuti`);
          onSessionExpiring?.();
          hasWarned.current = true;
        }

        // Reset warning se la sessione è stata refreshata
        if (minutesUntilExpiry > warningThreshold) {
          hasWarned.current = false;
        }
      }

      lastSessionState.current = currentSessionState;
    };

    // Controllo iniziale
    checkSession();

    // Controllo periodico
    const interval = setInterval(checkSession, checkInterval);

    return () => clearInterval(interval);
  }, [
    session,
    isLoggedIn,
    isOnlineFeatureEnabled,
    onSessionExpiring,
    onSessionLost,
    onSessionRestored,
    checkInterval,
    warningThreshold,
  ]);

  return {
    isSessionValid: !!session?.access_token,
    sessionExpiresAt: session?.expires_at
      ? new Date(session.expires_at * 1000)
      : null,
    minutesUntilExpiry: session?.expires_at
      ? Math.round((session.expires_at * 1000 - Date.now()) / 60000)
      : null,
  };
};

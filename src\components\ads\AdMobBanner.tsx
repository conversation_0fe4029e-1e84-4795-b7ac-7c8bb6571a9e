import React, { useEffect, useState, useRef } from "react";
import { Capacitor } from "@capacitor/core";
import { useLocation } from "react-router-dom";
import AdMobService from "@/services/adMobService";
import ShareButton from "./ShareButton";

interface AdMobBannerProps {
  className?: string;
  onAdLoaded?: (loaded: boolean) => void;
}

const AdMobBanner: React.FC<AdMobBannerProps> = ({
  className = "",
  onAdLoaded,
}) => {
  const location = useLocation();
  const [isAdLoaded, setIsAdLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [retryCount, setRetryCount] = useState(0);
  const [shouldHide, setShouldHide] = useState(false); // Nuovo stato per nascondere completamente
  const [isDisabled, setIsDisabled] = useState(false); // Disabilita completamente dopo troppi errori
  const [showShareButton, setShowShareButton] = useState(false); // Mostra bottone condividi come fallback
  const isAdLoadedRef = useRef(false);
  const initializationRef = useRef(false);
  const previousPathRef = useRef(location.pathname);
  const failureCountRef = useRef(0); // Conta i fallimenti consecutivi
  const lastAttemptTimeRef = useRef(0); // Timestamp dell'ultimo tentativo
  const maxRetries = 0; // Nessun retry immediato
  const maxLoadingTime = 5000; // 5 secondi massimi - più veloce!
  const maxFailures = 3; // Dopo 3 fallimenti, disabilita per un po'
  const cooldownTime = 60000; // 1 minuto di pausa dopo troppi fallimenti

  // Effetto per rilevare quando l'utente torna da una pagina di gioco
  useEffect(() => {
    const currentPath = location.pathname;
    const previousPath = previousPathRef.current;

    // Se l'utente torna da una pagina di gioco (/game o /play) a una pagina normale
    const wasInGame = previousPath === "/game" || previousPath === "/play";
    const isNowInNormalPage = ![
      "/game",
      "/play",
      "/terms",
      "/privacy-policy",
      "/rules",
    ].includes(currentPath);

    if (wasInGame && isNowInNormalPage && AdMobService.shouldShowAds()) {
      console.log("🎮 Rilevato ritorno da partita");

      // Controlla se il banner è disabilitato
      const now = Date.now();
      if (isDisabled && now - lastAttemptTimeRef.current < cooldownTime) {
        const remainingTime = Math.ceil(
          (cooldownTime - (now - lastAttemptTimeRef.current)) / 1000
        );
        console.log(
          `🎮 Banner disabilitato per altri ${remainingTime} secondi - skip refresh`
        );
        return;
      }

      console.log("🎮 Forzo refresh banner post-partita...");

      // Reset completo dello stato (ma mantieni il contatore fallimenti)
      setShouldHide(false);
      setShowShareButton(false); // Nasconde il bottone condividi per provare il banner
      setIsAdLoaded(false);
      setIsLoading(true);
      setRetryCount(0);
      isAdLoadedRef.current = false;
      initializationRef.current = false;

      // Forza un refresh dopo una breve pausa
      setTimeout(() => {
        if (AdMobService.shouldShowAds()) {
          console.log("🔄 Avvio refresh banner post-partita...");
          // Il banner verrà ricaricato dal useEffect principale
        }
      }, 2000); // Aumentato a 2 secondi per dare più tempo
    }

    previousPathRef.current = currentPath;
  }, [location.pathname]);

  useEffect(() => {
    let isMounted = true;
    let timeout: NodeJS.Timeout;
    let retryTimeout: NodeJS.Timeout;
    let globalTimeout: NodeJS.Timeout;

    // Reset stati quando il componente viene rimontato
    setRetryCount(0);
    setShouldHide(false);

    // Timeout globale: se dopo 10 secondi non è caricato, mostra bottone condividi
    globalTimeout = setTimeout(() => {
      if (isMounted && !isAdLoadedRef.current) {
        console.warn("⚠️ Timeout globale banner - mostro bottone condividi");

        // Incrementa contatore fallimenti
        failureCountRef.current++;

        // SEMPRE mostra il bottone condividi quando il banner non si carica
        setShowShareButton(true);
        setShouldHide(false); // Non nascondere, mostra sempre il bottone condividi

        // Se ha raggiunto il limite, disabilita per il cooldown
        if (failureCountRef.current >= maxFailures) {
          console.warn("❌ Troppi timeout - disabilito per cooldown");
          setIsDisabled(true);
        }

        setIsLoading(false);
        setIsAdLoaded(false);
        onAdLoaded?.(false);
        initializationRef.current = false;
      }
    }, maxLoadingTime);

    const initializeAd = async () => {
      // Evita inizializzazioni multiple simultanee
      if (initializationRef.current) {
        console.log("AdMob: Inizializzazione già in corso, skip...");
        return;
      }

      // Controlla se il banner è temporaneamente disabilitato
      const now = Date.now();
      if (isDisabled && now - lastAttemptTimeRef.current < cooldownTime) {
        const remainingTime = Math.ceil(
          (cooldownTime - (now - lastAttemptTimeRef.current)) / 1000
        );
        console.log(
          `AdMob: Banner disabilitato per altri ${remainingTime} secondi - mostro bottone condividi`
        );
        setShouldHide(false); // Non nascondere, mostra il bottone condividi
        setShowShareButton(true);
        setIsLoading(false);
        onAdLoaded?.(false);
        return;
      }

      // Se è passato il cooldown, riabilita
      if (isDisabled && now - lastAttemptTimeRef.current >= cooldownTime) {
        console.log("AdMob: Cooldown terminato, riabilito banner");
        setIsDisabled(false);
        failureCountRef.current = 0;
        setShouldHide(false);
        setShowShareButton(false); // Nasconde il bottone condividi
      }

      initializationRef.current = true;
      setIsAdLoaded(false);
      setIsLoading(true);
      isAdLoadedRef.current = false;
      lastAttemptTimeRef.current = now;

      if (!AdMobService.shouldShowAds()) {
        console.log("AdMob: Annunci disabilitati per questo utente");
        if (isMounted) {
          setIsLoading(false);
          onAdLoaded?.(false);
          initializationRef.current = false;
        }
        return;
      }

      // Timeout di 6 secondi per ogni singolo tentativo
      timeout = setTimeout(() => {
        if (isMounted && !isAdLoadedRef.current && retryCount < maxRetries) {
          console.warn(
            `⚠️ Timeout tentativo ${retryCount + 1} - retry in 2 secondi...`
          );
          // Retry dopo 2 secondi
          retryTimeout = setTimeout(() => {
            if (isMounted && !isAdLoadedRef.current) {
              console.log(`🔄 Retry ${retryCount + 1} caricamento banner...`);
              setRetryCount((prev) => prev + 1);
              initializeAd();
            }
          }, 2000);
        } else if (
          isMounted &&
          !isAdLoadedRef.current &&
          retryCount >= maxRetries
        ) {
          console.warn(
            "⚠️ Tutti i tentativi completati - aspetto timeout globale"
          );
          initializationRef.current = false;
          // Non settare stati qui, lascia che il timeout globale gestisca tutto
        }
      }, 6000);

      try {
        console.log(
          `🔄 Inizializzazione AdMob Banner (tentativo ${retryCount + 1})...`
        );

        // Pulizia veloce solo per il primo tentativo
        if (retryCount === 0) {
          try {
            console.log("🧹 Pulizia banner iniziale...");
            await AdMobService.hideBanner();
            await new Promise((resolve) => setTimeout(resolve, 500));
          } catch (cleanupError) {
            console.log("Pulizia completata");
          }
        }

        // Inizializza il servizio solo se non è già inizializzato
        if (!AdMobService.isAdMobInitialized()) {
          await AdMobService.initialize();
        }

        // Mostra il nuovo banner
        const success = await AdMobService.showBanner();

        if (isMounted) {
          clearTimeout(timeout);
          clearTimeout(retryTimeout);

          if (success) {
            clearTimeout(globalTimeout); // Cancella il timeout globale solo se ha successo
            setIsAdLoaded(true);
            isAdLoadedRef.current = true;
            setIsLoading(false);
            setShowShareButton(false); // Nasconde il bottone condividi
            onAdLoaded?.(true);
            initializationRef.current = false;
            failureCountRef.current = 0; // Reset contatore fallimenti
            console.log("✅ Banner AdMob caricato con successo");
          } else {
            // Se fallisce, prova il retry solo se non abbiamo raggiunto il limite
            if (retryCount < maxRetries) {
              console.warn(`⚠️ Banner AdMob fallito - retry in 2 secondi...`);
              initializationRef.current = false; // Reset per permettere retry
              retryTimeout = setTimeout(() => {
                if (isMounted) {
                  setRetryCount((prev) => prev + 1);
                  initializeAd();
                }
              }, 2000);
            } else {
              // Incrementa contatore fallimenti
              failureCountRef.current++;
              console.warn(
                `⚠️ Banner AdMob fallito - mostro bottone condividi (${failureCountRef.current}/${maxFailures})`
              );

              // SEMPRE mostra il bottone condividi quando il banner fallisce
              setShowShareButton(true);
              setShouldHide(false); // Non nascondere, mostra sempre il bottone condividi
              setIsLoading(false);
              onAdLoaded?.(false);

              // Se ha raggiunto il limite, disabilita per il cooldown
              if (failureCountRef.current >= maxFailures) {
                console.warn("❌ Troppi fallimenti - disabilito per cooldown");
                setIsDisabled(true);
              }

              initializationRef.current = false;
            }
          }
        }
      } catch (err) {
        console.error("❌ Errore nel caricamento del banner AdMob:", err);
        if (isMounted) {
          clearTimeout(timeout);
          clearTimeout(retryTimeout);
          // NON cancellare globalTimeout qui - lascia che faccia il suo lavoro

          // Se fallisce, prova il retry solo se non abbiamo raggiunto il limite
          if (retryCount < maxRetries) {
            console.warn(`⚠️ Errore banner - retry in 3 secondi...`);
            initializationRef.current = false; // Reset per permettere retry
            retryTimeout = setTimeout(() => {
              if (isMounted) {
                setRetryCount((prev) => prev + 1);
                initializeAd();
              }
            }, 3000);
          } else {
            // Incrementa contatore fallimenti
            failureCountRef.current++;
            console.warn(
              `❌ Errore banner - mostro bottone condividi (${failureCountRef.current}/${maxFailures})`
            );

            // SEMPRE mostra il bottone condividi quando c'è un errore
            setShowShareButton(true);
            setShouldHide(false); // Non nascondere, mostra sempre il bottone condividi
            setIsLoading(false);
            onAdLoaded?.(false);

            // Se ha raggiunto il limite, disabilita per il cooldown
            if (failureCountRef.current >= maxFailures) {
              console.warn("❌ Troppi errori - disabilito per cooldown");
              setIsDisabled(true);
            }

            initializationRef.current = false;
          }
        }
      }
    };

    const cleanupAd = async () => {
      try {
        await AdMobService.hideBanner();
        console.log("🧹 Banner AdMob pulito");
      } catch (err) {
        console.error("❌ Errore nel nascondere il banner:", err);
      }
    };

    initializeAd();

    return () => {
      isMounted = false;
      initializationRef.current = false;
      clearTimeout(timeout);
      clearTimeout(retryTimeout);
      clearTimeout(globalTimeout);
      if (AdMobService.shouldShowAds()) {
        cleanupAd();
      }
    };
  }, [onAdLoaded]);

  // Se gli annunci sono disabilitati o il banner deve essere nascosto, non renderizza nulla
  if (!AdMobService.shouldShowAds() || shouldHide) {
    return null;
  }

  // Se il banner è disabilitato, mostra il bottone condividi
  if (isDisabled || showShareButton) {
    return <ShareButton className={className} />;
  }

  // Se sta caricando, mostra placeholder temporaneo
  if (isLoading) {
    return (
      <div
        className={`w-full bg-transparent ${className}`}
        style={{
          height: "50px",
          minHeight: "50px",
          maxHeight: "50px",
          overflow: "hidden",
          position: "relative",
          zIndex: 30,
        }}
        aria-label="Caricamento annuncio"
      >
        <div className="w-full h-full flex items-center justify-center bg-gray-100/10">
          <div className="text-xs text-gray-400">Caricamento...</div>
        </div>
      </div>
    );
  }

  // Se l'annuncio è caricato, mostra il banner
  if (isAdLoaded) {
    return (
      <div
        className={`w-full bg-transparent ${className}`}
        style={{
          height: "50px",
          minHeight: "50px",
          maxHeight: "50px",
          overflow: "hidden",
          position: "relative",
          zIndex: 30,
        }}
        aria-label="Spazio pubblicitario"
      />
    );
  }

  // Se non è caricato e non sta caricando, non renderizza nulla
  return null;
};

export default AdMobBanner;

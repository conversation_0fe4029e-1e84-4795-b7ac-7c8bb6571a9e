import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useHardwareBackButton } from "@/hooks/useHardwareBackButton";
import { useIsMobile } from "@/hooks/use-mobile";
import { useUserState } from "@/hooks/useUserState";
import { useAuth } from "@/context/auth-context";
import { useCustomToast } from "@/hooks/useCustomToast";
import { useAudio } from "@/hooks/useAudio";
import { useSessionMonitor } from "@/hooks/useSessionMonitor";
import RankRoadmap from "@/components/profile/RankRoadmap";
import ProfileCard from "@/components/account/ProfileCard";
import SettingsCard from "@/components/account/SettingsCard";
import DeveloperCard from "@/components/account/DeveloperCard";
import {
  getXpForNextLevel,
  getPlayerStats, // Solo per fallback offline
  type PlayerStats,
} from "@/services/localStatsService";
import {
  getGameSettings,
  updateVictoryPoints,
} from "@/services/gameSettingsService";
import {
  getPlayerTitle,
  getNextPlayerTitle,
} from "@/services/playerTitlesService";
import { signInWithGoogle, signInWithFacebook } from "@/services/authService";
// 🎯 RIMOSSO: getActiveProfileUnified non più necessario

const Account = () => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  // 🚀 NUOVO: Usa il sistema di stato globale persistente
  const {
    userState,
    isLoading: userStateLoading,
    isSyncing,
    updateUsername,
    refreshState,
  } = useUserState();
  // 🔐 NUOVO: Usa direttamente l'auth context per evitare flicker
  const {
    user: authUser,
    isLoggedIn,
    isLoading: authLoading,
    signOut,
  } = useAuth();

  // 🎯 NUOVO: Toast centralizzato con grafica avanzata
  const { showToast } = useCustomToast();

  // Gestisce il tasto indietro hardware solo su mobile
  useHardwareBackButton("/", isMobile);

  // State for rank roadmap modal
  const [isRankRoadmapOpen, setIsRankRoadmapOpen] = useState(false);
  // State per il nome personalizzato
  const [isEditingName, setIsEditingName] = useState(false);
  const [tempName, setTempName] = useState("");

  // Stato per tracciare azioni utente reali
  const [hasUserAction, setHasUserAction] = useState(false);

  // 🔄 CONTROLLO PERIODICO STATO AUTENTICAZIONE
  useEffect(() => {
    if (!isLoggedIn || !authUser) return;

    const checkAuthStatus = () => {
      // Verifica se l'utente è ancora autenticato
      if (isLoggedIn && authUser) {
        console.log("🔍 Controllo stato autenticazione: OK");
      } else {
        console.warn(
          "⚠️ Stato autenticazione inconsistente, ma NESSUN REFRESH AUTOMATICO"
        );
        // DISABILITATO: window.location.reload(); - L'utente deve gestire manualmente
      }
    };

    // Controllo ogni 30 secondi
    const authCheckInterval = setInterval(checkAuthStatus, 30000);

    return () => clearInterval(authCheckInterval);
  }, [isLoggedIn, authUser]);
  const [isRecovering, setIsRecovering] = useState(false);

  // Audio e impostazioni di gioco
  const {
    getConfig,
    setEffectsEnabled,
    setMusicEnabled,
    setSoundVolume,
    setMusicVolume,
    playSound,
  } = useAudio();
  const audioConfig = getConfig();

  const initialGameSettings = getGameSettings();
  const [gamePoints, setGamePoints] = useState<"21" | "31" | "41">(
    initialGameSettings.victoryPoints
  );
  const [soundEnabled, setSoundEnabled] = useState(
    audioConfig.soundEffectsEnabled
  );
  const [musicEnabled, setMusicEnabledState] = useState(
    audioConfig.musicEnabled
  );
  const [soundVolume, setSoundVolumeState] = useState(
    Math.round(audioConfig.soundEffectsVolume * 100)
  );
  const [musicVolumeState, setMusicVolumeState] = useState(
    Math.round(audioConfig.musicVolume * 100)
  ); // 🎯 SEMPLIFICATO: Un solo stato per il profilo completo
  const [profileData, setProfileData] = useState<{
    stats: PlayerStats;
    isOnline: boolean;
    username?: string;
    userId?: string;
  } | null>(null);
  const [profileLoading, setProfileLoading] = useState(true);

  // 🎯 UNICO EFFECT: Carica il profilo una sola volta e ascolta aggiornamenti
  useEffect(() => {
    let isMounted = true;

    const loadProfile = async () => {
      if (!isMounted) return;

      try {
        setProfileLoading(true);
        console.log("🔄 Caricamento profilo...");

        // Determina modalità online/offline

        if (!userState.isOfflineMode && authUser && isLoggedIn) {
          // Modalità online: usa cache intelligente
          const { getActiveProfile } = await import(
            "@/services/profileService"
          );
          const profile = await getActiveProfile();

          if (isMounted) {
            setProfileData({
              stats: profile.stats,
              isOnline: true,
              username: profile.username,
              userId: profile.userId,
            });
            console.log("✅ Profilo online caricato:", profile.username);
          }
        } else {
          // Modalità offline: usa statistiche locali più aggiornate
          const localStats = getPlayerStats();

          if (isMounted) {
            setProfileData({
              stats: {
                level: localStats.level || userState.level,
                xp: localStats.xp || userState.xp,
                totalGames: localStats.totalGames || userState.gamesPlayed,
                gamesWon: localStats.gamesWon || userState.gamesWon,
                gamesLost:
                  (localStats.totalGames || userState.gamesPlayed) -
                  (localStats.gamesWon || userState.gamesWon),
                winRate: localStats.winRate || userState.winRate,
                maraffeMade: localStats.maraffeMade || 0,
                achievementsUnlocked: localStats.achievementsUnlocked || [],
                lastPlayed: localStats.lastPlayed || new Date().toISOString(),
                currentWinStreak: localStats.currentWinStreak || 0,
                bestWinStreak: localStats.bestWinStreak || 0,
                recentGames: localStats.recentGames || [],
                createdAt: localStats.createdAt || new Date().toISOString(),
                updatedAt: localStats.updatedAt || userState.lastUpdated,
              },
              isOnline: false,
              username: userState.username || "Giocatore Locale",
            });
            console.log("✅ Profilo offline caricato con dati aggiornati:", {
              level: localStats.level,
              xp: localStats.xp,
              totalGames: localStats.totalGames,
              username: userState.username,
            });
          }
        }
      } catch (error) {
        console.error("❌ Errore caricamento profilo:", error);
        if (isMounted) {
          setProfileData(null);
        }
      } finally {
        if (isMounted) {
          setProfileLoading(false);
        }
      }
    };

    // Carica solo se necessario (alla prima volta o dopo aggiornamenti)
    loadProfile();

    // 🔒 LISTENER DISABILITATO - NESSUN RICARICAMENTO AUTOMATICO
    // const handleStatsUpdate = () => {
    //   console.log("🔒 Aggiornamento statistiche IGNORATO per stabilità");
    // };
    // window.addEventListener("statsUpdated", handleStatsUpdate);

    return () => {
      isMounted = false;
      // 🔒 NESSUN CLEANUP NECESSARIO - LISTENER DISABILITATO
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    userState.isOfflineMode,
    authUser?.id, // Solo l'ID, non tutto l'oggetto
    isLoggedIn,
  ]); // 🎯 RIDOTTO: Solo dipendenze essenziali per evitare ricaricamenti

  // 🎯 FALLBACK MIGLIORATO: Usa sempre dati più aggiornati
  const getLatestStats = () => {
    // Prima prova a usare profileData se disponibile
    if (profileData?.stats) {
      return profileData.stats;
    }

    // Altrimenti usa statistiche locali più aggiornate
    const localStats = getPlayerStats();
    return {
      level: localStats.level || userState.level,
      xp: localStats.xp || userState.xp,
      totalGames: localStats.totalGames || userState.gamesPlayed,
      gamesWon: localStats.gamesWon || userState.gamesWon,
      gamesLost:
        (localStats.totalGames || userState.gamesPlayed) -
        (localStats.gamesWon || userState.gamesWon),
      winRate: localStats.winRate || userState.winRate,
      maraffeMade: localStats.maraffeMade || 0,
      achievementsUnlocked: localStats.achievementsUnlocked || [],
      lastPlayed: localStats.lastPlayed || new Date().toISOString(),
      currentWinStreak: localStats.currentWinStreak || 0,
      bestWinStreak: localStats.bestWinStreak || 0,
      recentGames: localStats.recentGames || [],
      createdAt: localStats.createdAt || new Date().toISOString(),
      updatedAt: localStats.updatedAt || userState.lastUpdated,
    };
  };

  const playerStats = getLatestStats();
  // 🎯 SEMPLIFICATO: Gestione audio sincronizzata
  useEffect(() => {
    const config = getConfig();
    setSoundEnabled(config.soundEffectsEnabled);
    setMusicEnabledState(config.musicEnabled);
    setSoundVolumeState(Math.round(config.soundEffectsVolume * 100));
    setMusicVolumeState(Math.round(config.musicVolume * 100));
  }, [getConfig]);

  // Handlers that update both local state and AudioManager
  const handleSoundEnabledChange = (enabled: boolean) => {
    setSoundEnabled(enabled);
    setEffectsEnabled(enabled);
    playSound("buttonClick");
  };

  const handleMusicEnabledChange = (enabled: boolean) => {
    setMusicEnabledState(enabled);
    setMusicEnabled(enabled);
    playSound("buttonClick");
  };

  const handleSoundVolumeChange = (volume: number) => {
    setSoundVolumeState(volume);
    setSoundVolume(volume / 100);
  };
  const handleMusicVolumeChange = (volume: number) => {
    setMusicVolumeState(volume);
    setMusicVolume(volume / 100);
  };

  // Handler per cambiare i punti vittoria
  const handleGamePointsChange = (points: "21" | "31" | "41") => {
    setGamePoints(points);
    updateVictoryPoints(points);
    playSound("buttonClick");
  }; // 🎯 CORRETTO: Usa playerStats per dati aggiornati
  const currentPlayerTitle = getPlayerTitle(playerStats.level);
  const nextPlayerTitle = getNextPlayerTitle(playerStats.level);

  // 🎯 LOGICA SEMPLIFICATA: Priorità all'username quando disponibile
  const getDisplayName = () => {
    // 1. Prima priorità: username dal profilo caricato (online o offline)
    if (profileData?.username && profileData.username !== "Giocatore Locale") {
      return profileData.username;
    }

    // 2. Seconda priorità: username dal context (sistema unificato)
    if (userState.username && userState.username !== "Giocatore") {
      return userState.username;
    }

    // 3. Fallback: titolo del grado
    return currentPlayerTitle.title;
  };

  // 🔒 FUNZIONE BLOCCATA: SEMPRE E SOLO NOME RANK!
  const getDisplayDescription = () => {
    // 🔒 SEMPRE E SOLO IL NOME DEL RANK - MAI PIÙ "Esplora i segreti"!
    return currentPlayerTitle.title;
  };

  const accountInfo = {
    name: getDisplayName(),
    level: userState.level,
    xp: userState.xp,
    nextLevelXp: getXpForNextLevel(userState.level),
    rankImage: currentPlayerTitle.rankImage,
    titleDescription: getDisplayDescription(),
    nextTitle: nextPlayerTitle,
    // 🔧 CORREZIONE: L'icona di modifica deve essere sempre disponibile se l'utente è loggato
    isOnline: isLoggedIn && !userState.isOfflineMode,
  };

  // Debug solo occasionale per ridurre spam
  if (process.env.NODE_ENV === "development" && Math.random() < 0.1) {
    console.log(
      "🏷️ Account info:",
      accountInfo.name,
      "isOnline:",
      accountInfo.isOnline
    );
  }

  // Funzioni per gestire la modifica del nome (aggiornate per il nuovo sistema)
  const handleEditName = () => {
    setTempName(accountInfo.name);
    setIsEditingName(true);
  };
  const handleSaveName = async () => {
    const trimmedName = tempName.trim();

    // Validazione caratteri permessi (solo lettere, numeri e alcuni caratteri speciali)
    const validNameRegex = /^[a-zA-Z0-9_-]+$/;
    if (!validNameRegex.test(trimmedName)) {
      showToast("Il nome può contenere solo lettere, numeri, _ e -", "error");
      return; // Non chiude la modalità editing
    }

    if (trimmedName.length < 2) {
      showToast("Il nome deve avere almeno 2 caratteri", "error");
      return; // Non chiude la modalità editing
    }

    if (trimmedName.length > 18) {
      showToast("Il nome non può superare i 18 caratteri", "error");
      return; // Non chiude la modalità editing
    }

    if (trimmedName === accountInfo.name) {
      // Se il nome non è cambiato, chiudi semplicemente la modalità editing
      setIsEditingName(false);
      return;
    }

    try {
      // Usa il nuovo sistema di stato globale
      await updateUsername(trimmedName);

      // 🎯 AGGIORNA LA CACHE DEL PROFILO con il nuovo username
      if (profileData?.isOnline) {
        const { updateUsernameInCache } = await import(
          "@/services/profileService"
        );
        updateUsernameInCache(trimmedName);

        // Aggiorna anche lo stato locale del profilo
        setProfileData((prev) =>
          prev
            ? {
                ...prev,
                username: trimmedName,
              }
            : null
        );
      }

      showToast("Nome aggiornato con successo!", "success");
      setIsEditingName(false);
    } catch (error) {
      console.error("Errore aggiornamento nome:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Errore aggiornamento nome";
      showToast(errorMessage, "error");
      // Non chiude la modalità editing se c'è un errore
    }
  };
  const handleCancelEdit = () => {
    setIsEditingName(false);
    setTempName("");
  };

  const handleNameKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSaveName();
    } else if (e.key === "Escape") {
      handleCancelEdit();
    }
  };

  // Funzione per resettare le statistiche (debug)
  const handleBack = () => {
    navigate("/");
  };
  // Gestisce lo scroll automatico alla sezione quando c'è un hash nell'URL
  useEffect(() => {
    const hash = window.location.hash;
    if (hash) {
      // Usa un piccolo delay per assicurarsi che il DOM sia renderizzato
      setTimeout(() => {
        const element = document.querySelector(hash);
        if (element) {
          element.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }, 100);
    }
  }, []);

  // 🔐 Funzioni di autenticazione semplificate usando auth context
  const handleGoogleLogin = async () => {
    setHasUserAction(true); // Marca come azione utente

    const { error } = await signInWithGoogle();

    if (error) {
      showToast(`Errore: ${error}`, "error");
      setHasUserAction(false); // Reset se errore
    } else {
      // 🎯 NUOVO: Trigger refresh immediato dopo login per sincronizzazione veloce
      setTimeout(async () => {
        try {
          console.log("🚀 Trigger refresh post-login Google...");
          await refreshState();
          console.log("✅ Refresh post-login completato");
        } catch (error) {
          console.warn("⚠️ Refresh post-login fallito (non critico):", error);
        }
      }, 2000); // Delay di 2 secondi per permettere al context di stabilizzarsi
    }
  };
  const handleFacebookLogin = async () => {
    setHasUserAction(true); // Marca come azione utente

    const { error } = await signInWithFacebook();

    if (error) {
      showToast(`Errore: ${error}`, "error");
      setHasUserAction(false); // Reset se errore
    } else {
      // Login riuscito - l'auth context gestisce tutto
      showToast("🎉 Accesso effettuato con successo!", "success");
      console.log(
        "🔄 Login riuscito, auth context gestisce la sincronizzazione"
      );
    }
  };

  const handleLogout = async () => {
    setHasUserAction(true); // Marca come azione utente

    try {
      console.log("🚪 Avvio logout con timeout aggressivo...");

      // 🔥 TIMEOUT AGGRESSIVO - SE NON RISPONDE IN 3 SECONDI, FORZA LOGOUT
      const logoutPromise = signOut();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Timeout logout")), 3000)
      );

      await Promise.race([logoutPromise, timeoutPromise]);

      console.log("✅ Logout completato con successo");
      showToast("Logout effettuato con successo", "success");
    } catch (error) {
      console.error("❌ Errore durante logout:", error);

      // 🔥 FORZA LOGOUT ANCHE SE ERRORE
      localStorage.clear();
      sessionStorage.clear();
      setTimeout(() => window.location.reload(), 500);
    } finally {
      setHasUserAction(false); // Reset sempre
    }
  };
  // 🎯 RIMOSSO: Effect per recent games ora gestito nel profilo unificato  // 🎯 RIMOSSO: Sistema di controllo blocchi che causava interferenze
  // useEffect rimosso per eliminare problemi di cache bloccata
  // 🎯 SEMPLIFICATO: Funzione refresh senza controlli di sistema bloccato
  const handleForceRefresh = async () => {
    if (isRecovering) return;

    setIsRecovering(true);
    try {
      showToast("Aggiornamento in corso...", "info");

      // Usa la funzione refreshState dal context
      await refreshState();

      showToast("Dati aggiornati con successo!", "success");
    } catch (error) {
      console.error("❌ Errore nel refresh:", error);
      showToast("Errore nell'aggiornamento dei dati", "error");
    } finally {
      setIsRecovering(false);
    }
  };

  return (
    <div className="min-h-screen  flex flex-col">
      {/* Header with back button and title */}
      <div
        className="p-4 flex items-center justify-between"
        style={{ paddingTop: `calc(1rem + env(safe-area-inset-top, 0px))` }}
      >
        <div className="flex items-center gap-4">
          <h1
            className="text-2xl font-bold text-romagna-darkWood"
            style={{
              fontFamily: "'DynaPuff', cursive",
              fontWeight: 600,
            }}
          >
            Profilo
          </h1>
        </div>
      </div>
      {/* Main content - scrollable container */}
      <div className="flex-1 px-4 pb-8 overflow-y-auto">
        <div className="max-w-3xl mx-auto space-y-4">
          {/* Profile Card */}
          <ProfileCard
            accountInfo={accountInfo}
            playerStats={playerStats}
            isEditingName={isEditingName}
            tempName={tempName}
            userStateLoading={userStateLoading}
            authLoading={authLoading}
            authUser={authUser}
            isLoggedIn={isLoggedIn}
            isSyncing={isSyncing}
            onEditName={handleEditName}
            onSaveName={handleSaveName}
            onCancelEdit={handleCancelEdit}
            onNameChange={setTempName}
            onNameKeyPress={handleNameKeyPress}
            onLogout={handleLogout}
            onGoogleLogin={handleGoogleLogin}
            onFacebookLogin={handleFacebookLogin}
            onRankRoadmapOpen={() => setIsRankRoadmapOpen(true)}
          />

          {/* Settings Card */}
          <SettingsCard
            gamePoints={gamePoints}
            soundEnabled={soundEnabled}
            musicEnabled={musicEnabled}
            soundVolume={soundVolume}
            musicVolumeState={musicVolumeState}
            onGamePointsChange={handleGamePointsChange}
            onSoundEnabledChange={handleSoundEnabledChange}
            onMusicEnabledChange={handleMusicEnabledChange}
            onSoundVolumeChange={handleSoundVolumeChange}
            onMusicVolumeChange={handleMusicVolumeChange}
          />

          {/* Developer Card */}
          <DeveloperCard />
        </div>
      </div>
      {/* Rank Roadmap Modal */}
      <RankRoadmap
        isOpen={isRankRoadmapOpen}
        onClose={() => setIsRankRoadmapOpen(false)}
        currentLevel={playerStats.level}
        currentXp={playerStats.xp}
      />
    </div>
  );
};

export default Account;

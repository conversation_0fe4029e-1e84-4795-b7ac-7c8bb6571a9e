import React, { useEffect, useState, useCallback } from "react";
import AdMobBanner from "@/components/ads/AdMobBanner";
import { useAdBannerVisibility } from "@/hooks/useAdBannerVisibility";
import { useTermsAcceptance } from "@/hooks/useTermsAcceptance";
import TermsAcceptanceModal from "@/components/modals/TermsAcceptanceModal";
import { initializeOAuthListeners } from "@/services/authService";

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const { shouldShowBanner } = useAdBannerVisibility();
  const { needsAcceptance, isLoading, acceptTerms, declineTerms } =
    useTermsAcceptance();
  const [showRefreshModal, setShowRefreshModal] = useState(false);
  const [isBannerLoaded, setIsBannerLoaded] = useState(false);

  // Callback per gestire lo stato del banner
  const handleAdLoaded = useCallback((loaded: boolean) => {
    setIsBannerLoaded(loaded);
  }, []);

  // Inizializza i listener OAuth all'avvio
  useEffect(() => {
    console.log("🚀 Inizializzazione AppLayout - Setup listener OAuth...");

    // Inizializza i listener per OAuth (solo per mobile)
    initializeOAuthListeners();
  }, []);

  // Mostra uno spinner discreto mentre carica lo stato dei termini (senza l'icona che causa il flash)
  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-amber-50">
        <div className="text-center">
          {/* Rimosso l'icona elia-gallo per evitare il flash indesiderato */}
          <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center">
            <div
              className="w-8 h-8 border-4 border-amber-300 border-t-amber-600 rounded-full animate-spin"
              style={{
                filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.3))",
              }}
            ></div>
          </div>
          <p className="text-amber-800 font-medium">Preparazione...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="h-screen flex flex-col">
        <div className="flex-1 overflow-auto">{children}</div>
      </div>

      {/* Banner AdMob fisso in fondo - z-index 30 per essere sotto il footer (z-50) */}
      {shouldShowBanner && (
        <div className="fixed bottom-0 left-0 right-0 z-30">
          <AdMobBanner onAdLoaded={handleAdLoaded} className="w-full" />
        </div>
      )}

      {/* Modale di accettazione termini - appare solo se necessario */}
      <TermsAcceptanceModal
        isOpen={needsAcceptance}
        onAccept={acceptTerms}
        onDecline={declineTerms}
      />

      {/* Modale di refresh post-login */}
      {/* <RefreshModal
        isOpen={showRefreshModal}
        onRefresh={() => {
          setShowRefreshModal(false);
          window.location.reload();
        }}
        onDismiss={() => setShowRefreshModal(false)}
      /> */}
    </>
  );
};

export default AppLayout;

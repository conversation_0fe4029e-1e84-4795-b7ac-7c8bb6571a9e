import React from "react";
import { Card, CardContent } from "@/components/ui/card";

interface LeaderboardLoadingSkeletonProps {
  message?: string;
  count?: number;
}

const LeaderboardLoadingSkeleton: React.FC<LeaderboardLoadingSkeletonProps> = ({
  message = "Caricamento classifica...",
  count = 5,
}) => {
  return (
    <>
      {/* CSS inline per garantire che l'animazione funzioni */}
      <style>
        {`
          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
          @-webkit-keyframes spin {
            from { -webkit-transform: rotate(0deg); }
            to { -webkit-transform: rotate(360deg); }
          }
        `}
      </style>

      <div className="space-y-2">
        {/* Intestazione con spinner */}
        <Card className="border-2 border-amber-800/30 shadow-md bg-amber-50/80 backdrop-blur-sm">
          <CardContent className="p-6 text-center">
            {/* Spinner principale con trofeo */}
            <div className="relative mx-auto mb-4 w-12 h-12">
              {/* Anello esterno */}
              <div className="absolute inset-0 border-4 border-yellow-200 rounded-full"></div>
              {/* Anello animato */}
              <div
                className="absolute inset-0 border-4 border-transparent border-t-yellow-600 rounded-full"
                style={{
                  animation: "spin 1s linear infinite",
                  WebkitAnimation: "spin 1s linear infinite",
                  filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.3))",
                }}
              ></div>
              {/* Icona trofeo centrale */}
              <div className="absolute inset-0 flex items-center justify-center p-2">
                <img
                  src="/images/icons/amici 100x100.png"
                  alt="Classifica"
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
            <p className="text-romagna-darkWood font-medium">{message}</p>
          </CardContent>
        </Card>

        {/* Skeleton delle posizioni della classifica */}
        {Array.from({ length: count }).map((_, index) => (
          <Card
            key={`leaderboard-skeleton-${index}`}
            className="border-2 border-amber-800/30 shadow-md bg-amber-50/80 backdrop-blur-sm opacity-60"
          >
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                {/* Posizione skeleton */}
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-200 rounded-full animate-pulse flex items-center justify-center">
                    <span className="text-sm font-bold text-yellow-600">
                      {index + 1}
                    </span>
                  </div>
                </div>

                {/* Avatar e badge livello skeleton */}
                <div className="relative flex-shrink-0">
                  <div className="w-10 h-10 bg-amber-200 rounded-full animate-pulse"></div>
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-amber-300 rounded-full animate-pulse"></div>
                </div>

                {/* Info giocatore skeleton */}
                <div className="flex-1 min-w-0">
                  <div className="w-20 h-4 bg-amber-200 rounded animate-pulse mb-1"></div>
                  <div className="w-16 h-3 bg-amber-200 rounded animate-pulse"></div>
                </div>

                {/* Statistiche skeleton */}
                <div className="text-right flex-shrink-0">
                  <div className="w-12 h-4 bg-amber-200 rounded animate-pulse mb-1"></div>
                  <div className="w-8 h-3 bg-amber-200 rounded animate-pulse"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </>
  );
};

export default LeaderboardLoadingSkeleton;

import{c as Ut}from"./vendor-COrNHRvO.js";var Ri={};/*!
 *  howler.js v2.2.4
 *  howlerjs.com
 *
 *  (c) 2013-2020, <PERSON> of GoldFire Studios
 *  goldfirestudios.com
 *
 *  MIT License
 */(function(r){(function(){var e=function(){this.init()};e.prototype={init:function(){var i=this||t;return i._counter=1e3,i._html5AudioPool=[],i.html5PoolSize=10,i._codecs={},i._howls=[],i._muted=!1,i._volume=1,i._canPlayEvent="canplaythrough",i._navigator=typeof window<"u"&&window.navigator?window.navigator:null,i.masterGain=null,i.noAudio=!1,i.usingWebAudio=!0,i.autoSuspend=!0,i.ctx=null,i.autoUnlock=!0,i._setup(),i},volume:function(i){var a=this||t;if(i=parseFloat(i),a.ctx||p(),typeof i<"u"&&i>=0&&i<=1){if(a._volume=i,a._muted)return a;a.usingWebAudio&&a.masterGain.gain.setValueAtTime(i,t.ctx.currentTime);for(var d=0;d<a._howls.length;d++)if(!a._howls[d]._webAudio)for(var f=a._howls[d]._getSoundIds(),_=0;_<f.length;_++){var g=a._howls[d]._soundById(f[_]);g&&g._node&&(g._node.volume=g._volume*i)}return a}return a._volume},mute:function(i){var a=this||t;a.ctx||p(),a._muted=i,a.usingWebAudio&&a.masterGain.gain.setValueAtTime(i?0:a._volume,t.ctx.currentTime);for(var d=0;d<a._howls.length;d++)if(!a._howls[d]._webAudio)for(var f=a._howls[d]._getSoundIds(),_=0;_<f.length;_++){var g=a._howls[d]._soundById(f[_]);g&&g._node&&(g._node.muted=i?!0:g._muted)}return a},stop:function(){for(var i=this||t,a=0;a<i._howls.length;a++)i._howls[a].stop();return i},unload:function(){for(var i=this||t,a=i._howls.length-1;a>=0;a--)i._howls[a].unload();return i.usingWebAudio&&i.ctx&&typeof i.ctx.close<"u"&&(i.ctx.close(),i.ctx=null,p()),i},codecs:function(i){return(this||t)._codecs[i.replace(/^x-/,"")]},_setup:function(){var i=this||t;if(i.state=i.ctx&&i.ctx.state||"suspended",i._autoSuspend(),!i.usingWebAudio)if(typeof Audio<"u")try{var a=new Audio;typeof a.oncanplaythrough>"u"&&(i._canPlayEvent="canplay")}catch{i.noAudio=!0}else i.noAudio=!0;try{var a=new Audio;a.muted&&(i.noAudio=!0)}catch{}return i.noAudio||i._setupCodecs(),i},_setupCodecs:function(){var i=this||t,a=null;try{a=typeof Audio<"u"?new Audio:null}catch{return i}if(!a||typeof a.canPlayType!="function")return i;var d=a.canPlayType("audio/mpeg;").replace(/^no$/,""),f=i._navigator?i._navigator.userAgent:"",_=f.match(/OPR\/(\d+)/g),g=_&&parseInt(_[0].split("/")[1],10)<33,m=f.indexOf("Safari")!==-1&&f.indexOf("Chrome")===-1,y=f.match(/Version\/(.*?) /),b=m&&y&&parseInt(y[1],10)<15;return i._codecs={mp3:!!(!g&&(d||a.canPlayType("audio/mp3;").replace(/^no$/,""))),mpeg:!!d,opus:!!a.canPlayType('audio/ogg; codecs="opus"').replace(/^no$/,""),ogg:!!a.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,""),oga:!!a.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,""),wav:!!(a.canPlayType('audio/wav; codecs="1"')||a.canPlayType("audio/wav")).replace(/^no$/,""),aac:!!a.canPlayType("audio/aac;").replace(/^no$/,""),caf:!!a.canPlayType("audio/x-caf;").replace(/^no$/,""),m4a:!!(a.canPlayType("audio/x-m4a;")||a.canPlayType("audio/m4a;")||a.canPlayType("audio/aac;")).replace(/^no$/,""),m4b:!!(a.canPlayType("audio/x-m4b;")||a.canPlayType("audio/m4b;")||a.canPlayType("audio/aac;")).replace(/^no$/,""),mp4:!!(a.canPlayType("audio/x-mp4;")||a.canPlayType("audio/mp4;")||a.canPlayType("audio/aac;")).replace(/^no$/,""),weba:!!(!b&&a.canPlayType('audio/webm; codecs="vorbis"').replace(/^no$/,"")),webm:!!(!b&&a.canPlayType('audio/webm; codecs="vorbis"').replace(/^no$/,"")),dolby:!!a.canPlayType('audio/mp4; codecs="ec-3"').replace(/^no$/,""),flac:!!(a.canPlayType("audio/x-flac;")||a.canPlayType("audio/flac;")).replace(/^no$/,"")},i},_unlockAudio:function(){var i=this||t;if(!(i._audioUnlocked||!i.ctx)){i._audioUnlocked=!1,i.autoUnlock=!1,!i._mobileUnloaded&&i.ctx.sampleRate!==44100&&(i._mobileUnloaded=!0,i.unload()),i._scratchBuffer=i.ctx.createBuffer(1,1,22050);var a=function(d){for(;i._html5AudioPool.length<i.html5PoolSize;)try{var f=new Audio;f._unlocked=!0,i._releaseHtml5Audio(f)}catch{i.noAudio=!0;break}for(var _=0;_<i._howls.length;_++)if(!i._howls[_]._webAudio)for(var g=i._howls[_]._getSoundIds(),m=0;m<g.length;m++){var y=i._howls[_]._soundById(g[m]);y&&y._node&&!y._node._unlocked&&(y._node._unlocked=!0,y._node.load())}i._autoResume();var b=i.ctx.createBufferSource();b.buffer=i._scratchBuffer,b.connect(i.ctx.destination),typeof b.start>"u"?b.noteOn(0):b.start(0),typeof i.ctx.resume=="function"&&i.ctx.resume(),b.onended=function(){b.disconnect(0),i._audioUnlocked=!0,document.removeEventListener("touchstart",a,!0),document.removeEventListener("touchend",a,!0),document.removeEventListener("click",a,!0),document.removeEventListener("keydown",a,!0);for(var S=0;S<i._howls.length;S++)i._howls[S]._emit("unlock")}};return document.addEventListener("touchstart",a,!0),document.addEventListener("touchend",a,!0),document.addEventListener("click",a,!0),document.addEventListener("keydown",a,!0),i}},_obtainHtml5Audio:function(){var i=this||t;if(i._html5AudioPool.length)return i._html5AudioPool.pop();var a=new Audio().play();return a&&typeof Promise<"u"&&(a instanceof Promise||typeof a.then=="function")&&a.catch(function(){console.warn("HTML5 Audio pool exhausted, returning potentially locked audio object.")}),new Audio},_releaseHtml5Audio:function(i){var a=this||t;return i._unlocked&&a._html5AudioPool.push(i),a},_autoSuspend:function(){var i=this;if(!(!i.autoSuspend||!i.ctx||typeof i.ctx.suspend>"u"||!t.usingWebAudio)){for(var a=0;a<i._howls.length;a++)if(i._howls[a]._webAudio){for(var d=0;d<i._howls[a]._sounds.length;d++)if(!i._howls[a]._sounds[d]._paused)return i}return i._suspendTimer&&clearTimeout(i._suspendTimer),i._suspendTimer=setTimeout(function(){if(i.autoSuspend){i._suspendTimer=null,i.state="suspending";var f=function(){i.state="suspended",i._resumeAfterSuspend&&(delete i._resumeAfterSuspend,i._autoResume())};i.ctx.suspend().then(f,f)}},3e4),i}},_autoResume:function(){var i=this;if(!(!i.ctx||typeof i.ctx.resume>"u"||!t.usingWebAudio))return i.state==="running"&&i.ctx.state!=="interrupted"&&i._suspendTimer?(clearTimeout(i._suspendTimer),i._suspendTimer=null):i.state==="suspended"||i.state==="running"&&i.ctx.state==="interrupted"?(i.ctx.resume().then(function(){i.state="running";for(var a=0;a<i._howls.length;a++)i._howls[a]._emit("resume")}),i._suspendTimer&&(clearTimeout(i._suspendTimer),i._suspendTimer=null)):i.state==="suspending"&&(i._resumeAfterSuspend=!0),i}};var t=new e,n=function(i){var a=this;if(!i.src||i.src.length===0){console.error("An array of source files must be passed with any new Howl.");return}a.init(i)};n.prototype={init:function(i){var a=this;return t.ctx||p(),a._autoplay=i.autoplay||!1,a._format=typeof i.format!="string"?i.format:[i.format],a._html5=i.html5||!1,a._muted=i.mute||!1,a._loop=i.loop||!1,a._pool=i.pool||5,a._preload=typeof i.preload=="boolean"||i.preload==="metadata"?i.preload:!0,a._rate=i.rate||1,a._sprite=i.sprite||{},a._src=typeof i.src!="string"?i.src:[i.src],a._volume=i.volume!==void 0?i.volume:1,a._xhr={method:i.xhr&&i.xhr.method?i.xhr.method:"GET",headers:i.xhr&&i.xhr.headers?i.xhr.headers:null,withCredentials:i.xhr&&i.xhr.withCredentials?i.xhr.withCredentials:!1},a._duration=0,a._state="unloaded",a._sounds=[],a._endTimers={},a._queue=[],a._playLock=!1,a._onend=i.onend?[{fn:i.onend}]:[],a._onfade=i.onfade?[{fn:i.onfade}]:[],a._onload=i.onload?[{fn:i.onload}]:[],a._onloaderror=i.onloaderror?[{fn:i.onloaderror}]:[],a._onplayerror=i.onplayerror?[{fn:i.onplayerror}]:[],a._onpause=i.onpause?[{fn:i.onpause}]:[],a._onplay=i.onplay?[{fn:i.onplay}]:[],a._onstop=i.onstop?[{fn:i.onstop}]:[],a._onmute=i.onmute?[{fn:i.onmute}]:[],a._onvolume=i.onvolume?[{fn:i.onvolume}]:[],a._onrate=i.onrate?[{fn:i.onrate}]:[],a._onseek=i.onseek?[{fn:i.onseek}]:[],a._onunlock=i.onunlock?[{fn:i.onunlock}]:[],a._onresume=[],a._webAudio=t.usingWebAudio&&!a._html5,typeof t.ctx<"u"&&t.ctx&&t.autoUnlock&&t._unlockAudio(),t._howls.push(a),a._autoplay&&a._queue.push({event:"play",action:function(){a.play()}}),a._preload&&a._preload!=="none"&&a.load(),a},load:function(){var i=this,a=null;if(t.noAudio){i._emit("loaderror",null,"No audio support.");return}typeof i._src=="string"&&(i._src=[i._src]);for(var d=0;d<i._src.length;d++){var f,_;if(i._format&&i._format[d])f=i._format[d];else{if(_=i._src[d],typeof _!="string"){i._emit("loaderror",null,"Non-string found in selected audio sources - ignoring.");continue}f=/^data:audio\/([^;,]+);/i.exec(_),f||(f=/\.([^.]+)$/.exec(_.split("?",1)[0])),f&&(f=f[1].toLowerCase())}if(f||console.warn('No file extension was found. Consider using the "format" property or specify an extension.'),f&&t.codecs(f)){a=i._src[d];break}}if(!a){i._emit("loaderror",null,"No codec support for selected audio sources.");return}return i._src=a,i._state="loading",window.location.protocol==="https:"&&a.slice(0,5)==="http:"&&(i._html5=!0,i._webAudio=!1),new s(i),i._webAudio&&c(i),i},play:function(i,a){var d=this,f=null;if(typeof i=="number")f=i,i=null;else{if(typeof i=="string"&&d._state==="loaded"&&!d._sprite[i])return null;if(typeof i>"u"&&(i="__default",!d._playLock)){for(var _=0,g=0;g<d._sounds.length;g++)d._sounds[g]._paused&&!d._sounds[g]._ended&&(_++,f=d._sounds[g]._id);_===1?i=null:f=null}}var m=f?d._soundById(f):d._inactiveSound();if(!m)return null;if(f&&!i&&(i=m._sprite||"__default"),d._state!=="loaded"){m._sprite=i,m._ended=!1;var y=m._id;return d._queue.push({event:"play",action:function(){d.play(y)}}),y}if(f&&!m._paused)return a||d._loadQueue("play"),m._id;d._webAudio&&t._autoResume();var b=Math.max(0,m._seek>0?m._seek:d._sprite[i][0]/1e3),S=Math.max(0,(d._sprite[i][0]+d._sprite[i][1])/1e3-b),v=S*1e3/Math.abs(m._rate),w=d._sprite[i][0]/1e3,A=(d._sprite[i][0]+d._sprite[i][1])/1e3;m._sprite=i,m._ended=!1;var T=function(){m._paused=!1,m._seek=b,m._start=w,m._stop=A,m._loop=!!(m._loop||d._sprite[i][2])};if(b>=A){d._ended(m);return}var x=m._node;if(d._webAudio){var O=function(){d._playLock=!1,T(),d._refreshBuffer(m);var N=m._muted||d._muted?0:m._volume;x.gain.setValueAtTime(N,t.ctx.currentTime),m._playStart=t.ctx.currentTime,typeof x.bufferSource.start>"u"?m._loop?x.bufferSource.noteGrainOn(0,b,86400):x.bufferSource.noteGrainOn(0,b,S):m._loop?x.bufferSource.start(0,b,86400):x.bufferSource.start(0,b,S),v!==1/0&&(d._endTimers[m._id]=setTimeout(d._ended.bind(d,m),v)),a||setTimeout(function(){d._emit("play",m._id),d._loadQueue()},0)};t.state==="running"&&t.ctx.state!=="interrupted"?O():(d._playLock=!0,d.once("resume",O),d._clearTimer(m._id))}else{var C=function(){x.currentTime=b,x.muted=m._muted||d._muted||t._muted||x.muted,x.volume=m._volume*t.volume(),x.playbackRate=m._rate;try{var N=x.play();if(N&&typeof Promise<"u"&&(N instanceof Promise||typeof N.then=="function")?(d._playLock=!0,T(),N.then(function(){d._playLock=!1,x._unlocked=!0,a?d._loadQueue():d._emit("play",m._id)}).catch(function(){d._playLock=!1,d._emit("playerror",m._id,"Playback was unable to start. This is most commonly an issue on mobile devices and Chrome where playback was not within a user interaction."),m._ended=!0,m._paused=!0})):a||(d._playLock=!1,T(),d._emit("play",m._id)),x.playbackRate=m._rate,x.paused){d._emit("playerror",m._id,"Playback was unable to start. This is most commonly an issue on mobile devices and Chrome where playback was not within a user interaction.");return}i!=="__default"||m._loop?d._endTimers[m._id]=setTimeout(d._ended.bind(d,m),v):(d._endTimers[m._id]=function(){d._ended(m),x.removeEventListener("ended",d._endTimers[m._id],!1)},x.addEventListener("ended",d._endTimers[m._id],!1))}catch(P){d._emit("playerror",m._id,P)}};x.src==="data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA"&&(x.src=d._src,x.load());var k=window&&window.ejecta||!x.readyState&&t._navigator.isCocoonJS;if(x.readyState>=3||k)C();else{d._playLock=!0,d._state="loading";var I=function(){d._state="loaded",C(),x.removeEventListener(t._canPlayEvent,I,!1)};x.addEventListener(t._canPlayEvent,I,!1),d._clearTimer(m._id)}}return m._id},pause:function(i){var a=this;if(a._state!=="loaded"||a._playLock)return a._queue.push({event:"pause",action:function(){a.pause(i)}}),a;for(var d=a._getSoundIds(i),f=0;f<d.length;f++){a._clearTimer(d[f]);var _=a._soundById(d[f]);if(_&&!_._paused&&(_._seek=a.seek(d[f]),_._rateSeek=0,_._paused=!0,a._stopFade(d[f]),_._node))if(a._webAudio){if(!_._node.bufferSource)continue;typeof _._node.bufferSource.stop>"u"?_._node.bufferSource.noteOff(0):_._node.bufferSource.stop(0),a._cleanBuffer(_._node)}else(!isNaN(_._node.duration)||_._node.duration===1/0)&&_._node.pause();arguments[1]||a._emit("pause",_?_._id:null)}return a},stop:function(i,a){var d=this;if(d._state!=="loaded"||d._playLock)return d._queue.push({event:"stop",action:function(){d.stop(i)}}),d;for(var f=d._getSoundIds(i),_=0;_<f.length;_++){d._clearTimer(f[_]);var g=d._soundById(f[_]);g&&(g._seek=g._start||0,g._rateSeek=0,g._paused=!0,g._ended=!0,d._stopFade(f[_]),g._node&&(d._webAudio?g._node.bufferSource&&(typeof g._node.bufferSource.stop>"u"?g._node.bufferSource.noteOff(0):g._node.bufferSource.stop(0),d._cleanBuffer(g._node)):(!isNaN(g._node.duration)||g._node.duration===1/0)&&(g._node.currentTime=g._start||0,g._node.pause(),g._node.duration===1/0&&d._clearSound(g._node))),a||d._emit("stop",g._id))}return d},mute:function(i,a){var d=this;if(d._state!=="loaded"||d._playLock)return d._queue.push({event:"mute",action:function(){d.mute(i,a)}}),d;if(typeof a>"u")if(typeof i=="boolean")d._muted=i;else return d._muted;for(var f=d._getSoundIds(a),_=0;_<f.length;_++){var g=d._soundById(f[_]);g&&(g._muted=i,g._interval&&d._stopFade(g._id),d._webAudio&&g._node?g._node.gain.setValueAtTime(i?0:g._volume,t.ctx.currentTime):g._node&&(g._node.muted=t._muted?!0:i),d._emit("mute",g._id))}return d},volume:function(){var i=this,a=arguments,d,f;if(a.length===0)return i._volume;if(a.length===1||a.length===2&&typeof a[1]>"u"){var _=i._getSoundIds(),g=_.indexOf(a[0]);g>=0?f=parseInt(a[0],10):d=parseFloat(a[0])}else a.length>=2&&(d=parseFloat(a[0]),f=parseInt(a[1],10));var m;if(typeof d<"u"&&d>=0&&d<=1){if(i._state!=="loaded"||i._playLock)return i._queue.push({event:"volume",action:function(){i.volume.apply(i,a)}}),i;typeof f>"u"&&(i._volume=d),f=i._getSoundIds(f);for(var y=0;y<f.length;y++)m=i._soundById(f[y]),m&&(m._volume=d,a[2]||i._stopFade(f[y]),i._webAudio&&m._node&&!m._muted?m._node.gain.setValueAtTime(d,t.ctx.currentTime):m._node&&!m._muted&&(m._node.volume=d*t.volume()),i._emit("volume",m._id))}else return m=f?i._soundById(f):i._sounds[0],m?m._volume:0;return i},fade:function(i,a,d,f){var _=this;if(_._state!=="loaded"||_._playLock)return _._queue.push({event:"fade",action:function(){_.fade(i,a,d,f)}}),_;i=Math.min(Math.max(0,parseFloat(i)),1),a=Math.min(Math.max(0,parseFloat(a)),1),d=parseFloat(d),_.volume(i,f);for(var g=_._getSoundIds(f),m=0;m<g.length;m++){var y=_._soundById(g[m]);if(y){if(f||_._stopFade(g[m]),_._webAudio&&!y._muted){var b=t.ctx.currentTime,S=b+d/1e3;y._volume=i,y._node.gain.setValueAtTime(i,b),y._node.gain.linearRampToValueAtTime(a,S)}_._startFadeInterval(y,i,a,d,g[m],typeof f>"u")}}return _},_startFadeInterval:function(i,a,d,f,_,g){var m=this,y=a,b=d-a,S=Math.abs(b/.01),v=Math.max(4,S>0?f/S:f),w=Date.now();i._fadeTo=d,i._interval=setInterval(function(){var A=(Date.now()-w)/f;w=Date.now(),y+=b*A,y=Math.round(y*100)/100,b<0?y=Math.max(d,y):y=Math.min(d,y),m._webAudio?i._volume=y:m.volume(y,i._id,!0),g&&(m._volume=y),(d<a&&y<=d||d>a&&y>=d)&&(clearInterval(i._interval),i._interval=null,i._fadeTo=null,m.volume(d,i._id),m._emit("fade",i._id))},v)},_stopFade:function(i){var a=this,d=a._soundById(i);return d&&d._interval&&(a._webAudio&&d._node.gain.cancelScheduledValues(t.ctx.currentTime),clearInterval(d._interval),d._interval=null,a.volume(d._fadeTo,i),d._fadeTo=null,a._emit("fade",i)),a},loop:function(){var i=this,a=arguments,d,f,_;if(a.length===0)return i._loop;if(a.length===1)if(typeof a[0]=="boolean")d=a[0],i._loop=d;else return _=i._soundById(parseInt(a[0],10)),_?_._loop:!1;else a.length===2&&(d=a[0],f=parseInt(a[1],10));for(var g=i._getSoundIds(f),m=0;m<g.length;m++)_=i._soundById(g[m]),_&&(_._loop=d,i._webAudio&&_._node&&_._node.bufferSource&&(_._node.bufferSource.loop=d,d&&(_._node.bufferSource.loopStart=_._start||0,_._node.bufferSource.loopEnd=_._stop,i.playing(g[m])&&(i.pause(g[m],!0),i.play(g[m],!0)))));return i},rate:function(){var i=this,a=arguments,d,f;if(a.length===0)f=i._sounds[0]._id;else if(a.length===1){var _=i._getSoundIds(),g=_.indexOf(a[0]);g>=0?f=parseInt(a[0],10):d=parseFloat(a[0])}else a.length===2&&(d=parseFloat(a[0]),f=parseInt(a[1],10));var m;if(typeof d=="number"){if(i._state!=="loaded"||i._playLock)return i._queue.push({event:"rate",action:function(){i.rate.apply(i,a)}}),i;typeof f>"u"&&(i._rate=d),f=i._getSoundIds(f);for(var y=0;y<f.length;y++)if(m=i._soundById(f[y]),m){i.playing(f[y])&&(m._rateSeek=i.seek(f[y]),m._playStart=i._webAudio?t.ctx.currentTime:m._playStart),m._rate=d,i._webAudio&&m._node&&m._node.bufferSource?m._node.bufferSource.playbackRate.setValueAtTime(d,t.ctx.currentTime):m._node&&(m._node.playbackRate=d);var b=i.seek(f[y]),S=(i._sprite[m._sprite][0]+i._sprite[m._sprite][1])/1e3-b,v=S*1e3/Math.abs(m._rate);(i._endTimers[f[y]]||!m._paused)&&(i._clearTimer(f[y]),i._endTimers[f[y]]=setTimeout(i._ended.bind(i,m),v)),i._emit("rate",m._id)}}else return m=i._soundById(f),m?m._rate:i._rate;return i},seek:function(){var i=this,a=arguments,d,f;if(a.length===0)i._sounds.length&&(f=i._sounds[0]._id);else if(a.length===1){var _=i._getSoundIds(),g=_.indexOf(a[0]);g>=0?f=parseInt(a[0],10):i._sounds.length&&(f=i._sounds[0]._id,d=parseFloat(a[0]))}else a.length===2&&(d=parseFloat(a[0]),f=parseInt(a[1],10));if(typeof f>"u")return 0;if(typeof d=="number"&&(i._state!=="loaded"||i._playLock))return i._queue.push({event:"seek",action:function(){i.seek.apply(i,a)}}),i;var m=i._soundById(f);if(m)if(typeof d=="number"&&d>=0){var y=i.playing(f);y&&i.pause(f,!0),m._seek=d,m._ended=!1,i._clearTimer(f),!i._webAudio&&m._node&&!isNaN(m._node.duration)&&(m._node.currentTime=d);var b=function(){y&&i.play(f,!0),i._emit("seek",f)};if(y&&!i._webAudio){var S=function(){i._playLock?setTimeout(S,0):b()};setTimeout(S,0)}else b()}else if(i._webAudio){var v=i.playing(f)?t.ctx.currentTime-m._playStart:0,w=m._rateSeek?m._rateSeek-m._seek:0;return m._seek+(w+v*Math.abs(m._rate))}else return m._node.currentTime;return i},playing:function(i){var a=this;if(typeof i=="number"){var d=a._soundById(i);return d?!d._paused:!1}for(var f=0;f<a._sounds.length;f++)if(!a._sounds[f]._paused)return!0;return!1},duration:function(i){var a=this,d=a._duration,f=a._soundById(i);return f&&(d=a._sprite[f._sprite][1]/1e3),d},state:function(){return this._state},unload:function(){for(var i=this,a=i._sounds,d=0;d<a.length;d++)a[d]._paused||i.stop(a[d]._id),i._webAudio||(i._clearSound(a[d]._node),a[d]._node.removeEventListener("error",a[d]._errorFn,!1),a[d]._node.removeEventListener(t._canPlayEvent,a[d]._loadFn,!1),a[d]._node.removeEventListener("ended",a[d]._endFn,!1),t._releaseHtml5Audio(a[d]._node)),delete a[d]._node,i._clearTimer(a[d]._id);var f=t._howls.indexOf(i);f>=0&&t._howls.splice(f,1);var _=!0;for(d=0;d<t._howls.length;d++)if(t._howls[d]._src===i._src||i._src.indexOf(t._howls[d]._src)>=0){_=!1;break}return o&&_&&delete o[i._src],t.noAudio=!1,i._state="unloaded",i._sounds=[],i=null,null},on:function(i,a,d,f){var _=this,g=_["_on"+i];return typeof a=="function"&&g.push(f?{id:d,fn:a,once:f}:{id:d,fn:a}),_},off:function(i,a,d){var f=this,_=f["_on"+i],g=0;if(typeof a=="number"&&(d=a,a=null),a||d)for(g=0;g<_.length;g++){var m=d===_[g].id;if(a===_[g].fn&&m||!a&&m){_.splice(g,1);break}}else if(i)f["_on"+i]=[];else{var y=Object.keys(f);for(g=0;g<y.length;g++)y[g].indexOf("_on")===0&&Array.isArray(f[y[g]])&&(f[y[g]]=[])}return f},once:function(i,a,d){var f=this;return f.on(i,a,d,1),f},_emit:function(i,a,d){for(var f=this,_=f["_on"+i],g=_.length-1;g>=0;g--)(!_[g].id||_[g].id===a||i==="load")&&(setTimeout((function(m){m.call(this,a,d)}).bind(f,_[g].fn),0),_[g].once&&f.off(i,_[g].fn,_[g].id));return f._loadQueue(i),f},_loadQueue:function(i){var a=this;if(a._queue.length>0){var d=a._queue[0];d.event===i&&(a._queue.shift(),a._loadQueue()),i||d.action()}return a},_ended:function(i){var a=this,d=i._sprite;if(!a._webAudio&&i._node&&!i._node.paused&&!i._node.ended&&i._node.currentTime<i._stop)return setTimeout(a._ended.bind(a,i),100),a;var f=!!(i._loop||a._sprite[d][2]);if(a._emit("end",i._id),!a._webAudio&&f&&a.stop(i._id,!0).play(i._id),a._webAudio&&f){a._emit("play",i._id),i._seek=i._start||0,i._rateSeek=0,i._playStart=t.ctx.currentTime;var _=(i._stop-i._start)*1e3/Math.abs(i._rate);a._endTimers[i._id]=setTimeout(a._ended.bind(a,i),_)}return a._webAudio&&!f&&(i._paused=!0,i._ended=!0,i._seek=i._start||0,i._rateSeek=0,a._clearTimer(i._id),a._cleanBuffer(i._node),t._autoSuspend()),!a._webAudio&&!f&&a.stop(i._id,!0),a},_clearTimer:function(i){var a=this;if(a._endTimers[i]){if(typeof a._endTimers[i]!="function")clearTimeout(a._endTimers[i]);else{var d=a._soundById(i);d&&d._node&&d._node.removeEventListener("ended",a._endTimers[i],!1)}delete a._endTimers[i]}return a},_soundById:function(i){for(var a=this,d=0;d<a._sounds.length;d++)if(i===a._sounds[d]._id)return a._sounds[d];return null},_inactiveSound:function(){var i=this;i._drain();for(var a=0;a<i._sounds.length;a++)if(i._sounds[a]._ended)return i._sounds[a].reset();return new s(i)},_drain:function(){var i=this,a=i._pool,d=0,f=0;if(!(i._sounds.length<a)){for(f=0;f<i._sounds.length;f++)i._sounds[f]._ended&&d++;for(f=i._sounds.length-1;f>=0;f--){if(d<=a)return;i._sounds[f]._ended&&(i._webAudio&&i._sounds[f]._node&&i._sounds[f]._node.disconnect(0),i._sounds.splice(f,1),d--)}}},_getSoundIds:function(i){var a=this;if(typeof i>"u"){for(var d=[],f=0;f<a._sounds.length;f++)d.push(a._sounds[f]._id);return d}else return[i]},_refreshBuffer:function(i){var a=this;return i._node.bufferSource=t.ctx.createBufferSource(),i._node.bufferSource.buffer=o[a._src],i._panner?i._node.bufferSource.connect(i._panner):i._node.bufferSource.connect(i._node),i._node.bufferSource.loop=i._loop,i._loop&&(i._node.bufferSource.loopStart=i._start||0,i._node.bufferSource.loopEnd=i._stop||0),i._node.bufferSource.playbackRate.setValueAtTime(i._rate,t.ctx.currentTime),a},_cleanBuffer:function(i){var a=this,d=t._navigator&&t._navigator.vendor.indexOf("Apple")>=0;if(!i.bufferSource)return a;if(t._scratchBuffer&&i.bufferSource&&(i.bufferSource.onended=null,i.bufferSource.disconnect(0),d))try{i.bufferSource.buffer=t._scratchBuffer}catch{}return i.bufferSource=null,a},_clearSound:function(i){var a=/MSIE |Trident\//.test(t._navigator&&t._navigator.userAgent);a||(i.src="data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA")}};var s=function(i){this._parent=i,this.init()};s.prototype={init:function(){var i=this,a=i._parent;return i._muted=a._muted,i._loop=a._loop,i._volume=a._volume,i._rate=a._rate,i._seek=0,i._paused=!0,i._ended=!0,i._sprite="__default",i._id=++t._counter,a._sounds.push(i),i.create(),i},create:function(){var i=this,a=i._parent,d=t._muted||i._muted||i._parent._muted?0:i._volume;return a._webAudio?(i._node=typeof t.ctx.createGain>"u"?t.ctx.createGainNode():t.ctx.createGain(),i._node.gain.setValueAtTime(d,t.ctx.currentTime),i._node.paused=!0,i._node.connect(t.masterGain)):t.noAudio||(i._node=t._obtainHtml5Audio(),i._errorFn=i._errorListener.bind(i),i._node.addEventListener("error",i._errorFn,!1),i._loadFn=i._loadListener.bind(i),i._node.addEventListener(t._canPlayEvent,i._loadFn,!1),i._endFn=i._endListener.bind(i),i._node.addEventListener("ended",i._endFn,!1),i._node.src=a._src,i._node.preload=a._preload===!0?"auto":a._preload,i._node.volume=d*t.volume(),i._node.load()),i},reset:function(){var i=this,a=i._parent;return i._muted=a._muted,i._loop=a._loop,i._volume=a._volume,i._rate=a._rate,i._seek=0,i._rateSeek=0,i._paused=!0,i._ended=!0,i._sprite="__default",i._id=++t._counter,i},_errorListener:function(){var i=this;i._parent._emit("loaderror",i._id,i._node.error?i._node.error.code:0),i._node.removeEventListener("error",i._errorFn,!1)},_loadListener:function(){var i=this,a=i._parent;a._duration=Math.ceil(i._node.duration*10)/10,Object.keys(a._sprite).length===0&&(a._sprite={__default:[0,a._duration*1e3]}),a._state!=="loaded"&&(a._state="loaded",a._emit("load"),a._loadQueue()),i._node.removeEventListener(t._canPlayEvent,i._loadFn,!1)},_endListener:function(){var i=this,a=i._parent;a._duration===1/0&&(a._duration=Math.ceil(i._node.duration*10)/10,a._sprite.__default[1]===1/0&&(a._sprite.__default[1]=a._duration*1e3),a._ended(i)),i._node.removeEventListener("ended",i._endFn,!1)}};var o={},c=function(i){var a=i._src;if(o[a]){i._duration=o[a].duration,h(i);return}if(/^data:[^;]+;base64,/.test(a)){for(var d=atob(a.split(",")[1]),f=new Uint8Array(d.length),_=0;_<d.length;++_)f[_]=d.charCodeAt(_);l(f.buffer,i)}else{var g=new XMLHttpRequest;g.open(i._xhr.method,a,!0),g.withCredentials=i._xhr.withCredentials,g.responseType="arraybuffer",i._xhr.headers&&Object.keys(i._xhr.headers).forEach(function(m){g.setRequestHeader(m,i._xhr.headers[m])}),g.onload=function(){var m=(g.status+"")[0];if(m!=="0"&&m!=="2"&&m!=="3"){i._emit("loaderror",null,"Failed loading audio file with status: "+g.status+".");return}l(g.response,i)},g.onerror=function(){i._webAudio&&(i._html5=!0,i._webAudio=!1,i._sounds=[],delete o[a],i.load())},u(g)}},u=function(i){try{i.send()}catch{i.onerror()}},l=function(i,a){var d=function(){a._emit("loaderror",null,"Decoding audio data failed.")},f=function(_){_&&a._sounds.length>0?(o[a._src]=_,h(a,_)):d()};typeof Promise<"u"&&t.ctx.decodeAudioData.length===1?t.ctx.decodeAudioData(i).then(f).catch(d):t.ctx.decodeAudioData(i,f,d)},h=function(i,a){a&&!i._duration&&(i._duration=a.duration),Object.keys(i._sprite).length===0&&(i._sprite={__default:[0,i._duration*1e3]}),i._state!=="loaded"&&(i._state="loaded",i._emit("load"),i._loadQueue())},p=function(){if(t.usingWebAudio){try{typeof AudioContext<"u"?t.ctx=new AudioContext:typeof webkitAudioContext<"u"?t.ctx=new webkitAudioContext:t.usingWebAudio=!1}catch{t.usingWebAudio=!1}t.ctx||(t.usingWebAudio=!1);var i=/iP(hone|od|ad)/.test(t._navigator&&t._navigator.platform),a=t._navigator&&t._navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/),d=a?parseInt(a[1],10):null;if(i&&d&&d<9){var f=/safari/.test(t._navigator&&t._navigator.userAgent.toLowerCase());t._navigator&&!f&&(t.usingWebAudio=!1)}t.usingWebAudio&&(t.masterGain=typeof t.ctx.createGain>"u"?t.ctx.createGainNode():t.ctx.createGain(),t.masterGain.gain.setValueAtTime(t._muted?0:t._volume,t.ctx.currentTime),t.masterGain.connect(t.ctx.destination)),t._setup()}};r.Howler=t,r.Howl=n,typeof Ut<"u"?(Ut.HowlerGlobal=e,Ut.Howler=t,Ut.Howl=n,Ut.Sound=s):typeof window<"u"&&(window.HowlerGlobal=e,window.Howler=t,window.Howl=n,window.Sound=s)})();/*!
 *  Spatial Plugin - Adds support for stereo and 3D audio where Web Audio is supported.
 *  
 *  howler.js v2.2.4
 *  howlerjs.com
 *
 *  (c) 2013-2020, James Simpson of GoldFire Studios
 *  goldfirestudios.com
 *
 *  MIT License
 */(function(){HowlerGlobal.prototype._pos=[0,0,0],HowlerGlobal.prototype._orientation=[0,0,-1,0,1,0],HowlerGlobal.prototype.stereo=function(t){var n=this;if(!n.ctx||!n.ctx.listener)return n;for(var s=n._howls.length-1;s>=0;s--)n._howls[s].stereo(t);return n},HowlerGlobal.prototype.pos=function(t,n,s){var o=this;if(!o.ctx||!o.ctx.listener)return o;if(n=typeof n!="number"?o._pos[1]:n,s=typeof s!="number"?o._pos[2]:s,typeof t=="number")o._pos=[t,n,s],typeof o.ctx.listener.positionX<"u"?(o.ctx.listener.positionX.setTargetAtTime(o._pos[0],Howler.ctx.currentTime,.1),o.ctx.listener.positionY.setTargetAtTime(o._pos[1],Howler.ctx.currentTime,.1),o.ctx.listener.positionZ.setTargetAtTime(o._pos[2],Howler.ctx.currentTime,.1)):o.ctx.listener.setPosition(o._pos[0],o._pos[1],o._pos[2]);else return o._pos;return o},HowlerGlobal.prototype.orientation=function(t,n,s,o,c,u){var l=this;if(!l.ctx||!l.ctx.listener)return l;var h=l._orientation;if(n=typeof n!="number"?h[1]:n,s=typeof s!="number"?h[2]:s,o=typeof o!="number"?h[3]:o,c=typeof c!="number"?h[4]:c,u=typeof u!="number"?h[5]:u,typeof t=="number")l._orientation=[t,n,s,o,c,u],typeof l.ctx.listener.forwardX<"u"?(l.ctx.listener.forwardX.setTargetAtTime(t,Howler.ctx.currentTime,.1),l.ctx.listener.forwardY.setTargetAtTime(n,Howler.ctx.currentTime,.1),l.ctx.listener.forwardZ.setTargetAtTime(s,Howler.ctx.currentTime,.1),l.ctx.listener.upX.setTargetAtTime(o,Howler.ctx.currentTime,.1),l.ctx.listener.upY.setTargetAtTime(c,Howler.ctx.currentTime,.1),l.ctx.listener.upZ.setTargetAtTime(u,Howler.ctx.currentTime,.1)):l.ctx.listener.setOrientation(t,n,s,o,c,u);else return h;return l},Howl.prototype.init=function(t){return function(n){var s=this;return s._orientation=n.orientation||[1,0,0],s._stereo=n.stereo||null,s._pos=n.pos||null,s._pannerAttr={coneInnerAngle:typeof n.coneInnerAngle<"u"?n.coneInnerAngle:360,coneOuterAngle:typeof n.coneOuterAngle<"u"?n.coneOuterAngle:360,coneOuterGain:typeof n.coneOuterGain<"u"?n.coneOuterGain:0,distanceModel:typeof n.distanceModel<"u"?n.distanceModel:"inverse",maxDistance:typeof n.maxDistance<"u"?n.maxDistance:1e4,panningModel:typeof n.panningModel<"u"?n.panningModel:"HRTF",refDistance:typeof n.refDistance<"u"?n.refDistance:1,rolloffFactor:typeof n.rolloffFactor<"u"?n.rolloffFactor:1},s._onstereo=n.onstereo?[{fn:n.onstereo}]:[],s._onpos=n.onpos?[{fn:n.onpos}]:[],s._onorientation=n.onorientation?[{fn:n.onorientation}]:[],t.call(this,n)}}(Howl.prototype.init),Howl.prototype.stereo=function(t,n){var s=this;if(!s._webAudio)return s;if(s._state!=="loaded")return s._queue.push({event:"stereo",action:function(){s.stereo(t,n)}}),s;var o=typeof Howler.ctx.createStereoPanner>"u"?"spatial":"stereo";if(typeof n>"u")if(typeof t=="number")s._stereo=t,s._pos=[t,0,0];else return s._stereo;for(var c=s._getSoundIds(n),u=0;u<c.length;u++){var l=s._soundById(c[u]);if(l)if(typeof t=="number")l._stereo=t,l._pos=[t,0,0],l._node&&(l._pannerAttr.panningModel="equalpower",(!l._panner||!l._panner.pan)&&e(l,o),o==="spatial"?typeof l._panner.positionX<"u"?(l._panner.positionX.setValueAtTime(t,Howler.ctx.currentTime),l._panner.positionY.setValueAtTime(0,Howler.ctx.currentTime),l._panner.positionZ.setValueAtTime(0,Howler.ctx.currentTime)):l._panner.setPosition(t,0,0):l._panner.pan.setValueAtTime(t,Howler.ctx.currentTime)),s._emit("stereo",l._id);else return l._stereo}return s},Howl.prototype.pos=function(t,n,s,o){var c=this;if(!c._webAudio)return c;if(c._state!=="loaded")return c._queue.push({event:"pos",action:function(){c.pos(t,n,s,o)}}),c;if(n=typeof n!="number"?0:n,s=typeof s!="number"?-.5:s,typeof o>"u")if(typeof t=="number")c._pos=[t,n,s];else return c._pos;for(var u=c._getSoundIds(o),l=0;l<u.length;l++){var h=c._soundById(u[l]);if(h)if(typeof t=="number")h._pos=[t,n,s],h._node&&((!h._panner||h._panner.pan)&&e(h,"spatial"),typeof h._panner.positionX<"u"?(h._panner.positionX.setValueAtTime(t,Howler.ctx.currentTime),h._panner.positionY.setValueAtTime(n,Howler.ctx.currentTime),h._panner.positionZ.setValueAtTime(s,Howler.ctx.currentTime)):h._panner.setPosition(t,n,s)),c._emit("pos",h._id);else return h._pos}return c},Howl.prototype.orientation=function(t,n,s,o){var c=this;if(!c._webAudio)return c;if(c._state!=="loaded")return c._queue.push({event:"orientation",action:function(){c.orientation(t,n,s,o)}}),c;if(n=typeof n!="number"?c._orientation[1]:n,s=typeof s!="number"?c._orientation[2]:s,typeof o>"u")if(typeof t=="number")c._orientation=[t,n,s];else return c._orientation;for(var u=c._getSoundIds(o),l=0;l<u.length;l++){var h=c._soundById(u[l]);if(h)if(typeof t=="number")h._orientation=[t,n,s],h._node&&(h._panner||(h._pos||(h._pos=c._pos||[0,0,-.5]),e(h,"spatial")),typeof h._panner.orientationX<"u"?(h._panner.orientationX.setValueAtTime(t,Howler.ctx.currentTime),h._panner.orientationY.setValueAtTime(n,Howler.ctx.currentTime),h._panner.orientationZ.setValueAtTime(s,Howler.ctx.currentTime)):h._panner.setOrientation(t,n,s)),c._emit("orientation",h._id);else return h._orientation}return c},Howl.prototype.pannerAttr=function(){var t=this,n=arguments,s,o,c;if(!t._webAudio)return t;if(n.length===0)return t._pannerAttr;if(n.length===1)if(typeof n[0]=="object")s=n[0],typeof o>"u"&&(s.pannerAttr||(s.pannerAttr={coneInnerAngle:s.coneInnerAngle,coneOuterAngle:s.coneOuterAngle,coneOuterGain:s.coneOuterGain,distanceModel:s.distanceModel,maxDistance:s.maxDistance,refDistance:s.refDistance,rolloffFactor:s.rolloffFactor,panningModel:s.panningModel}),t._pannerAttr={coneInnerAngle:typeof s.pannerAttr.coneInnerAngle<"u"?s.pannerAttr.coneInnerAngle:t._coneInnerAngle,coneOuterAngle:typeof s.pannerAttr.coneOuterAngle<"u"?s.pannerAttr.coneOuterAngle:t._coneOuterAngle,coneOuterGain:typeof s.pannerAttr.coneOuterGain<"u"?s.pannerAttr.coneOuterGain:t._coneOuterGain,distanceModel:typeof s.pannerAttr.distanceModel<"u"?s.pannerAttr.distanceModel:t._distanceModel,maxDistance:typeof s.pannerAttr.maxDistance<"u"?s.pannerAttr.maxDistance:t._maxDistance,refDistance:typeof s.pannerAttr.refDistance<"u"?s.pannerAttr.refDistance:t._refDistance,rolloffFactor:typeof s.pannerAttr.rolloffFactor<"u"?s.pannerAttr.rolloffFactor:t._rolloffFactor,panningModel:typeof s.pannerAttr.panningModel<"u"?s.pannerAttr.panningModel:t._panningModel});else return c=t._soundById(parseInt(n[0],10)),c?c._pannerAttr:t._pannerAttr;else n.length===2&&(s=n[0],o=parseInt(n[1],10));for(var u=t._getSoundIds(o),l=0;l<u.length;l++)if(c=t._soundById(u[l]),c){var h=c._pannerAttr;h={coneInnerAngle:typeof s.coneInnerAngle<"u"?s.coneInnerAngle:h.coneInnerAngle,coneOuterAngle:typeof s.coneOuterAngle<"u"?s.coneOuterAngle:h.coneOuterAngle,coneOuterGain:typeof s.coneOuterGain<"u"?s.coneOuterGain:h.coneOuterGain,distanceModel:typeof s.distanceModel<"u"?s.distanceModel:h.distanceModel,maxDistance:typeof s.maxDistance<"u"?s.maxDistance:h.maxDistance,refDistance:typeof s.refDistance<"u"?s.refDistance:h.refDistance,rolloffFactor:typeof s.rolloffFactor<"u"?s.rolloffFactor:h.rolloffFactor,panningModel:typeof s.panningModel<"u"?s.panningModel:h.panningModel};var p=c._panner;p||(c._pos||(c._pos=t._pos||[0,0,-.5]),e(c,"spatial"),p=c._panner),p.coneInnerAngle=h.coneInnerAngle,p.coneOuterAngle=h.coneOuterAngle,p.coneOuterGain=h.coneOuterGain,p.distanceModel=h.distanceModel,p.maxDistance=h.maxDistance,p.refDistance=h.refDistance,p.rolloffFactor=h.rolloffFactor,p.panningModel=h.panningModel}return t},Sound.prototype.init=function(t){return function(){var n=this,s=n._parent;n._orientation=s._orientation,n._stereo=s._stereo,n._pos=s._pos,n._pannerAttr=s._pannerAttr,t.call(this),n._stereo?s.stereo(n._stereo):n._pos&&s.pos(n._pos[0],n._pos[1],n._pos[2],n._id)}}(Sound.prototype.init),Sound.prototype.reset=function(t){return function(){var n=this,s=n._parent;return n._orientation=s._orientation,n._stereo=s._stereo,n._pos=s._pos,n._pannerAttr=s._pannerAttr,n._stereo?s.stereo(n._stereo):n._pos?s.pos(n._pos[0],n._pos[1],n._pos[2],n._id):n._panner&&(n._panner.disconnect(0),n._panner=void 0,s._refreshBuffer(n)),t.call(this)}}(Sound.prototype.reset);var e=function(t,n){n=n||"spatial",n==="spatial"?(t._panner=Howler.ctx.createPanner(),t._panner.coneInnerAngle=t._pannerAttr.coneInnerAngle,t._panner.coneOuterAngle=t._pannerAttr.coneOuterAngle,t._panner.coneOuterGain=t._pannerAttr.coneOuterGain,t._panner.distanceModel=t._pannerAttr.distanceModel,t._panner.maxDistance=t._pannerAttr.maxDistance,t._panner.refDistance=t._pannerAttr.refDistance,t._panner.rolloffFactor=t._pannerAttr.rolloffFactor,t._panner.panningModel=t._pannerAttr.panningModel,typeof t._panner.positionX<"u"?(t._panner.positionX.setValueAtTime(t._pos[0],Howler.ctx.currentTime),t._panner.positionY.setValueAtTime(t._pos[1],Howler.ctx.currentTime),t._panner.positionZ.setValueAtTime(t._pos[2],Howler.ctx.currentTime)):t._panner.setPosition(t._pos[0],t._pos[1],t._pos[2]),typeof t._panner.orientationX<"u"?(t._panner.orientationX.setValueAtTime(t._orientation[0],Howler.ctx.currentTime),t._panner.orientationY.setValueAtTime(t._orientation[1],Howler.ctx.currentTime),t._panner.orientationZ.setValueAtTime(t._orientation[2],Howler.ctx.currentTime)):t._panner.setOrientation(t._orientation[0],t._orientation[1],t._orientation[2])):(t._panner=Howler.ctx.createStereoPanner(),t._panner.pan.setValueAtTime(t._stereo,Howler.ctx.currentTime)),t._panner.connect(t._node),t._paused||t._parent.pause(t._id,!0).play(t._id,!0)}})()})(Ri);const yr="15.1.22",Zs=(r,e,t)=>({endTime:e,insertTime:t,type:"exponentialRampToValue",value:r}),zs=(r,e,t)=>({endTime:e,insertTime:t,type:"linearRampToValue",value:r}),es=(r,e)=>({startTime:e,type:"setValue",value:r}),Tr=(r,e,t)=>({duration:t,startTime:e,type:"setValueCurve",values:r}),wr=(r,e,{startTime:t,target:n,timeConstant:s})=>n+(e-n)*Math.exp((t-r)/s),Nt=r=>r.type==="exponentialRampToValue",mn=r=>r.type==="linearRampToValue",nt=r=>Nt(r)||mn(r),hs=r=>r.type==="setValue",Ze=r=>r.type==="setValueCurve",gn=(r,e,t,n)=>{const s=r[e];return s===void 0?n:nt(s)||hs(s)?s.value:Ze(s)?s.values[s.values.length-1]:wr(t,gn(r,e-1,s.startTime,n),s)},Ys=(r,e,t,n,s)=>t===void 0?[n.insertTime,s]:nt(t)?[t.endTime,t.value]:hs(t)?[t.startTime,t.value]:Ze(t)?[t.startTime+t.duration,t.values[t.values.length-1]]:[t.startTime,gn(r,e-1,t.startTime,s)],ts=r=>r.type==="cancelAndHold",ns=r=>r.type==="cancelScheduledValues",tt=r=>ts(r)||ns(r)?r.cancelTime:Nt(r)||mn(r)?r.endTime:r.startTime,Qs=(r,e,t,{endTime:n,value:s})=>t===s?s:0<t&&0<s||t<0&&s<0?t*(s/t)**((r-e)/(n-e)):0,Js=(r,e,t,{endTime:n,value:s})=>t+(r-e)/(n-e)*(s-t),Pi=(r,e)=>{const t=Math.floor(e),n=Math.ceil(e);return t===n?r[t]:(1-(e-t))*r[t]+(1-(n-e))*r[n]},Vi=(r,{duration:e,startTime:t,values:n})=>{const s=(r-t)/e*(n.length-1);return Pi(n,s)},ln=r=>r.type==="setTarget";class Fi{constructor(e){this._automationEvents=[],this._currenTime=0,this._defaultValue=e}[Symbol.iterator](){return this._automationEvents[Symbol.iterator]()}add(e){const t=tt(e);if(ts(e)||ns(e)){const n=this._automationEvents.findIndex(o=>ns(e)&&Ze(o)?o.startTime+o.duration>=t:tt(o)>=t),s=this._automationEvents[n];if(n!==-1&&(this._automationEvents=this._automationEvents.slice(0,n)),ts(e)){const o=this._automationEvents[this._automationEvents.length-1];if(s!==void 0&&nt(s)){if(o!==void 0&&ln(o))throw new Error("The internal list is malformed.");const c=o===void 0?s.insertTime:Ze(o)?o.startTime+o.duration:tt(o),u=o===void 0?this._defaultValue:Ze(o)?o.values[o.values.length-1]:o.value,l=Nt(s)?Qs(t,c,u,s):Js(t,c,u,s),h=Nt(s)?Zs(l,t,this._currenTime):zs(l,t,this._currenTime);this._automationEvents.push(h)}if(o!==void 0&&ln(o)&&this._automationEvents.push(es(this.getValue(t),t)),o!==void 0&&Ze(o)&&o.startTime+o.duration>t){const c=t-o.startTime,u=(o.values.length-1)/o.duration,l=Math.max(2,1+Math.ceil(c*u)),h=c/(l-1)*u,p=o.values.slice(0,l);if(h<1)for(let i=1;i<l;i+=1){const a=h*i%1;p[i]=o.values[i-1]*(1-a)+o.values[i]*a}this._automationEvents[this._automationEvents.length-1]=Tr(p,o.startTime,c)}}}else{const n=this._automationEvents.findIndex(c=>tt(c)>t),s=n===-1?this._automationEvents[this._automationEvents.length-1]:this._automationEvents[n-1];if(s!==void 0&&Ze(s)&&tt(s)+s.duration>t)return!1;const o=Nt(e)?Zs(e.value,e.endTime,this._currenTime):mn(e)?zs(e.value,t,this._currenTime):e;if(n===-1)this._automationEvents.push(o);else{if(Ze(e)&&t+e.duration>tt(this._automationEvents[n]))return!1;this._automationEvents.splice(n,0,o)}}return!0}flush(e){const t=this._automationEvents.findIndex(n=>tt(n)>e);if(t>1){const n=this._automationEvents.slice(t-1),s=n[0];ln(s)&&n.unshift(es(gn(this._automationEvents,t-2,s.startTime,this._defaultValue),s.startTime)),this._automationEvents=n}}getValue(e){if(this._automationEvents.length===0)return this._defaultValue;const t=this._automationEvents.findIndex(c=>tt(c)>e),n=this._automationEvents[t],s=(t===-1?this._automationEvents.length:t)-1,o=this._automationEvents[s];if(o!==void 0&&ln(o)&&(n===void 0||!nt(n)||n.insertTime>e))return wr(e,gn(this._automationEvents,s-1,o.startTime,this._defaultValue),o);if(o!==void 0&&hs(o)&&(n===void 0||!nt(n)))return o.value;if(o!==void 0&&Ze(o)&&(n===void 0||!nt(n)||o.startTime+o.duration>e))return e<o.startTime+o.duration?Vi(e,o):o.values[o.values.length-1];if(o!==void 0&&nt(o)&&(n===void 0||!nt(n)))return o.value;if(n!==void 0&&Nt(n)){const[c,u]=Ys(this._automationEvents,s,o,n,this._defaultValue);return Qs(e,c,u,n)}if(n!==void 0&&mn(n)){const[c,u]=Ys(this._automationEvents,s,o,n,this._defaultValue);return Js(e,c,u,n)}return this._defaultValue}}const Li=r=>({cancelTime:r,type:"cancelAndHold"}),Wi=r=>({cancelTime:r,type:"cancelScheduledValues"}),qi=(r,e)=>({endTime:e,type:"exponentialRampToValue",value:r}),Bi=(r,e)=>({endTime:e,type:"linearRampToValue",value:r}),ji=(r,e,t)=>({startTime:e,target:r,timeConstant:t,type:"setTarget"}),Gi=()=>new DOMException("","AbortError"),Ui=r=>(e,t,[n,s,o],c)=>{r(e[s],[t,n,o],u=>u[0]===t&&u[1]===n,c)},Hi=r=>(e,t,n)=>{const s=[];for(let o=0;o<n.numberOfInputs;o+=1)s.push(new Set);r.set(e,{activeInputs:s,outputs:new Set,passiveInputs:new WeakMap,renderer:t})},$i=r=>(e,t)=>{r.set(e,{activeInputs:new Set,passiveInputs:new WeakMap,renderer:t})},Mt=new WeakSet,br=new WeakMap,ds=new WeakMap,Ar=new WeakMap,fs=new WeakMap,In=new WeakMap,Sr=new WeakMap,ss=new WeakMap,rs=new WeakMap,is=new WeakMap,xr={construct(){return xr}},Xi=r=>{try{const e=new Proxy(r,xr);new e}catch{return!1}return!0},Ks=/^import(?:(?:[\s]+[\w]+|(?:[\s]+[\w]+[\s]*,)?[\s]*\{[\s]*[\w]+(?:[\s]+as[\s]+[\w]+)?(?:[\s]*,[\s]*[\w]+(?:[\s]+as[\s]+[\w]+)?)*[\s]*}|(?:[\s]+[\w]+[\s]*,)?[\s]*\*[\s]+as[\s]+[\w]+)[\s]+from)?(?:[\s]*)("([^"\\]|\\.)+"|'([^'\\]|\\.)+')(?:[\s]*);?/,er=(r,e)=>{const t=[];let n=r.replace(/^[\s]+/,""),s=n.match(Ks);for(;s!==null;){const o=s[1].slice(1,-1),c=s[0].replace(/([\s]+)?;?$/,"").replace(o,new URL(o,e).toString());t.push(c),n=n.slice(s[0].length).replace(/^[\s]+/,""),s=n.match(Ks)}return[t.join(";"),n]},tr=r=>{if(r!==void 0&&!Array.isArray(r))throw new TypeError("The parameterDescriptors property of given value for processorCtor is not an array.")},nr=r=>{if(!Xi(r))throw new TypeError("The given value for processorCtor should be a constructor.");if(r.prototype===null||typeof r.prototype!="object")throw new TypeError("The given value for processorCtor should have a prototype.")},Zi=(r,e,t,n,s,o,c,u,l,h,p,i,a)=>{let d=0;return(f,_,g={credentials:"omit"})=>{const m=p.get(f);if(m!==void 0&&m.has(_))return Promise.resolve();const y=h.get(f);if(y!==void 0){const v=y.get(_);if(v!==void 0)return v}const b=o(f),S=b.audioWorklet===void 0?s(_).then(([v,w])=>{const[A,T]=er(v,w),x=`${A};((a,b)=>{(a[b]=a[b]||[]).push((AudioWorkletProcessor,global,registerProcessor,sampleRate,self,window)=>{${T}
})})(window,'_AWGS')`;return t(x)}).then(()=>{const v=a._AWGS.pop();if(v===void 0)throw new SyntaxError;n(b.currentTime,b.sampleRate,()=>v(class{},void 0,(w,A)=>{if(w.trim()==="")throw e();const T=rs.get(b);if(T!==void 0){if(T.has(w))throw e();nr(A),tr(A.parameterDescriptors),T.set(w,A)}else nr(A),tr(A.parameterDescriptors),rs.set(b,new Map([[w,A]]))},b.sampleRate,void 0,void 0))}):Promise.all([s(_),Promise.resolve(r(i,i))]).then(([[v,w],A])=>{const T=d+1;d=T;const[x,O]=er(v,w),N=`${x};((AudioWorkletProcessor,registerProcessor)=>{${O}
})(${A?"AudioWorkletProcessor":"class extends AudioWorkletProcessor {__b=new WeakSet();constructor(){super();(p=>p.postMessage=(q=>(m,t)=>q.call(p,m,t?t.filter(u=>!this.__b.has(u)):t))(p.postMessage))(this.port)}}"},(n,p)=>registerProcessor(n,class extends p{${A?"":"__c = (a) => a.forEach(e=>this.__b.add(e.buffer));"}process(i,o,p){${A?"":"i.forEach(this.__c);o.forEach(this.__c);this.__c(Object.values(p));"}return super.process(i.map(j=>j.some(k=>k.length===0)?[]:j),o,p)}}));registerProcessor('__sac${T}',class extends AudioWorkletProcessor{process(){return !1}})`,P=new Blob([N],{type:"application/javascript; charset=utf-8"}),E=URL.createObjectURL(P);return b.audioWorklet.addModule(E,g).then(()=>{if(u(b))return b;const D=c(b);return D.audioWorklet.addModule(E,g).then(()=>D)}).then(D=>{if(l===null)throw new SyntaxError;try{new l(D,`__sac${T}`)}catch{throw new SyntaxError}}).finally(()=>URL.revokeObjectURL(E))});return y===void 0?h.set(f,new Map([[_,S]])):y.set(_,S),S.then(()=>{const v=p.get(f);v===void 0?p.set(f,new Set([_])):v.add(_)}).finally(()=>{const v=h.get(f);v!==void 0&&v.delete(_)}),S}},Le=(r,e)=>{const t=r.get(e);if(t===void 0)throw new Error("A value with the given key could not be found.");return t},Mn=(r,e)=>{const t=Array.from(r).filter(e);if(t.length>1)throw Error("More than one element was found.");if(t.length===0)throw Error("No element was found.");const[n]=t;return r.delete(n),n},kr=(r,e,t,n)=>{const s=Le(r,e),o=Mn(s,c=>c[0]===t&&c[1]===n);return s.size===0&&r.delete(e),o},Qt=r=>Le(Sr,r),Et=r=>{if(Mt.has(r))throw new Error("The AudioNode is already stored.");Mt.add(r),Qt(r).forEach(e=>e(!0))},Cr=r=>"port"in r,Jt=r=>{if(!Mt.has(r))throw new Error("The AudioNode is not stored.");Mt.delete(r),Qt(r).forEach(e=>e(!1))},os=(r,e)=>{!Cr(r)&&e.every(t=>t.size===0)&&Jt(r)},zi=(r,e,t,n,s,o,c,u,l,h,p,i,a)=>{const d=new WeakMap;return(f,_,g,m,y)=>{const{activeInputs:b,passiveInputs:S}=o(_),{outputs:v}=o(f),w=u(f),A=T=>{const x=l(_),O=l(f);if(T){const C=kr(S,f,g,m);r(b,f,C,!1),!y&&!i(f)&&t(O,x,g,m),a(_)&&Et(_)}else{const C=n(b,f,g,m);e(S,m,C,!1),!y&&!i(f)&&s(O,x,g,m);const k=c(_);if(k===0)p(_)&&os(_,b);else{const I=d.get(_);I!==void 0&&clearTimeout(I),d.set(_,setTimeout(()=>{p(_)&&os(_,b)},k*1e3))}}};return h(v,[_,g,m],T=>T[0]===_&&T[1]===g&&T[2]===m,!0)?(w.add(A),p(f)?r(b,f,[g,m,A],!0):e(S,m,[f,g,A],!0),!0):!1}},Yi=r=>(e,t,[n,s,o],c)=>{const u=e.get(n);u===void 0?e.set(n,new Set([[s,t,o]])):r(u,[s,t,o],l=>l[0]===s&&l[1]===t,c)},Qi=r=>(e,t)=>{const n=r(e,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});t.connect(n).connect(e.destination);const s=()=>{t.removeEventListener("ended",s),t.disconnect(n),n.disconnect()};t.addEventListener("ended",s)},Ji=r=>(e,t)=>{r(e).add(t)},Ki={channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",fftSize:2048,maxDecibels:-30,minDecibels:-100,smoothingTimeConstant:.8},eo=(r,e,t,n,s,o)=>class extends r{constructor(u,l){const h=s(u),p={...Ki,...l},i=n(h,p),a=o(h)?e():null;super(u,!1,i,a),this._nativeAnalyserNode=i}get fftSize(){return this._nativeAnalyserNode.fftSize}set fftSize(u){this._nativeAnalyserNode.fftSize=u}get frequencyBinCount(){return this._nativeAnalyserNode.frequencyBinCount}get maxDecibels(){return this._nativeAnalyserNode.maxDecibels}set maxDecibels(u){const l=this._nativeAnalyserNode.maxDecibels;if(this._nativeAnalyserNode.maxDecibels=u,!(u>this._nativeAnalyserNode.minDecibels))throw this._nativeAnalyserNode.maxDecibels=l,t()}get minDecibels(){return this._nativeAnalyserNode.minDecibels}set minDecibels(u){const l=this._nativeAnalyserNode.minDecibels;if(this._nativeAnalyserNode.minDecibels=u,!(this._nativeAnalyserNode.maxDecibels>u))throw this._nativeAnalyserNode.minDecibels=l,t()}get smoothingTimeConstant(){return this._nativeAnalyserNode.smoothingTimeConstant}set smoothingTimeConstant(u){this._nativeAnalyserNode.smoothingTimeConstant=u}getByteFrequencyData(u){this._nativeAnalyserNode.getByteFrequencyData(u)}getByteTimeDomainData(u){this._nativeAnalyserNode.getByteTimeDomainData(u)}getFloatFrequencyData(u){this._nativeAnalyserNode.getFloatFrequencyData(u)}getFloatTimeDomainData(u){this._nativeAnalyserNode.getFloatTimeDomainData(u)}},ge=(r,e)=>r.context===e,to=(r,e,t)=>()=>{const n=new WeakMap,s=async(o,c)=>{let u=e(o);if(!ge(u,c)){const h={channelCount:u.channelCount,channelCountMode:u.channelCountMode,channelInterpretation:u.channelInterpretation,fftSize:u.fftSize,maxDecibels:u.maxDecibels,minDecibels:u.minDecibels,smoothingTimeConstant:u.smoothingTimeConstant};u=r(c,h)}return n.set(c,u),await t(o,c,u),u};return{render(o,c){const u=n.get(c);return u!==void 0?Promise.resolve(u):s(o,c)}}},vn=r=>{try{r.copyToChannel(new Float32Array(1),0,-1)}catch{return!1}return!0},Ue=()=>new DOMException("","IndexSizeError"),ps=r=>{r.getChannelData=(e=>t=>{try{return e.call(r,t)}catch(n){throw n.code===12?Ue():n}})(r.getChannelData)},no={numberOfChannels:1},so=(r,e,t,n,s,o,c,u)=>{let l=null;return class Or{constructor(p){if(s===null)throw new Error("Missing the native OfflineAudioContext constructor.");const{length:i,numberOfChannels:a,sampleRate:d}={...no,...p};l===null&&(l=new s(1,1,44100));const f=n!==null&&e(o,o)?new n({length:i,numberOfChannels:a,sampleRate:d}):l.createBuffer(a,i,d);if(f.numberOfChannels===0)throw t();return typeof f.copyFromChannel!="function"?(c(f),ps(f)):e(vn,()=>vn(f))||u(f),r.add(f),f}static[Symbol.hasInstance](p){return p!==null&&typeof p=="object"&&Object.getPrototypeOf(p)===Or.prototype||r.has(p)}}},Ae=-34028234663852886e22,ye=-Ae,ze=r=>Mt.has(r),ro={buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1},io=(r,e,t,n,s,o,c,u)=>class extends r{constructor(h,p){const i=o(h),a={...ro,...p},d=s(i,a),f=c(i),_=f?e():null;super(h,!1,d,_),this._audioBufferSourceNodeRenderer=_,this._isBufferNullified=!1,this._isBufferSet=a.buffer!==null,this._nativeAudioBufferSourceNode=d,this._onended=null,this._playbackRate=t(this,f,d.playbackRate,ye,Ae)}get buffer(){return this._isBufferNullified?null:this._nativeAudioBufferSourceNode.buffer}set buffer(h){if(this._nativeAudioBufferSourceNode.buffer=h,h!==null){if(this._isBufferSet)throw n();this._isBufferSet=!0}}get loop(){return this._nativeAudioBufferSourceNode.loop}set loop(h){this._nativeAudioBufferSourceNode.loop=h}get loopEnd(){return this._nativeAudioBufferSourceNode.loopEnd}set loopEnd(h){this._nativeAudioBufferSourceNode.loopEnd=h}get loopStart(){return this._nativeAudioBufferSourceNode.loopStart}set loopStart(h){this._nativeAudioBufferSourceNode.loopStart=h}get onended(){return this._onended}set onended(h){const p=typeof h=="function"?u(this,h):null;this._nativeAudioBufferSourceNode.onended=p;const i=this._nativeAudioBufferSourceNode.onended;this._onended=i!==null&&i===p?h:i}get playbackRate(){return this._playbackRate}start(h=0,p=0,i){if(this._nativeAudioBufferSourceNode.start(h,p,i),this._audioBufferSourceNodeRenderer!==null&&(this._audioBufferSourceNodeRenderer.start=i===void 0?[h,p]:[h,p,i]),this.context.state!=="closed"){Et(this);const a=()=>{this._nativeAudioBufferSourceNode.removeEventListener("ended",a),ze(this)&&Jt(this)};this._nativeAudioBufferSourceNode.addEventListener("ended",a)}}stop(h=0){this._nativeAudioBufferSourceNode.stop(h),this._audioBufferSourceNodeRenderer!==null&&(this._audioBufferSourceNodeRenderer.stop=h)}},oo=(r,e,t,n,s)=>()=>{const o=new WeakMap;let c=null,u=null;const l=async(h,p)=>{let i=t(h);const a=ge(i,p);if(!a){const d={buffer:i.buffer,channelCount:i.channelCount,channelCountMode:i.channelCountMode,channelInterpretation:i.channelInterpretation,loop:i.loop,loopEnd:i.loopEnd,loopStart:i.loopStart,playbackRate:i.playbackRate.value};i=e(p,d),c!==null&&i.start(...c),u!==null&&i.stop(u)}return o.set(p,i),a?await r(p,h.playbackRate,i.playbackRate):await n(p,h.playbackRate,i.playbackRate),await s(h,p,i),i};return{set start(h){c=h},set stop(h){u=h},render(h,p){const i=o.get(p);return i!==void 0?Promise.resolve(i):l(h,p)}}},ao=r=>"playbackRate"in r,co=r=>"frequency"in r&&"gain"in r,uo=r=>"offset"in r,lo=r=>!("frequency"in r)&&"gain"in r,ho=r=>"detune"in r&&"frequency"in r&&!("gain"in r),fo=r=>"pan"in r,Te=r=>Le(br,r),Kt=r=>Le(Ar,r),as=(r,e)=>{const{activeInputs:t}=Te(r);t.forEach(s=>s.forEach(([o])=>{e.includes(r)||as(o,[...e,r])}));const n=ao(r)?[r.playbackRate]:Cr(r)?Array.from(r.parameters.values()):co(r)?[r.Q,r.detune,r.frequency,r.gain]:uo(r)?[r.offset]:lo(r)?[r.gain]:ho(r)?[r.detune,r.frequency]:fo(r)?[r.pan]:[];for(const s of n){const o=Kt(s);o!==void 0&&o.activeInputs.forEach(([c])=>as(c,e))}ze(r)&&Jt(r)},Nr=r=>{as(r.destination,[])},po=r=>r===void 0||typeof r=="number"||typeof r=="string"&&(r==="balanced"||r==="interactive"||r==="playback"),_o=(r,e,t,n,s,o,c,u,l)=>class extends r{constructor(p={}){if(l===null)throw new Error("Missing the native AudioContext constructor.");let i;try{i=new l(p)}catch(f){throw f.code===12&&f.message==="sampleRate is not in range"?t():f}if(i===null)throw n();if(!po(p.latencyHint))throw new TypeError(`The provided value '${p.latencyHint}' is not a valid enum value of type AudioContextLatencyCategory.`);if(p.sampleRate!==void 0&&i.sampleRate!==p.sampleRate)throw t();super(i,2);const{latencyHint:a}=p,{sampleRate:d}=i;if(this._baseLatency=typeof i.baseLatency=="number"?i.baseLatency:a==="balanced"?512/d:a==="interactive"||a===void 0?256/d:a==="playback"?1024/d:Math.max(2,Math.min(128,Math.round(a*d/128)))*128/d,this._nativeAudioContext=i,l.name==="webkitAudioContext"?(this._nativeGainNode=i.createGain(),this._nativeOscillatorNode=i.createOscillator(),this._nativeGainNode.gain.value=1e-37,this._nativeOscillatorNode.connect(this._nativeGainNode).connect(i.destination),this._nativeOscillatorNode.start()):(this._nativeGainNode=null,this._nativeOscillatorNode=null),this._state=null,i.state==="running"){this._state="suspended";const f=()=>{this._state==="suspended"&&(this._state=null),i.removeEventListener("statechange",f)};i.addEventListener("statechange",f)}}get baseLatency(){return this._baseLatency}get state(){return this._state!==null?this._state:this._nativeAudioContext.state}close(){return this.state==="closed"?this._nativeAudioContext.close().then(()=>{throw e()}):(this._state==="suspended"&&(this._state=null),this._nativeAudioContext.close().then(()=>{this._nativeGainNode!==null&&this._nativeOscillatorNode!==null&&(this._nativeOscillatorNode.stop(),this._nativeGainNode.disconnect(),this._nativeOscillatorNode.disconnect()),Nr(this)}))}createMediaElementSource(p){return new s(this,{mediaElement:p})}createMediaStreamDestination(){return new o(this)}createMediaStreamSource(p){return new c(this,{mediaStream:p})}createMediaStreamTrackSource(p){return new u(this,{mediaStreamTrack:p})}resume(){return this._state==="suspended"?new Promise((p,i)=>{const a=()=>{this._nativeAudioContext.removeEventListener("statechange",a),this._nativeAudioContext.state==="running"?p():this.resume().then(p,i)};this._nativeAudioContext.addEventListener("statechange",a)}):this._nativeAudioContext.resume().catch(p=>{throw p===void 0||p.code===15?e():p})}suspend(){return this._nativeAudioContext.suspend().catch(p=>{throw p===void 0?e():p})}},mo=(r,e,t,n,s,o,c,u)=>class extends r{constructor(h,p){const i=o(h),a=c(i),d=s(i,p,a),f=a?e(u):null;super(h,!1,d,f),this._isNodeOfNativeOfflineAudioContext=a,this._nativeAudioDestinationNode=d}get channelCount(){return this._nativeAudioDestinationNode.channelCount}set channelCount(h){if(this._isNodeOfNativeOfflineAudioContext)throw n();if(h>this._nativeAudioDestinationNode.maxChannelCount)throw t();this._nativeAudioDestinationNode.channelCount=h}get channelCountMode(){return this._nativeAudioDestinationNode.channelCountMode}set channelCountMode(h){if(this._isNodeOfNativeOfflineAudioContext)throw n();this._nativeAudioDestinationNode.channelCountMode=h}get maxChannelCount(){return this._nativeAudioDestinationNode.maxChannelCount}},go=r=>{const e=new WeakMap,t=async(n,s)=>{const o=s.destination;return e.set(s,o),await r(n,s,o),o};return{render(n,s){const o=e.get(s);return o!==void 0?Promise.resolve(o):t(n,s)}}},vo=(r,e,t,n,s,o,c,u)=>(l,h)=>{const p=h.listener,i=()=>{const v=new Float32Array(1),w=e(h,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:9}),A=c(h);let T=!1,x=[0,0,-1,0,1,0],O=[0,0,0];const C=()=>{if(T)return;T=!0;const P=n(h,256,9,0);P.onaudioprocess=({inputBuffer:E})=>{const D=[o(E,v,0),o(E,v,1),o(E,v,2),o(E,v,3),o(E,v,4),o(E,v,5)];D.some((V,B)=>V!==x[B])&&(p.setOrientation(...D),x=D);const G=[o(E,v,6),o(E,v,7),o(E,v,8)];G.some((V,B)=>V!==O[B])&&(p.setPosition(...G),O=G)},w.connect(P)},k=P=>E=>{E!==x[P]&&(x[P]=E,p.setOrientation(...x))},I=P=>E=>{E!==O[P]&&(O[P]=E,p.setPosition(...O))},N=(P,E,D)=>{const G=t(h,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",offset:E});G.connect(w,0,P),G.start(),Object.defineProperty(G.offset,"defaultValue",{get(){return E}});const V=r({context:l},A,G.offset,ye,Ae);return u(V,"value",B=>()=>B.call(V),B=>q=>{try{B.call(V,q)}catch(Z){if(Z.code!==9)throw Z}C(),A&&D(q)}),V.cancelAndHoldAtTime=(B=>A?()=>{throw s()}:(...q)=>{const Z=B.apply(V,q);return C(),Z})(V.cancelAndHoldAtTime),V.cancelScheduledValues=(B=>A?()=>{throw s()}:(...q)=>{const Z=B.apply(V,q);return C(),Z})(V.cancelScheduledValues),V.exponentialRampToValueAtTime=(B=>A?()=>{throw s()}:(...q)=>{const Z=B.apply(V,q);return C(),Z})(V.exponentialRampToValueAtTime),V.linearRampToValueAtTime=(B=>A?()=>{throw s()}:(...q)=>{const Z=B.apply(V,q);return C(),Z})(V.linearRampToValueAtTime),V.setTargetAtTime=(B=>A?()=>{throw s()}:(...q)=>{const Z=B.apply(V,q);return C(),Z})(V.setTargetAtTime),V.setValueAtTime=(B=>A?()=>{throw s()}:(...q)=>{const Z=B.apply(V,q);return C(),Z})(V.setValueAtTime),V.setValueCurveAtTime=(B=>A?()=>{throw s()}:(...q)=>{const Z=B.apply(V,q);return C(),Z})(V.setValueCurveAtTime),V};return{forwardX:N(0,0,k(0)),forwardY:N(1,0,k(1)),forwardZ:N(2,-1,k(2)),positionX:N(6,0,I(0)),positionY:N(7,0,I(1)),positionZ:N(8,0,I(2)),upX:N(3,0,k(3)),upY:N(4,1,k(4)),upZ:N(5,0,k(5))}},{forwardX:a,forwardY:d,forwardZ:f,positionX:_,positionY:g,positionZ:m,upX:y,upY:b,upZ:S}=p.forwardX===void 0?i():p;return{get forwardX(){return a},get forwardY(){return d},get forwardZ(){return f},get positionX(){return _},get positionY(){return g},get positionZ(){return m},get upX(){return y},get upY(){return b},get upZ(){return S}}},yn=r=>"context"in r,en=r=>yn(r[0]),wt=(r,e,t,n)=>{for(const s of r)if(t(s)){if(n)return!1;throw Error("The set contains at least one similar element.")}return r.add(e),!0},sr=(r,e,[t,n],s)=>{wt(r,[e,t,n],o=>o[0]===e&&o[1]===t,s)},rr=(r,[e,t,n],s)=>{const o=r.get(e);o===void 0?r.set(e,new Set([[t,n]])):wt(o,[t,n],c=>c[0]===t,s)},Vt=r=>"inputs"in r,Tn=(r,e,t,n)=>{if(Vt(e)){const s=e.inputs[n];return r.connect(s,t,0),[s,t,0]}return r.connect(e,t,n),[e,t,n]},Ir=(r,e,t)=>{for(const n of r)if(n[0]===e&&n[1]===t)return r.delete(n),n;return null},yo=(r,e,t)=>Mn(r,n=>n[0]===e&&n[1]===t),Mr=(r,e)=>{if(!Qt(r).delete(e))throw new Error("Missing the expected event listener.")},Er=(r,e,t)=>{const n=Le(r,e),s=Mn(n,o=>o[0]===t);return n.size===0&&r.delete(e),s},wn=(r,e,t,n)=>{Vt(e)?r.disconnect(e.inputs[n],t,0):r.disconnect(e,t,n)},J=r=>Le(ds,r),Xt=r=>Le(fs,r),gt=r=>ss.has(r),pn=r=>!Mt.has(r),ir=(r,e)=>new Promise(t=>{if(e!==null)t(!0);else{const n=r.createScriptProcessor(256,1,1),s=r.createGain(),o=r.createBuffer(1,2,44100),c=o.getChannelData(0);c[0]=1,c[1]=1;const u=r.createBufferSource();u.buffer=o,u.loop=!0,u.connect(n).connect(r.destination),u.connect(s),u.disconnect(s),n.onaudioprocess=l=>{const h=l.inputBuffer.getChannelData(0);Array.prototype.some.call(h,p=>p===1)?t(!0):t(!1),u.stop(),n.onaudioprocess=null,u.disconnect(n),n.disconnect(r.destination)},u.start()}}),Jn=(r,e)=>{const t=new Map;for(const n of r)for(const s of n){const o=t.get(s);t.set(s,o===void 0?1:o+1)}t.forEach((n,s)=>e(s,n))},bn=r=>"context"in r,To=r=>{const e=new Map;r.connect=(t=>(n,s=0,o=0)=>{const c=bn(n)?t(n,s,o):t(n,s),u=e.get(n);return u===void 0?e.set(n,[{input:o,output:s}]):u.every(l=>l.input!==o||l.output!==s)&&u.push({input:o,output:s}),c})(r.connect.bind(r)),r.disconnect=(t=>(n,s,o)=>{if(t.apply(r),n===void 0)e.clear();else if(typeof n=="number")for(const[c,u]of e){const l=u.filter(h=>h.output!==n);l.length===0?e.delete(c):e.set(c,l)}else if(e.has(n))if(s===void 0)e.delete(n);else{const c=e.get(n);if(c!==void 0){const u=c.filter(l=>l.output!==s&&(l.input!==o||o===void 0));u.length===0?e.delete(n):e.set(n,u)}}for(const[c,u]of e)u.forEach(l=>{bn(c)?r.connect(c,l.output,l.input):r.connect(c,l.output)})})(r.disconnect)},wo=(r,e,t,n)=>{const{activeInputs:s,passiveInputs:o}=Kt(e),{outputs:c}=Te(r),u=Qt(r),l=h=>{const p=J(r),i=Xt(e);if(h){const a=Er(o,r,t);sr(s,r,a,!1),!n&&!gt(r)&&p.connect(i,t)}else{const a=yo(s,r,t);rr(o,a,!1),!n&&!gt(r)&&p.disconnect(i,t)}};return wt(c,[e,t],h=>h[0]===e&&h[1]===t,!0)?(u.add(l),ze(r)?sr(s,r,[t,l],!0):rr(o,[r,t,l],!0),!0):!1},bo=(r,e,t,n)=>{const{activeInputs:s,passiveInputs:o}=Te(e),c=Ir(s[n],r,t);return c===null?[kr(o,r,t,n)[2],!1]:[c[2],!0]},Ao=(r,e,t)=>{const{activeInputs:n,passiveInputs:s}=Kt(e),o=Ir(n,r,t);return o===null?[Er(s,r,t)[1],!1]:[o[2],!0]},_s=(r,e,t,n,s)=>{const[o,c]=bo(r,t,n,s);if(o!==null&&(Mr(r,o),c&&!e&&!gt(r)&&wn(J(r),J(t),n,s)),ze(t)){const{activeInputs:u}=Te(t);os(t,u)}},ms=(r,e,t,n)=>{const[s,o]=Ao(r,t,n);s!==null&&(Mr(r,s),o&&!e&&!gt(r)&&J(r).disconnect(Xt(t),n))},So=(r,e)=>{const t=Te(r),n=[];for(const s of t.outputs)en(s)?_s(r,e,...s):ms(r,e,...s),n.push(s[0]);return t.outputs.clear(),n},xo=(r,e,t)=>{const n=Te(r),s=[];for(const o of n.outputs)o[1]===t&&(en(o)?_s(r,e,...o):ms(r,e,...o),s.push(o[0]),n.outputs.delete(o));return s},ko=(r,e,t,n,s)=>{const o=Te(r);return Array.from(o.outputs).filter(c=>c[0]===t&&(n===void 0||c[1]===n)&&(s===void 0||c[2]===s)).map(c=>(en(c)?_s(r,e,...c):ms(r,e,...c),o.outputs.delete(c),c[0]))},Co=(r,e,t,n,s,o,c,u,l,h,p,i,a,d,f,_)=>class extends h{constructor(m,y,b,S){super(b),this._context=m,this._nativeAudioNode=b;const v=p(m);i(v)&&t(ir,()=>ir(v,_))!==!0&&To(b),ds.set(this,b),Sr.set(this,new Set),m.state!=="closed"&&y&&Et(this),r(this,S,b)}get channelCount(){return this._nativeAudioNode.channelCount}set channelCount(m){this._nativeAudioNode.channelCount=m}get channelCountMode(){return this._nativeAudioNode.channelCountMode}set channelCountMode(m){this._nativeAudioNode.channelCountMode=m}get channelInterpretation(){return this._nativeAudioNode.channelInterpretation}set channelInterpretation(m){this._nativeAudioNode.channelInterpretation=m}get context(){return this._context}get numberOfInputs(){return this._nativeAudioNode.numberOfInputs}get numberOfOutputs(){return this._nativeAudioNode.numberOfOutputs}connect(m,y=0,b=0){if(y<0||y>=this._nativeAudioNode.numberOfOutputs)throw s();const S=p(this._context),v=f(S);if(a(m)||d(m))throw o();if(yn(m)){const T=J(m);try{const O=Tn(this._nativeAudioNode,T,y,b),C=pn(this);(v||C)&&this._nativeAudioNode.disconnect(...O),this.context.state!=="closed"&&!C&&pn(m)&&Et(m)}catch(O){throw O.code===12?o():O}if(e(this,m,y,b,v)){const O=l([this],m);Jn(O,n(v))}return m}const w=Xt(m);if(w.name==="playbackRate"&&w.maxValue===1024)throw c();try{this._nativeAudioNode.connect(w,y),(v||pn(this))&&this._nativeAudioNode.disconnect(w,y)}catch(T){throw T.code===12?o():T}if(wo(this,m,y,v)){const T=l([this],m);Jn(T,n(v))}}disconnect(m,y,b){let S;const v=p(this._context),w=f(v);if(m===void 0)S=So(this,w);else if(typeof m=="number"){if(m<0||m>=this.numberOfOutputs)throw s();S=xo(this,w,m)}else{if(y!==void 0&&(y<0||y>=this.numberOfOutputs)||yn(m)&&b!==void 0&&(b<0||b>=m.numberOfInputs))throw s();if(S=ko(this,w,m,y,b),S.length===0)throw o()}for(const A of S){const T=l([this],A);Jn(T,u)}}},Oo=(r,e,t,n,s,o,c,u,l,h,p,i,a)=>(d,f,_,g=null,m=null)=>{const y=_.value,b=new Fi(y),S=f?n(b):null,v={get defaultValue(){return y},get maxValue(){return g===null?_.maxValue:g},get minValue(){return m===null?_.minValue:m},get value(){return _.value},set value(w){_.value=w,v.setValueAtTime(w,d.context.currentTime)},cancelAndHoldAtTime(w){if(typeof _.cancelAndHoldAtTime=="function")S===null&&b.flush(d.context.currentTime),b.add(s(w)),_.cancelAndHoldAtTime(w);else{const A=Array.from(b).pop();S===null&&b.flush(d.context.currentTime),b.add(s(w));const T=Array.from(b).pop();_.cancelScheduledValues(w),A!==T&&T!==void 0&&(T.type==="exponentialRampToValue"?_.exponentialRampToValueAtTime(T.value,T.endTime):T.type==="linearRampToValue"?_.linearRampToValueAtTime(T.value,T.endTime):T.type==="setValue"?_.setValueAtTime(T.value,T.startTime):T.type==="setValueCurve"&&_.setValueCurveAtTime(T.values,T.startTime,T.duration))}return v},cancelScheduledValues(w){return S===null&&b.flush(d.context.currentTime),b.add(o(w)),_.cancelScheduledValues(w),v},exponentialRampToValueAtTime(w,A){if(w===0)throw new RangeError;if(!Number.isFinite(A)||A<0)throw new RangeError;const T=d.context.currentTime;return S===null&&b.flush(T),Array.from(b).length===0&&(b.add(h(y,T)),_.setValueAtTime(y,T)),b.add(c(w,A)),_.exponentialRampToValueAtTime(w,A),v},linearRampToValueAtTime(w,A){const T=d.context.currentTime;return S===null&&b.flush(T),Array.from(b).length===0&&(b.add(h(y,T)),_.setValueAtTime(y,T)),b.add(u(w,A)),_.linearRampToValueAtTime(w,A),v},setTargetAtTime(w,A,T){return S===null&&b.flush(d.context.currentTime),b.add(l(w,A,T)),_.setTargetAtTime(w,A,T),v},setValueAtTime(w,A){return S===null&&b.flush(d.context.currentTime),b.add(h(w,A)),_.setValueAtTime(w,A),v},setValueCurveAtTime(w,A,T){const x=w instanceof Float32Array?w:new Float32Array(w);if(i!==null&&i.name==="webkitAudioContext"){const O=A+T,C=d.context.sampleRate,k=Math.ceil(A*C),I=Math.floor(O*C),N=I-k,P=new Float32Array(N);for(let D=0;D<N;D+=1){const G=(x.length-1)/T*((k+D)/C-A),V=Math.floor(G),B=Math.ceil(G);P[D]=V===B?x[V]:(1-(G-V))*x[V]+(1-(B-G))*x[B]}S===null&&b.flush(d.context.currentTime),b.add(p(P,A,T)),_.setValueCurveAtTime(P,A,T);const E=I/C;E<O&&a(v,P[P.length-1],E),a(v,x[x.length-1],O)}else S===null&&b.flush(d.context.currentTime),b.add(p(x,A,T)),_.setValueCurveAtTime(x,A,T);return v}};return t.set(v,_),e.set(v,d),r(v,S),v},No=r=>({replay(e){for(const t of r)if(t.type==="exponentialRampToValue"){const{endTime:n,value:s}=t;e.exponentialRampToValueAtTime(s,n)}else if(t.type==="linearRampToValue"){const{endTime:n,value:s}=t;e.linearRampToValueAtTime(s,n)}else if(t.type==="setTarget"){const{startTime:n,target:s,timeConstant:o}=t;e.setTargetAtTime(s,n,o)}else if(t.type==="setValue"){const{startTime:n,value:s}=t;e.setValueAtTime(s,n)}else if(t.type==="setValueCurve"){const{duration:n,startTime:s,values:o}=t;e.setValueCurveAtTime(o,s,n)}else throw new Error("Can't apply an unknown automation.")}});class Dr{constructor(e){this._map=new Map(e)}get size(){return this._map.size}entries(){return this._map.entries()}forEach(e,t=null){return this._map.forEach((n,s)=>e.call(t,n,s,this))}get(e){return this._map.get(e)}has(e){return this._map.has(e)}keys(){return this._map.keys()}values(){return this._map.values()}}const Io={channelCount:2,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:1,numberOfOutputs:1,parameterData:{},processorOptions:{}},Mo=(r,e,t,n,s,o,c,u,l,h,p,i,a,d)=>class extends e{constructor(_,g,m){var y;const b=u(_),S=l(b),v=p({...Io,...m});a(v);const w=rs.get(b),A=w==null?void 0:w.get(g),T=S||b.state!=="closed"?b:(y=c(b))!==null&&y!==void 0?y:b,x=s(T,S?null:_.baseLatency,h,g,A,v),O=S?n(g,v,A):null;super(_,!0,x,O);const C=[];x.parameters.forEach((I,N)=>{const P=t(this,S,I);C.push([N,P])}),this._nativeAudioWorkletNode=x,this._onprocessorerror=null,this._parameters=new Dr(C),S&&r(b,this);const{activeInputs:k}=o(this);i(x,k)}get onprocessorerror(){return this._onprocessorerror}set onprocessorerror(_){const g=typeof _=="function"?d(this,_):null;this._nativeAudioWorkletNode.onprocessorerror=g;const m=this._nativeAudioWorkletNode.onprocessorerror;this._onprocessorerror=m!==null&&m===g?_:m}get parameters(){return this._parameters===null?this._nativeAudioWorkletNode.parameters:this._parameters}get port(){return this._nativeAudioWorkletNode.port}};function An(r,e,t,n,s){if(typeof r.copyFromChannel=="function")e[t].byteLength===0&&(e[t]=new Float32Array(128)),r.copyFromChannel(e[t],n,s);else{const o=r.getChannelData(n);if(e[t].byteLength===0)e[t]=o.slice(s,s+128);else{const c=new Float32Array(o.buffer,s*Float32Array.BYTES_PER_ELEMENT,128);e[t].set(c)}}}const Rr=(r,e,t,n,s)=>{typeof r.copyToChannel=="function"?e[t].byteLength!==0&&r.copyToChannel(e[t],n,s):e[t].byteLength!==0&&r.getChannelData(n).set(e[t],s)},Sn=(r,e)=>{const t=[];for(let n=0;n<r;n+=1){const s=[],o=typeof e=="number"?e:e[n];for(let c=0;c<o;c+=1)s.push(new Float32Array(128));t.push(s)}return t},Eo=(r,e)=>{const t=Le(is,r),n=J(e);return Le(t,n)},Do=async(r,e,t,n,s,o,c)=>{const u=e===null?Math.ceil(r.context.length/128)*128:e.length,l=n.channelCount*n.numberOfInputs,h=s.reduce((g,m)=>g+m,0),p=h===0?null:t.createBuffer(h,u,t.sampleRate);if(o===void 0)throw new Error("Missing the processor constructor.");const i=Te(r),a=await Eo(t,r),d=Sn(n.numberOfInputs,n.channelCount),f=Sn(n.numberOfOutputs,s),_=Array.from(r.parameters.keys()).reduce((g,m)=>({...g,[m]:new Float32Array(128)}),{});for(let g=0;g<u;g+=128){if(n.numberOfInputs>0&&e!==null)for(let m=0;m<n.numberOfInputs;m+=1)for(let y=0;y<n.channelCount;y+=1)An(e,d[m],y,y,g);o.parameterDescriptors!==void 0&&e!==null&&o.parameterDescriptors.forEach(({name:m},y)=>{An(e,_,m,l+y,g)});for(let m=0;m<n.numberOfInputs;m+=1)for(let y=0;y<s[m];y+=1)f[m][y].byteLength===0&&(f[m][y]=new Float32Array(128));try{const m=d.map((b,S)=>i.activeInputs[S].size===0?[]:b),y=c(g/t.sampleRate,t.sampleRate,()=>a.process(m,f,_));if(p!==null)for(let b=0,S=0;b<n.numberOfOutputs;b+=1){for(let v=0;v<s[b];v+=1)Rr(p,f[b],v,S+v,g);S+=s[b]}if(!y)break}catch(m){r.dispatchEvent(new ErrorEvent("processorerror",{colno:m.colno,filename:m.filename,lineno:m.lineno,message:m.message}));break}}return p},Ro=(r,e,t,n,s,o,c,u,l,h,p,i,a,d,f,_)=>(g,m,y)=>{const b=new WeakMap;let S=null;const v=async(w,A)=>{let T=p(w),x=null;const O=ge(T,A),C=Array.isArray(m.outputChannelCount)?m.outputChannelCount:Array.from(m.outputChannelCount);if(i===null){const k=C.reduce((E,D)=>E+D,0),I=s(A,{channelCount:Math.max(1,k),channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:Math.max(1,k)}),N=[];for(let E=0;E<w.numberOfOutputs;E+=1)N.push(n(A,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:C[E]}));const P=c(A,{channelCount:m.channelCount,channelCountMode:m.channelCountMode,channelInterpretation:m.channelInterpretation,gain:1});P.connect=e.bind(null,N),P.disconnect=l.bind(null,N),x=[I,N,P]}else O||(T=new i(A,g));if(b.set(A,x===null?T:x[2]),x!==null){if(S===null){if(y===void 0)throw new Error("Missing the processor constructor.");if(a===null)throw new Error("Missing the native OfflineAudioContext constructor.");const D=w.channelCount*w.numberOfInputs,G=y.parameterDescriptors===void 0?0:y.parameterDescriptors.length,V=D+G;S=Do(w,V===0?null:await(async()=>{const q=new a(V,Math.ceil(w.context.length/128)*128,A.sampleRate),Z=[],xe=[];for(let K=0;K<m.numberOfInputs;K+=1)Z.push(c(q,{channelCount:m.channelCount,channelCountMode:m.channelCountMode,channelInterpretation:m.channelInterpretation,gain:1})),xe.push(s(q,{channelCount:m.channelCount,channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:m.channelCount}));const ke=await Promise.all(Array.from(w.parameters.values()).map(async K=>{const ve=o(q,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",offset:K.value});return await d(q,K,ve.offset),ve})),U=n(q,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:Math.max(1,D+G)});for(let K=0;K<m.numberOfInputs;K+=1){Z[K].connect(xe[K]);for(let ve=0;ve<m.channelCount;ve+=1)xe[K].connect(U,ve,K*m.channelCount+ve)}for(const[K,ve]of ke.entries())ve.connect(U,0,D+K),ve.start(0);return U.connect(q.destination),await Promise.all(Z.map(K=>f(w,q,K))),_(q)})(),A,m,C,y,h)}const k=await S,I=t(A,{buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1}),[N,P,E]=x;k!==null&&(I.buffer=k,I.start(0)),I.connect(N);for(let D=0,G=0;D<w.numberOfOutputs;D+=1){const V=P[D];for(let B=0;B<C[D];B+=1)N.connect(V,G+B,B);G+=C[D]}return E}if(O)for(const[k,I]of w.parameters.entries())await r(A,I,T.parameters.get(k));else for(const[k,I]of w.parameters.entries())await d(A,I,T.parameters.get(k));return await f(w,A,T),T};return{render(w,A){u(A,w);const T=b.get(A);return T!==void 0?Promise.resolve(T):v(w,A)}}},Po=(r,e,t,n,s,o,c,u,l,h,p,i,a,d,f,_,g,m,y,b)=>class extends f{constructor(v,w){super(v,w),this._nativeContext=v,this._audioWorklet=r===void 0?void 0:{addModule:(A,T)=>r(this,A,T)}}get audioWorklet(){return this._audioWorklet}createAnalyser(){return new e(this)}createBiquadFilter(){return new s(this)}createBuffer(v,w,A){return new t({length:w,numberOfChannels:v,sampleRate:A})}createBufferSource(){return new n(this)}createChannelMerger(v=6){return new o(this,{numberOfInputs:v})}createChannelSplitter(v=6){return new c(this,{numberOfOutputs:v})}createConstantSource(){return new u(this)}createConvolver(){return new l(this)}createDelay(v=1){return new p(this,{maxDelayTime:v})}createDynamicsCompressor(){return new i(this)}createGain(){return new a(this)}createIIRFilter(v,w){return new d(this,{feedback:w,feedforward:v})}createOscillator(){return new _(this)}createPanner(){return new g(this)}createPeriodicWave(v,w,A={disableNormalization:!1}){return new m(this,{...A,imag:w,real:v})}createStereoPanner(){return new y(this)}createWaveShaper(){return new b(this)}decodeAudioData(v,w,A){return h(this._nativeContext,v).then(T=>(typeof w=="function"&&w(T),T),T=>{throw typeof A=="function"&&A(T),T})}},Vo={Q:1,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",detune:0,frequency:350,gain:0,type:"lowpass"},Fo=(r,e,t,n,s,o,c,u)=>class extends r{constructor(h,p){const i=o(h),a={...Vo,...p},d=s(i,a),f=c(i),_=f?t():null;super(h,!1,d,_),this._Q=e(this,f,d.Q,ye,Ae),this._detune=e(this,f,d.detune,1200*Math.log2(ye),-1200*Math.log2(ye)),this._frequency=e(this,f,d.frequency,h.sampleRate/2,0),this._gain=e(this,f,d.gain,40*Math.log10(ye),Ae),this._nativeBiquadFilterNode=d,u(this,1)}get detune(){return this._detune}get frequency(){return this._frequency}get gain(){return this._gain}get Q(){return this._Q}get type(){return this._nativeBiquadFilterNode.type}set type(h){this._nativeBiquadFilterNode.type=h}getFrequencyResponse(h,p,i){try{this._nativeBiquadFilterNode.getFrequencyResponse(h,p,i)}catch(a){throw a.code===11?n():a}if(h.length!==p.length||p.length!==i.length)throw n()}},Lo=(r,e,t,n,s)=>()=>{const o=new WeakMap,c=async(u,l)=>{let h=t(u);const p=ge(h,l);if(!p){const i={Q:h.Q.value,channelCount:h.channelCount,channelCountMode:h.channelCountMode,channelInterpretation:h.channelInterpretation,detune:h.detune.value,frequency:h.frequency.value,gain:h.gain.value,type:h.type};h=e(l,i)}return o.set(l,h),p?(await r(l,u.Q,h.Q),await r(l,u.detune,h.detune),await r(l,u.frequency,h.frequency),await r(l,u.gain,h.gain)):(await n(l,u.Q,h.Q),await n(l,u.detune,h.detune),await n(l,u.frequency,h.frequency),await n(l,u.gain,h.gain)),await s(u,l,h),h};return{render(u,l){const h=o.get(l);return h!==void 0?Promise.resolve(h):c(u,l)}}},Wo=(r,e)=>(t,n)=>{const s=e.get(t);if(s!==void 0)return s;const o=r.get(t);if(o!==void 0)return o;try{const c=n();return c instanceof Promise?(r.set(t,c),c.catch(()=>!1).then(u=>(r.delete(t),e.set(t,u),u))):(e.set(t,c),c)}catch{return e.set(t,!1),!1}},qo={channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:6},Bo=(r,e,t,n,s)=>class extends r{constructor(c,u){const l=n(c),h={...qo,...u},p=t(l,h),i=s(l)?e():null;super(c,!1,p,i)}},jo=(r,e,t)=>()=>{const n=new WeakMap,s=async(o,c)=>{let u=e(o);if(!ge(u,c)){const h={channelCount:u.channelCount,channelCountMode:u.channelCountMode,channelInterpretation:u.channelInterpretation,numberOfInputs:u.numberOfInputs};u=r(c,h)}return n.set(c,u),await t(o,c,u),u};return{render(o,c){const u=n.get(c);return u!==void 0?Promise.resolve(u):s(o,c)}}},Go={channelCount:6,channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:6},Uo=(r,e,t,n,s,o)=>class extends r{constructor(u,l){const h=n(u),p=o({...Go,...l}),i=t(h,p),a=s(h)?e():null;super(u,!1,i,a)}},Ho=(r,e,t)=>()=>{const n=new WeakMap,s=async(o,c)=>{let u=e(o);if(!ge(u,c)){const h={channelCount:u.channelCount,channelCountMode:u.channelCountMode,channelInterpretation:u.channelInterpretation,numberOfOutputs:u.numberOfOutputs};u=r(c,h)}return n.set(c,u),await t(o,c,u),u};return{render(o,c){const u=n.get(c);return u!==void 0?Promise.resolve(u):s(o,c)}}},$o=r=>(e,t,n)=>r(t,e,n),Xo=r=>(e,t,n=0,s=0)=>{const o=e[n];if(o===void 0)throw r();return bn(t)?o.connect(t,0,s):o.connect(t,0)},Zo=r=>(e,t)=>{const n=r(e,{buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1}),s=e.createBuffer(1,2,44100);return n.buffer=s,n.loop=!0,n.connect(t),n.start(),()=>{n.stop(),n.disconnect(t)}},zo={channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",offset:1},Yo=(r,e,t,n,s,o,c)=>class extends r{constructor(l,h){const p=s(l),i={...zo,...h},a=n(p,i),d=o(p),f=d?t():null;super(l,!1,a,f),this._constantSourceNodeRenderer=f,this._nativeConstantSourceNode=a,this._offset=e(this,d,a.offset,ye,Ae),this._onended=null}get offset(){return this._offset}get onended(){return this._onended}set onended(l){const h=typeof l=="function"?c(this,l):null;this._nativeConstantSourceNode.onended=h;const p=this._nativeConstantSourceNode.onended;this._onended=p!==null&&p===h?l:p}start(l=0){if(this._nativeConstantSourceNode.start(l),this._constantSourceNodeRenderer!==null&&(this._constantSourceNodeRenderer.start=l),this.context.state!=="closed"){Et(this);const h=()=>{this._nativeConstantSourceNode.removeEventListener("ended",h),ze(this)&&Jt(this)};this._nativeConstantSourceNode.addEventListener("ended",h)}}stop(l=0){this._nativeConstantSourceNode.stop(l),this._constantSourceNodeRenderer!==null&&(this._constantSourceNodeRenderer.stop=l)}},Qo=(r,e,t,n,s)=>()=>{const o=new WeakMap;let c=null,u=null;const l=async(h,p)=>{let i=t(h);const a=ge(i,p);if(!a){const d={channelCount:i.channelCount,channelCountMode:i.channelCountMode,channelInterpretation:i.channelInterpretation,offset:i.offset.value};i=e(p,d),c!==null&&i.start(c),u!==null&&i.stop(u)}return o.set(p,i),a?await r(p,h.offset,i.offset):await n(p,h.offset,i.offset),await s(h,p,i),i};return{set start(h){c=h},set stop(h){u=h},render(h,p){const i=o.get(p);return i!==void 0?Promise.resolve(i):l(h,p)}}},Jo=r=>e=>(r[0]=e,r[0]),Ko={buffer:null,channelCount:2,channelCountMode:"clamped-max",channelInterpretation:"speakers",disableNormalization:!1},ea=(r,e,t,n,s,o)=>class extends r{constructor(u,l){const h=n(u),p={...Ko,...l},i=t(h,p),d=s(h)?e():null;super(u,!1,i,d),this._isBufferNullified=!1,this._nativeConvolverNode=i,p.buffer!==null&&o(this,p.buffer.duration)}get buffer(){return this._isBufferNullified?null:this._nativeConvolverNode.buffer}set buffer(u){if(this._nativeConvolverNode.buffer=u,u===null&&this._nativeConvolverNode.buffer!==null){const l=this._nativeConvolverNode.context;this._nativeConvolverNode.buffer=l.createBuffer(1,1,l.sampleRate),this._isBufferNullified=!0,o(this,0)}else this._isBufferNullified=!1,o(this,this._nativeConvolverNode.buffer===null?0:this._nativeConvolverNode.buffer.duration)}get normalize(){return this._nativeConvolverNode.normalize}set normalize(u){this._nativeConvolverNode.normalize=u}},ta=(r,e,t)=>()=>{const n=new WeakMap,s=async(o,c)=>{let u=e(o);if(!ge(u,c)){const h={buffer:u.buffer,channelCount:u.channelCount,channelCountMode:u.channelCountMode,channelInterpretation:u.channelInterpretation,disableNormalization:!u.normalize};u=r(c,h)}return n.set(c,u),Vt(u)?await t(o,c,u.inputs[0]):await t(o,c,u),u};return{render(o,c){const u=n.get(c);return u!==void 0?Promise.resolve(u):s(o,c)}}},na=(r,e)=>(t,n,s)=>{if(e===null)throw new Error("Missing the native OfflineAudioContext constructor.");try{return new e(t,n,s)}catch(o){throw o.name==="SyntaxError"?r():o}},sa=()=>new DOMException("","DataCloneError"),or=r=>{const{port1:e,port2:t}=new MessageChannel;return new Promise(n=>{const s=()=>{t.onmessage=null,e.close(),t.close(),n()};t.onmessage=()=>s();try{e.postMessage(r,[r])}catch{}finally{s()}})},ra=(r,e,t,n,s,o,c,u,l,h,p)=>(i,a)=>{const d=c(i)?i:o(i);if(s.has(a)){const f=t();return Promise.reject(f)}try{s.add(a)}catch{}return e(l,()=>l(d))?d.decodeAudioData(a).then(f=>(or(a).catch(()=>{}),e(u,()=>u(f))||p(f),r.add(f),f)):new Promise((f,_)=>{const g=async()=>{try{await or(a)}catch{}},m=y=>{_(y),g()};try{d.decodeAudioData(a,y=>{typeof y.copyFromChannel!="function"&&(h(y),ps(y)),r.add(y),g().then(()=>f(y))},y=>{m(y===null?n():y)})}catch(y){m(y)}})},ia=(r,e,t,n,s,o,c,u)=>(l,h)=>{const p=e.get(l);if(p===void 0)throw new Error("Missing the expected cycle count.");const i=o(l.context),a=u(i);if(p===h){if(e.delete(l),!a&&c(l)){const d=n(l),{outputs:f}=t(l);for(const _ of f)if(en(_)){const g=n(_[0]);r(d,g,_[1],_[2])}else{const g=s(_[0]);d.connect(g,_[1])}}}else e.set(l,p-h)},oa={channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",delayTime:0,maxDelayTime:1},aa=(r,e,t,n,s,o,c)=>class extends r{constructor(l,h){const p=s(l),i={...oa,...h},a=n(p,i),d=o(p),f=d?t(i.maxDelayTime):null;super(l,!1,a,f),this._delayTime=e(this,d,a.delayTime),c(this,i.maxDelayTime)}get delayTime(){return this._delayTime}},ca=(r,e,t,n,s)=>o=>{const c=new WeakMap,u=async(l,h)=>{let p=t(l);const i=ge(p,h);if(!i){const a={channelCount:p.channelCount,channelCountMode:p.channelCountMode,channelInterpretation:p.channelInterpretation,delayTime:p.delayTime.value,maxDelayTime:o};p=e(h,a)}return c.set(h,p),i?await r(h,l.delayTime,p.delayTime):await n(h,l.delayTime,p.delayTime),await s(l,h,p),p};return{render(l,h){const p=c.get(h);return p!==void 0?Promise.resolve(p):u(l,h)}}},ua=r=>(e,t,n,s)=>r(e[s],o=>o[0]===t&&o[1]===n),la=r=>(e,t)=>{r(e).delete(t)},ha=r=>"delayTime"in r,da=(r,e,t)=>function n(s,o){const c=yn(o)?o:t(r,o);if(ha(c))return[];if(s[0]===c)return[s];if(s.includes(c))return[];const{outputs:u}=e(c);return Array.from(u).map(l=>n([...s,c],l[0])).reduce((l,h)=>l.concat(h),[])},hn=(r,e,t)=>{const n=e[t];if(n===void 0)throw r();return n},fa=r=>(e,t=void 0,n=void 0,s=0)=>t===void 0?e.forEach(o=>o.disconnect()):typeof t=="number"?hn(r,e,t).disconnect():bn(t)?n===void 0?e.forEach(o=>o.disconnect(t)):s===void 0?hn(r,e,n).disconnect(t,0):hn(r,e,n).disconnect(t,0,s):n===void 0?e.forEach(o=>o.disconnect(t)):hn(r,e,n).disconnect(t,0),pa={attack:.003,channelCount:2,channelCountMode:"clamped-max",channelInterpretation:"speakers",knee:30,ratio:12,release:.25,threshold:-24},_a=(r,e,t,n,s,o,c,u)=>class extends r{constructor(h,p){const i=o(h),a={...pa,...p},d=n(i,a),f=c(i),_=f?t():null;super(h,!1,d,_),this._attack=e(this,f,d.attack),this._knee=e(this,f,d.knee),this._nativeDynamicsCompressorNode=d,this._ratio=e(this,f,d.ratio),this._release=e(this,f,d.release),this._threshold=e(this,f,d.threshold),u(this,.006)}get attack(){return this._attack}get channelCount(){return this._nativeDynamicsCompressorNode.channelCount}set channelCount(h){const p=this._nativeDynamicsCompressorNode.channelCount;if(this._nativeDynamicsCompressorNode.channelCount=h,h>2)throw this._nativeDynamicsCompressorNode.channelCount=p,s()}get channelCountMode(){return this._nativeDynamicsCompressorNode.channelCountMode}set channelCountMode(h){const p=this._nativeDynamicsCompressorNode.channelCountMode;if(this._nativeDynamicsCompressorNode.channelCountMode=h,h==="max")throw this._nativeDynamicsCompressorNode.channelCountMode=p,s()}get knee(){return this._knee}get ratio(){return this._ratio}get reduction(){return typeof this._nativeDynamicsCompressorNode.reduction.value=="number"?this._nativeDynamicsCompressorNode.reduction.value:this._nativeDynamicsCompressorNode.reduction}get release(){return this._release}get threshold(){return this._threshold}},ma=(r,e,t,n,s)=>()=>{const o=new WeakMap,c=async(u,l)=>{let h=t(u);const p=ge(h,l);if(!p){const i={attack:h.attack.value,channelCount:h.channelCount,channelCountMode:h.channelCountMode,channelInterpretation:h.channelInterpretation,knee:h.knee.value,ratio:h.ratio.value,release:h.release.value,threshold:h.threshold.value};h=e(l,i)}return o.set(l,h),p?(await r(l,u.attack,h.attack),await r(l,u.knee,h.knee),await r(l,u.ratio,h.ratio),await r(l,u.release,h.release),await r(l,u.threshold,h.threshold)):(await n(l,u.attack,h.attack),await n(l,u.knee,h.knee),await n(l,u.ratio,h.ratio),await n(l,u.release,h.release),await n(l,u.threshold,h.threshold)),await s(u,l,h),h};return{render(u,l){const h=o.get(l);return h!==void 0?Promise.resolve(h):c(u,l)}}},ga=()=>new DOMException("","EncodingError"),va=r=>e=>new Promise((t,n)=>{if(r===null){n(new SyntaxError);return}const s=r.document.head;if(s===null)n(new SyntaxError);else{const o=r.document.createElement("script"),c=new Blob([e],{type:"application/javascript"}),u=URL.createObjectURL(c),l=r.onerror,h=()=>{r.onerror=l,URL.revokeObjectURL(u)};r.onerror=(p,i,a,d,f)=>{if(i===u||i===r.location.href&&a===1&&d===1)return h(),n(f),!1;if(l!==null)return l(p,i,a,d,f)},o.onerror=()=>{h(),n(new SyntaxError)},o.onload=()=>{h(),t()},o.src=u,o.type="module",s.appendChild(o)}}),ya=r=>class{constructor(t){this._nativeEventTarget=t,this._listeners=new WeakMap}addEventListener(t,n,s){if(n!==null){let o=this._listeners.get(n);o===void 0&&(o=r(this,n),typeof n=="function"&&this._listeners.set(n,o)),this._nativeEventTarget.addEventListener(t,o,s)}}dispatchEvent(t){return this._nativeEventTarget.dispatchEvent(t)}removeEventListener(t,n,s){const o=n===null?void 0:this._listeners.get(n);this._nativeEventTarget.removeEventListener(t,o===void 0?null:o,s)}},Ta=r=>(e,t,n)=>{Object.defineProperties(r,{currentFrame:{configurable:!0,get(){return Math.round(e*t)}},currentTime:{configurable:!0,get(){return e}}});try{return n()}finally{r!==null&&(delete r.currentFrame,delete r.currentTime)}},wa=r=>async e=>{try{const t=await fetch(e);if(t.ok)return[await t.text(),t.url]}catch{}throw r()},ba={channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",gain:1},Aa=(r,e,t,n,s,o)=>class extends r{constructor(u,l){const h=s(u),p={...ba,...l},i=n(h,p),a=o(h),d=a?t():null;super(u,!1,i,d),this._gain=e(this,a,i.gain,ye,Ae)}get gain(){return this._gain}},Sa=(r,e,t,n,s)=>()=>{const o=new WeakMap,c=async(u,l)=>{let h=t(u);const p=ge(h,l);if(!p){const i={channelCount:h.channelCount,channelCountMode:h.channelCountMode,channelInterpretation:h.channelInterpretation,gain:h.gain.value};h=e(l,i)}return o.set(l,h),p?await r(l,u.gain,h.gain):await n(l,u.gain,h.gain),await s(u,l,h),h};return{render(u,l){const h=o.get(l);return h!==void 0?Promise.resolve(h):c(u,l)}}},xa=(r,e)=>t=>e(r,t),ka=r=>e=>{const t=r(e);if(t.renderer===null)throw new Error("Missing the renderer of the given AudioNode in the audio graph.");return t.renderer},Ca=r=>e=>{var t;return(t=r.get(e))!==null&&t!==void 0?t:0},Oa=r=>e=>{const t=r(e);if(t.renderer===null)throw new Error("Missing the renderer of the given AudioParam in the audio graph.");return t.renderer},Na=r=>e=>r.get(e),pe=()=>new DOMException("","InvalidStateError"),Ia=r=>e=>{const t=r.get(e);if(t===void 0)throw pe();return t},Ma=(r,e)=>t=>{let n=r.get(t);if(n!==void 0)return n;if(e===null)throw new Error("Missing the native OfflineAudioContext constructor.");return n=new e(1,1,44100),r.set(t,n),n},Ea=r=>e=>{const t=r.get(e);if(t===void 0)throw new Error("The context has no set of AudioWorkletNodes.");return t},En=()=>new DOMException("","InvalidAccessError"),Da=r=>{r.getFrequencyResponse=(e=>(t,n,s)=>{if(t.length!==n.length||n.length!==s.length)throw En();return e.call(r,t,n,s)})(r.getFrequencyResponse)},Ra={channelCount:2,channelCountMode:"max",channelInterpretation:"speakers"},Pa=(r,e,t,n,s,o)=>class extends r{constructor(u,l){const h=n(u),p=s(h),i={...Ra,...l},a=e(h,p?null:u.baseLatency,i),d=p?t(i.feedback,i.feedforward):null;super(u,!1,a,d),Da(a),this._nativeIIRFilterNode=a,o(this,1)}getFrequencyResponse(u,l,h){return this._nativeIIRFilterNode.getFrequencyResponse(u,l,h)}},Pr=(r,e,t,n,s,o,c,u,l,h,p)=>{const i=h.length;let a=u;for(let d=0;d<i;d+=1){let f=t[0]*h[d];for(let _=1;_<s;_+=1){const g=a-_&l-1;f+=t[_]*o[g],f-=r[_]*c[g]}for(let _=s;_<n;_+=1)f+=t[_]*o[a-_&l-1];for(let _=s;_<e;_+=1)f-=r[_]*c[a-_&l-1];o[a]=h[d],c[a]=f,a=a+1&l-1,p[d]=f}return a},Va=(r,e,t,n)=>{const s=t instanceof Float64Array?t:new Float64Array(t),o=n instanceof Float64Array?n:new Float64Array(n),c=s.length,u=o.length,l=Math.min(c,u);if(s[0]!==1){for(let f=0;f<c;f+=1)o[f]/=s[0];for(let f=1;f<u;f+=1)s[f]/=s[0]}const h=32,p=new Float32Array(h),i=new Float32Array(h),a=e.createBuffer(r.numberOfChannels,r.length,r.sampleRate),d=r.numberOfChannels;for(let f=0;f<d;f+=1){const _=r.getChannelData(f),g=a.getChannelData(f);p.fill(0),i.fill(0),Pr(s,c,o,u,l,p,i,0,h,_,g)}return a},Fa=(r,e,t,n,s)=>(o,c)=>{const u=new WeakMap;let l=null;const h=async(p,i)=>{let a=null,d=e(p);const f=ge(d,i);if(i.createIIRFilter===void 0?a=r(i,{buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1}):f||(d=i.createIIRFilter(c,o)),u.set(i,a===null?d:a),a!==null){if(l===null){if(t===null)throw new Error("Missing the native OfflineAudioContext constructor.");const g=new t(p.context.destination.channelCount,p.context.length,i.sampleRate);l=(async()=>{await n(p,g,g.destination);const m=await s(g);return Va(m,i,o,c)})()}const _=await l;return a.buffer=_,a.start(0),a}return await n(p,i,d),d};return{render(p,i){const a=u.get(i);return a!==void 0?Promise.resolve(a):h(p,i)}}},La=(r,e,t,n,s,o)=>c=>(u,l)=>{const h=r.get(u);if(h===void 0){if(!c&&o(u)){const p=n(u),{outputs:i}=t(u);for(const a of i)if(en(a)){const d=n(a[0]);e(p,d,a[1],a[2])}else{const d=s(a[0]);p.disconnect(d,a[1])}}r.set(u,l)}else r.set(u,h+l)},Wa=(r,e)=>t=>{const n=r.get(t);return e(n)||e(t)},qa=(r,e)=>t=>r.has(t)||e(t),Ba=(r,e)=>t=>r.has(t)||e(t),ja=(r,e)=>t=>{const n=r.get(t);return e(n)||e(t)},Ga=r=>e=>r!==null&&e instanceof r,Ua=r=>e=>r!==null&&typeof r.AudioNode=="function"&&e instanceof r.AudioNode,Ha=r=>e=>r!==null&&typeof r.AudioParam=="function"&&e instanceof r.AudioParam,$a=(r,e)=>t=>r(t)||e(t),Xa=r=>e=>r!==null&&e instanceof r,Za=r=>r!==null&&r.isSecureContext,za=(r,e,t,n)=>class extends r{constructor(o,c){const u=t(o),l=e(u,c);if(n(u))throw TypeError();super(o,!0,l,null),this._nativeMediaElementAudioSourceNode=l}get mediaElement(){return this._nativeMediaElementAudioSourceNode.mediaElement}},Ya={channelCount:2,channelCountMode:"explicit",channelInterpretation:"speakers"},Qa=(r,e,t,n)=>class extends r{constructor(o,c){const u=t(o);if(n(u))throw new TypeError;const l={...Ya,...c},h=e(u,l);super(o,!1,h,null),this._nativeMediaStreamAudioDestinationNode=h}get stream(){return this._nativeMediaStreamAudioDestinationNode.stream}},Ja=(r,e,t,n)=>class extends r{constructor(o,c){const u=t(o),l=e(u,c);if(n(u))throw new TypeError;super(o,!0,l,null),this._nativeMediaStreamAudioSourceNode=l}get mediaStream(){return this._nativeMediaStreamAudioSourceNode.mediaStream}},Ka=(r,e,t)=>class extends r{constructor(s,o){const c=t(s),u=e(c,o);super(s,!0,u,null)}},ec=(r,e,t,n,s,o)=>class extends t{constructor(u,l){super(u),this._nativeContext=u,In.set(this,u),n(u)&&s.set(u,new Set),this._destination=new r(this,l),this._listener=e(this,u),this._onstatechange=null}get currentTime(){return this._nativeContext.currentTime}get destination(){return this._destination}get listener(){return this._listener}get onstatechange(){return this._onstatechange}set onstatechange(u){const l=typeof u=="function"?o(this,u):null;this._nativeContext.onstatechange=l;const h=this._nativeContext.onstatechange;this._onstatechange=h!==null&&h===l?u:h}get sampleRate(){return this._nativeContext.sampleRate}get state(){return this._nativeContext.state}},Zt=r=>{const e=new Uint32Array([1179011410,40,1163280727,544501094,16,131073,44100,176400,1048580,1635017060,4,0]);try{const t=r.decodeAudioData(e.buffer,()=>{});return t===void 0?!1:(t.catch(()=>{}),!0)}catch{}return!1},tc=(r,e)=>(t,n,s)=>{const o=new Set;return t.connect=(c=>(u,l=0,h=0)=>{const p=o.size===0;if(e(u))return c.call(t,u,l,h),r(o,[u,l,h],i=>i[0]===u&&i[1]===l&&i[2]===h,!0),p&&n(),u;c.call(t,u,l),r(o,[u,l],i=>i[0]===u&&i[1]===l,!0),p&&n()})(t.connect),t.disconnect=(c=>(u,l,h)=>{const p=o.size>0;if(u===void 0)c.apply(t),o.clear();else if(typeof u=="number"){c.call(t,u);for(const a of o)a[1]===u&&o.delete(a)}else{e(u)?c.call(t,u,l,h):c.call(t,u,l);for(const a of o)a[0]===u&&(l===void 0||a[1]===l)&&(h===void 0||a[2]===h)&&o.delete(a)}const i=o.size===0;p&&i&&s()})(t.disconnect),t},ee=(r,e,t)=>{const n=e[t];n!==void 0&&n!==r[t]&&(r[t]=n)},de=(r,e)=>{ee(r,e,"channelCount"),ee(r,e,"channelCountMode"),ee(r,e,"channelInterpretation")},ar=r=>typeof r.getFloatTimeDomainData=="function",nc=r=>{r.getFloatTimeDomainData=e=>{const t=new Uint8Array(e.length);r.getByteTimeDomainData(t);const n=Math.max(t.length,r.fftSize);for(let s=0;s<n;s+=1)e[s]=(t[s]-128)*.0078125;return e}},sc=(r,e)=>(t,n)=>{const s=t.createAnalyser();if(de(s,n),!(n.maxDecibels>n.minDecibels))throw e();return ee(s,n,"fftSize"),ee(s,n,"maxDecibels"),ee(s,n,"minDecibels"),ee(s,n,"smoothingTimeConstant"),r(ar,()=>ar(s))||nc(s),s},rc=r=>r===null?null:r.hasOwnProperty("AudioBuffer")?r.AudioBuffer:null,oe=(r,e,t)=>{const n=e[t];n!==void 0&&n!==r[t].value&&(r[t].value=n)},ic=r=>{r.start=(e=>{let t=!1;return(n=0,s=0,o)=>{if(t)throw pe();e.call(r,n,s,o),t=!0}})(r.start)},gs=r=>{r.start=(e=>(t=0,n=0,s)=>{if(typeof s=="number"&&s<0||n<0||t<0)throw new RangeError("The parameters can't be negative.");e.call(r,t,n,s)})(r.start)},vs=r=>{r.stop=(e=>(t=0)=>{if(t<0)throw new RangeError("The parameter can't be negative.");e.call(r,t)})(r.stop)},oc=(r,e,t,n,s,o,c,u,l,h,p)=>(i,a)=>{const d=i.createBufferSource();return de(d,a),oe(d,a,"playbackRate"),ee(d,a,"buffer"),ee(d,a,"loop"),ee(d,a,"loopEnd"),ee(d,a,"loopStart"),e(t,()=>t(i))||ic(d),e(n,()=>n(i))||l(d),e(s,()=>s(i))||h(d,i),e(o,()=>o(i))||gs(d),e(c,()=>c(i))||p(d,i),e(u,()=>u(i))||vs(d),r(i,d),d},ac=r=>r===null?null:r.hasOwnProperty("AudioContext")?r.AudioContext:r.hasOwnProperty("webkitAudioContext")?r.webkitAudioContext:null,cc=(r,e)=>(t,n,s)=>{const o=t.destination;if(o.channelCount!==n)try{o.channelCount=n}catch{}s&&o.channelCountMode!=="explicit"&&(o.channelCountMode="explicit"),o.maxChannelCount===0&&Object.defineProperty(o,"maxChannelCount",{value:n});const c=r(t,{channelCount:n,channelCountMode:o.channelCountMode,channelInterpretation:o.channelInterpretation,gain:1});return e(c,"channelCount",u=>()=>u.call(c),u=>l=>{u.call(c,l);try{o.channelCount=l}catch(h){if(l>o.maxChannelCount)throw h}}),e(c,"channelCountMode",u=>()=>u.call(c),u=>l=>{u.call(c,l),o.channelCountMode=l}),e(c,"channelInterpretation",u=>()=>u.call(c),u=>l=>{u.call(c,l),o.channelInterpretation=l}),Object.defineProperty(c,"maxChannelCount",{get:()=>o.maxChannelCount}),c.connect(o),c},uc=r=>r===null?null:r.hasOwnProperty("AudioWorkletNode")?r.AudioWorkletNode:null,lc=r=>{const{port1:e}=new MessageChannel;try{e.postMessage(r)}finally{e.close()}},hc=(r,e,t,n,s)=>(o,c,u,l,h,p)=>{if(u!==null)try{const i=new u(o,l,p),a=new Map;let d=null;if(Object.defineProperties(i,{channelCount:{get:()=>p.channelCount,set:()=>{throw r()}},channelCountMode:{get:()=>"explicit",set:()=>{throw r()}},onprocessorerror:{get:()=>d,set:f=>{typeof d=="function"&&i.removeEventListener("processorerror",d),d=typeof f=="function"?f:null,typeof d=="function"&&i.addEventListener("processorerror",d)}}}),i.addEventListener=(f=>(..._)=>{if(_[0]==="processorerror"){const g=typeof _[1]=="function"?_[1]:typeof _[1]=="object"&&_[1]!==null&&typeof _[1].handleEvent=="function"?_[1].handleEvent:null;if(g!==null){const m=a.get(_[1]);m!==void 0?_[1]=m:(_[1]=y=>{y.type==="error"?(Object.defineProperties(y,{type:{value:"processorerror"}}),g(y)):g(new ErrorEvent(_[0],{...y}))},a.set(g,_[1]))}}return f.call(i,"error",_[1],_[2]),f.call(i,..._)})(i.addEventListener),i.removeEventListener=(f=>(..._)=>{if(_[0]==="processorerror"){const g=a.get(_[1]);g!==void 0&&(a.delete(_[1]),_[1]=g)}return f.call(i,"error",_[1],_[2]),f.call(i,_[0],_[1],_[2])})(i.removeEventListener),p.numberOfOutputs!==0){const f=t(o,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});return i.connect(f).connect(o.destination),s(i,()=>f.disconnect(),()=>f.connect(o.destination))}return i}catch(i){throw i.code===11?n():i}if(h===void 0)throw n();return lc(p),e(o,c,h,p)},Vr=(r,e)=>r===null?512:Math.max(512,Math.min(16384,Math.pow(2,Math.round(Math.log2(r*e))))),dc=r=>new Promise((e,t)=>{const{port1:n,port2:s}=new MessageChannel;n.onmessage=({data:o})=>{n.close(),s.close(),e(o)},n.onmessageerror=({data:o})=>{n.close(),s.close(),t(o)},s.postMessage(r)}),fc=async(r,e)=>{const t=await dc(e);return new r(t)},pc=(r,e,t,n)=>{let s=is.get(r);s===void 0&&(s=new WeakMap,is.set(r,s));const o=fc(t,n);return s.set(e,o),o},_c=(r,e,t,n,s,o,c,u,l,h,p,i,a)=>(d,f,_,g)=>{if(g.numberOfInputs===0&&g.numberOfOutputs===0)throw l();const m=Array.isArray(g.outputChannelCount)?g.outputChannelCount:Array.from(g.outputChannelCount);if(m.some(M=>M<1))throw l();if(m.length!==g.numberOfOutputs)throw e();if(g.channelCountMode!=="explicit")throw l();const y=g.channelCount*g.numberOfInputs,b=m.reduce((M,W)=>M+W,0),S=_.parameterDescriptors===void 0?0:_.parameterDescriptors.length;if(y+S>6||b>6)throw l();const v=new MessageChannel,w=[],A=[];for(let M=0;M<g.numberOfInputs;M+=1)w.push(c(d,{channelCount:g.channelCount,channelCountMode:g.channelCountMode,channelInterpretation:g.channelInterpretation,gain:1})),A.push(s(d,{channelCount:g.channelCount,channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:g.channelCount}));const T=[];if(_.parameterDescriptors!==void 0)for(const{defaultValue:M,maxValue:W,minValue:he,name:ne}of _.parameterDescriptors){const X=o(d,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",offset:g.parameterData[ne]!==void 0?g.parameterData[ne]:M===void 0?0:M});Object.defineProperties(X.offset,{defaultValue:{get:()=>M===void 0?0:M},maxValue:{get:()=>W===void 0?ye:W},minValue:{get:()=>he===void 0?Ae:he}}),T.push(X)}const x=n(d,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:Math.max(1,y+S)}),O=Vr(f,d.sampleRate),C=u(d,O,y+S,Math.max(1,b)),k=s(d,{channelCount:Math.max(1,b),channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:Math.max(1,b)}),I=[];for(let M=0;M<g.numberOfOutputs;M+=1)I.push(n(d,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:m[M]}));for(let M=0;M<g.numberOfInputs;M+=1){w[M].connect(A[M]);for(let W=0;W<g.channelCount;W+=1)A[M].connect(x,W,M*g.channelCount+W)}const N=new Dr(_.parameterDescriptors===void 0?[]:_.parameterDescriptors.map(({name:M},W)=>{const he=T[W];return he.connect(x,0,y+W),he.start(0),[M,he.offset]}));x.connect(C);let P=g.channelInterpretation,E=null;const D=g.numberOfOutputs===0?[C]:I,G={get bufferSize(){return O},get channelCount(){return g.channelCount},set channelCount(M){throw t()},get channelCountMode(){return g.channelCountMode},set channelCountMode(M){throw t()},get channelInterpretation(){return P},set channelInterpretation(M){for(const W of w)W.channelInterpretation=M;P=M},get context(){return C.context},get inputs(){return w},get numberOfInputs(){return g.numberOfInputs},get numberOfOutputs(){return g.numberOfOutputs},get onprocessorerror(){return E},set onprocessorerror(M){typeof E=="function"&&G.removeEventListener("processorerror",E),E=typeof M=="function"?M:null,typeof E=="function"&&G.addEventListener("processorerror",E)},get parameters(){return N},get port(){return v.port2},addEventListener(...M){return C.addEventListener(M[0],M[1],M[2])},connect:r.bind(null,D),disconnect:h.bind(null,D),dispatchEvent(...M){return C.dispatchEvent(M[0])},removeEventListener(...M){return C.removeEventListener(M[0],M[1],M[2])}},V=new Map;v.port1.addEventListener=(M=>(...W)=>{if(W[0]==="message"){const he=typeof W[1]=="function"?W[1]:typeof W[1]=="object"&&W[1]!==null&&typeof W[1].handleEvent=="function"?W[1].handleEvent:null;if(he!==null){const ne=V.get(W[1]);ne!==void 0?W[1]=ne:(W[1]=X=>{p(d.currentTime,d.sampleRate,()=>he(X))},V.set(he,W[1]))}}return M.call(v.port1,W[0],W[1],W[2])})(v.port1.addEventListener),v.port1.removeEventListener=(M=>(...W)=>{if(W[0]==="message"){const he=V.get(W[1]);he!==void 0&&(V.delete(W[1]),W[1]=he)}return M.call(v.port1,W[0],W[1],W[2])})(v.port1.removeEventListener);let B=null;Object.defineProperty(v.port1,"onmessage",{get:()=>B,set:M=>{typeof B=="function"&&v.port1.removeEventListener("message",B),B=typeof M=="function"?M:null,typeof B=="function"&&(v.port1.addEventListener("message",B),v.port1.start())}}),_.prototype.port=v.port1;let q=null;pc(d,G,_,g).then(M=>q=M);const xe=Sn(g.numberOfInputs,g.channelCount),ke=Sn(g.numberOfOutputs,m),U=_.parameterDescriptors===void 0?[]:_.parameterDescriptors.reduce((M,{name:W})=>({...M,[W]:new Float32Array(128)}),{});let K=!0;const ve=()=>{g.numberOfOutputs>0&&C.disconnect(k);for(let M=0,W=0;M<g.numberOfOutputs;M+=1){const he=I[M];for(let ne=0;ne<m[M];ne+=1)k.disconnect(he,W+ne,ne);W+=m[M]}},R=new Map;C.onaudioprocess=({inputBuffer:M,outputBuffer:W})=>{if(q!==null){const he=i(G);for(let ne=0;ne<O;ne+=128){for(let X=0;X<g.numberOfInputs;X+=1)for(let ie=0;ie<g.channelCount;ie+=1)An(M,xe[X],ie,ie,ne);_.parameterDescriptors!==void 0&&_.parameterDescriptors.forEach(({name:X},ie)=>{An(M,U,X,y+ie,ne)});for(let X=0;X<g.numberOfInputs;X+=1)for(let ie=0;ie<m[X];ie+=1)ke[X][ie].byteLength===0&&(ke[X][ie]=new Float32Array(128));try{const X=xe.map((Ie,et)=>{if(he[et].size>0)return R.set(et,O/128),Ie;const Qn=R.get(et);return Qn===void 0?[]:(Ie.every(Ei=>Ei.every(Di=>Di===0))&&(Qn===1?R.delete(et):R.set(et,Qn-1)),Ie)});K=p(d.currentTime+ne/d.sampleRate,d.sampleRate,()=>q.process(X,ke,U));for(let Ie=0,et=0;Ie<g.numberOfOutputs;Ie+=1){for(let Gt=0;Gt<m[Ie];Gt+=1)Rr(W,ke[Ie],Gt,et+Gt,ne);et+=m[Ie]}}catch(X){K=!1,G.dispatchEvent(new ErrorEvent("processorerror",{colno:X.colno,filename:X.filename,lineno:X.lineno,message:X.message}))}if(!K){for(let X=0;X<g.numberOfInputs;X+=1){w[X].disconnect(A[X]);for(let ie=0;ie<g.channelCount;ie+=1)A[ne].disconnect(x,ie,X*g.channelCount+ie)}if(_.parameterDescriptors!==void 0){const X=_.parameterDescriptors.length;for(let ie=0;ie<X;ie+=1){const Ie=T[ie];Ie.disconnect(x,0,y+ie),Ie.stop()}}x.disconnect(C),C.onaudioprocess=null,lt?ve():kt();break}}}};let lt=!1;const ht=c(d,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0}),xt=()=>C.connect(ht).connect(d.destination),kt=()=>{C.disconnect(ht),ht.disconnect()},Ii=()=>{if(K){kt(),g.numberOfOutputs>0&&C.connect(k);for(let M=0,W=0;M<g.numberOfOutputs;M+=1){const he=I[M];for(let ne=0;ne<m[M];ne+=1)k.connect(he,W+ne,ne);W+=m[M]}}lt=!0},Mi=()=>{K&&(xt(),ve()),lt=!1};return xt(),a(G,Ii,Mi)},Fr=(r,e)=>{const t=r.createBiquadFilter();return de(t,e),oe(t,e,"Q"),oe(t,e,"detune"),oe(t,e,"frequency"),oe(t,e,"gain"),ee(t,e,"type"),t},mc=(r,e)=>(t,n)=>{const s=t.createChannelMerger(n.numberOfInputs);return r!==null&&r.name==="webkitAudioContext"&&e(t,s),de(s,n),s},gc=r=>{const e=r.numberOfOutputs;Object.defineProperty(r,"channelCount",{get:()=>e,set:t=>{if(t!==e)throw pe()}}),Object.defineProperty(r,"channelCountMode",{get:()=>"explicit",set:t=>{if(t!=="explicit")throw pe()}}),Object.defineProperty(r,"channelInterpretation",{get:()=>"discrete",set:t=>{if(t!=="discrete")throw pe()}})},tn=(r,e)=>{const t=r.createChannelSplitter(e.numberOfOutputs);return de(t,e),gc(t),t},vc=(r,e,t,n,s)=>(o,c)=>{if(o.createConstantSource===void 0)return t(o,c);const u=o.createConstantSource();return de(u,c),oe(u,c,"offset"),e(n,()=>n(o))||gs(u),e(s,()=>s(o))||vs(u),r(o,u),u},Ft=(r,e)=>(r.connect=e.connect.bind(e),r.disconnect=e.disconnect.bind(e),r),yc=(r,e,t,n)=>(s,{offset:o,...c})=>{const u=s.createBuffer(1,2,44100),l=e(s,{buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1}),h=t(s,{...c,gain:o}),p=u.getChannelData(0);p[0]=1,p[1]=1,l.buffer=u,l.loop=!0;const i={get bufferSize(){},get channelCount(){return h.channelCount},set channelCount(f){h.channelCount=f},get channelCountMode(){return h.channelCountMode},set channelCountMode(f){h.channelCountMode=f},get channelInterpretation(){return h.channelInterpretation},set channelInterpretation(f){h.channelInterpretation=f},get context(){return h.context},get inputs(){return[]},get numberOfInputs(){return l.numberOfInputs},get numberOfOutputs(){return h.numberOfOutputs},get offset(){return h.gain},get onended(){return l.onended},set onended(f){l.onended=f},addEventListener(...f){return l.addEventListener(f[0],f[1],f[2])},dispatchEvent(...f){return l.dispatchEvent(f[0])},removeEventListener(...f){return l.removeEventListener(f[0],f[1],f[2])},start(f=0){l.start.call(l,f)},stop(f=0){l.stop.call(l,f)}},a=()=>l.connect(h),d=()=>l.disconnect(h);return r(s,l),n(Ft(i,h),a,d)},Tc=(r,e)=>(t,n)=>{const s=t.createConvolver();if(de(s,n),n.disableNormalization===s.normalize&&(s.normalize=!n.disableNormalization),ee(s,n,"buffer"),n.channelCount>2||(e(s,"channelCount",o=>()=>o.call(s),o=>c=>{if(c>2)throw r();return o.call(s,c)}),n.channelCountMode==="max"))throw r();return e(s,"channelCountMode",o=>()=>o.call(s),o=>c=>{if(c==="max")throw r();return o.call(s,c)}),s},Lr=(r,e)=>{const t=r.createDelay(e.maxDelayTime);return de(t,e),oe(t,e,"delayTime"),t},wc=r=>(e,t)=>{const n=e.createDynamicsCompressor();if(de(n,t),t.channelCount>2||t.channelCountMode==="max")throw r();return oe(n,t,"attack"),oe(n,t,"knee"),oe(n,t,"ratio"),oe(n,t,"release"),oe(n,t,"threshold"),n},Se=(r,e)=>{const t=r.createGain();return de(t,e),oe(t,e,"gain"),t},bc=r=>(e,t,n)=>{if(e.createIIRFilter===void 0)return r(e,t,n);const s=e.createIIRFilter(n.feedforward,n.feedback);return de(s,n),s};function Ac(r,e){const t=e[0]*e[0]+e[1]*e[1];return[(r[0]*e[0]+r[1]*e[1])/t,(r[1]*e[0]-r[0]*e[1])/t]}function Sc(r,e){return[r[0]*e[0]-r[1]*e[1],r[0]*e[1]+r[1]*e[0]]}function cr(r,e){let t=[0,0];for(let n=r.length-1;n>=0;n-=1)t=Sc(t,e),t[0]+=r[n];return t}const xc=(r,e,t,n)=>(s,o,{channelCount:c,channelCountMode:u,channelInterpretation:l,feedback:h,feedforward:p})=>{const i=Vr(o,s.sampleRate),a=h instanceof Float64Array?h:new Float64Array(h),d=p instanceof Float64Array?p:new Float64Array(p),f=a.length,_=d.length,g=Math.min(f,_);if(f===0||f>20)throw n();if(a[0]===0)throw e();if(_===0||_>20)throw n();if(d[0]===0)throw e();if(a[0]!==1){for(let T=0;T<_;T+=1)d[T]/=a[0];for(let T=1;T<f;T+=1)a[T]/=a[0]}const m=t(s,i,c,c);m.channelCount=c,m.channelCountMode=u,m.channelInterpretation=l;const y=32,b=[],S=[],v=[];for(let T=0;T<c;T+=1){b.push(0);const x=new Float32Array(y),O=new Float32Array(y);x.fill(0),O.fill(0),S.push(x),v.push(O)}m.onaudioprocess=T=>{const x=T.inputBuffer,O=T.outputBuffer,C=x.numberOfChannels;for(let k=0;k<C;k+=1){const I=x.getChannelData(k),N=O.getChannelData(k);b[k]=Pr(a,f,d,_,g,S[k],v[k],b[k],y,I,N)}};const w=s.sampleRate/2;return Ft({get bufferSize(){return i},get channelCount(){return m.channelCount},set channelCount(T){m.channelCount=T},get channelCountMode(){return m.channelCountMode},set channelCountMode(T){m.channelCountMode=T},get channelInterpretation(){return m.channelInterpretation},set channelInterpretation(T){m.channelInterpretation=T},get context(){return m.context},get inputs(){return[m]},get numberOfInputs(){return m.numberOfInputs},get numberOfOutputs(){return m.numberOfOutputs},addEventListener(...T){return m.addEventListener(T[0],T[1],T[2])},dispatchEvent(...T){return m.dispatchEvent(T[0])},getFrequencyResponse(T,x,O){if(T.length!==x.length||x.length!==O.length)throw r();const C=T.length;for(let k=0;k<C;k+=1){const I=-Math.PI*(T[k]/w),N=[Math.cos(I),Math.sin(I)],P=cr(d,N),E=cr(a,N),D=Ac(P,E);x[k]=Math.sqrt(D[0]*D[0]+D[1]*D[1]),O[k]=Math.atan2(D[1],D[0])}},removeEventListener(...T){return m.removeEventListener(T[0],T[1],T[2])}},m)},kc=(r,e)=>r.createMediaElementSource(e.mediaElement),Cc=(r,e)=>{const t=r.createMediaStreamDestination();return de(t,e),t.numberOfOutputs===1&&Object.defineProperty(t,"numberOfOutputs",{get:()=>0}),t},Oc=(r,{mediaStream:e})=>{const t=e.getAudioTracks();t.sort((o,c)=>o.id<c.id?-1:o.id>c.id?1:0);const n=t.slice(0,1),s=r.createMediaStreamSource(new MediaStream(n));return Object.defineProperty(s,"mediaStream",{value:e}),s},Nc=(r,e)=>(t,{mediaStreamTrack:n})=>{if(typeof t.createMediaStreamTrackSource=="function")return t.createMediaStreamTrackSource(n);const s=new MediaStream([n]),o=t.createMediaStreamSource(s);if(n.kind!=="audio")throw r();if(e(t))throw new TypeError;return o},Ic=r=>r===null?null:r.hasOwnProperty("OfflineAudioContext")?r.OfflineAudioContext:r.hasOwnProperty("webkitOfflineAudioContext")?r.webkitOfflineAudioContext:null,Mc=(r,e,t,n,s,o)=>(c,u)=>{const l=c.createOscillator();return de(l,u),oe(l,u,"detune"),oe(l,u,"frequency"),u.periodicWave!==void 0?l.setPeriodicWave(u.periodicWave):ee(l,u,"type"),e(t,()=>t(c))||gs(l),e(n,()=>n(c))||o(l,c),e(s,()=>s(c))||vs(l),r(c,l),l},Ec=r=>(e,t)=>{const n=e.createPanner();return n.orientationX===void 0?r(e,t):(de(n,t),oe(n,t,"orientationX"),oe(n,t,"orientationY"),oe(n,t,"orientationZ"),oe(n,t,"positionX"),oe(n,t,"positionY"),oe(n,t,"positionZ"),ee(n,t,"coneInnerAngle"),ee(n,t,"coneOuterAngle"),ee(n,t,"coneOuterGain"),ee(n,t,"distanceModel"),ee(n,t,"maxDistance"),ee(n,t,"panningModel"),ee(n,t,"refDistance"),ee(n,t,"rolloffFactor"),n)},Dc=(r,e,t,n,s,o,c,u,l,h)=>(p,{coneInnerAngle:i,coneOuterAngle:a,coneOuterGain:d,distanceModel:f,maxDistance:_,orientationX:g,orientationY:m,orientationZ:y,panningModel:b,positionX:S,positionY:v,positionZ:w,refDistance:A,rolloffFactor:T,...x})=>{const O=p.createPanner();if(x.channelCount>2||x.channelCountMode==="max")throw c();de(O,x);const C={channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete"},k=t(p,{...C,channelInterpretation:"speakers",numberOfInputs:6}),I=n(p,{...x,gain:1}),N=n(p,{...C,gain:1}),P=n(p,{...C,gain:0}),E=n(p,{...C,gain:0}),D=n(p,{...C,gain:0}),G=n(p,{...C,gain:0}),V=n(p,{...C,gain:0}),B=s(p,256,6,1),q=o(p,{...C,curve:new Float32Array([1,1]),oversample:"none"});let Z=[g,m,y],xe=[S,v,w];const ke=new Float32Array(1);B.onaudioprocess=({inputBuffer:R})=>{const lt=[l(R,ke,0),l(R,ke,1),l(R,ke,2)];lt.some((xt,kt)=>xt!==Z[kt])&&(O.setOrientation(...lt),Z=lt);const ht=[l(R,ke,3),l(R,ke,4),l(R,ke,5)];ht.some((xt,kt)=>xt!==xe[kt])&&(O.setPosition(...ht),xe=ht)},Object.defineProperty(P.gain,"defaultValue",{get:()=>0}),Object.defineProperty(E.gain,"defaultValue",{get:()=>0}),Object.defineProperty(D.gain,"defaultValue",{get:()=>0}),Object.defineProperty(G.gain,"defaultValue",{get:()=>0}),Object.defineProperty(V.gain,"defaultValue",{get:()=>0});const U={get bufferSize(){},get channelCount(){return O.channelCount},set channelCount(R){if(R>2)throw c();I.channelCount=R,O.channelCount=R},get channelCountMode(){return O.channelCountMode},set channelCountMode(R){if(R==="max")throw c();I.channelCountMode=R,O.channelCountMode=R},get channelInterpretation(){return O.channelInterpretation},set channelInterpretation(R){I.channelInterpretation=R,O.channelInterpretation=R},get coneInnerAngle(){return O.coneInnerAngle},set coneInnerAngle(R){O.coneInnerAngle=R},get coneOuterAngle(){return O.coneOuterAngle},set coneOuterAngle(R){O.coneOuterAngle=R},get coneOuterGain(){return O.coneOuterGain},set coneOuterGain(R){if(R<0||R>1)throw e();O.coneOuterGain=R},get context(){return O.context},get distanceModel(){return O.distanceModel},set distanceModel(R){O.distanceModel=R},get inputs(){return[I]},get maxDistance(){return O.maxDistance},set maxDistance(R){if(R<0)throw new RangeError;O.maxDistance=R},get numberOfInputs(){return O.numberOfInputs},get numberOfOutputs(){return O.numberOfOutputs},get orientationX(){return N.gain},get orientationY(){return P.gain},get orientationZ(){return E.gain},get panningModel(){return O.panningModel},set panningModel(R){O.panningModel=R},get positionX(){return D.gain},get positionY(){return G.gain},get positionZ(){return V.gain},get refDistance(){return O.refDistance},set refDistance(R){if(R<0)throw new RangeError;O.refDistance=R},get rolloffFactor(){return O.rolloffFactor},set rolloffFactor(R){if(R<0)throw new RangeError;O.rolloffFactor=R},addEventListener(...R){return I.addEventListener(R[0],R[1],R[2])},dispatchEvent(...R){return I.dispatchEvent(R[0])},removeEventListener(...R){return I.removeEventListener(R[0],R[1],R[2])}};i!==U.coneInnerAngle&&(U.coneInnerAngle=i),a!==U.coneOuterAngle&&(U.coneOuterAngle=a),d!==U.coneOuterGain&&(U.coneOuterGain=d),f!==U.distanceModel&&(U.distanceModel=f),_!==U.maxDistance&&(U.maxDistance=_),g!==U.orientationX.value&&(U.orientationX.value=g),m!==U.orientationY.value&&(U.orientationY.value=m),y!==U.orientationZ.value&&(U.orientationZ.value=y),b!==U.panningModel&&(U.panningModel=b),S!==U.positionX.value&&(U.positionX.value=S),v!==U.positionY.value&&(U.positionY.value=v),w!==U.positionZ.value&&(U.positionZ.value=w),A!==U.refDistance&&(U.refDistance=A),T!==U.rolloffFactor&&(U.rolloffFactor=T),(Z[0]!==1||Z[1]!==0||Z[2]!==0)&&O.setOrientation(...Z),(xe[0]!==0||xe[1]!==0||xe[2]!==0)&&O.setPosition(...xe);const K=()=>{I.connect(O),r(I,q,0,0),q.connect(N).connect(k,0,0),q.connect(P).connect(k,0,1),q.connect(E).connect(k,0,2),q.connect(D).connect(k,0,3),q.connect(G).connect(k,0,4),q.connect(V).connect(k,0,5),k.connect(B).connect(p.destination)},ve=()=>{I.disconnect(O),u(I,q,0,0),q.disconnect(N),N.disconnect(k),q.disconnect(P),P.disconnect(k),q.disconnect(E),E.disconnect(k),q.disconnect(D),D.disconnect(k),q.disconnect(G),G.disconnect(k),q.disconnect(V),V.disconnect(k),k.disconnect(B),B.disconnect(p.destination)};return h(Ft(U,O),K,ve)},Rc=r=>(e,{disableNormalization:t,imag:n,real:s})=>{const o=n instanceof Float32Array?n:new Float32Array(n),c=s instanceof Float32Array?s:new Float32Array(s),u=e.createPeriodicWave(c,o,{disableNormalization:t});if(Array.from(n).length<2)throw r();return u},nn=(r,e,t,n)=>r.createScriptProcessor(e,t,n),Pc=(r,e)=>(t,n)=>{const s=n.channelCountMode;if(s==="clamped-max")throw e();if(t.createStereoPanner===void 0)return r(t,n);const o=t.createStereoPanner();return de(o,n),oe(o,n,"pan"),Object.defineProperty(o,"channelCountMode",{get:()=>s,set:c=>{if(c!==s)throw e()}}),o},Vc=(r,e,t,n,s,o)=>{const u=new Float32Array([1,1]),l=Math.PI/2,h={channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete"},p={...h,oversample:"none"},i=(f,_,g,m)=>{const y=new Float32Array(16385),b=new Float32Array(16385);for(let x=0;x<16385;x+=1){const O=x/16384*l;y[x]=Math.cos(O),b[x]=Math.sin(O)}const S=t(f,{...h,gain:0}),v=n(f,{...p,curve:y}),w=n(f,{...p,curve:u}),A=t(f,{...h,gain:0}),T=n(f,{...p,curve:b});return{connectGraph(){_.connect(S),_.connect(w.inputs===void 0?w:w.inputs[0]),_.connect(A),w.connect(g),g.connect(v.inputs===void 0?v:v.inputs[0]),g.connect(T.inputs===void 0?T:T.inputs[0]),v.connect(S.gain),T.connect(A.gain),S.connect(m,0,0),A.connect(m,0,1)},disconnectGraph(){_.disconnect(S),_.disconnect(w.inputs===void 0?w:w.inputs[0]),_.disconnect(A),w.disconnect(g),g.disconnect(v.inputs===void 0?v:v.inputs[0]),g.disconnect(T.inputs===void 0?T:T.inputs[0]),v.disconnect(S.gain),T.disconnect(A.gain),S.disconnect(m,0,0),A.disconnect(m,0,1)}}},a=(f,_,g,m)=>{const y=new Float32Array(16385),b=new Float32Array(16385),S=new Float32Array(16385),v=new Float32Array(16385),w=Math.floor(16385/2);for(let D=0;D<16385;D+=1)if(D>w){const G=(D-w)/(16384-w)*l;y[D]=Math.cos(G),b[D]=Math.sin(G),S[D]=0,v[D]=1}else{const G=D/(16384-w)*l;y[D]=1,b[D]=0,S[D]=Math.cos(G),v[D]=Math.sin(G)}const A=e(f,{channelCount:2,channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:2}),T=t(f,{...h,gain:0}),x=n(f,{...p,curve:y}),O=t(f,{...h,gain:0}),C=n(f,{...p,curve:b}),k=n(f,{...p,curve:u}),I=t(f,{...h,gain:0}),N=n(f,{...p,curve:S}),P=t(f,{...h,gain:0}),E=n(f,{...p,curve:v});return{connectGraph(){_.connect(A),_.connect(k.inputs===void 0?k:k.inputs[0]),A.connect(T,0),A.connect(O,0),A.connect(I,1),A.connect(P,1),k.connect(g),g.connect(x.inputs===void 0?x:x.inputs[0]),g.connect(C.inputs===void 0?C:C.inputs[0]),g.connect(N.inputs===void 0?N:N.inputs[0]),g.connect(E.inputs===void 0?E:E.inputs[0]),x.connect(T.gain),C.connect(O.gain),N.connect(I.gain),E.connect(P.gain),T.connect(m,0,0),I.connect(m,0,0),O.connect(m,0,1),P.connect(m,0,1)},disconnectGraph(){_.disconnect(A),_.disconnect(k.inputs===void 0?k:k.inputs[0]),A.disconnect(T,0),A.disconnect(O,0),A.disconnect(I,1),A.disconnect(P,1),k.disconnect(g),g.disconnect(x.inputs===void 0?x:x.inputs[0]),g.disconnect(C.inputs===void 0?C:C.inputs[0]),g.disconnect(N.inputs===void 0?N:N.inputs[0]),g.disconnect(E.inputs===void 0?E:E.inputs[0]),x.disconnect(T.gain),C.disconnect(O.gain),N.disconnect(I.gain),E.disconnect(P.gain),T.disconnect(m,0,0),I.disconnect(m,0,0),O.disconnect(m,0,1),P.disconnect(m,0,1)}}},d=(f,_,g,m,y)=>{if(_===1)return i(f,g,m,y);if(_===2)return a(f,g,m,y);throw s()};return(f,{channelCount:_,channelCountMode:g,pan:m,...y})=>{if(g==="max")throw s();const b=r(f,{...y,channelCount:1,channelCountMode:g,numberOfInputs:2}),S=t(f,{...y,channelCount:_,channelCountMode:g,gain:1}),v=t(f,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:m});let{connectGraph:w,disconnectGraph:A}=d(f,_,S,v,b);Object.defineProperty(v.gain,"defaultValue",{get:()=>0}),Object.defineProperty(v.gain,"maxValue",{get:()=>1}),Object.defineProperty(v.gain,"minValue",{get:()=>-1});const T={get bufferSize(){},get channelCount(){return S.channelCount},set channelCount(k){S.channelCount!==k&&(x&&A(),{connectGraph:w,disconnectGraph:A}=d(f,k,S,v,b),x&&w()),S.channelCount=k},get channelCountMode(){return S.channelCountMode},set channelCountMode(k){if(k==="clamped-max"||k==="max")throw s();S.channelCountMode=k},get channelInterpretation(){return S.channelInterpretation},set channelInterpretation(k){S.channelInterpretation=k},get context(){return S.context},get inputs(){return[S]},get numberOfInputs(){return S.numberOfInputs},get numberOfOutputs(){return S.numberOfOutputs},get pan(){return v.gain},addEventListener(...k){return S.addEventListener(k[0],k[1],k[2])},dispatchEvent(...k){return S.dispatchEvent(k[0])},removeEventListener(...k){return S.removeEventListener(k[0],k[1],k[2])}};let x=!1;const O=()=>{w(),x=!0},C=()=>{A(),x=!1};return o(Ft(T,b),O,C)}},Fc=(r,e,t,n,s,o,c)=>(u,l)=>{const h=u.createWaveShaper();if(o!==null&&o.name==="webkitAudioContext"&&u.createGain().gain.automationRate===void 0)return t(u,l);de(h,l);const p=l.curve===null||l.curve instanceof Float32Array?l.curve:new Float32Array(l.curve);if(p!==null&&p.length<2)throw e();ee(h,{curve:p},"curve"),ee(h,l,"oversample");let i=null,a=!1;return c(h,"curve",_=>()=>_.call(h),_=>g=>(_.call(h,g),a&&(n(g)&&i===null?i=r(u,h):!n(g)&&i!==null&&(i(),i=null)),g)),s(h,()=>{a=!0,n(h.curve)&&(i=r(u,h))},()=>{a=!1,i!==null&&(i(),i=null)})},Lc=(r,e,t,n,s)=>(o,{curve:c,oversample:u,...l})=>{const h=o.createWaveShaper(),p=o.createWaveShaper();de(h,l),de(p,l);const i=t(o,{...l,gain:1}),a=t(o,{...l,gain:-1}),d=t(o,{...l,gain:1}),f=t(o,{...l,gain:-1});let _=null,g=!1,m=null;const y={get bufferSize(){},get channelCount(){return h.channelCount},set channelCount(v){i.channelCount=v,a.channelCount=v,h.channelCount=v,d.channelCount=v,p.channelCount=v,f.channelCount=v},get channelCountMode(){return h.channelCountMode},set channelCountMode(v){i.channelCountMode=v,a.channelCountMode=v,h.channelCountMode=v,d.channelCountMode=v,p.channelCountMode=v,f.channelCountMode=v},get channelInterpretation(){return h.channelInterpretation},set channelInterpretation(v){i.channelInterpretation=v,a.channelInterpretation=v,h.channelInterpretation=v,d.channelInterpretation=v,p.channelInterpretation=v,f.channelInterpretation=v},get context(){return h.context},get curve(){return m},set curve(v){if(v!==null&&v.length<2)throw e();if(v===null)h.curve=v,p.curve=v;else{const w=v.length,A=new Float32Array(w+2-w%2),T=new Float32Array(w+2-w%2);A[0]=v[0],T[0]=-v[w-1];const x=Math.ceil((w+1)/2),O=(w+1)/2-1;for(let C=1;C<x;C+=1){const k=C/x*O,I=Math.floor(k),N=Math.ceil(k);A[C]=I===N?v[I]:(1-(k-I))*v[I]+(1-(N-k))*v[N],T[C]=I===N?-v[w-1-I]:-((1-(k-I))*v[w-1-I])-(1-(N-k))*v[w-1-N]}A[x]=w%2===1?v[x-1]:(v[x-2]+v[x-1])/2,h.curve=A,p.curve=T}m=v,g&&(n(m)&&_===null?_=r(o,i):_!==null&&(_(),_=null))},get inputs(){return[i]},get numberOfInputs(){return h.numberOfInputs},get numberOfOutputs(){return h.numberOfOutputs},get oversample(){return h.oversample},set oversample(v){h.oversample=v,p.oversample=v},addEventListener(...v){return i.addEventListener(v[0],v[1],v[2])},dispatchEvent(...v){return i.dispatchEvent(v[0])},removeEventListener(...v){return i.removeEventListener(v[0],v[1],v[2])}};c!==null&&(y.curve=c instanceof Float32Array?c:new Float32Array(c)),u!==y.oversample&&(y.oversample=u);const b=()=>{i.connect(h).connect(d),i.connect(a).connect(p).connect(f).connect(d),g=!0,n(m)&&(_=r(o,i))},S=()=>{i.disconnect(h),h.disconnect(d),i.disconnect(a),a.disconnect(p),p.disconnect(f),f.disconnect(d),g=!1,_!==null&&(_(),_=null)};return s(Ft(y,d),b,S)},be=()=>new DOMException("","NotSupportedError"),Wc={numberOfChannels:1},qc=(r,e,t,n,s)=>class extends r{constructor(c,u,l){let h;if(typeof c=="number"&&u!==void 0&&l!==void 0)h={length:u,numberOfChannels:c,sampleRate:l};else if(typeof c=="object")h=c;else throw new Error("The given parameters are not valid.");const{length:p,numberOfChannels:i,sampleRate:a}={...Wc,...h},d=n(i,p,a);e(Zt,()=>Zt(d))||d.addEventListener("statechange",(()=>{let f=0;const _=g=>{this._state==="running"&&(f>0?(d.removeEventListener("statechange",_),g.stopImmediatePropagation(),this._waitForThePromiseToSettle(g)):f+=1)};return _})()),super(d,i),this._length=p,this._nativeOfflineAudioContext=d,this._state=null}get length(){return this._nativeOfflineAudioContext.length===void 0?this._length:this._nativeOfflineAudioContext.length}get state(){return this._state===null?this._nativeOfflineAudioContext.state:this._state}startRendering(){return this._state==="running"?Promise.reject(t()):(this._state="running",s(this.destination,this._nativeOfflineAudioContext).finally(()=>{this._state=null,Nr(this)}))}_waitForThePromiseToSettle(c){this._state===null?this._nativeOfflineAudioContext.dispatchEvent(c):setTimeout(()=>this._waitForThePromiseToSettle(c))}},Bc={channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",detune:0,frequency:440,periodicWave:void 0,type:"sine"},jc=(r,e,t,n,s,o,c)=>class extends r{constructor(l,h){const p=s(l),i={...Bc,...h},a=t(p,i),d=o(p),f=d?n():null,_=l.sampleRate/2;super(l,!1,a,f),this._detune=e(this,d,a.detune,153600,-153600),this._frequency=e(this,d,a.frequency,_,-_),this._nativeOscillatorNode=a,this._onended=null,this._oscillatorNodeRenderer=f,this._oscillatorNodeRenderer!==null&&i.periodicWave!==void 0&&(this._oscillatorNodeRenderer.periodicWave=i.periodicWave)}get detune(){return this._detune}get frequency(){return this._frequency}get onended(){return this._onended}set onended(l){const h=typeof l=="function"?c(this,l):null;this._nativeOscillatorNode.onended=h;const p=this._nativeOscillatorNode.onended;this._onended=p!==null&&p===h?l:p}get type(){return this._nativeOscillatorNode.type}set type(l){this._nativeOscillatorNode.type=l,this._oscillatorNodeRenderer!==null&&(this._oscillatorNodeRenderer.periodicWave=null)}setPeriodicWave(l){this._nativeOscillatorNode.setPeriodicWave(l),this._oscillatorNodeRenderer!==null&&(this._oscillatorNodeRenderer.periodicWave=l)}start(l=0){if(this._nativeOscillatorNode.start(l),this._oscillatorNodeRenderer!==null&&(this._oscillatorNodeRenderer.start=l),this.context.state!=="closed"){Et(this);const h=()=>{this._nativeOscillatorNode.removeEventListener("ended",h),ze(this)&&Jt(this)};this._nativeOscillatorNode.addEventListener("ended",h)}}stop(l=0){this._nativeOscillatorNode.stop(l),this._oscillatorNodeRenderer!==null&&(this._oscillatorNodeRenderer.stop=l)}},Gc=(r,e,t,n,s)=>()=>{const o=new WeakMap;let c=null,u=null,l=null;const h=async(p,i)=>{let a=t(p);const d=ge(a,i);if(!d){const f={channelCount:a.channelCount,channelCountMode:a.channelCountMode,channelInterpretation:a.channelInterpretation,detune:a.detune.value,frequency:a.frequency.value,periodicWave:c===null?void 0:c,type:a.type};a=e(i,f),u!==null&&a.start(u),l!==null&&a.stop(l)}return o.set(i,a),d?(await r(i,p.detune,a.detune),await r(i,p.frequency,a.frequency)):(await n(i,p.detune,a.detune),await n(i,p.frequency,a.frequency)),await s(p,i,a),a};return{set periodicWave(p){c=p},set start(p){u=p},set stop(p){l=p},render(p,i){const a=o.get(i);return a!==void 0?Promise.resolve(a):h(p,i)}}},Uc={channelCount:2,channelCountMode:"clamped-max",channelInterpretation:"speakers",coneInnerAngle:360,coneOuterAngle:360,coneOuterGain:0,distanceModel:"inverse",maxDistance:1e4,orientationX:1,orientationY:0,orientationZ:0,panningModel:"equalpower",positionX:0,positionY:0,positionZ:0,refDistance:1,rolloffFactor:1},Hc=(r,e,t,n,s,o,c)=>class extends r{constructor(l,h){const p=s(l),i={...Uc,...h},a=t(p,i),d=o(p),f=d?n():null;super(l,!1,a,f),this._nativePannerNode=a,this._orientationX=e(this,d,a.orientationX,ye,Ae),this._orientationY=e(this,d,a.orientationY,ye,Ae),this._orientationZ=e(this,d,a.orientationZ,ye,Ae),this._positionX=e(this,d,a.positionX,ye,Ae),this._positionY=e(this,d,a.positionY,ye,Ae),this._positionZ=e(this,d,a.positionZ,ye,Ae),c(this,1)}get coneInnerAngle(){return this._nativePannerNode.coneInnerAngle}set coneInnerAngle(l){this._nativePannerNode.coneInnerAngle=l}get coneOuterAngle(){return this._nativePannerNode.coneOuterAngle}set coneOuterAngle(l){this._nativePannerNode.coneOuterAngle=l}get coneOuterGain(){return this._nativePannerNode.coneOuterGain}set coneOuterGain(l){this._nativePannerNode.coneOuterGain=l}get distanceModel(){return this._nativePannerNode.distanceModel}set distanceModel(l){this._nativePannerNode.distanceModel=l}get maxDistance(){return this._nativePannerNode.maxDistance}set maxDistance(l){this._nativePannerNode.maxDistance=l}get orientationX(){return this._orientationX}get orientationY(){return this._orientationY}get orientationZ(){return this._orientationZ}get panningModel(){return this._nativePannerNode.panningModel}set panningModel(l){this._nativePannerNode.panningModel=l}get positionX(){return this._positionX}get positionY(){return this._positionY}get positionZ(){return this._positionZ}get refDistance(){return this._nativePannerNode.refDistance}set refDistance(l){this._nativePannerNode.refDistance=l}get rolloffFactor(){return this._nativePannerNode.rolloffFactor}set rolloffFactor(l){this._nativePannerNode.rolloffFactor=l}},$c=(r,e,t,n,s,o,c,u,l,h)=>()=>{const p=new WeakMap;let i=null;const a=async(d,f)=>{let _=null,g=o(d);const m={channelCount:g.channelCount,channelCountMode:g.channelCountMode,channelInterpretation:g.channelInterpretation},y={...m,coneInnerAngle:g.coneInnerAngle,coneOuterAngle:g.coneOuterAngle,coneOuterGain:g.coneOuterGain,distanceModel:g.distanceModel,maxDistance:g.maxDistance,panningModel:g.panningModel,refDistance:g.refDistance,rolloffFactor:g.rolloffFactor},b=ge(g,f);if("bufferSize"in g)_=n(f,{...m,gain:1});else if(!b){const S={...y,orientationX:g.orientationX.value,orientationY:g.orientationY.value,orientationZ:g.orientationZ.value,positionX:g.positionX.value,positionY:g.positionY.value,positionZ:g.positionZ.value};g=s(f,S)}if(p.set(f,_===null?g:_),_!==null){if(i===null){if(c===null)throw new Error("Missing the native OfflineAudioContext constructor.");const C=new c(6,d.context.length,f.sampleRate),k=e(C,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:6});k.connect(C.destination),i=(async()=>{const I=await Promise.all([d.orientationX,d.orientationY,d.orientationZ,d.positionX,d.positionY,d.positionZ].map(async(N,P)=>{const E=t(C,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",offset:P===0?1:0});return await u(C,N,E.offset),E}));for(let N=0;N<6;N+=1)I[N].connect(k,0,N),I[N].start(0);return h(C)})()}const S=await i,v=n(f,{...m,gain:1});await l(d,f,v);const w=[];for(let C=0;C<S.numberOfChannels;C+=1)w.push(S.getChannelData(C));let A=[w[0][0],w[1][0],w[2][0]],T=[w[3][0],w[4][0],w[5][0]],x=n(f,{...m,gain:1}),O=s(f,{...y,orientationX:A[0],orientationY:A[1],orientationZ:A[2],positionX:T[0],positionY:T[1],positionZ:T[2]});v.connect(x).connect(O.inputs[0]),O.connect(_);for(let C=128;C<S.length;C+=128){const k=[w[0][C],w[1][C],w[2][C]],I=[w[3][C],w[4][C],w[5][C]];if(k.some((N,P)=>N!==A[P])||I.some((N,P)=>N!==T[P])){A=k,T=I;const N=C/f.sampleRate;x.gain.setValueAtTime(0,N),x=n(f,{...m,gain:0}),O=s(f,{...y,orientationX:A[0],orientationY:A[1],orientationZ:A[2],positionX:T[0],positionY:T[1],positionZ:T[2]}),x.gain.setValueAtTime(1,N),v.connect(x).connect(O.inputs[0]),O.connect(_)}}return _}return b?(await r(f,d.orientationX,g.orientationX),await r(f,d.orientationY,g.orientationY),await r(f,d.orientationZ,g.orientationZ),await r(f,d.positionX,g.positionX),await r(f,d.positionY,g.positionY),await r(f,d.positionZ,g.positionZ)):(await u(f,d.orientationX,g.orientationX),await u(f,d.orientationY,g.orientationY),await u(f,d.orientationZ,g.orientationZ),await u(f,d.positionX,g.positionX),await u(f,d.positionY,g.positionY),await u(f,d.positionZ,g.positionZ)),Vt(g)?await l(d,f,g.inputs[0]):await l(d,f,g),g};return{render(d,f){const _=p.get(f);return _!==void 0?Promise.resolve(_):a(d,f)}}},Xc={disableNormalization:!1},Zc=(r,e,t,n)=>class Wr{constructor(o,c){const u=e(o),l=n({...Xc,...c}),h=r(u,l);return t.add(h),h}static[Symbol.hasInstance](o){return o!==null&&typeof o=="object"&&Object.getPrototypeOf(o)===Wr.prototype||t.has(o)}},zc=(r,e)=>(t,n,s)=>(r(n).replay(s),e(n,t,s)),Yc=(r,e,t)=>async(n,s,o)=>{const c=r(n);await Promise.all(c.activeInputs.map((u,l)=>Array.from(u).map(async([h,p])=>{const a=await e(h).render(h,s),d=n.context.destination;!t(h)&&(n!==d||!t(n))&&a.connect(o,p,l)})).reduce((u,l)=>[...u,...l],[]))},Qc=(r,e,t)=>async(n,s,o)=>{const c=e(n);await Promise.all(Array.from(c.activeInputs).map(async([u,l])=>{const p=await r(u).render(u,s);t(u)||p.connect(o,l)}))},Jc=(r,e,t,n)=>s=>r(Zt,()=>Zt(s))?Promise.resolve(r(n,n)).then(o=>{if(!o){const c=t(s,512,0,1);s.oncomplete=()=>{c.onaudioprocess=null,c.disconnect()},c.onaudioprocess=()=>s.currentTime,c.connect(s.destination)}return s.startRendering()}):new Promise(o=>{const c=e(s,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});s.oncomplete=u=>{c.disconnect(),o(u.renderedBuffer)},c.connect(s.destination),s.startRendering()}),Kc=r=>(e,t)=>{r.set(e,t)},eu=r=>(e,t)=>r.set(e,t),tu=(r,e,t,n,s,o,c,u)=>(l,h)=>t(l).render(l,h).then(()=>Promise.all(Array.from(n(h)).map(p=>t(p).render(p,h)))).then(()=>s(h)).then(p=>(typeof p.copyFromChannel!="function"?(c(p),ps(p)):e(o,()=>o(p))||u(p),r.add(p),p)),nu={channelCount:2,channelCountMode:"explicit",channelInterpretation:"speakers",pan:0},su=(r,e,t,n,s,o)=>class extends r{constructor(u,l){const h=s(u),p={...nu,...l},i=t(h,p),a=o(h),d=a?n():null;super(u,!1,i,d),this._pan=e(this,a,i.pan)}get pan(){return this._pan}},ru=(r,e,t,n,s)=>()=>{const o=new WeakMap,c=async(u,l)=>{let h=t(u);const p=ge(h,l);if(!p){const i={channelCount:h.channelCount,channelCountMode:h.channelCountMode,channelInterpretation:h.channelInterpretation,pan:h.pan.value};h=e(l,i)}return o.set(l,h),p?await r(l,u.pan,h.pan):await n(l,u.pan,h.pan),Vt(h)?await s(u,l,h.inputs[0]):await s(u,l,h),h};return{render(u,l){const h=o.get(l);return h!==void 0?Promise.resolve(h):c(u,l)}}},iu=r=>()=>{if(r===null)return!1;try{new r({length:1,sampleRate:44100})}catch{return!1}return!0},ou=(r,e)=>async()=>{if(r===null)return!0;if(e===null)return!1;const t=new Blob(['class A extends AudioWorkletProcessor{process(i){this.port.postMessage(i,[i[0][0].buffer])}}registerProcessor("a",A)'],{type:"application/javascript; charset=utf-8"}),n=new e(1,128,44100),s=URL.createObjectURL(t);let o=!1,c=!1;try{await n.audioWorklet.addModule(s);const u=new r(n,"a",{numberOfOutputs:0}),l=n.createOscillator();u.port.onmessage=()=>o=!0,u.onprocessorerror=()=>c=!0,l.connect(u),l.start(0),await n.startRendering(),await new Promise(h=>setTimeout(h))}catch{}finally{URL.revokeObjectURL(s)}return o&&!c},au=(r,e)=>()=>{if(e===null)return Promise.resolve(!1);const t=new e(1,1,44100),n=r(t,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});return new Promise(s=>{t.oncomplete=()=>{n.disconnect(),s(t.currentTime!==0)},t.startRendering()})},cu=()=>new DOMException("","UnknownError"),uu={channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",curve:null,oversample:"none"},lu=(r,e,t,n,s,o,c)=>class extends r{constructor(l,h){const p=s(l),i={...uu,...h},a=t(p,i),f=o(p)?n():null;super(l,!0,a,f),this._isCurveNullified=!1,this._nativeWaveShaperNode=a,c(this,1)}get curve(){return this._isCurveNullified?null:this._nativeWaveShaperNode.curve}set curve(l){if(l===null)this._isCurveNullified=!0,this._nativeWaveShaperNode.curve=new Float32Array([0,0]);else{if(l.length<2)throw e();this._isCurveNullified=!1,this._nativeWaveShaperNode.curve=l}}get oversample(){return this._nativeWaveShaperNode.oversample}set oversample(l){this._nativeWaveShaperNode.oversample=l}},hu=(r,e,t)=>()=>{const n=new WeakMap,s=async(o,c)=>{let u=e(o);if(!ge(u,c)){const h={channelCount:u.channelCount,channelCountMode:u.channelCountMode,channelInterpretation:u.channelInterpretation,curve:u.curve,oversample:u.oversample};u=r(c,h)}return n.set(c,u),Vt(u)?await t(o,c,u.inputs[0]):await t(o,c,u),u};return{render(o,c){const u=n.get(c);return u!==void 0?Promise.resolve(u):s(o,c)}}},du=()=>typeof window>"u"?null:window,fu=(r,e)=>t=>{t.copyFromChannel=(n,s,o=0)=>{const c=r(o),u=r(s);if(u>=t.numberOfChannels)throw e();const l=t.length,h=t.getChannelData(u),p=n.length;for(let i=c<0?-c:0;i+c<l&&i<p;i+=1)n[i]=h[i+c]},t.copyToChannel=(n,s,o=0)=>{const c=r(o),u=r(s);if(u>=t.numberOfChannels)throw e();const l=t.length,h=t.getChannelData(u),p=n.length;for(let i=c<0?-c:0;i+c<l&&i<p;i+=1)h[i+c]=n[i]}},pu=r=>e=>{e.copyFromChannel=(t=>(n,s,o=0)=>{const c=r(o),u=r(s);if(c<e.length)return t.call(e,n,u,c)})(e.copyFromChannel),e.copyToChannel=(t=>(n,s,o=0)=>{const c=r(o),u=r(s);if(c<e.length)return t.call(e,n,u,c)})(e.copyToChannel)},_u=r=>(e,t)=>{const n=t.createBuffer(1,1,44100);e.buffer===null&&(e.buffer=n),r(e,"buffer",s=>()=>{const o=s.call(e);return o===n?null:o},s=>o=>s.call(e,o===null?n:o))},mu=(r,e)=>(t,n)=>{n.channelCount=1,n.channelCountMode="explicit",Object.defineProperty(n,"channelCount",{get:()=>1,set:()=>{throw r()}}),Object.defineProperty(n,"channelCountMode",{get:()=>"explicit",set:()=>{throw r()}});const s=t.createBufferSource();e(n,()=>{const u=n.numberOfInputs;for(let l=0;l<u;l+=1)s.connect(n,0,l)},()=>s.disconnect(n))},qr=(r,e,t)=>r.copyFromChannel===void 0?r.getChannelData(t)[0]:(r.copyFromChannel(e,t),e[0]),Br=r=>{if(r===null)return!1;const e=r.length;return e%2!==0?r[Math.floor(e/2)]!==0:r[e/2-1]+r[e/2]!==0},sn=(r,e,t,n)=>{let s=r;for(;!s.hasOwnProperty(e);)s=Object.getPrototypeOf(s);const{get:o,set:c}=Object.getOwnPropertyDescriptor(s,e);Object.defineProperty(r,e,{get:t(o),set:n(c)})},gu=r=>({...r,outputChannelCount:r.outputChannelCount!==void 0?r.outputChannelCount:r.numberOfInputs===1&&r.numberOfOutputs===1?[r.channelCount]:Array.from({length:r.numberOfOutputs},()=>1)}),vu=r=>({...r,channelCount:r.numberOfOutputs}),yu=r=>{const{imag:e,real:t}=r;return e===void 0?t===void 0?{...r,imag:[0,0],real:[0,0]}:{...r,imag:Array.from(t,()=>0),real:t}:t===void 0?{...r,imag:e,real:Array.from(e,()=>0)}:{...r,imag:e,real:t}},jr=(r,e,t)=>{try{r.setValueAtTime(e,t)}catch(n){if(n.code!==9)throw n;jr(r,e,t+1e-7)}},Tu=r=>{const e=r.createBufferSource();e.start();try{e.start()}catch{return!0}return!1},wu=r=>{const e=r.createBufferSource(),t=r.createBuffer(1,1,44100);e.buffer=t;try{e.start(0,1)}catch{return!1}return!0},bu=r=>{const e=r.createBufferSource();e.start();try{e.stop()}catch{return!1}return!0},ys=r=>{const e=r.createOscillator();try{e.start(-1)}catch(t){return t instanceof RangeError}return!1},Gr=r=>{const e=r.createBuffer(1,1,44100),t=r.createBufferSource();t.buffer=e,t.start(),t.stop();try{return t.stop(),!0}catch{return!1}},Ts=r=>{const e=r.createOscillator();try{e.stop(-1)}catch(t){return t instanceof RangeError}return!1},Au=r=>{const{port1:e,port2:t}=new MessageChannel;try{e.postMessage(r)}finally{e.close(),t.close()}},Su=r=>{r.start=(e=>(t=0,n=0,s)=>{const o=r.buffer,c=o===null?n:Math.min(o.duration,n);o!==null&&c>o.duration-.5/r.context.sampleRate?e.call(r,t,0,0):e.call(r,t,c,s)})(r.start)},Ur=(r,e)=>{const t=e.createGain();r.connect(t);const n=(s=>()=>{s.call(r,t),r.removeEventListener("ended",n)})(r.disconnect);r.addEventListener("ended",n),Ft(r,t),r.stop=(s=>{let o=!1;return(c=0)=>{if(o)try{s.call(r,c)}catch{t.gain.setValueAtTime(0,c)}else s.call(r,c),o=!0}})(r.stop)},Lt=(r,e)=>t=>{const n={value:r};return Object.defineProperties(t,{currentTarget:n,target:n}),typeof e=="function"?e.call(r,t):e.handleEvent.call(r,t)},xu=Ui(wt),ku=Yi(wt),Cu=ua(Mn),Hr=new WeakMap,Ou=Ca(Hr),We=Wo(new Map,new WeakMap),je=du(),$r=sc(We,Ue),ws=ka(Te),me=Yc(Te,ws,gt),Nu=to($r,J,me),Q=Ia(In),Qe=Ic(je),z=Xa(Qe),Xr=new WeakMap,Zr=ya(Lt),rn=ac(je),bs=Ga(rn),As=Ua(je),zr=Ha(je),zt=uc(je),ce=Co(Hi(br),zi(xu,ku,Tn,Cu,wn,Te,Ou,Qt,J,wt,ze,gt,pn),We,La(ss,wn,Te,J,Xt,ze),Ue,En,be,ia(Tn,ss,Te,J,Xt,Q,ze,z),da(Xr,Te,Le),Zr,Q,bs,As,zr,z,zt),Iu=eo(ce,Nu,Ue,$r,Q,z),Ss=new WeakSet,ur=rc(je),Yr=Jo(new Uint32Array(1)),xs=fu(Yr,Ue),ks=pu(Yr),Qr=so(Ss,We,be,ur,Qe,iu(ur),xs,ks),Dn=Qi(Se),Jr=Qc(ws,Kt,gt),He=$o(Jr),Wt=oc(Dn,We,Tu,wu,bu,ys,Gr,Ts,Su,_u(sn),Ur),$e=zc(Oa(Kt),Jr),Mu=oo(He,Wt,J,$e,me),qe=Oo($i(Ar),Xr,fs,No,Li,Wi,qi,Bi,ji,es,Tr,rn,jr),Eu=io(ce,Mu,qe,pe,Wt,Q,z,Lt),Du=mo(ce,go,Ue,pe,cc(Se,sn),Q,z,me),Ru=Lo(He,Fr,J,$e,me),bt=eu(Hr),Pu=Fo(ce,qe,Ru,En,Fr,Q,z,bt),at=tc(wt,As),Vu=mu(pe,at),ct=mc(rn,Vu),Fu=jo(ct,J,me),Lu=Bo(ce,Fu,ct,Q,z),Wu=Ho(tn,J,me),qu=Uo(ce,Wu,tn,Q,z,vu),Bu=yc(Dn,Wt,Se,at),qt=vc(Dn,We,Bu,ys,Ts),ju=Qo(He,qt,J,$e,me),Gu=Yo(ce,qe,ju,qt,Q,z,Lt),Kr=Tc(be,sn),Uu=ta(Kr,J,me),Hu=ea(ce,Uu,Kr,Q,z,bt),$u=ca(He,Lr,J,$e,me),Xu=aa(ce,qe,$u,Lr,Q,z,bt),ei=wc(be),Zu=ma(He,ei,J,$e,me),zu=_a(ce,qe,Zu,ei,be,Q,z,bt),Yu=Sa(He,Se,J,$e,me),Qu=Aa(ce,qe,Yu,Se,Q,z),Ju=xc(En,pe,nn,be),Rn=Jc(We,Se,nn,au(Se,Qe)),Ku=Fa(Wt,J,Qe,me,Rn),el=bc(Ju),tl=Pa(ce,el,Ku,Q,z,bt),nl=vo(qe,ct,qt,nn,be,qr,z,sn),ti=new WeakMap,sl=ec(Du,nl,Zr,z,ti,Lt),ni=Mc(Dn,We,ys,Gr,Ts,Ur),rl=Gc(He,ni,J,$e,me),il=jc(ce,qe,ni,rl,Q,z,Lt),si=Zo(Wt),ol=Lc(si,pe,Se,Br,at),Pn=Fc(si,pe,ol,Br,at,rn,sn),al=Dc(Tn,pe,ct,Se,nn,Pn,be,wn,qr,at),ri=Ec(al),cl=$c(He,ct,qt,Se,ri,J,Qe,$e,me,Rn),ul=Hc(ce,qe,ri,cl,Q,z,bt),ll=Rc(Ue),hl=Zc(ll,Q,new WeakSet,yu),dl=Vc(ct,tn,Se,Pn,be,at),ii=Pc(dl,be),fl=ru(He,ii,J,$e,me),pl=su(ce,qe,ii,fl,Q,z),_l=hu(Pn,J,me),ml=lu(ce,pe,Pn,_l,Q,z,bt),oi=Za(je),Cs=Ta(je),ai=new WeakMap,gl=Ma(ai,Qe),vl=oi?Zi(We,be,va(je),Cs,wa(Gi),Q,gl,z,zt,new WeakMap,new WeakMap,ou(zt,Qe),je):void 0,yl=$a(bs,z),Tl=ra(Ss,We,sa,ga,new WeakSet,Q,yl,vn,Zt,xs,ks),ci=Po(vl,Iu,Qr,Eu,Pu,Lu,qu,Gu,Hu,Tl,Xu,zu,Qu,tl,sl,il,ul,hl,pl,ml),wl=za(ce,kc,Q,z),bl=Qa(ce,Cc,Q,z),Al=Ja(ce,Oc,Q,z),Sl=Nc(pe,z),xl=Ka(ce,Sl,Q),kl=_o(ci,pe,be,cu,wl,bl,Al,xl,rn),Os=Ea(ti),Cl=Ji(Os),ui=Xo(Ue),Ol=la(Os),li=fa(Ue),hi=new WeakMap,Nl=xa(hi,Le),Il=_c(ui,Ue,pe,ct,tn,qt,Se,nn,be,li,Cs,Nl,at),Ml=hc(pe,Il,Se,be,at),El=Ro(He,ui,Wt,ct,tn,qt,Se,Ol,li,Cs,J,zt,Qe,$e,me,Rn),Dl=Na(ai),Rl=Kc(hi),lr=oi?Mo(Cl,ce,qe,El,Ml,Te,Dl,Q,z,zt,gu,Rl,Au,Lt):void 0,Pl=na(be,Qe),Vl=tu(Ss,We,ws,Os,Rn,vn,xs,ks),Fl=qc(ci,We,pe,Pl,Vl),Ll=Wa(In,bs),Wl=qa(ds,As),ql=Ba(fs,zr),Bl=ja(In,z);function De(r){return r===void 0}function H(r){return r!==void 0}function jl(r){return typeof r=="function"}function Ge(r){return typeof r=="number"}function pt(r){return Object.prototype.toString.call(r)==="[object Object]"&&r.constructor===Object}function di(r){return typeof r=="boolean"}function Ne(r){return Array.isArray(r)}function Ye(r){return typeof r=="string"}function dn(r){return Ye(r)&&/^([a-g]{1}(?:b|#|x|bb)?)(-?[0-9]+)/i.test(r)}function L(r,e){if(!r)throw new Error(e)}function Ee(r,e,t=1/0){if(!(e<=r&&r<=t))throw new RangeError(`Value must be within [${e}, ${t}], got: ${r}`)}function fi(r){!r.isOffline&&r.state!=="running"&&Vn('The AudioContext is "suspended". Invoke Tone.start() from a user action to start the audio.')}let pi=!1,hr=!1;function dr(r){pi=r}function Gl(r){De(r)&&pi&&!hr&&(hr=!0,Vn("Events scheduled inside of scheduled callbacks should use the passed in scheduling time. See https://github.com/Tonejs/Tone.js/wiki/Accurate-Timing"))}let _i=console;function Ul(...r){_i.log(...r)}function Vn(...r){_i.warn(...r)}function Hl(r){return new kl(r)}function $l(r,e,t){return new Fl(r,e,t)}const Ce=typeof self=="object"?self:null,Xl=Ce&&(Ce.hasOwnProperty("AudioContext")||Ce.hasOwnProperty("webkitAudioContext"));function Zl(r,e,t){return L(H(lr),"AudioWorkletNode only works in a secure context (https or localhost)"),new(r instanceof(Ce==null?void 0:Ce.BaseAudioContext)?Ce==null?void 0:Ce.AudioWorkletNode:lr)(r,e,t)}var fr=function(){return fr=Object.assign||function(e){for(var t,n=1,s=arguments.length;n<s;n++){t=arguments[n];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])}return e},fr.apply(this,arguments)};function ad(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t}function Be(r,e,t,n){var s=arguments.length,o=s<3?e:n===null?n=Object.getOwnPropertyDescriptor(e,t):n,c;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")o=Reflect.decorate(r,e,t,n);else for(var u=r.length-1;u>=0;u--)(c=r[u])&&(o=(s<3?c(o):s>3?c(e,t,o):c(e,t))||o);return s>3&&o&&Object.defineProperty(e,t,o),o}function ae(r,e,t,n){function s(o){return o instanceof t?o:new t(function(c){c(o)})}return new(t||(t=Promise))(function(o,c){function u(p){try{h(n.next(p))}catch(i){c(i)}}function l(p){try{h(n.throw(p))}catch(i){c(i)}}function h(p){p.done?o(p.value):s(p.value).then(u,l)}h((n=n.apply(r,e||[])).next())})}function cd(r,e,t){if(t||arguments.length===2)for(var n=0,s=e.length,o;n<s;n++)(o||!(n in e))&&(o||(o=Array.prototype.slice.call(e,0,n)),o[n]=e[n]);return r.concat(o||Array.prototype.slice.call(e))}class zl{constructor(e,t,n,s){this._callback=e,this._type=t,this._minimumUpdateInterval=Math.max(128/(s||44100),.001),this.updateInterval=n,this._createClock()}_createWorker(){const e=new Blob([`
			// the initial timeout time
			let timeoutTime =  ${(this._updateInterval*1e3).toFixed(1)};
			// onmessage callback
			self.onmessage = function(msg){
				timeoutTime = parseInt(msg.data);
			};
			// the tick function which posts a message
			// and schedules a new tick
			function tick(){
				setTimeout(tick, timeoutTime);
				self.postMessage('tick');
			}
			// call tick initially
			tick();
			`],{type:"text/javascript"}),t=URL.createObjectURL(e),n=new Worker(t);n.onmessage=this._callback.bind(this),this._worker=n}_createTimeout(){this._timeout=setTimeout(()=>{this._createTimeout(),this._callback()},this._updateInterval*1e3)}_createClock(){if(this._type==="worker")try{this._createWorker()}catch{this._type="timeout",this._createClock()}else this._type==="timeout"&&this._createTimeout()}_disposeClock(){this._timeout&&clearTimeout(this._timeout),this._worker&&(this._worker.terminate(),this._worker.onmessage=null)}get updateInterval(){return this._updateInterval}set updateInterval(e){var t;this._updateInterval=Math.max(e,this._minimumUpdateInterval),this._type==="worker"&&((t=this._worker)===null||t===void 0||t.postMessage(this._updateInterval*1e3))}get type(){return this._type}set type(e){this._disposeClock(),this._type=e,this._createClock()}dispose(){this._disposeClock()}}function vt(r){return ql(r)}function st(r){return Wl(r)}function _n(r){return Bl(r)}function Ot(r){return Ll(r)}function Yl(r){return r instanceof Qr}function Ql(r,e){return r==="value"||vt(e)||st(e)||Yl(e)}function _t(r,...e){if(!e.length)return r;const t=e.shift();if(pt(r)&&pt(t))for(const n in t)Ql(n,t[n])?r[n]=t[n]:pt(t[n])?(r[n]||Object.assign(r,{[n]:{}}),_t(r[n],t[n])):Object.assign(r,{[n]:t[n]});return _t(r,...e)}function Jl(r,e){return r.length===e.length&&r.every((t,n)=>e[n]===t)}function F(r,e,t=[],n){const s={},o=Array.from(e);if(pt(o[0])&&n&&!Reflect.has(o[0],n)&&(Object.keys(o[0]).some(u=>Reflect.has(r,u))||(_t(s,{[n]:o[0]}),t.splice(t.indexOf(n),1),o.shift())),o.length===1&&pt(o[0]))_t(s,o[0]);else for(let c=0;c<t.length;c++)H(o[c])&&(s[t[c]]=o[c]);return _t(r,s)}function Kl(r){return r.constructor.getDefaults()}function mt(r,e){return De(r)?e:r}function cs(r,e){return e.forEach(t=>{Reflect.has(r,t)&&delete r[t]}),r}/**
 * Tone.js
 * <AUTHOR> Mann
 * @license http://opensource.org/licenses/MIT MIT License
 * @copyright 2014-2024 Yotam Mann
 */class Je{constructor(){this.debug=!1,this._wasDisposed=!1}static getDefaults(){return{}}log(...e){(this.debug||Ce&&this.toString()===Ce.TONE_DEBUG_CLASS)&&Ul(this,...e)}dispose(){return this._wasDisposed=!0,this}get disposed(){return this._wasDisposed}toString(){return this.name}}Je.version=yr;const Ns=1e-6;function Dt(r,e){return r>e+Ns}function us(r,e){return Dt(r,e)||Pe(r,e)}function xn(r,e){return r+Ns<e}function Pe(r,e){return Math.abs(r-e)<Ns}function At(r,e,t){return Math.max(Math.min(r,t),e)}class Re extends Je{constructor(){super(),this.name="Timeline",this._timeline=[];const e=F(Re.getDefaults(),arguments,["memory"]);this.memory=e.memory,this.increasing=e.increasing}static getDefaults(){return{memory:1/0,increasing:!1}}get length(){return this._timeline.length}add(e){if(L(Reflect.has(e,"time"),"Timeline: events must have a time attribute"),e.time=e.time.valueOf(),this.increasing&&this.length){const t=this._timeline[this.length-1];L(us(e.time,t.time),"The time must be greater than or equal to the last scheduled time"),this._timeline.push(e)}else{const t=this._search(e.time);this._timeline.splice(t+1,0,e)}if(this.length>this.memory){const t=this.length-this.memory;this._timeline.splice(0,t)}return this}remove(e){const t=this._timeline.indexOf(e);return t!==-1&&this._timeline.splice(t,1),this}get(e,t="time"){const n=this._search(e,t);return n!==-1?this._timeline[n]:null}peek(){return this._timeline[0]}shift(){return this._timeline.shift()}getAfter(e,t="time"){const n=this._search(e,t);return n+1<this._timeline.length?this._timeline[n+1]:null}getBefore(e){const t=this._timeline.length;if(t>0&&this._timeline[t-1].time<e)return this._timeline[t-1];const n=this._search(e);return n-1>=0?this._timeline[n-1]:null}cancel(e){if(this._timeline.length>1){let t=this._search(e);if(t>=0)if(Pe(this._timeline[t].time,e)){for(let n=t;n>=0&&Pe(this._timeline[n].time,e);n--)t=n;this._timeline=this._timeline.slice(0,t)}else this._timeline=this._timeline.slice(0,t+1);else this._timeline=[]}else this._timeline.length===1&&us(this._timeline[0].time,e)&&(this._timeline=[]);return this}cancelBefore(e){const t=this._search(e);return t>=0&&(this._timeline=this._timeline.slice(t+1)),this}previousEvent(e){const t=this._timeline.indexOf(e);return t>0?this._timeline[t-1]:null}_search(e,t="time"){if(this._timeline.length===0)return-1;let n=0;const s=this._timeline.length;let o=s;if(s>0&&this._timeline[s-1][t]<=e)return s-1;for(;n<o;){let c=Math.floor(n+(o-n)/2);const u=this._timeline[c],l=this._timeline[c+1];if(Pe(u[t],e)){for(let h=c;h<this._timeline.length;h++){const p=this._timeline[h];if(Pe(p[t],e))c=h;else break}return c}else{if(xn(u[t],e)&&Dt(l[t],e))return c;Dt(u[t],e)?o=c:n=c+1}}return-1}_iterate(e,t=0,n=this._timeline.length-1){this._timeline.slice(t,n+1).forEach(e)}forEach(e){return this._iterate(e),this}forEachBefore(e,t){const n=this._search(e);return n!==-1&&this._iterate(t,0,n),this}forEachAfter(e,t){const n=this._search(e);return this._iterate(t,n+1),this}forEachBetween(e,t,n){let s=this._search(e),o=this._search(t);return s!==-1&&o!==-1?(this._timeline[s].time!==e&&(s+=1),this._timeline[o].time===t&&(o-=1),this._iterate(n,s,o)):s===-1&&this._iterate(n,0,o),this}forEachFrom(e,t){let n=this._search(e);for(;n>=0&&this._timeline[n].time>=e;)n--;return this._iterate(t,n+1),this}forEachAtTime(e,t){const n=this._search(e);if(n!==-1&&Pe(this._timeline[n].time,e)){let s=n;for(let o=n;o>=0&&Pe(this._timeline[o].time,e);o--)s=o;this._iterate(o=>{t(o)},s,n)}return this}dispose(){return super.dispose(),this._timeline=[],this}}const mi=[];function Fn(r){mi.push(r)}function eh(r){mi.forEach(e=>e(r))}const gi=[];function Ln(r){gi.push(r)}function th(r){gi.forEach(e=>e(r))}class on extends Je{constructor(){super(...arguments),this.name="Emitter"}on(e,t){return e.split(/\W+/).forEach(s=>{De(this._events)&&(this._events={}),this._events.hasOwnProperty(s)||(this._events[s]=[]),this._events[s].push(t)}),this}once(e,t){const n=(...s)=>{t(...s),this.off(e,n)};return this.on(e,n),this}off(e,t){return e.split(/\W+/).forEach(s=>{if(De(this._events)&&(this._events={}),this._events.hasOwnProperty(s))if(De(t))this._events[s]=[];else{const o=this._events[s];for(let c=o.length-1;c>=0;c--)o[c]===t&&o.splice(c,1)}}),this}emit(e,...t){if(this._events&&this._events.hasOwnProperty(e)){const n=this._events[e].slice(0);for(let s=0,o=n.length;s<o;s++)n[s].apply(this,t)}return this}static mixin(e){["on","once","off","emit"].forEach(t=>{const n=Object.getOwnPropertyDescriptor(on.prototype,t);Object.defineProperty(e.prototype,t,n)})}dispose(){return super.dispose(),this._events=void 0,this}}class vi extends on{constructor(){super(...arguments),this.isOffline=!1}toJSON(){return{}}}class an extends vi{constructor(){var e,t;super(),this.name="Context",this._constants=new Map,this._timeouts=new Re,this._timeoutIds=0,this._initialized=!1,this._closeStarted=!1,this.isOffline=!1,this._workletPromise=null;const n=F(an.getDefaults(),arguments,["context"]);n.context?(this._context=n.context,this._latencyHint=((e=arguments[0])===null||e===void 0?void 0:e.latencyHint)||""):(this._context=Hl({latencyHint:n.latencyHint}),this._latencyHint=n.latencyHint),this._ticker=new zl(this.emit.bind(this,"tick"),n.clockSource,n.updateInterval,this._context.sampleRate),this.on("tick",this._timeoutLoop.bind(this)),this._context.onstatechange=()=>{this.emit("statechange",this.state)},this[!((t=arguments[0])===null||t===void 0)&&t.hasOwnProperty("updateInterval")?"_lookAhead":"lookAhead"]=n.lookAhead}static getDefaults(){return{clockSource:"worker",latencyHint:"interactive",lookAhead:.1,updateInterval:.05}}initialize(){return this._initialized||(eh(this),this._initialized=!0),this}createAnalyser(){return this._context.createAnalyser()}createOscillator(){return this._context.createOscillator()}createBufferSource(){return this._context.createBufferSource()}createBiquadFilter(){return this._context.createBiquadFilter()}createBuffer(e,t,n){return this._context.createBuffer(e,t,n)}createChannelMerger(e){return this._context.createChannelMerger(e)}createChannelSplitter(e){return this._context.createChannelSplitter(e)}createConstantSource(){return this._context.createConstantSource()}createConvolver(){return this._context.createConvolver()}createDelay(e){return this._context.createDelay(e)}createDynamicsCompressor(){return this._context.createDynamicsCompressor()}createGain(){return this._context.createGain()}createIIRFilter(e,t){return this._context.createIIRFilter(e,t)}createPanner(){return this._context.createPanner()}createPeriodicWave(e,t,n){return this._context.createPeriodicWave(e,t,n)}createStereoPanner(){return this._context.createStereoPanner()}createWaveShaper(){return this._context.createWaveShaper()}createMediaStreamSource(e){return L(Ot(this._context),"Not available if OfflineAudioContext"),this._context.createMediaStreamSource(e)}createMediaElementSource(e){return L(Ot(this._context),"Not available if OfflineAudioContext"),this._context.createMediaElementSource(e)}createMediaStreamDestination(){return L(Ot(this._context),"Not available if OfflineAudioContext"),this._context.createMediaStreamDestination()}decodeAudioData(e){return this._context.decodeAudioData(e)}get currentTime(){return this._context.currentTime}get state(){return this._context.state}get sampleRate(){return this._context.sampleRate}get listener(){return this.initialize(),this._listener}set listener(e){L(!this._initialized,"The listener cannot be set after initialization."),this._listener=e}get transport(){return this.initialize(),this._transport}set transport(e){L(!this._initialized,"The transport cannot be set after initialization."),this._transport=e}get draw(){return this.initialize(),this._draw}set draw(e){L(!this._initialized,"Draw cannot be set after initialization."),this._draw=e}get destination(){return this.initialize(),this._destination}set destination(e){L(!this._initialized,"The destination cannot be set after initialization."),this._destination=e}createAudioWorkletNode(e,t){return Zl(this.rawContext,e,t)}addAudioWorkletModule(e){return ae(this,void 0,void 0,function*(){L(H(this.rawContext.audioWorklet),"AudioWorkletNode is only available in a secure context (https or localhost)"),this._workletPromise||(this._workletPromise=this.rawContext.audioWorklet.addModule(e)),yield this._workletPromise})}workletsAreReady(){return ae(this,void 0,void 0,function*(){(yield this._workletPromise)?this._workletPromise:Promise.resolve()})}get updateInterval(){return this._ticker.updateInterval}set updateInterval(e){this._ticker.updateInterval=e}get clockSource(){return this._ticker.type}set clockSource(e){this._ticker.type=e}get lookAhead(){return this._lookAhead}set lookAhead(e){this._lookAhead=e,this.updateInterval=e?e/2:.01}get latencyHint(){return this._latencyHint}get rawContext(){return this._context}now(){return this._context.currentTime+this._lookAhead}immediate(){return this._context.currentTime}resume(){return Ot(this._context)?this._context.resume():Promise.resolve()}close(){return ae(this,void 0,void 0,function*(){Ot(this._context)&&this.state!=="closed"&&!this._closeStarted&&(this._closeStarted=!0,yield this._context.close()),this._initialized&&th(this)})}getConstant(e){if(this._constants.has(e))return this._constants.get(e);{const t=this._context.createBuffer(1,128,this._context.sampleRate),n=t.getChannelData(0);for(let o=0;o<n.length;o++)n[o]=e;const s=this._context.createBufferSource();return s.channelCount=1,s.channelCountMode="explicit",s.buffer=t,s.loop=!0,s.start(0),this._constants.set(e,s),s}}dispose(){return super.dispose(),this._ticker.dispose(),this._timeouts.dispose(),Object.keys(this._constants).map(e=>this._constants[e].disconnect()),this.close(),this}_timeoutLoop(){const e=this.now();this._timeouts.forEachBefore(e,t=>{t.callback(),this._timeouts.remove(t)})}setTimeout(e,t){this._timeoutIds++;const n=this.now();return this._timeouts.add({callback:e,id:this._timeoutIds,time:n+t}),this._timeoutIds}clearTimeout(e){return this._timeouts.forEach(t=>{t.id===e&&this._timeouts.remove(t)}),this}clearInterval(e){return this.clearTimeout(e)}setInterval(e,t){const n=++this._timeoutIds,s=()=>{const o=this.now();this._timeouts.add({callback:()=>{e(),s()},id:n,time:o+t})};return s(),n}}class nh extends vi{constructor(){super(...arguments),this.lookAhead=0,this.latencyHint=0,this.isOffline=!1}createAnalyser(){return{}}createOscillator(){return{}}createBufferSource(){return{}}createBiquadFilter(){return{}}createBuffer(e,t,n){return{}}createChannelMerger(e){return{}}createChannelSplitter(e){return{}}createConstantSource(){return{}}createConvolver(){return{}}createDelay(e){return{}}createDynamicsCompressor(){return{}}createGain(){return{}}createIIRFilter(e,t){return{}}createPanner(){return{}}createPeriodicWave(e,t,n){return{}}createStereoPanner(){return{}}createWaveShaper(){return{}}createMediaStreamSource(e){return{}}createMediaElementSource(e){return{}}createMediaStreamDestination(){return{}}decodeAudioData(e){return Promise.resolve({})}createAudioWorkletNode(e,t){return{}}get rawContext(){return{}}addAudioWorkletModule(e){return ae(this,void 0,void 0,function*(){return Promise.resolve()})}resume(){return Promise.resolve()}setTimeout(e,t){return 0}clearTimeout(e){return this}setInterval(e,t){return 0}clearInterval(e){return this}getConstant(e){return{}}get currentTime(){return 0}get state(){return{}}get sampleRate(){return 0}get listener(){return{}}get transport(){return{}}get draw(){return{}}set draw(e){}get destination(){return{}}set destination(e){}now(){return 0}immediate(){return 0}}function te(r,e){Ne(e)?e.forEach(t=>te(r,t)):Object.defineProperty(r,e,{enumerable:!0,writable:!1})}function yi(r,e){Ne(e)?e.forEach(t=>yi(r,t)):Object.defineProperty(r,e,{writable:!0})}const $=()=>{};class Y extends Je{constructor(){super(),this.name="ToneAudioBuffer",this.onload=$;const e=F(Y.getDefaults(),arguments,["url","onload","onerror"]);this.reverse=e.reverse,this.onload=e.onload,Ye(e.url)?this.load(e.url).catch(e.onerror):e.url&&this.set(e.url)}static getDefaults(){return{onerror:$,onload:$,reverse:!1}}get sampleRate(){return this._buffer?this._buffer.sampleRate:Ve().sampleRate}set(e){return e instanceof Y?e.loaded?this._buffer=e.get():e.onload=()=>{this.set(e),this.onload(this)}:this._buffer=e,this._reversed&&this._reverse(),this}get(){return this._buffer}load(e){return ae(this,void 0,void 0,function*(){const t=Y.load(e).then(n=>{this.set(n),this.onload(this)});Y.downloads.push(t);try{yield t}finally{const n=Y.downloads.indexOf(t);Y.downloads.splice(n,1)}return this})}dispose(){return super.dispose(),this._buffer=void 0,this}fromArray(e){const t=Ne(e)&&e[0].length>0,n=t?e.length:1,s=t?e[0].length:e.length,o=Ve(),c=o.createBuffer(n,s,o.sampleRate),u=!t&&n===1?[e]:e;for(let l=0;l<n;l++)c.copyToChannel(u[l],l);return this._buffer=c,this}toMono(e){if(Ge(e))this.fromArray(this.toArray(e));else{let t=new Float32Array(this.length);const n=this.numberOfChannels;for(let s=0;s<n;s++){const o=this.toArray(s);for(let c=0;c<o.length;c++)t[c]+=o[c]}t=t.map(s=>s/n),this.fromArray(t)}return this}toArray(e){if(Ge(e))return this.getChannelData(e);if(this.numberOfChannels===1)return this.toArray(0);{const t=[];for(let n=0;n<this.numberOfChannels;n++)t[n]=this.getChannelData(n);return t}}getChannelData(e){return this._buffer?this._buffer.getChannelData(e):new Float32Array(0)}slice(e,t=this.duration){L(this.loaded,"Buffer is not loaded");const n=Math.floor(e*this.sampleRate),s=Math.floor(t*this.sampleRate);L(n<s,"The start time must be less than the end time");const o=s-n,c=Ve().createBuffer(this.numberOfChannels,o,this.sampleRate);for(let u=0;u<this.numberOfChannels;u++)c.copyToChannel(this.getChannelData(u).subarray(n,s),u);return new Y(c)}_reverse(){if(this.loaded)for(let e=0;e<this.numberOfChannels;e++)this.getChannelData(e).reverse();return this}get loaded(){return this.length>0}get duration(){return this._buffer?this._buffer.duration:0}get length(){return this._buffer?this._buffer.length:0}get numberOfChannels(){return this._buffer?this._buffer.numberOfChannels:0}get reverse(){return this._reversed}set reverse(e){this._reversed!==e&&(this._reversed=e,this._reverse())}static fromArray(e){return new Y().fromArray(e)}static fromUrl(e){return ae(this,void 0,void 0,function*(){return yield new Y().load(e)})}static load(e){return ae(this,void 0,void 0,function*(){const t=Y.baseUrl===""||Y.baseUrl.endsWith("/")?Y.baseUrl:Y.baseUrl+"/",n=yield fetch(t+e);if(!n.ok)throw new Error(`could not load url: ${e}`);const s=yield n.arrayBuffer();return yield Ve().decodeAudioData(s)})}static supportsType(e){const t=e.split("."),n=t[t.length-1];return document.createElement("audio").canPlayType("audio/"+n)!==""}static loaded(){return ae(this,void 0,void 0,function*(){for(yield Promise.resolve();Y.downloads.length;)yield Y.downloads[0]})}}Y.baseUrl="";Y.downloads=[];class Wn extends an{constructor(){super({clockSource:"offline",context:_n(arguments[0])?arguments[0]:$l(arguments[0],arguments[1]*arguments[2],arguments[2]),lookAhead:0,updateInterval:_n(arguments[0])?128/arguments[0].sampleRate:128/arguments[2]}),this.name="OfflineContext",this._currentTime=0,this.isOffline=!0,this._duration=_n(arguments[0])?arguments[0].length/arguments[0].sampleRate:arguments[1]}now(){return this._currentTime}get currentTime(){return this._currentTime}_renderClock(e){return ae(this,void 0,void 0,function*(){let t=0;for(;this._duration-this._currentTime>=0;){this.emit("tick"),this._currentTime+=128/this.sampleRate,t++;const n=Math.floor(this.sampleRate/128);e&&t%n===0&&(yield new Promise(s=>setTimeout(s,1)))}})}render(){return ae(this,arguments,void 0,function*(e=!0){yield this.workletsAreReady(),yield this._renderClock(e);const t=yield this._context.startRendering();return new Y(t)})}close(){return Promise.resolve()}}const Ti=new nh;let dt=Ti;function Ve(){return dt===Ti&&Xl&&sh(new an),dt}function sh(r,e=!1){e&&dt.dispose(),Ot(r)?dt=new an(r):_n(r)?dt=new Wn(r):dt=r}function ud(){return dt.resume()}if(Ce&&!Ce.TONE_SILENCE_LOGGING){const e=` * Tone.js v${yr} * `;console.log(`%c${e}`,"background: #000; color: #fff")}function rh(r){return Math.pow(10,r/20)}function ih(r){return 20*(Math.log(r)/Math.LN10)}function wi(r){return Math.pow(2,r/12)}let qn=440;function oh(){return qn}function ah(r){qn=r}function ft(r){return Math.round(bi(r))}function bi(r){return 69+12*Math.log2(r/qn)}function Ai(r){return qn*Math.pow(2,(r-69)/12)}class Is extends Je{constructor(e,t,n){super(),this.defaultUnits="s",this._val=t,this._units=n,this.context=e,this._expressions=this._getExpressions()}_getExpressions(){return{hz:{method:e=>this._frequencyToUnits(parseFloat(e)),regexp:/^(\d+(?:\.\d+)?)hz$/i},i:{method:e=>this._ticksToUnits(parseInt(e,10)),regexp:/^(\d+)i$/i},m:{method:e=>this._beatsToUnits(parseInt(e,10)*this._getTimeSignature()),regexp:/^(\d+)m$/i},n:{method:(e,t)=>{const n=parseInt(e,10),s=t==="."?1.5:1;return n===1?this._beatsToUnits(this._getTimeSignature())*s:this._beatsToUnits(4/n)*s},regexp:/^(\d+)n(\.?)$/i},number:{method:e=>this._expressions[this.defaultUnits].method.call(this,e),regexp:/^(\d+(?:\.\d+)?)$/},s:{method:e=>this._secondsToUnits(parseFloat(e)),regexp:/^(\d+(?:\.\d+)?)s$/},samples:{method:e=>parseInt(e,10)/this.context.sampleRate,regexp:/^(\d+)samples$/},t:{method:e=>{const t=parseInt(e,10);return this._beatsToUnits(8/(Math.floor(t)*3))},regexp:/^(\d+)t$/i},tr:{method:(e,t,n)=>{let s=0;return e&&e!=="0"&&(s+=this._beatsToUnits(this._getTimeSignature()*parseFloat(e))),t&&t!=="0"&&(s+=this._beatsToUnits(parseFloat(t))),n&&n!=="0"&&(s+=this._beatsToUnits(parseFloat(n)/4)),s},regexp:/^(\d+(?:\.\d+)?):(\d+(?:\.\d+)?):?(\d+(?:\.\d+)?)?$/}}}valueOf(){if(this._val instanceof Is&&this.fromType(this._val),De(this._val))return this._noArg();if(Ye(this._val)&&De(this._units)){for(const e in this._expressions)if(this._expressions[e].regexp.test(this._val.trim())){this._units=e;break}}else if(pt(this._val)){let e=0;for(const t in this._val)if(H(this._val[t])){const n=this._val[t],s=new this.constructor(this.context,t).valueOf()*n;e+=s}return e}if(H(this._units)){const e=this._expressions[this._units],t=this._val.toString().trim().match(e.regexp);return t?e.method.apply(this,t.slice(1)):e.method.call(this,this._val)}else return Ye(this._val)?parseFloat(this._val):this._val}_frequencyToUnits(e){return 1/e}_beatsToUnits(e){return 60/this._getBpm()*e}_secondsToUnits(e){return e}_ticksToUnits(e){return e*this._beatsToUnits(1)/this._getPPQ()}_noArg(){return this._now()}_getBpm(){return this.context.transport.bpm.value}_getTimeSignature(){return this.context.transport.timeSignature}_getPPQ(){return this.context.transport.PPQ}fromType(e){switch(this._units=void 0,this.defaultUnits){case"s":this._val=e.toSeconds();break;case"i":this._val=e.toTicks();break;case"hz":this._val=e.toFrequency();break;case"midi":this._val=e.toMidi();break}return this}toFrequency(){return 1/this.toSeconds()}toSamples(){return this.toSeconds()*this.context.sampleRate}toMilliseconds(){return this.toSeconds()*1e3}}class Fe extends Is{constructor(){super(...arguments),this.name="TimeClass"}_getExpressions(){return Object.assign(super._getExpressions(),{now:{method:e=>this._now()+new this.constructor(this.context,e).valueOf(),regexp:/^\+(.+)/},quantize:{method:e=>{const t=new Fe(this.context,e).valueOf();return this._secondsToUnits(this.context.transport.nextSubdivision(t))},regexp:/^@(.+)/}})}quantize(e,t=1){const n=new this.constructor(this.context,e).valueOf(),s=this.valueOf(),u=Math.round(s/n)*n-s;return s+u*t}toNotation(){const e=this.toSeconds(),t=["1m"];for(let o=1;o<9;o++){const c=Math.pow(2,o);t.push(c+"n."),t.push(c+"n"),t.push(c+"t")}t.push("0");let n=t[0],s=new Fe(this.context,t[0]).toSeconds();return t.forEach(o=>{const c=new Fe(this.context,o).toSeconds();Math.abs(c-e)<Math.abs(s-e)&&(n=o,s=c)}),n}toBarsBeatsSixteenths(){const e=this._beatsToUnits(1);let t=this.valueOf()/e;t=parseFloat(t.toFixed(4));const n=Math.floor(t/this._getTimeSignature());let s=t%1*4;t=Math.floor(t)%this._getTimeSignature();const o=s.toString();return o.length>3&&(s=parseFloat(parseFloat(o).toFixed(3))),[n,t,s].join(":")}toTicks(){const e=this._beatsToUnits(1);return this.valueOf()/e*this._getPPQ()}toSeconds(){return this.valueOf()}toMidi(){return ft(this.toFrequency())}_now(){return this.context.now()}}class Oe extends Fe{constructor(){super(...arguments),this.name="Frequency",this.defaultUnits="hz"}static get A4(){return oh()}static set A4(e){ah(e)}_getExpressions(){return Object.assign({},super._getExpressions(),{midi:{regexp:/^(\d+(?:\.\d+)?midi)/,method(e){return this.defaultUnits==="midi"?e:Oe.mtof(e)}},note:{regexp:/^([a-g]{1}(?:b|#|##|x|bb|###|#x|x#|bbb)?)(-?[0-9]+)/i,method(e,t){const s=ch[e.toLowerCase()]+(parseInt(t,10)+1)*12;return this.defaultUnits==="midi"?s:Oe.mtof(s)}},tr:{regexp:/^(\d+(?:\.\d+)?):(\d+(?:\.\d+)?):?(\d+(?:\.\d+)?)?/,method(e,t,n){let s=1;return e&&e!=="0"&&(s*=this._beatsToUnits(this._getTimeSignature()*parseFloat(e))),t&&t!=="0"&&(s*=this._beatsToUnits(parseFloat(t))),n&&n!=="0"&&(s*=this._beatsToUnits(parseFloat(n)/4)),s}}})}transpose(e){return new Oe(this.context,this.valueOf()*wi(e))}harmonize(e){return e.map(t=>this.transpose(t))}toMidi(){return ft(this.valueOf())}toNote(){const e=this.toFrequency(),t=Math.log2(e/Oe.A4);let n=Math.round(12*t)+57;const s=Math.floor(n/12);return s<0&&(n+=-12*s),uh[n%12]+s.toString()}toSeconds(){return 1/super.toSeconds()}toTicks(){const e=this._beatsToUnits(1),t=this.valueOf()/e;return Math.floor(t*this._getPPQ())}_noArg(){return 0}_frequencyToUnits(e){return e}_ticksToUnits(e){return 1/(e*60/(this._getBpm()*this._getPPQ()))}_beatsToUnits(e){return 1/super._beatsToUnits(e)}_secondsToUnits(e){return 1/e}static mtof(e){return Ai(e)}static ftom(e){return ft(e)}}const ch={cbbb:-3,cbb:-2,cb:-1,c:0,"c#":1,cx:2,"c##":2,"c###":3,"cx#":3,"c#x":3,dbbb:-1,dbb:0,db:1,d:2,"d#":3,dx:4,"d##":4,"d###":5,"dx#":5,"d#x":5,ebbb:1,ebb:2,eb:3,e:4,"e#":5,ex:6,"e##":6,"e###":7,"ex#":7,"e#x":7,fbbb:2,fbb:3,fb:4,f:5,"f#":6,fx:7,"f##":7,"f###":8,"fx#":8,"f#x":8,gbbb:4,gbb:5,gb:6,g:7,"g#":8,gx:9,"g##":9,"g###":10,"gx#":10,"g#x":10,abbb:6,abb:7,ab:8,a:9,"a#":10,ax:11,"a##":11,"a###":12,"ax#":12,"a#x":12,bbbb:8,bbb:9,bb:10,b:11,"b#":12,bx:13,"b##":13,"b###":14,"bx#":14,"b#x":14},uh=["C","C#","D","D#","E","F","F#","G","G#","A","A#","B"];class $t extends Fe{constructor(){super(...arguments),this.name="TransportTime"}_now(){return this.context.transport.seconds}}class _e extends Je{constructor(){super();const e=F(_e.getDefaults(),arguments,["context"]);this.defaultContext?this.context=this.defaultContext:this.context=e.context}static getDefaults(){return{context:Ve()}}now(){return this.context.currentTime+this.context.lookAhead}immediate(){return this.context.currentTime}get sampleTime(){return 1/this.context.sampleRate}get blockTime(){return 128/this.context.sampleRate}toSeconds(e){return Gl(e),new Fe(this.context,e).toSeconds()}toFrequency(e){return new Oe(this.context,e).toFrequency()}toTicks(e){return new $t(this.context,e).toTicks()}_getPartialProperties(e){const t=this.get();return Object.keys(t).forEach(n=>{De(e[n])&&delete t[n]}),t}get(){const e=Kl(this);return Object.keys(e).forEach(t=>{if(Reflect.has(this,t)){const n=this[t];H(n)&&H(n.value)&&H(n.setValueAtTime)?e[t]=n.value:n instanceof _e?e[t]=n._getPartialProperties(e[t]):Ne(n)||Ge(n)||Ye(n)||di(n)?e[t]=n:delete e[t]}}),e}set(e){return Object.keys(e).forEach(t=>{Reflect.has(this,t)&&H(this[t])&&(this[t]&&H(this[t].value)&&H(this[t].setValueAtTime)?this[t].value!==e[t]&&(this[t].value=e[t]):this[t]instanceof _e?this[t].set(e[t]):this[t]=e[t])}),this}}class Bn extends Re{constructor(e="stopped"){super(),this.name="StateTimeline",this._initial=e,this.setStateAtTime(this._initial,0)}getValueAtTime(e){const t=this.get(e);return t!==null?t.state:this._initial}setStateAtTime(e,t,n){return Ee(t,0),this.add(Object.assign({},n,{state:e,time:t})),this}getLastState(e,t){const n=this._search(t);for(let s=n;s>=0;s--){const o=this._timeline[s];if(o.state===e)return o}}getNextState(e,t){const n=this._search(t);if(n!==-1)for(let s=n;s<this._timeline.length;s++){const o=this._timeline[s];if(o.state===e)return o}}}class se extends _e{constructor(){const e=F(se.getDefaults(),arguments,["param","units","convert"]);for(super(e),this.name="Param",this.overridden=!1,this._minOutput=1e-7,L(H(e.param)&&(vt(e.param)||e.param instanceof se),"param must be an AudioParam");!vt(e.param);)e.param=e.param._param;this._swappable=H(e.swappable)?e.swappable:!1,this._swappable?(this.input=this.context.createGain(),this._param=e.param,this.input.connect(this._param)):this._param=this.input=e.param,this._events=new Re(1e3),this._initialValue=this._param.defaultValue,this.units=e.units,this.convert=e.convert,this._minValue=e.minValue,this._maxValue=e.maxValue,H(e.value)&&e.value!==this._toType(this._initialValue)&&this.setValueAtTime(e.value,0)}static getDefaults(){return Object.assign(_e.getDefaults(),{convert:!0,units:"number"})}get value(){const e=this.now();return this.getValueAtTime(e)}set value(e){this.cancelScheduledValues(this.now()),this.setValueAtTime(e,this.now())}get minValue(){return H(this._minValue)?this._minValue:this.units==="time"||this.units==="frequency"||this.units==="normalRange"||this.units==="positive"||this.units==="transportTime"||this.units==="ticks"||this.units==="bpm"||this.units==="hertz"||this.units==="samples"?0:this.units==="audioRange"?-1:this.units==="decibels"?-1/0:this._param.minValue}get maxValue(){return H(this._maxValue)?this._maxValue:this.units==="normalRange"||this.units==="audioRange"?1:this._param.maxValue}_is(e,t){return this.units===t}_assertRange(e){return H(this.maxValue)&&H(this.minValue)&&Ee(e,this._fromType(this.minValue),this._fromType(this.maxValue)),e}_fromType(e){return this.convert&&!this.overridden?this._is(e,"time")?this.toSeconds(e):this._is(e,"decibels")?rh(e):this._is(e,"frequency")?this.toFrequency(e):e:this.overridden?0:e}_toType(e){return this.convert&&this.units==="decibels"?ih(e):e}setValueAtTime(e,t){const n=this.toSeconds(t),s=this._fromType(e);return L(isFinite(s)&&isFinite(n),`Invalid argument(s) to setValueAtTime: ${JSON.stringify(e)}, ${JSON.stringify(t)}`),this._assertRange(s),this.log(this.units,"setValueAtTime",e,n),this._events.add({time:n,type:"setValueAtTime",value:s}),this._param.setValueAtTime(s,n),this}getValueAtTime(e){const t=Math.max(this.toSeconds(e),0),n=this._events.getAfter(t),s=this._events.get(t);let o=this._initialValue;if(s===null)o=this._initialValue;else if(s.type==="setTargetAtTime"&&(n===null||n.type==="setValueAtTime")){const c=this._events.getBefore(s.time);let u;c===null?u=this._initialValue:u=c.value,s.type==="setTargetAtTime"&&(o=this._exponentialApproach(s.time,u,s.value,s.constant,t))}else if(n===null)o=s.value;else if(n.type==="linearRampToValueAtTime"||n.type==="exponentialRampToValueAtTime"){let c=s.value;if(s.type==="setTargetAtTime"){const u=this._events.getBefore(s.time);u===null?c=this._initialValue:c=u.value}n.type==="linearRampToValueAtTime"?o=this._linearInterpolate(s.time,c,n.time,n.value,t):o=this._exponentialInterpolate(s.time,c,n.time,n.value,t)}else o=s.value;return this._toType(o)}setRampPoint(e){e=this.toSeconds(e);let t=this.getValueAtTime(e);return this.cancelAndHoldAtTime(e),this._fromType(t)===0&&(t=this._toType(this._minOutput)),this.setValueAtTime(t,e),this}linearRampToValueAtTime(e,t){const n=this._fromType(e),s=this.toSeconds(t);return L(isFinite(n)&&isFinite(s),`Invalid argument(s) to linearRampToValueAtTime: ${JSON.stringify(e)}, ${JSON.stringify(t)}`),this._assertRange(n),this._events.add({time:s,type:"linearRampToValueAtTime",value:n}),this.log(this.units,"linearRampToValueAtTime",e,s),this._param.linearRampToValueAtTime(n,s),this}exponentialRampToValueAtTime(e,t){let n=this._fromType(e);n=Pe(n,0)?this._minOutput:n,this._assertRange(n);const s=this.toSeconds(t);return L(isFinite(n)&&isFinite(s),`Invalid argument(s) to exponentialRampToValueAtTime: ${JSON.stringify(e)}, ${JSON.stringify(t)}`),this._events.add({time:s,type:"exponentialRampToValueAtTime",value:n}),this.log(this.units,"exponentialRampToValueAtTime",e,s),this._param.exponentialRampToValueAtTime(n,s),this}exponentialRampTo(e,t,n){return n=this.toSeconds(n),this.setRampPoint(n),this.exponentialRampToValueAtTime(e,n+this.toSeconds(t)),this}linearRampTo(e,t,n){return n=this.toSeconds(n),this.setRampPoint(n),this.linearRampToValueAtTime(e,n+this.toSeconds(t)),this}targetRampTo(e,t,n){return n=this.toSeconds(n),this.setRampPoint(n),this.exponentialApproachValueAtTime(e,n,t),this}exponentialApproachValueAtTime(e,t,n){t=this.toSeconds(t),n=this.toSeconds(n);const s=Math.log(n+1)/Math.log(200);return this.setTargetAtTime(e,t,s),this.cancelAndHoldAtTime(t+n*.9),this.linearRampToValueAtTime(e,t+n),this}setTargetAtTime(e,t,n){const s=this._fromType(e);L(isFinite(n)&&n>0,"timeConstant must be a number greater than 0");const o=this.toSeconds(t);return this._assertRange(s),L(isFinite(s)&&isFinite(o),`Invalid argument(s) to setTargetAtTime: ${JSON.stringify(e)}, ${JSON.stringify(t)}`),this._events.add({constant:n,time:o,type:"setTargetAtTime",value:s}),this.log(this.units,"setTargetAtTime",e,o,n),this._param.setTargetAtTime(s,o,n),this}setValueCurveAtTime(e,t,n,s=1){n=this.toSeconds(n),t=this.toSeconds(t);const o=this._fromType(e[0])*s;this.setValueAtTime(this._toType(o),t);const c=n/(e.length-1);for(let u=1;u<e.length;u++){const l=this._fromType(e[u])*s;this.linearRampToValueAtTime(this._toType(l),t+u*c)}return this}cancelScheduledValues(e){const t=this.toSeconds(e);return L(isFinite(t),`Invalid argument to cancelScheduledValues: ${JSON.stringify(e)}`),this._events.cancel(t),this._param.cancelScheduledValues(t),this.log(this.units,"cancelScheduledValues",t),this}cancelAndHoldAtTime(e){const t=this.toSeconds(e),n=this._fromType(this.getValueAtTime(t));L(isFinite(t),`Invalid argument to cancelAndHoldAtTime: ${JSON.stringify(e)}`),this.log(this.units,"cancelAndHoldAtTime",t,"value="+n);const s=this._events.get(t),o=this._events.getAfter(t);return s&&Pe(s.time,t)?o?(this._param.cancelScheduledValues(o.time),this._events.cancel(o.time)):(this._param.cancelAndHoldAtTime(t),this._events.cancel(t+this.sampleTime)):o&&(this._param.cancelScheduledValues(o.time),this._events.cancel(o.time),o.type==="linearRampToValueAtTime"?this.linearRampToValueAtTime(this._toType(n),t):o.type==="exponentialRampToValueAtTime"&&this.exponentialRampToValueAtTime(this._toType(n),t)),this._events.add({time:t,type:"setValueAtTime",value:n}),this._param.setValueAtTime(n,t),this}rampTo(e,t=.1,n){return this.units==="frequency"||this.units==="bpm"||this.units==="decibels"?this.exponentialRampTo(e,t,n):this.linearRampTo(e,t,n),this}apply(e){const t=this.context.currentTime;e.setValueAtTime(this.getValueAtTime(t),t);const n=this._events.get(t);if(n&&n.type==="setTargetAtTime"){const s=this._events.getAfter(n.time),o=s?s.time:t+2,c=(o-t)/10;for(let u=t;u<o;u+=c)e.linearRampToValueAtTime(this.getValueAtTime(u),u)}return this._events.forEachAfter(this.context.currentTime,s=>{s.type==="cancelScheduledValues"?e.cancelScheduledValues(s.time):s.type==="setTargetAtTime"?e.setTargetAtTime(s.value,s.time,s.constant):e[s.type](s.value,s.time)}),this}setParam(e){L(this._swappable,"The Param must be assigned as 'swappable' in the constructor");const t=this.input;return t.disconnect(this._param),this.apply(e),this._param=e,t.connect(this._param),this}dispose(){return super.dispose(),this._events.dispose(),this}get defaultValue(){return this._toType(this._param.defaultValue)}_exponentialApproach(e,t,n,s,o){return n+(t-n)*Math.exp(-(o-e)/s)}_linearInterpolate(e,t,n,s,o){return t+(s-t)*((o-e)/(n-e))}_exponentialInterpolate(e,t,n,s,o){return t*Math.pow(s/t,(o-e)/(n-e))}}class j extends _e{constructor(){super(...arguments),this._internalChannels=[]}get numberOfInputs(){return H(this.input)?vt(this.input)||this.input instanceof se?1:this.input.numberOfInputs:0}get numberOfOutputs(){return H(this.output)?this.output.numberOfOutputs:0}_isAudioNode(e){return H(e)&&(e instanceof j||st(e))}_getInternalNodes(){const e=this._internalChannels.slice(0);return this._isAudioNode(this.input)&&e.push(this.input),this._isAudioNode(this.output)&&this.input!==this.output&&e.push(this.output),e}_setChannelProperties(e){this._getInternalNodes().forEach(n=>{n.channelCount=e.channelCount,n.channelCountMode=e.channelCountMode,n.channelInterpretation=e.channelInterpretation})}_getChannelProperties(){const e=this._getInternalNodes();L(e.length>0,"ToneAudioNode does not have any internal nodes");const t=e[0];return{channelCount:t.channelCount,channelCountMode:t.channelCountMode,channelInterpretation:t.channelInterpretation}}get channelCount(){return this._getChannelProperties().channelCount}set channelCount(e){const t=this._getChannelProperties();this._setChannelProperties(Object.assign(t,{channelCount:e}))}get channelCountMode(){return this._getChannelProperties().channelCountMode}set channelCountMode(e){const t=this._getChannelProperties();this._setChannelProperties(Object.assign(t,{channelCountMode:e}))}get channelInterpretation(){return this._getChannelProperties().channelInterpretation}set channelInterpretation(e){const t=this._getChannelProperties();this._setChannelProperties(Object.assign(t,{channelInterpretation:e}))}connect(e,t=0,n=0){return rt(this,e,t,n),this}toDestination(){return this.connect(this.context.destination),this}toMaster(){return Vn("toMaster() has been renamed toDestination()"),this.toDestination()}disconnect(e,t=0,n=0){return lh(this,e,t,n),this}chain(...e){return ls(this,...e),this}fan(...e){return e.forEach(t=>this.connect(t)),this}dispose(){return super.dispose(),H(this.input)&&(this.input instanceof j?this.input.dispose():st(this.input)&&this.input.disconnect()),H(this.output)&&(this.output instanceof j?this.output.dispose():st(this.output)&&this.output.disconnect()),this._internalChannels=[],this}}function ls(...r){const e=r.shift();r.reduce((t,n)=>(t instanceof j?t.connect(n):st(t)&&rt(t,n),n),e)}function rt(r,e,t=0,n=0){for(L(H(r),"Cannot connect from undefined node"),L(H(e),"Cannot connect to undefined node"),(e instanceof j||st(e))&&L(e.numberOfInputs>0,"Cannot connect to node with no inputs"),L(r.numberOfOutputs>0,"Cannot connect from node with no outputs");e instanceof j||e instanceof se;)H(e.input)&&(e=e.input);for(;r instanceof j;)H(r.output)&&(r=r.output);vt(e)?r.connect(e,t):r.connect(e,t,n)}function lh(r,e,t=0,n=0){if(H(e))for(;e instanceof j;)e=e.input;for(;!st(r);)H(r.output)&&(r=r.output);vt(e)?r.disconnect(e,t):st(e)?r.disconnect(e,t,n):r.disconnect()}class re extends j{constructor(){const e=F(re.getDefaults(),arguments,["gain","units"]);super(e),this.name="Gain",this._gainNode=this.context.createGain(),this.input=this._gainNode,this.output=this._gainNode,this.gain=new se({context:this.context,convert:e.convert,param:this._gainNode.gain,units:e.units,value:e.gain,minValue:e.minValue,maxValue:e.maxValue}),te(this,"gain")}static getDefaults(){return Object.assign(j.getDefaults(),{convert:!0,gain:1,units:"gain"})}dispose(){return super.dispose(),this._gainNode.disconnect(),this.gain.dispose(),this}}class Rt extends j{constructor(e){super(e),this.onended=$,this._startTime=-1,this._stopTime=-1,this._timeout=-1,this.output=new re({context:this.context,gain:0}),this._gainNode=this.output,this.getStateAtTime=function(t){const n=this.toSeconds(t);return this._startTime!==-1&&n>=this._startTime&&(this._stopTime===-1||n<=this._stopTime)?"started":"stopped"},this._fadeIn=e.fadeIn,this._fadeOut=e.fadeOut,this._curve=e.curve,this.onended=e.onended}static getDefaults(){return Object.assign(j.getDefaults(),{curve:"linear",fadeIn:0,fadeOut:0,onended:$})}_startGain(e,t=1){L(this._startTime===-1,"Source cannot be started more than once");const n=this.toSeconds(this._fadeIn);return this._startTime=e+n,this._startTime=Math.max(this._startTime,this.context.currentTime),n>0?(this._gainNode.gain.setValueAtTime(0,e),this._curve==="linear"?this._gainNode.gain.linearRampToValueAtTime(t,e+n):this._gainNode.gain.exponentialApproachValueAtTime(t,e,n)):this._gainNode.gain.setValueAtTime(t,e),this}stop(e){return this.log("stop",e),this._stopGain(this.toSeconds(e)),this}_stopGain(e){L(this._startTime!==-1,"'start' must be called before 'stop'"),this.cancelStop();const t=this.toSeconds(this._fadeOut);return this._stopTime=this.toSeconds(e)+t,this._stopTime=Math.max(this._stopTime,this.now()),t>0?this._curve==="linear"?this._gainNode.gain.linearRampTo(0,t,e):this._gainNode.gain.targetRampTo(0,t,e):(this._gainNode.gain.cancelAndHoldAtTime(e),this._gainNode.gain.setValueAtTime(0,e)),this.context.clearTimeout(this._timeout),this._timeout=this.context.setTimeout(()=>{const n=this._curve==="exponential"?t*2:0;this._stopSource(this.now()+n),this._onended()},this._stopTime-this.context.currentTime),this}_onended(){if(this.onended!==$&&(this.onended(this),this.onended=$,!this.context.isOffline)){const e=()=>this.dispose();typeof requestIdleCallback<"u"?requestIdleCallback(e):setTimeout(e,10)}}get state(){return this.getStateAtTime(this.now())}cancelStop(){return this.log("cancelStop"),L(this._startTime!==-1,"Source is not started"),this._gainNode.gain.cancelScheduledValues(this._startTime+this.sampleTime),this.context.clearTimeout(this._timeout),this._stopTime=-1,this}dispose(){return super.dispose(),this._gainNode.dispose(),this.onended=$,this}}class Ms extends Rt{constructor(){const e=F(Ms.getDefaults(),arguments,["offset"]);super(e),this.name="ToneConstantSource",this._source=this.context.createConstantSource(),rt(this._source,this._gainNode),this.offset=new se({context:this.context,convert:e.convert,param:this._source.offset,units:e.units,value:e.offset,minValue:e.minValue,maxValue:e.maxValue})}static getDefaults(){return Object.assign(Rt.getDefaults(),{convert:!0,offset:1,units:"number"})}start(e){const t=this.toSeconds(e);return this.log("start",t),this._startGain(t),this._source.start(t),this}_stopSource(e){this._source.stop(e)}dispose(){return super.dispose(),this.state==="started"&&this.stop(),this._source.disconnect(),this.offset.dispose(),this}}class fe extends j{constructor(){const e=F(fe.getDefaults(),arguments,["value","units"]);super(e),this.name="Signal",this.override=!0,this.output=this._constantSource=new Ms({context:this.context,convert:e.convert,offset:e.value,units:e.units,minValue:e.minValue,maxValue:e.maxValue}),this._constantSource.start(0),this.input=this._param=this._constantSource.offset}static getDefaults(){return Object.assign(j.getDefaults(),{convert:!0,units:"number",value:0})}connect(e,t=0,n=0){return Es(this,e,t,n),this}dispose(){return super.dispose(),this._param.dispose(),this._constantSource.dispose(),this}setValueAtTime(e,t){return this._param.setValueAtTime(e,t),this}getValueAtTime(e){return this._param.getValueAtTime(e)}setRampPoint(e){return this._param.setRampPoint(e),this}linearRampToValueAtTime(e,t){return this._param.linearRampToValueAtTime(e,t),this}exponentialRampToValueAtTime(e,t){return this._param.exponentialRampToValueAtTime(e,t),this}exponentialRampTo(e,t,n){return this._param.exponentialRampTo(e,t,n),this}linearRampTo(e,t,n){return this._param.linearRampTo(e,t,n),this}targetRampTo(e,t,n){return this._param.targetRampTo(e,t,n),this}exponentialApproachValueAtTime(e,t,n){return this._param.exponentialApproachValueAtTime(e,t,n),this}setTargetAtTime(e,t,n){return this._param.setTargetAtTime(e,t,n),this}setValueCurveAtTime(e,t,n,s){return this._param.setValueCurveAtTime(e,t,n,s),this}cancelScheduledValues(e){return this._param.cancelScheduledValues(e),this}cancelAndHoldAtTime(e){return this._param.cancelAndHoldAtTime(e),this}rampTo(e,t,n){return this._param.rampTo(e,t,n),this}get value(){return this._param.value}set value(e){this._param.value=e}get convert(){return this._param.convert}set convert(e){this._param.convert=e}get units(){return this._param.units}get overridden(){return this._param.overridden}set overridden(e){this._param.overridden=e}get maxValue(){return this._param.maxValue}get minValue(){return this._param.minValue}apply(e){return this._param.apply(e),this}}function Es(r,e,t,n){(e instanceof se||vt(e)||e instanceof fe&&e.override)&&(e.cancelScheduledValues(0),e.setValueAtTime(0,0),e instanceof fe&&(e.overridden=!0)),rt(r,e,t,n)}class Ds extends se{constructor(){const e=F(Ds.getDefaults(),arguments,["value"]);super(e),this.name="TickParam",this._events=new Re(1/0),this._multiplier=1,this._multiplier=e.multiplier,this._events.cancel(0),this._events.add({ticks:0,time:0,type:"setValueAtTime",value:this._fromType(e.value)}),this.setValueAtTime(e.value,0)}static getDefaults(){return Object.assign(se.getDefaults(),{multiplier:1,units:"hertz",value:1})}setTargetAtTime(e,t,n){t=this.toSeconds(t),this.setRampPoint(t);const s=this._fromType(e),o=this._events.get(t),c=Math.round(Math.max(1/n,1));for(let u=0;u<=c;u++){const l=n*u+t,h=this._exponentialApproach(o.time,o.value,s,n,l);this.linearRampToValueAtTime(this._toType(h),l)}return this}setValueAtTime(e,t){const n=this.toSeconds(t);super.setValueAtTime(e,t);const s=this._events.get(n),o=this._events.previousEvent(s),c=this._getTicksUntilEvent(o,n);return s.ticks=Math.max(c,0),this}linearRampToValueAtTime(e,t){const n=this.toSeconds(t);super.linearRampToValueAtTime(e,t);const s=this._events.get(n),o=this._events.previousEvent(s),c=this._getTicksUntilEvent(o,n);return s.ticks=Math.max(c,0),this}exponentialRampToValueAtTime(e,t){t=this.toSeconds(t);const n=this._fromType(e),s=this._events.get(t),o=Math.round(Math.max((t-s.time)*10,1)),c=(t-s.time)/o;for(let u=0;u<=o;u++){const l=c*u+s.time,h=this._exponentialInterpolate(s.time,s.value,t,n,l);this.linearRampToValueAtTime(this._toType(h),l)}return this}_getTicksUntilEvent(e,t){if(e===null)e={ticks:0,time:0,type:"setValueAtTime",value:0};else if(De(e.ticks)){const c=this._events.previousEvent(e);e.ticks=this._getTicksUntilEvent(c,e.time)}const n=this._fromType(this.getValueAtTime(e.time));let s=this._fromType(this.getValueAtTime(t));const o=this._events.get(t);return o&&o.time===t&&o.type==="setValueAtTime"&&(s=this._fromType(this.getValueAtTime(t-this.sampleTime))),.5*(t-e.time)*(n+s)+e.ticks}getTicksAtTime(e){const t=this.toSeconds(e),n=this._events.get(t);return Math.max(this._getTicksUntilEvent(n,t),0)}getDurationOfTicks(e,t){const n=this.toSeconds(t),s=this.getTicksAtTime(t);return this.getTimeOfTick(s+e)-n}getTimeOfTick(e){const t=this._events.get(e,"ticks"),n=this._events.getAfter(e,"ticks");if(t&&t.ticks===e)return t.time;if(t&&n&&n.type==="linearRampToValueAtTime"&&t.value!==n.value){const s=this._fromType(this.getValueAtTime(t.time)),c=(this._fromType(this.getValueAtTime(n.time))-s)/(n.time-t.time),u=Math.sqrt(Math.pow(s,2)-2*c*(t.ticks-e)),l=(-s+u)/c,h=(-s-u)/c;return(l>0?l:h)+t.time}else return t?t.value===0?1/0:t.time+(e-t.ticks)/t.value:e/this._initialValue}ticksToTime(e,t){return this.getDurationOfTicks(e,t)}timeToTicks(e,t){const n=this.toSeconds(t),s=this.toSeconds(e),o=this.getTicksAtTime(n);return this.getTicksAtTime(n+s)-o}_fromType(e){return this.units==="bpm"&&this.multiplier?1/(60/e/this.multiplier):super._fromType(e)}_toType(e){return this.units==="bpm"&&this.multiplier?e/this.multiplier*60:super._toType(e)}get multiplier(){return this._multiplier}set multiplier(e){const t=this.value;this._multiplier=e,this.cancelScheduledValues(0),this.setValueAtTime(t,0)}}class Rs extends fe{constructor(){const e=F(Rs.getDefaults(),arguments,["value"]);super(e),this.name="TickSignal",this.input=this._param=new Ds({context:this.context,convert:e.convert,multiplier:e.multiplier,param:this._constantSource.offset,units:e.units,value:e.value})}static getDefaults(){return Object.assign(fe.getDefaults(),{multiplier:1,units:"hertz",value:1})}ticksToTime(e,t){return this._param.ticksToTime(e,t)}timeToTicks(e,t){return this._param.timeToTicks(e,t)}getTimeOfTick(e){return this._param.getTimeOfTick(e)}getDurationOfTicks(e,t){return this._param.getDurationOfTicks(e,t)}getTicksAtTime(e){return this._param.getTicksAtTime(e)}get multiplier(){return this._param.multiplier}set multiplier(e){this._param.multiplier=e}dispose(){return super.dispose(),this._param.dispose(),this}}class Ps extends _e{constructor(){const e=F(Ps.getDefaults(),arguments,["frequency"]);super(e),this.name="TickSource",this._state=new Bn,this._tickOffset=new Re,this._ticksAtTime=new Re,this._secondsAtTime=new Re,this.frequency=new Rs({context:this.context,units:e.units,value:e.frequency}),te(this,"frequency"),this._state.setStateAtTime("stopped",0),this.setTicksAtTime(0,0)}static getDefaults(){return Object.assign({frequency:1,units:"hertz"},_e.getDefaults())}get state(){return this.getStateAtTime(this.now())}start(e,t){const n=this.toSeconds(e);return this._state.getValueAtTime(n)!=="started"&&(this._state.setStateAtTime("started",n),H(t)&&this.setTicksAtTime(t,n),this._ticksAtTime.cancel(n),this._secondsAtTime.cancel(n)),this}stop(e){const t=this.toSeconds(e);if(this._state.getValueAtTime(t)==="stopped"){const n=this._state.get(t);n&&n.time>0&&(this._tickOffset.cancel(n.time),this._state.cancel(n.time))}return this._state.cancel(t),this._state.setStateAtTime("stopped",t),this.setTicksAtTime(0,t),this._ticksAtTime.cancel(t),this._secondsAtTime.cancel(t),this}pause(e){const t=this.toSeconds(e);return this._state.getValueAtTime(t)==="started"&&(this._state.setStateAtTime("paused",t),this._ticksAtTime.cancel(t),this._secondsAtTime.cancel(t)),this}cancel(e){return e=this.toSeconds(e),this._state.cancel(e),this._tickOffset.cancel(e),this._ticksAtTime.cancel(e),this._secondsAtTime.cancel(e),this}getTicksAtTime(e){const t=this.toSeconds(e),n=this._state.getLastState("stopped",t),s=this._ticksAtTime.get(t),o={state:"paused",time:t};this._state.add(o);let c=s||n,u=s?s.ticks:0,l=null;return this._state.forEachBetween(c.time,t+this.sampleTime,h=>{let p=c.time;const i=this._tickOffset.get(h.time);i&&i.time>=c.time&&(u=i.ticks,p=i.time),c.state==="started"&&h.state!=="started"&&(u+=this.frequency.getTicksAtTime(h.time)-this.frequency.getTicksAtTime(p),h.time!==o.time&&(l={state:h.state,time:h.time,ticks:u})),c=h}),this._state.remove(o),l&&this._ticksAtTime.add(l),u}get ticks(){return this.getTicksAtTime(this.now())}set ticks(e){this.setTicksAtTime(e,this.now())}get seconds(){return this.getSecondsAtTime(this.now())}set seconds(e){const t=this.now(),n=this.frequency.timeToTicks(e,t);this.setTicksAtTime(n,t)}getSecondsAtTime(e){e=this.toSeconds(e);const t=this._state.getLastState("stopped",e),n={state:"paused",time:e};this._state.add(n);const s=this._secondsAtTime.get(e);let o=s||t,c=s?s.seconds:0,u=null;return this._state.forEachBetween(o.time,e+this.sampleTime,l=>{let h=o.time;const p=this._tickOffset.get(l.time);p&&p.time>=o.time&&(c=p.seconds,h=p.time),o.state==="started"&&l.state!=="started"&&(c+=l.time-h,l.time!==n.time&&(u={state:l.state,time:l.time,seconds:c})),o=l}),this._state.remove(n),u&&this._secondsAtTime.add(u),c}setTicksAtTime(e,t){return t=this.toSeconds(t),this._tickOffset.cancel(t),this._tickOffset.add({seconds:this.frequency.getDurationOfTicks(e,t),ticks:e,time:t}),this._ticksAtTime.cancel(t),this._secondsAtTime.cancel(t),this}getStateAtTime(e){return e=this.toSeconds(e),this._state.getValueAtTime(e)}getTimeOfTick(e,t=this.now()){const n=this._tickOffset.get(t),s=this._state.get(t),o=Math.max(n.time,s.time),c=this.frequency.getTicksAtTime(o)+e-n.ticks;return this.frequency.getTimeOfTick(c)}forEachTickBetween(e,t,n){let s=this._state.get(e);this._state.forEachBetween(e,t,c=>{s&&s.state==="started"&&c.state!=="started"&&this.forEachTickBetween(Math.max(s.time,e),c.time-this.sampleTime,n),s=c});let o=null;if(s&&s.state==="started"){const c=Math.max(s.time,e),u=this.frequency.getTicksAtTime(c),l=this.frequency.getTicksAtTime(s.time),h=u-l;let p=Math.ceil(h)-h;p=Pe(p,1)?0:p;let i=this.frequency.getTimeOfTick(u+p);for(;i<t;){try{n(i,Math.round(this.getTicksAtTime(i)))}catch(a){o=a;break}i+=this.frequency.getDurationOfTicks(1,i)}}if(o)throw o;return this}dispose(){return super.dispose(),this._state.dispose(),this._tickOffset.dispose(),this._ticksAtTime.dispose(),this._secondsAtTime.dispose(),this.frequency.dispose(),this}}class jn extends _e{constructor(){const e=F(jn.getDefaults(),arguments,["callback","frequency"]);super(e),this.name="Clock",this.callback=$,this._lastUpdate=0,this._state=new Bn("stopped"),this._boundLoop=this._loop.bind(this),this.callback=e.callback,this._tickSource=new Ps({context:this.context,frequency:e.frequency,units:e.units}),this._lastUpdate=0,this.frequency=this._tickSource.frequency,te(this,"frequency"),this._state.setStateAtTime("stopped",0),this.context.on("tick",this._boundLoop)}static getDefaults(){return Object.assign(_e.getDefaults(),{callback:$,frequency:1,units:"hertz"})}get state(){return this._state.getValueAtTime(this.now())}start(e,t){fi(this.context);const n=this.toSeconds(e);return this.log("start",n),this._state.getValueAtTime(n)!=="started"&&(this._state.setStateAtTime("started",n),this._tickSource.start(n,t),n<this._lastUpdate&&this.emit("start",n,t)),this}stop(e){const t=this.toSeconds(e);return this.log("stop",t),this._state.cancel(t),this._state.setStateAtTime("stopped",t),this._tickSource.stop(t),t<this._lastUpdate&&this.emit("stop",t),this}pause(e){const t=this.toSeconds(e);return this._state.getValueAtTime(t)==="started"&&(this._state.setStateAtTime("paused",t),this._tickSource.pause(t),t<this._lastUpdate&&this.emit("pause",t)),this}get ticks(){return Math.ceil(this.getTicksAtTime(this.now()))}set ticks(e){this._tickSource.ticks=e}get seconds(){return this._tickSource.seconds}set seconds(e){this._tickSource.seconds=e}getSecondsAtTime(e){return this._tickSource.getSecondsAtTime(e)}setTicksAtTime(e,t){return this._tickSource.setTicksAtTime(e,t),this}getTimeOfTick(e,t=this.now()){return this._tickSource.getTimeOfTick(e,t)}getTicksAtTime(e){return this._tickSource.getTicksAtTime(e)}nextTickTime(e,t){const n=this.toSeconds(t),s=this.getTicksAtTime(n);return this._tickSource.getTimeOfTick(s+e,n)}_loop(){const e=this._lastUpdate,t=this.now();this._lastUpdate=t,this.log("loop",e,t),e!==t&&(this._state.forEachBetween(e,t,n=>{switch(n.state){case"started":const s=this._tickSource.getTicksAtTime(n.time);this.emit("start",n.time,s);break;case"stopped":n.time!==0&&this.emit("stop",n.time);break;case"paused":this.emit("pause",n.time);break}}),this._tickSource.forEachTickBetween(e,t,(n,s)=>{this.callback(n,s)}))}getStateAtTime(e){const t=this.toSeconds(e);return this._state.getValueAtTime(t)}dispose(){return super.dispose(),this.context.off("tick",this._boundLoop),this._tickSource.dispose(),this._state.dispose(),this}}on.mixin(jn);class Bt extends j{constructor(){const e=F(Bt.getDefaults(),arguments,["volume"]);super(e),this.name="Volume",this.input=this.output=new re({context:this.context,gain:e.volume,units:"decibels"}),this.volume=this.output.gain,te(this,"volume"),this._unmutedVolume=e.volume,this.mute=e.mute}static getDefaults(){return Object.assign(j.getDefaults(),{mute:!1,volume:0})}get mute(){return this.volume.value===-1/0}set mute(e){!this.mute&&e?(this._unmutedVolume=this.volume.value,this.volume.value=-1/0):this.mute&&!e&&(this.volume.value=this._unmutedVolume)}dispose(){return super.dispose(),this.input.dispose(),this.volume.dispose(),this}}class Vs extends j{constructor(){const e=F(Vs.getDefaults(),arguments);super(e),this.name="Destination",this.input=new Bt({context:this.context}),this.output=new re({context:this.context}),this.volume=this.input.volume,ls(this.input,this.output,this.context.rawContext.destination),this.mute=e.mute,this._internalChannels=[this.input,this.context.rawContext.destination,this.output]}static getDefaults(){return Object.assign(j.getDefaults(),{mute:!1,volume:0})}get mute(){return this.input.mute}set mute(e){this.input.mute=e}chain(...e){return this.input.disconnect(),e.unshift(this.input),e.push(this.output),ls(...e),this}get maxChannelCount(){return this.context.rawContext.destination.maxChannelCount}dispose(){return super.dispose(),this.volume.dispose(),this}}Fn(r=>{r.destination=new Vs({context:r})});Ln(r=>{r.destination.dispose()});class hh extends j{constructor(){super(...arguments),this.name="Listener",this.positionX=new se({context:this.context,param:this.context.rawContext.listener.positionX}),this.positionY=new se({context:this.context,param:this.context.rawContext.listener.positionY}),this.positionZ=new se({context:this.context,param:this.context.rawContext.listener.positionZ}),this.forwardX=new se({context:this.context,param:this.context.rawContext.listener.forwardX}),this.forwardY=new se({context:this.context,param:this.context.rawContext.listener.forwardY}),this.forwardZ=new se({context:this.context,param:this.context.rawContext.listener.forwardZ}),this.upX=new se({context:this.context,param:this.context.rawContext.listener.upX}),this.upY=new se({context:this.context,param:this.context.rawContext.listener.upY}),this.upZ=new se({context:this.context,param:this.context.rawContext.listener.upZ})}static getDefaults(){return Object.assign(j.getDefaults(),{positionX:0,positionY:0,positionZ:0,forwardX:0,forwardY:0,forwardZ:-1,upX:0,upY:1,upZ:0})}dispose(){return super.dispose(),this.positionX.dispose(),this.positionY.dispose(),this.positionZ.dispose(),this.forwardX.dispose(),this.forwardY.dispose(),this.forwardZ.dispose(),this.upX.dispose(),this.upY.dispose(),this.upZ.dispose(),this}}Fn(r=>{r.listener=new hh({context:r})});Ln(r=>{r.listener.dispose()});class Fs extends Je{constructor(){super(),this.name="ToneAudioBuffers",this._buffers=new Map,this._loadingCount=0;const e=F(Fs.getDefaults(),arguments,["urls","onload","baseUrl"],"urls");this.baseUrl=e.baseUrl,Object.keys(e.urls).forEach(t=>{this._loadingCount++;const n=e.urls[t];this.add(t,n,this._bufferLoaded.bind(this,e.onload),e.onerror)})}static getDefaults(){return{baseUrl:"",onerror:$,onload:$,urls:{}}}has(e){return this._buffers.has(e.toString())}get(e){return L(this.has(e),`ToneAudioBuffers has no buffer named: ${e}`),this._buffers.get(e.toString())}_bufferLoaded(e){this._loadingCount--,this._loadingCount===0&&e&&e()}get loaded(){return Array.from(this._buffers).every(([e,t])=>t.loaded)}add(e,t,n=$,s=$){return Ye(t)?(this.baseUrl&&t.trim().substring(0,11).toLowerCase()==="data:audio/"&&(this.baseUrl=""),this._buffers.set(e.toString(),new Y(this.baseUrl+t,n,s))):this._buffers.set(e.toString(),new Y(t,n,s)),this}dispose(){return super.dispose(),this._buffers.forEach(e=>e.dispose()),this._buffers.clear(),this}}class kn extends Oe{constructor(){super(...arguments),this.name="MidiClass",this.defaultUnits="midi"}_frequencyToUnits(e){return ft(super._frequencyToUnits(e))}_ticksToUnits(e){return ft(super._ticksToUnits(e))}_beatsToUnits(e){return ft(super._beatsToUnits(e))}_secondsToUnits(e){return ft(super._secondsToUnits(e))}toMidi(){return this.valueOf()}toFrequency(){return Ai(this.toMidi())}transpose(e){return new kn(this.context,this.toMidi()+e)}}class Me extends $t{constructor(){super(...arguments),this.name="Ticks",this.defaultUnits="i"}_now(){return this.context.transport.ticks}_beatsToUnits(e){return this._getPPQ()*e}_secondsToUnits(e){return Math.floor(e/(60/this._getBpm())*this._getPPQ())}_ticksToUnits(e){return e}toTicks(){return this.valueOf()}toSeconds(){return this.valueOf()/this._getPPQ()*(60/this._getBpm())}}class dh extends _e{constructor(){super(...arguments),this.name="Draw",this.expiration=.25,this.anticipation=.008,this._events=new Re,this._boundDrawLoop=this._drawLoop.bind(this),this._animationFrame=-1}schedule(e,t){return this._events.add({callback:e,time:this.toSeconds(t)}),this._events.length===1&&(this._animationFrame=requestAnimationFrame(this._boundDrawLoop)),this}cancel(e){return this._events.cancel(this.toSeconds(e)),this}_drawLoop(){const e=this.context.currentTime;this._events.forEachBefore(e+this.anticipation,t=>{e-t.time<=this.expiration&&t.callback(),this._events.remove(t)}),this._events.length>0&&(this._animationFrame=requestAnimationFrame(this._boundDrawLoop))}dispose(){return super.dispose(),this._events.dispose(),cancelAnimationFrame(this._animationFrame),this}}Fn(r=>{r.draw=new dh({context:r})});Ln(r=>{r.draw.dispose()});class fh extends Je{constructor(){super(...arguments),this.name="IntervalTimeline",this._root=null,this._length=0}add(e){L(H(e.time),"Events must have a time property"),L(H(e.duration),"Events must have a duration parameter"),e.time=e.time.valueOf();let t=new ph(e.time,e.time+e.duration,e);for(this._root===null?this._root=t:this._root.insert(t),this._length++;t!==null;)t.updateHeight(),t.updateMax(),this._rebalance(t),t=t.parent;return this}remove(e){if(this._root!==null){const t=[];this._root.search(e.time,t);for(const n of t)if(n.event===e){this._removeNode(n),this._length--;break}}return this}get length(){return this._length}cancel(e){return this.forEachFrom(e,t=>this.remove(t)),this}_setRoot(e){this._root=e,this._root!==null&&(this._root.parent=null)}_replaceNodeInParent(e,t){e.parent!==null?(e.isLeftChild()?e.parent.left=t:e.parent.right=t,this._rebalance(e.parent)):this._setRoot(t)}_removeNode(e){if(e.left===null&&e.right===null)this._replaceNodeInParent(e,null);else if(e.right===null)this._replaceNodeInParent(e,e.left);else if(e.left===null)this._replaceNodeInParent(e,e.right);else{const t=e.getBalance();let n,s=null;if(t>0)if(e.left.right===null)n=e.left,n.right=e.right,s=n;else{for(n=e.left.right;n.right!==null;)n=n.right;n.parent&&(n.parent.right=n.left,s=n.parent,n.left=e.left,n.right=e.right)}else if(e.right.left===null)n=e.right,n.left=e.left,s=n;else{for(n=e.right.left;n.left!==null;)n=n.left;n.parent&&(n.parent.left=n.right,s=n.parent,n.left=e.left,n.right=e.right)}e.parent!==null?e.isLeftChild()?e.parent.left=n:e.parent.right=n:this._setRoot(n),s&&this._rebalance(s)}e.dispose()}_rotateLeft(e){const t=e.parent,n=e.isLeftChild(),s=e.right;s&&(e.right=s.left,s.left=e),t!==null?n?t.left=s:t.right=s:this._setRoot(s)}_rotateRight(e){const t=e.parent,n=e.isLeftChild(),s=e.left;s&&(e.left=s.right,s.right=e),t!==null?n?t.left=s:t.right=s:this._setRoot(s)}_rebalance(e){const t=e.getBalance();t>1&&e.left?e.left.getBalance()<0?this._rotateLeft(e.left):this._rotateRight(e):t<-1&&e.right&&(e.right.getBalance()>0?this._rotateRight(e.right):this._rotateLeft(e))}get(e){if(this._root!==null){const t=[];if(this._root.search(e,t),t.length>0){let n=t[0];for(let s=1;s<t.length;s++)t[s].low>n.low&&(n=t[s]);return n.event}}return null}forEach(e){if(this._root!==null){const t=[];this._root.traverse(n=>t.push(n)),t.forEach(n=>{n.event&&e(n.event)})}return this}forEachAtTime(e,t){if(this._root!==null){const n=[];this._root.search(e,n),n.forEach(s=>{s.event&&t(s.event)})}return this}forEachFrom(e,t){if(this._root!==null){const n=[];this._root.searchAfter(e,n),n.forEach(s=>{s.event&&t(s.event)})}return this}dispose(){return super.dispose(),this._root!==null&&this._root.traverse(e=>e.dispose()),this._root=null,this}}class ph{constructor(e,t,n){this._left=null,this._right=null,this.parent=null,this.height=0,this.event=n,this.low=e,this.high=t,this.max=this.high}insert(e){e.low<=this.low?this.left===null?this.left=e:this.left.insert(e):this.right===null?this.right=e:this.right.insert(e)}search(e,t){e>this.max||(this.left!==null&&this.left.search(e,t),this.low<=e&&this.high>e&&t.push(this),!(this.low>e)&&this.right!==null&&this.right.search(e,t))}searchAfter(e,t){this.low>=e&&(t.push(this),this.left!==null&&this.left.searchAfter(e,t)),this.right!==null&&this.right.searchAfter(e,t)}traverse(e){e(this),this.left!==null&&this.left.traverse(e),this.right!==null&&this.right.traverse(e)}updateHeight(){this.left!==null&&this.right!==null?this.height=Math.max(this.left.height,this.right.height)+1:this.right!==null?this.height=this.right.height+1:this.left!==null?this.height=this.left.height+1:this.height=0}updateMax(){this.max=this.high,this.left!==null&&(this.max=Math.max(this.max,this.left.max)),this.right!==null&&(this.max=Math.max(this.max,this.right.max))}getBalance(){let e=0;return this.left!==null&&this.right!==null?e=this.left.height-this.right.height:this.left!==null?e=this.left.height+1:this.right!==null&&(e=-(this.right.height+1)),e}isLeftChild(){return this.parent!==null&&this.parent.left===this}get left(){return this._left}set left(e){this._left=e,e!==null&&(e.parent=this),this.updateHeight(),this.updateMax()}get right(){return this._right}set right(e){this._right=e,e!==null&&(e.parent=this),this.updateHeight(),this.updateMax()}dispose(){this.parent=null,this._left=null,this._right=null,this.event=null}}class _h extends Je{constructor(e){super(),this.name="TimelineValue",this._timeline=new Re({memory:10}),this._initialValue=e}set(e,t){return this._timeline.add({value:e,time:t}),this}get(e){const t=this._timeline.get(e);return t?t.value:this._initialValue}}class yt extends j{constructor(){super(F(yt.getDefaults(),arguments,["context"]))}connect(e,t=0,n=0){return Es(this,e,t,n),this}}class jt extends yt{constructor(){const e=F(jt.getDefaults(),arguments,["mapping","length"]);super(e),this.name="WaveShaper",this._shaper=this.context.createWaveShaper(),this.input=this._shaper,this.output=this._shaper,Ne(e.mapping)||e.mapping instanceof Float32Array?this.curve=Float32Array.from(e.mapping):jl(e.mapping)&&this.setMap(e.mapping,e.length)}static getDefaults(){return Object.assign(fe.getDefaults(),{length:1024})}setMap(e,t=1024){const n=new Float32Array(t);for(let s=0,o=t;s<o;s++){const c=s/(o-1)*2-1;n[s]=e(c,s)}return this.curve=n,this}get curve(){return this._shaper.curve}set curve(e){this._shaper.curve=e}get oversample(){return this._shaper.oversample}set oversample(e){const t=["none","2x","4x"].some(n=>n.includes(e));L(t,"oversampling must be either 'none', '2x', or '4x'"),this._shaper.oversample=e}dispose(){return super.dispose(),this._shaper.disconnect(),this}}class Ls extends yt{constructor(){const e=F(Ls.getDefaults(),arguments,["value"]);super(e),this.name="Pow",this._exponentScaler=this.input=this.output=new jt({context:this.context,mapping:this._expFunc(e.value),length:8192}),this._exponent=e.value}static getDefaults(){return Object.assign(yt.getDefaults(),{value:1})}_expFunc(e){return t=>Math.pow(Math.abs(t),e)}get value(){return this._exponent}set value(e){this._exponent=e,this._exponentScaler.setMap(this._expFunc(this._exponent))}dispose(){return super.dispose(),this._exponentScaler.dispose(),this}}class it{constructor(e,t){this.id=it._eventId++,this._remainderTime=0;const n=Object.assign(it.getDefaults(),t);this.transport=e,this.callback=n.callback,this._once=n.once,this.time=Math.floor(n.time),this._remainderTime=n.time-this.time}static getDefaults(){return{callback:$,once:!1,time:0}}get floatTime(){return this.time+this._remainderTime}invoke(e){if(this.callback){const t=this.transport.bpm.getDurationOfTicks(1,e);this.callback(e+this._remainderTime*t),this._once&&this.transport.clear(this.id)}}dispose(){return this.callback=void 0,this}}it._eventId=0;class Ws extends it{constructor(e,t){super(e,t),this._currentId=-1,this._nextId=-1,this._nextTick=this.time,this._boundRestart=this._restart.bind(this);const n=Object.assign(Ws.getDefaults(),t);this.duration=n.duration,this._interval=n.interval,this._nextTick=n.time,this.transport.on("start",this._boundRestart),this.transport.on("loopStart",this._boundRestart),this.transport.on("ticks",this._boundRestart),this.context=this.transport.context,this._restart()}static getDefaults(){return Object.assign({},it.getDefaults(),{duration:1/0,interval:1,once:!1})}invoke(e){this._createEvents(e),super.invoke(e)}_createEvent(){return xn(this._nextTick,this.floatTime+this.duration)?this.transport.scheduleOnce(this.invoke.bind(this),new Me(this.context,this._nextTick).toSeconds()):-1}_createEvents(e){xn(this._nextTick+this._interval,this.floatTime+this.duration)&&(this._nextTick+=this._interval,this._currentId=this._nextId,this._nextId=this.transport.scheduleOnce(this.invoke.bind(this),new Me(this.context,this._nextTick).toSeconds()))}_restart(e){this.transport.clear(this._currentId),this.transport.clear(this._nextId),this._nextTick=this.floatTime;const t=this.transport.getTicksAtTime(e);Dt(t,this.time)&&(this._nextTick=this.floatTime+Math.ceil((t-this.floatTime)/this._interval)*this._interval),this._currentId=this._createEvent(),this._nextTick+=this._interval,this._nextId=this._createEvent()}dispose(){return super.dispose(),this.transport.clear(this._currentId),this.transport.clear(this._nextId),this.transport.off("start",this._boundRestart),this.transport.off("loopStart",this._boundRestart),this.transport.off("ticks",this._boundRestart),this}}class Gn extends _e{constructor(){const e=F(Gn.getDefaults(),arguments);super(e),this.name="Transport",this._loop=new _h(!1),this._loopStart=0,this._loopEnd=0,this._scheduledEvents={},this._timeline=new Re,this._repeatedEvents=new fh,this._syncedSignals=[],this._swingAmount=0,this._ppq=e.ppq,this._clock=new jn({callback:this._processTick.bind(this),context:this.context,frequency:0,units:"bpm"}),this._bindClockEvents(),this.bpm=this._clock.frequency,this._clock.frequency.multiplier=e.ppq,this.bpm.setValueAtTime(e.bpm,0),te(this,"bpm"),this._timeSignature=e.timeSignature,this._swingTicks=e.ppq/2}static getDefaults(){return Object.assign(_e.getDefaults(),{bpm:120,loopEnd:"4m",loopStart:0,ppq:192,swing:0,swingSubdivision:"8n",timeSignature:4})}_processTick(e,t){if(this._loop.get(e)&&t>=this._loopEnd&&(this.emit("loopEnd",e),this._clock.setTicksAtTime(this._loopStart,e),t=this._loopStart,this.emit("loopStart",e,this._clock.getSecondsAtTime(e)),this.emit("loop",e)),this._swingAmount>0&&t%this._ppq!==0&&t%(this._swingTicks*2)!==0){const n=t%(this._swingTicks*2)/(this._swingTicks*2),s=Math.sin(n*Math.PI)*this._swingAmount;e+=new Me(this.context,this._swingTicks*2/3).toSeconds()*s}dr(!0),this._timeline.forEachAtTime(t,n=>n.invoke(e)),dr(!1)}schedule(e,t){const n=new it(this,{callback:e,time:new $t(this.context,t).toTicks()});return this._addEvent(n,this._timeline)}scheduleRepeat(e,t,n,s=1/0){const o=new Ws(this,{callback:e,duration:new Fe(this.context,s).toTicks(),interval:new Fe(this.context,t).toTicks(),time:new $t(this.context,n).toTicks()});return this._addEvent(o,this._repeatedEvents)}scheduleOnce(e,t){const n=new it(this,{callback:e,once:!0,time:new $t(this.context,t).toTicks()});return this._addEvent(n,this._timeline)}clear(e){if(this._scheduledEvents.hasOwnProperty(e)){const t=this._scheduledEvents[e.toString()];t.timeline.remove(t.event),t.event.dispose(),delete this._scheduledEvents[e.toString()]}return this}_addEvent(e,t){return this._scheduledEvents[e.id.toString()]={event:e,timeline:t},t.add(e),e.id}cancel(e=0){const t=this.toTicks(e);return this._timeline.forEachFrom(t,n=>this.clear(n.id)),this._repeatedEvents.forEachFrom(t,n=>this.clear(n.id)),this}_bindClockEvents(){this._clock.on("start",(e,t)=>{t=new Me(this.context,t).toSeconds(),this.emit("start",e,t)}),this._clock.on("stop",e=>{this.emit("stop",e)}),this._clock.on("pause",e=>{this.emit("pause",e)})}get state(){return this._clock.getStateAtTime(this.now())}start(e,t){this.context.resume();let n;return H(t)&&(n=this.toTicks(t)),this._clock.start(e,n),this}stop(e){return this._clock.stop(e),this}pause(e){return this._clock.pause(e),this}toggle(e){return e=this.toSeconds(e),this._clock.getStateAtTime(e)!=="started"?this.start(e):this.stop(e),this}get timeSignature(){return this._timeSignature}set timeSignature(e){Ne(e)&&(e=e[0]/e[1]*4),this._timeSignature=e}get loopStart(){return new Fe(this.context,this._loopStart,"i").toSeconds()}set loopStart(e){this._loopStart=this.toTicks(e)}get loopEnd(){return new Fe(this.context,this._loopEnd,"i").toSeconds()}set loopEnd(e){this._loopEnd=this.toTicks(e)}get loop(){return this._loop.get(this.now())}set loop(e){this._loop.set(e,this.now())}setLoopPoints(e,t){return this.loopStart=e,this.loopEnd=t,this}get swing(){return this._swingAmount}set swing(e){this._swingAmount=e}get swingSubdivision(){return new Me(this.context,this._swingTicks).toNotation()}set swingSubdivision(e){this._swingTicks=this.toTicks(e)}get position(){const e=this.now(),t=this._clock.getTicksAtTime(e);return new Me(this.context,t).toBarsBeatsSixteenths()}set position(e){const t=this.toTicks(e);this.ticks=t}get seconds(){return this._clock.seconds}set seconds(e){const t=this.now(),n=this._clock.frequency.timeToTicks(e,t);this.ticks=n}get progress(){if(this.loop){const e=this.now();return(this._clock.getTicksAtTime(e)-this._loopStart)/(this._loopEnd-this._loopStart)}else return 0}get ticks(){return this._clock.ticks}set ticks(e){if(this._clock.ticks!==e){const t=this.now();if(this.state==="started"){const n=this._clock.getTicksAtTime(t),s=this._clock.frequency.getDurationOfTicks(Math.ceil(n)-n,t),o=t+s;this.emit("stop",o),this._clock.setTicksAtTime(e,o),this.emit("start",o,this._clock.getSecondsAtTime(o))}else this.emit("ticks",t),this._clock.setTicksAtTime(e,t)}}getTicksAtTime(e){return this._clock.getTicksAtTime(e)}getSecondsAtTime(e){return this._clock.getSecondsAtTime(e)}get PPQ(){return this._clock.frequency.multiplier}set PPQ(e){this._clock.frequency.multiplier=e}nextSubdivision(e){if(e=this.toTicks(e),this.state!=="started")return 0;{const t=this.now(),n=this.getTicksAtTime(t),s=e-n%e;return this._clock.nextTickTime(s,t)}}syncSignal(e,t){const n=this.now();let s=this.bpm,o=1/(60/s.getValueAtTime(n)/this.PPQ),c=[];if(e.units==="time"){const l=.015625/o,h=new re(l),p=new Ls(-1),i=new re(l);s.chain(h,p,i),s=i,o=1/o,c=[h,p,i]}t||(e.getValueAtTime(n)!==0?t=e.getValueAtTime(n)/o:t=0);const u=new re(t);return s.connect(u),u.connect(e._param),c.push(u),this._syncedSignals.push({initial:e.value,nodes:c,signal:e}),e.value=0,this}unsyncSignal(e){for(let t=this._syncedSignals.length-1;t>=0;t--){const n=this._syncedSignals[t];n.signal===e&&(n.nodes.forEach(s=>s.dispose()),n.signal.value=n.initial,this._syncedSignals.splice(t,1))}return this}dispose(){return super.dispose(),this._clock.dispose(),yi(this,"bpm"),this._timeline.dispose(),this._repeatedEvents.dispose(),this}}on.mixin(Gn);Fn(r=>{r.transport=new Gn({context:r})});Ln(r=>{r.transport.dispose()});class we extends j{constructor(e){super(e),this.input=void 0,this._state=new Bn("stopped"),this._synced=!1,this._scheduled=[],this._syncedStart=$,this._syncedStop=$,this._state.memory=100,this._state.increasing=!0,this._volume=this.output=new Bt({context:this.context,mute:e.mute,volume:e.volume}),this.volume=this._volume.volume,te(this,"volume"),this.onstop=e.onstop}static getDefaults(){return Object.assign(j.getDefaults(),{mute:!1,onstop:$,volume:0})}get state(){return this._synced?this.context.transport.state==="started"?this._state.getValueAtTime(this.context.transport.seconds):"stopped":this._state.getValueAtTime(this.now())}get mute(){return this._volume.mute}set mute(e){this._volume.mute=e}_clampToCurrentTime(e){return this._synced?e:Math.max(e,this.context.currentTime)}start(e,t,n){let s=De(e)&&this._synced?this.context.transport.seconds:this.toSeconds(e);if(s=this._clampToCurrentTime(s),!this._synced&&this._state.getValueAtTime(s)==="started")L(Dt(s,this._state.get(s).time),"Start time must be strictly greater than previous start time"),this._state.cancel(s),this._state.setStateAtTime("started",s),this.log("restart",s),this.restart(s,t,n);else if(this.log("start",s),this._state.setStateAtTime("started",s),this._synced){const o=this._state.get(s);o&&(o.offset=this.toSeconds(mt(t,0)),o.duration=n?this.toSeconds(n):void 0);const c=this.context.transport.schedule(u=>{this._start(u,t,n)},s);this._scheduled.push(c),this.context.transport.state==="started"&&this.context.transport.getSecondsAtTime(this.immediate())>s&&this._syncedStart(this.now(),this.context.transport.seconds)}else fi(this.context),this._start(s,t,n);return this}stop(e){let t=De(e)&&this._synced?this.context.transport.seconds:this.toSeconds(e);if(t=this._clampToCurrentTime(t),this._state.getValueAtTime(t)==="started"||H(this._state.getNextState("started",t))){if(this.log("stop",t),!this._synced)this._stop(t);else{const n=this.context.transport.schedule(this._stop.bind(this),t);this._scheduled.push(n)}this._state.cancel(t),this._state.setStateAtTime("stopped",t)}return this}restart(e,t,n){return e=this.toSeconds(e),this._state.getValueAtTime(e)==="started"&&(this._state.cancel(e),this._restart(e,t,n)),this}sync(){return this._synced||(this._synced=!0,this._syncedStart=(e,t)=>{if(Dt(t,0)){const n=this._state.get(t);if(n&&n.state==="started"&&n.time!==t){const s=t-this.toSeconds(n.time);let o;n.duration&&(o=this.toSeconds(n.duration)-s),this._start(e,this.toSeconds(n.offset)+s,o)}}},this._syncedStop=e=>{const t=this.context.transport.getSecondsAtTime(Math.max(e-this.sampleTime,0));this._state.getValueAtTime(t)==="started"&&this._stop(e)},this.context.transport.on("start",this._syncedStart),this.context.transport.on("loopStart",this._syncedStart),this.context.transport.on("stop",this._syncedStop),this.context.transport.on("pause",this._syncedStop),this.context.transport.on("loopEnd",this._syncedStop)),this}unsync(){return this._synced&&(this.context.transport.off("stop",this._syncedStop),this.context.transport.off("pause",this._syncedStop),this.context.transport.off("loopEnd",this._syncedStop),this.context.transport.off("start",this._syncedStart),this.context.transport.off("loopStart",this._syncedStart)),this._synced=!1,this._scheduled.forEach(e=>this.context.transport.clear(e)),this._scheduled=[],this._state.cancel(0),this._stop(0),this}dispose(){return super.dispose(),this.onstop=$,this.unsync(),this._volume.dispose(),this._state.dispose(),this}}class cn extends Rt{constructor(){const e=F(cn.getDefaults(),arguments,["url","onload"]);super(e),this.name="ToneBufferSource",this._source=this.context.createBufferSource(),this._internalChannels=[this._source],this._sourceStarted=!1,this._sourceStopped=!1,rt(this._source,this._gainNode),this._source.onended=()=>this._stopSource(),this.playbackRate=new se({context:this.context,param:this._source.playbackRate,units:"positive",value:e.playbackRate}),this.loop=e.loop,this.loopStart=e.loopStart,this.loopEnd=e.loopEnd,this._buffer=new Y(e.url,e.onload,e.onerror),this._internalChannels.push(this._source)}static getDefaults(){return Object.assign(Rt.getDefaults(),{url:new Y,loop:!1,loopEnd:0,loopStart:0,onload:$,onerror:$,playbackRate:1})}get fadeIn(){return this._fadeIn}set fadeIn(e){this._fadeIn=e}get fadeOut(){return this._fadeOut}set fadeOut(e){this._fadeOut=e}get curve(){return this._curve}set curve(e){this._curve=e}start(e,t,n,s=1){L(this.buffer.loaded,"buffer is either not set or not loaded");const o=this.toSeconds(e);this._startGain(o,s),this.loop?t=mt(t,this.loopStart):t=mt(t,0);let c=Math.max(this.toSeconds(t),0);if(this.loop){const u=this.toSeconds(this.loopEnd)||this.buffer.duration,l=this.toSeconds(this.loopStart),h=u-l;us(c,u)&&(c=(c-l)%h+l),Pe(c,this.buffer.duration)&&(c=0)}if(this._source.buffer=this.buffer.get(),this._source.loopEnd=this.toSeconds(this.loopEnd)||this.buffer.duration,xn(c,this.buffer.duration)&&(this._sourceStarted=!0,this._source.start(o,c)),H(n)){let u=this.toSeconds(n);u=Math.max(u,0),this.stop(o+u)}return this}_stopSource(e){!this._sourceStopped&&this._sourceStarted&&(this._sourceStopped=!0,this._source.stop(this.toSeconds(e)),this._onended())}get loopStart(){return this._source.loopStart}set loopStart(e){this._source.loopStart=this.toSeconds(e)}get loopEnd(){return this._source.loopEnd}set loopEnd(e){this._source.loopEnd=this.toSeconds(e)}get buffer(){return this._buffer}set buffer(e){this._buffer.set(e)}get loop(){return this._source.loop}set loop(e){this._source.loop=e,this._sourceStarted&&this.cancelStop()}dispose(){return super.dispose(),this._source.onended=null,this._source.disconnect(),this._buffer.dispose(),this.playbackRate.dispose(),this}}class Cn extends we{constructor(){const e=F(Cn.getDefaults(),arguments,["type"]);super(e),this.name="Noise",this._source=null,this._playbackRate=e.playbackRate,this.type=e.type,this._fadeIn=e.fadeIn,this._fadeOut=e.fadeOut}static getDefaults(){return Object.assign(we.getDefaults(),{fadeIn:0,fadeOut:0,playbackRate:1,type:"white"})}get type(){return this._type}set type(e){if(L(e in pr,"Noise: invalid type: "+e),this._type!==e&&(this._type=e,this.state==="started")){const t=this.now();this._stop(t),this._start(t)}}get playbackRate(){return this._playbackRate}set playbackRate(e){this._playbackRate=e,this._source&&(this._source.playbackRate.value=e)}_start(e){const t=pr[this._type];this._source=new cn({url:t,context:this.context,fadeIn:this._fadeIn,fadeOut:this._fadeOut,loop:!0,onended:()=>this.onstop(this),playbackRate:this._playbackRate}).connect(this.output),this._source.start(this.toSeconds(e),Math.random()*(t.duration-.001))}_stop(e){this._source&&(this._source.stop(this.toSeconds(e)),this._source=null)}get fadeIn(){return this._fadeIn}set fadeIn(e){this._fadeIn=e,this._source&&(this._source.fadeIn=this._fadeIn)}get fadeOut(){return this._fadeOut}set fadeOut(e){this._fadeOut=e,this._source&&(this._source.fadeOut=this._fadeOut)}_restart(e){this._stop(e),this._start(e)}dispose(){return super.dispose(),this._source&&this._source.disconnect(),this}}const Ct=44100*5,Kn=2,Xe={brown:null,pink:null,white:null},pr={get brown(){if(!Xe.brown){const r=[];for(let e=0;e<Kn;e++){const t=new Float32Array(Ct);r[e]=t;let n=0;for(let s=0;s<Ct;s++){const o=Math.random()*2-1;t[s]=(n+.02*o)/1.02,n=t[s],t[s]*=3.5}}Xe.brown=new Y().fromArray(r)}return Xe.brown},get pink(){if(!Xe.pink){const r=[];for(let e=0;e<Kn;e++){const t=new Float32Array(Ct);r[e]=t;let n,s,o,c,u,l,h;n=s=o=c=u=l=h=0;for(let p=0;p<Ct;p++){const i=Math.random()*2-1;n=.99886*n+i*.0555179,s=.99332*s+i*.0750759,o=.969*o+i*.153852,c=.8665*c+i*.3104856,u=.55*u+i*.5329522,l=-.7616*l-i*.016898,t[p]=n+s+o+c+u+l+h+i*.5362,t[p]*=.11,h=i*.115926}}Xe.pink=new Y().fromArray(r)}return Xe.pink},get white(){if(!Xe.white){const r=[];for(let e=0;e<Kn;e++){const t=new Float32Array(Ct);r[e]=t;for(let n=0;n<Ct;n++)t[n]=Math.random()*2-1}Xe.white=new Y().fromArray(r)}return Xe.white}};function St(r,e){return ae(this,void 0,void 0,function*(){const t=e/r.context.sampleRate,n=new Wn(1,t,r.context.sampleRate);return new r.constructor(Object.assign(r.get(),{frequency:2/t,detune:0,context:n})).toDestination().start(0),(yield n.render()).getChannelData(0)})}class qs extends Rt{constructor(){const e=F(qs.getDefaults(),arguments,["frequency","type"]);super(e),this.name="ToneOscillatorNode",this._oscillator=this.context.createOscillator(),this._internalChannels=[this._oscillator],rt(this._oscillator,this._gainNode),this.type=e.type,this.frequency=new se({context:this.context,param:this._oscillator.frequency,units:"frequency",value:e.frequency}),this.detune=new se({context:this.context,param:this._oscillator.detune,units:"cents",value:e.detune}),te(this,["frequency","detune"])}static getDefaults(){return Object.assign(Rt.getDefaults(),{detune:0,frequency:440,type:"sine"})}start(e){const t=this.toSeconds(e);return this.log("start",t),this._startGain(t),this._oscillator.start(t),this}_stopSource(e){this._oscillator.stop(e)}setPeriodicWave(e){return this._oscillator.setPeriodicWave(e),this}get type(){return this._oscillator.type}set type(e){this._oscillator.type=e}dispose(){return super.dispose(),this.state==="started"&&this.stop(),this._oscillator.disconnect(),this.frequency.dispose(),this.detune.dispose(),this}}class le extends we{constructor(){const e=F(le.getDefaults(),arguments,["frequency","type"]);super(e),this.name="Oscillator",this._oscillator=null,this.frequency=new fe({context:this.context,units:"frequency",value:e.frequency}),te(this,"frequency"),this.detune=new fe({context:this.context,units:"cents",value:e.detune}),te(this,"detune"),this._partials=e.partials,this._partialCount=e.partialCount,this._type=e.type,e.partialCount&&e.type!=="custom"&&(this._type=this.baseType+e.partialCount.toString()),this.phase=e.phase}static getDefaults(){return Object.assign(we.getDefaults(),{detune:0,frequency:440,partialCount:0,partials:[],phase:0,type:"sine"})}_start(e){const t=this.toSeconds(e),n=new qs({context:this.context,onended:()=>this.onstop(this)});this._oscillator=n,this._wave?this._oscillator.setPeriodicWave(this._wave):this._oscillator.type=this._type,this._oscillator.connect(this.output),this.frequency.connect(this._oscillator.frequency),this.detune.connect(this._oscillator.detune),this._oscillator.start(t)}_stop(e){const t=this.toSeconds(e);this._oscillator&&this._oscillator.stop(t)}_restart(e){const t=this.toSeconds(e);return this.log("restart",t),this._oscillator&&this._oscillator.cancelStop(),this._state.cancel(t),this}syncFrequency(){return this.context.transport.syncSignal(this.frequency),this}unsyncFrequency(){return this.context.transport.unsyncSignal(this.frequency),this}_getCachedPeriodicWave(){if(this._type==="custom")return le._periodicWaveCache.find(t=>t.phase===this._phase&&Jl(t.partials,this._partials));{const e=le._periodicWaveCache.find(t=>t.type===this._type&&t.phase===this._phase);return this._partialCount=e?e.partialCount:this._partialCount,e}}get type(){return this._type}set type(e){this._type=e;const t=["sine","square","sawtooth","triangle"].indexOf(e)!==-1;if(this._phase===0&&t)this._wave=void 0,this._partialCount=0,this._oscillator!==null&&(this._oscillator.type=e);else{const n=this._getCachedPeriodicWave();if(H(n)){const{partials:s,wave:o}=n;this._wave=o,this._partials=s,this._oscillator!==null&&this._oscillator.setPeriodicWave(this._wave)}else{const[s,o]=this._getRealImaginary(e,this._phase),c=this.context.createPeriodicWave(s,o);this._wave=c,this._oscillator!==null&&this._oscillator.setPeriodicWave(this._wave),le._periodicWaveCache.push({imag:o,partialCount:this._partialCount,partials:this._partials,phase:this._phase,real:s,type:this._type,wave:this._wave}),le._periodicWaveCache.length>100&&le._periodicWaveCache.shift()}}}get baseType(){return this._type.replace(this.partialCount.toString(),"")}set baseType(e){this.partialCount&&this._type!=="custom"&&e!=="custom"?this.type=e+this.partialCount:this.type=e}get partialCount(){return this._partialCount}set partialCount(e){Ee(e,0);let t=this._type;const n=/^(sine|triangle|square|sawtooth)(\d+)$/.exec(this._type);if(n&&(t=n[1]),this._type!=="custom")e===0?this.type=t:this.type=t+e.toString();else{const s=new Float32Array(e);this._partials.forEach((o,c)=>s[c]=o),this._partials=Array.from(s),this.type=this._type}}_getRealImaginary(e,t){let s=2048;const o=new Float32Array(s),c=new Float32Array(s);let u=1;if(e==="custom"){if(u=this._partials.length+1,this._partialCount=this._partials.length,s=u,this._partials.length===0)return[o,c]}else{const l=/^(sine|triangle|square|sawtooth)(\d+)$/.exec(e);l?(u=parseInt(l[2],10)+1,this._partialCount=parseInt(l[2],10),e=l[1],u=Math.max(u,2),s=u):this._partialCount=0,this._partials=[]}for(let l=1;l<s;++l){const h=2/(l*Math.PI);let p;switch(e){case"sine":p=l<=u?1:0,this._partials[l-1]=p;break;case"square":p=l&1?2*h:0,this._partials[l-1]=p;break;case"sawtooth":p=h*(l&1?1:-1),this._partials[l-1]=p;break;case"triangle":l&1?p=2*(h*h)*(l-1>>1&1?-1:1):p=0,this._partials[l-1]=p;break;case"custom":p=this._partials[l-1];break;default:throw new TypeError("Oscillator: invalid type: "+e)}p!==0?(o[l]=-p*Math.sin(t*l),c[l]=p*Math.cos(t*l)):(o[l]=0,c[l]=0)}return[o,c]}_inverseFFT(e,t,n){let s=0;const o=e.length;for(let c=0;c<o;c++)s+=e[c]*Math.cos(c*n)+t[c]*Math.sin(c*n);return s}getInitialValue(){const[e,t]=this._getRealImaginary(this._type,0);let n=0;const s=Math.PI*2,o=32;for(let c=0;c<o;c++)n=Math.max(this._inverseFFT(e,t,c/o*s),n);return At(-this._inverseFFT(e,t,this._phase)/n,-1,1)}get partials(){return this._partials.slice(0,this.partialCount)}set partials(e){this._partials=e,this._partialCount=this._partials.length,e.length&&(this.type="custom")}get phase(){return this._phase*(180/Math.PI)}set phase(e){this._phase=e*Math.PI/180,this.type=this._type}asArray(){return ae(this,arguments,void 0,function*(e=1024){return St(this,e)})}dispose(){return super.dispose(),this._oscillator!==null&&this._oscillator.dispose(),this._wave=void 0,this.frequency.dispose(),this.detune.dispose(),this}}le._periodicWaveCache=[];class mh extends yt{constructor(){super(...arguments),this.name="AudioToGain",this._norm=new jt({context:this.context,mapping:e=>(e+1)/2}),this.input=this._norm,this.output=this._norm}dispose(){return super.dispose(),this._norm.dispose(),this}}class Pt extends fe{constructor(){const e=F(Pt.getDefaults(),arguments,["value"]);super(e),this.name="Multiply",this.override=!1,this._mult=this.input=this.output=new re({context:this.context,minValue:e.minValue,maxValue:e.maxValue}),this.factor=this._param=this._mult.gain,this.factor.setValueAtTime(e.value,0)}static getDefaults(){return Object.assign(fe.getDefaults(),{value:0})}dispose(){return super.dispose(),this._mult.dispose(),this}}class Un extends we{constructor(){const e=F(Un.getDefaults(),arguments,["frequency","type","modulationType"]);super(e),this.name="AMOscillator",this._modulationScale=new mh({context:this.context}),this._modulationNode=new re({context:this.context}),this._carrier=new le({context:this.context,detune:e.detune,frequency:e.frequency,onstop:()=>this.onstop(this),phase:e.phase,type:e.type}),this.frequency=this._carrier.frequency,this.detune=this._carrier.detune,this._modulator=new le({context:this.context,phase:e.phase,type:e.modulationType}),this.harmonicity=new Pt({context:this.context,units:"positive",value:e.harmonicity}),this.frequency.chain(this.harmonicity,this._modulator.frequency),this._modulator.chain(this._modulationScale,this._modulationNode.gain),this._carrier.chain(this._modulationNode,this.output),te(this,["frequency","detune","harmonicity"])}static getDefaults(){return Object.assign(le.getDefaults(),{harmonicity:1,modulationType:"square"})}_start(e){this._modulator.start(e),this._carrier.start(e)}_stop(e){this._modulator.stop(e),this._carrier.stop(e)}_restart(e){this._modulator.restart(e),this._carrier.restart(e)}get type(){return this._carrier.type}set type(e){this._carrier.type=e}get baseType(){return this._carrier.baseType}set baseType(e){this._carrier.baseType=e}get partialCount(){return this._carrier.partialCount}set partialCount(e){this._carrier.partialCount=e}get modulationType(){return this._modulator.type}set modulationType(e){this._modulator.type=e}get phase(){return this._carrier.phase}set phase(e){this._carrier.phase=e,this._modulator.phase=e}get partials(){return this._carrier.partials}set partials(e){this._carrier.partials=e}asArray(){return ae(this,arguments,void 0,function*(e=1024){return St(this,e)})}dispose(){return super.dispose(),this.frequency.dispose(),this.detune.dispose(),this.harmonicity.dispose(),this._carrier.dispose(),this._modulator.dispose(),this._modulationNode.dispose(),this._modulationScale.dispose(),this}}class Hn extends we{constructor(){const e=F(Hn.getDefaults(),arguments,["frequency","type","modulationType"]);super(e),this.name="FMOscillator",this._modulationNode=new re({context:this.context,gain:0}),this._carrier=new le({context:this.context,detune:e.detune,frequency:0,onstop:()=>this.onstop(this),phase:e.phase,type:e.type}),this.detune=this._carrier.detune,this.frequency=new fe({context:this.context,units:"frequency",value:e.frequency}),this._modulator=new le({context:this.context,phase:e.phase,type:e.modulationType}),this.harmonicity=new Pt({context:this.context,units:"positive",value:e.harmonicity}),this.modulationIndex=new Pt({context:this.context,units:"positive",value:e.modulationIndex}),this.frequency.connect(this._carrier.frequency),this.frequency.chain(this.harmonicity,this._modulator.frequency),this.frequency.chain(this.modulationIndex,this._modulationNode),this._modulator.connect(this._modulationNode.gain),this._modulationNode.connect(this._carrier.frequency),this._carrier.connect(this.output),this.detune.connect(this._modulator.detune),te(this,["modulationIndex","frequency","detune","harmonicity"])}static getDefaults(){return Object.assign(le.getDefaults(),{harmonicity:1,modulationIndex:2,modulationType:"square"})}_start(e){this._modulator.start(e),this._carrier.start(e)}_stop(e){this._modulator.stop(e),this._carrier.stop(e)}_restart(e){return this._modulator.restart(e),this._carrier.restart(e),this}get type(){return this._carrier.type}set type(e){this._carrier.type=e}get baseType(){return this._carrier.baseType}set baseType(e){this._carrier.baseType=e}get partialCount(){return this._carrier.partialCount}set partialCount(e){this._carrier.partialCount=e}get modulationType(){return this._modulator.type}set modulationType(e){this._modulator.type=e}get phase(){return this._carrier.phase}set phase(e){this._carrier.phase=e,this._modulator.phase=e}get partials(){return this._carrier.partials}set partials(e){this._carrier.partials=e}asArray(){return ae(this,arguments,void 0,function*(e=1024){return St(this,e)})}dispose(){return super.dispose(),this.frequency.dispose(),this.harmonicity.dispose(),this._carrier.dispose(),this._modulator.dispose(),this._modulationNode.dispose(),this.modulationIndex.dispose(),this}}class un extends we{constructor(){const e=F(un.getDefaults(),arguments,["frequency","width"]);super(e),this.name="PulseOscillator",this._widthGate=new re({context:this.context,gain:0}),this._thresh=new jt({context:this.context,mapping:t=>t<=0?-1:1}),this.width=new fe({context:this.context,units:"audioRange",value:e.width}),this._triangle=new le({context:this.context,detune:e.detune,frequency:e.frequency,onstop:()=>this.onstop(this),phase:e.phase,type:"triangle"}),this.frequency=this._triangle.frequency,this.detune=this._triangle.detune,this._triangle.chain(this._thresh,this.output),this.width.chain(this._widthGate,this._thresh),te(this,["width","frequency","detune"])}static getDefaults(){return Object.assign(we.getDefaults(),{detune:0,frequency:440,phase:0,type:"pulse",width:.2})}_start(e){e=this.toSeconds(e),this._triangle.start(e),this._widthGate.gain.setValueAtTime(1,e)}_stop(e){e=this.toSeconds(e),this._triangle.stop(e),this._widthGate.gain.cancelScheduledValues(e),this._widthGate.gain.setValueAtTime(0,e)}_restart(e){this._triangle.restart(e),this._widthGate.gain.cancelScheduledValues(e),this._widthGate.gain.setValueAtTime(1,e)}get phase(){return this._triangle.phase}set phase(e){this._triangle.phase=e}get type(){return"pulse"}get baseType(){return"pulse"}get partials(){return[]}get partialCount(){return 0}set carrierType(e){this._triangle.type=e}asArray(){return ae(this,arguments,void 0,function*(e=1024){return St(this,e)})}dispose(){return super.dispose(),this._triangle.dispose(),this.width.dispose(),this._widthGate.dispose(),this._thresh.dispose(),this}}class $n extends we{constructor(){const e=F($n.getDefaults(),arguments,["frequency","type","spread"]);super(e),this.name="FatOscillator",this._oscillators=[],this.frequency=new fe({context:this.context,units:"frequency",value:e.frequency}),this.detune=new fe({context:this.context,units:"cents",value:e.detune}),this._spread=e.spread,this._type=e.type,this._phase=e.phase,this._partials=e.partials,this._partialCount=e.partialCount,this.count=e.count,te(this,["frequency","detune"])}static getDefaults(){return Object.assign(le.getDefaults(),{count:3,spread:20,type:"sawtooth"})}_start(e){e=this.toSeconds(e),this._forEach(t=>t.start(e))}_stop(e){e=this.toSeconds(e),this._forEach(t=>t.stop(e))}_restart(e){this._forEach(t=>t.restart(e))}_forEach(e){for(let t=0;t<this._oscillators.length;t++)e(this._oscillators[t],t)}get type(){return this._type}set type(e){this._type=e,this._forEach(t=>t.type=e)}get spread(){return this._spread}set spread(e){if(this._spread=e,this._oscillators.length>1){const t=-e/2,n=e/(this._oscillators.length-1);this._forEach((s,o)=>s.detune.value=t+n*o)}}get count(){return this._oscillators.length}set count(e){if(Ee(e,1),this._oscillators.length!==e){this._forEach(t=>t.dispose()),this._oscillators=[];for(let t=0;t<e;t++){const n=new le({context:this.context,volume:-6-e*1.1,type:this._type,phase:this._phase+t/e*360,partialCount:this._partialCount,onstop:t===0?()=>this.onstop(this):$});this.type==="custom"&&(n.partials=this._partials),this.frequency.connect(n.frequency),this.detune.connect(n.detune),n.detune.overridden=!1,n.connect(this.output),this._oscillators[t]=n}this.spread=this._spread,this.state==="started"&&this._forEach(t=>t.start())}}get phase(){return this._phase}set phase(e){this._phase=e,this._forEach((t,n)=>t.phase=this._phase+n/this.count*360)}get baseType(){return this._oscillators[0].baseType}set baseType(e){this._forEach(t=>t.baseType=e),this._type=this._oscillators[0].type}get partials(){return this._oscillators[0].partials}set partials(e){this._partials=e,this._partialCount=this._partials.length,e.length&&(this._type="custom",this._forEach(t=>t.partials=e))}get partialCount(){return this._oscillators[0].partialCount}set partialCount(e){this._partialCount=e,this._forEach(t=>t.partialCount=e),this._type=this._oscillators[0].type}asArray(){return ae(this,arguments,void 0,function*(e=1024){return St(this,e)})}dispose(){return super.dispose(),this.frequency.dispose(),this.detune.dispose(),this._forEach(e=>e.dispose()),this}}class Xn extends we{constructor(){const e=F(Xn.getDefaults(),arguments,["frequency","modulationFrequency"]);super(e),this.name="PWMOscillator",this.sourceType="pwm",this._scale=new Pt({context:this.context,value:2}),this._pulse=new un({context:this.context,frequency:e.modulationFrequency}),this._pulse.carrierType="sine",this.modulationFrequency=this._pulse.frequency,this._modulator=new le({context:this.context,detune:e.detune,frequency:e.frequency,onstop:()=>this.onstop(this),phase:e.phase}),this.frequency=this._modulator.frequency,this.detune=this._modulator.detune,this._modulator.chain(this._scale,this._pulse.width),this._pulse.connect(this.output),te(this,["modulationFrequency","frequency","detune"])}static getDefaults(){return Object.assign(we.getDefaults(),{detune:0,frequency:440,modulationFrequency:.4,phase:0,type:"pwm"})}_start(e){e=this.toSeconds(e),this._modulator.start(e),this._pulse.start(e)}_stop(e){e=this.toSeconds(e),this._modulator.stop(e),this._pulse.stop(e)}_restart(e){this._modulator.restart(e),this._pulse.restart(e)}get type(){return"pwm"}get baseType(){return"pwm"}get partials(){return[]}get partialCount(){return 0}get phase(){return this._modulator.phase}set phase(e){this._modulator.phase=e}asArray(){return ae(this,arguments,void 0,function*(e=1024){return St(this,e)})}dispose(){return super.dispose(),this._pulse.dispose(),this._scale.dispose(),this._modulator.dispose(),this}}const _r={am:Un,fat:$n,fm:Hn,oscillator:le,pulse:un,pwm:Xn};class On extends we{constructor(){const e=F(On.getDefaults(),arguments,["frequency","type"]);super(e),this.name="OmniOscillator",this.frequency=new fe({context:this.context,units:"frequency",value:e.frequency}),this.detune=new fe({context:this.context,units:"cents",value:e.detune}),te(this,["frequency","detune"]),this.set(e)}static getDefaults(){return Object.assign(le.getDefaults(),Hn.getDefaults(),Un.getDefaults(),$n.getDefaults(),un.getDefaults(),Xn.getDefaults())}_start(e){this._oscillator.start(e)}_stop(e){this._oscillator.stop(e)}_restart(e){return this._oscillator.restart(e),this}get type(){let e="";return["am","fm","fat"].some(t=>this._sourceType===t)&&(e=this._sourceType),e+this._oscillator.type}set type(e){e.substr(0,2)==="fm"?(this._createNewOscillator("fm"),this._oscillator=this._oscillator,this._oscillator.type=e.substr(2)):e.substr(0,2)==="am"?(this._createNewOscillator("am"),this._oscillator=this._oscillator,this._oscillator.type=e.substr(2)):e.substr(0,3)==="fat"?(this._createNewOscillator("fat"),this._oscillator=this._oscillator,this._oscillator.type=e.substr(3)):e==="pwm"?(this._createNewOscillator("pwm"),this._oscillator=this._oscillator):e==="pulse"?this._createNewOscillator("pulse"):(this._createNewOscillator("oscillator"),this._oscillator=this._oscillator,this._oscillator.type=e)}get partials(){return this._oscillator.partials}set partials(e){!this._getOscType(this._oscillator,"pulse")&&!this._getOscType(this._oscillator,"pwm")&&(this._oscillator.partials=e)}get partialCount(){return this._oscillator.partialCount}set partialCount(e){!this._getOscType(this._oscillator,"pulse")&&!this._getOscType(this._oscillator,"pwm")&&(this._oscillator.partialCount=e)}set(e){return Reflect.has(e,"type")&&e.type&&(this.type=e.type),super.set(e),this}_createNewOscillator(e){if(e!==this._sourceType){this._sourceType=e;const t=_r[e],n=this.now();if(this._oscillator){const s=this._oscillator;s.stop(n),this.context.setTimeout(()=>s.dispose(),this.blockTime)}this._oscillator=new t({context:this.context}),this.frequency.connect(this._oscillator.frequency),this.detune.connect(this._oscillator.detune),this._oscillator.connect(this.output),this._oscillator.onstop=()=>this.onstop(this),this.state==="started"&&this._oscillator.start(n)}}get phase(){return this._oscillator.phase}set phase(e){this._oscillator.phase=e}get sourceType(){return this._sourceType}set sourceType(e){let t="sine";this._oscillator.type!=="pwm"&&this._oscillator.type!=="pulse"&&(t=this._oscillator.type),e==="fm"?this.type="fm"+t:e==="am"?this.type="am"+t:e==="fat"?this.type="fat"+t:e==="oscillator"?this.type=t:e==="pulse"?this.type="pulse":e==="pwm"&&(this.type="pwm")}_getOscType(e,t){return e instanceof _r[t]}get baseType(){return this._oscillator.baseType}set baseType(e){!this._getOscType(this._oscillator,"pulse")&&!this._getOscType(this._oscillator,"pwm")&&e!=="pulse"&&e!=="pwm"&&(this._oscillator.baseType=e)}get width(){if(this._getOscType(this._oscillator,"pulse"))return this._oscillator.width}get count(){if(this._getOscType(this._oscillator,"fat"))return this._oscillator.count}set count(e){this._getOscType(this._oscillator,"fat")&&Ge(e)&&(this._oscillator.count=e)}get spread(){if(this._getOscType(this._oscillator,"fat"))return this._oscillator.spread}set spread(e){this._getOscType(this._oscillator,"fat")&&Ge(e)&&(this._oscillator.spread=e)}get modulationType(){if(this._getOscType(this._oscillator,"fm")||this._getOscType(this._oscillator,"am"))return this._oscillator.modulationType}set modulationType(e){(this._getOscType(this._oscillator,"fm")||this._getOscType(this._oscillator,"am"))&&Ye(e)&&(this._oscillator.modulationType=e)}get modulationIndex(){if(this._getOscType(this._oscillator,"fm"))return this._oscillator.modulationIndex}get harmonicity(){if(this._getOscType(this._oscillator,"fm")||this._getOscType(this._oscillator,"am"))return this._oscillator.harmonicity}get modulationFrequency(){if(this._getOscType(this._oscillator,"pwm"))return this._oscillator.modulationFrequency}asArray(){return ae(this,arguments,void 0,function*(e=1024){return St(this,e)})}dispose(){return super.dispose(),this.detune.dispose(),this.frequency.dispose(),this._oscillator.dispose(),this}}function Si(r,e=1/0){const t=new WeakMap;return function(n,s){Reflect.defineProperty(n,s,{configurable:!0,enumerable:!0,get:function(){return t.get(this)},set:function(o){Ee(o,r,e),t.set(this,o)}})}}function Ke(r,e=1/0){const t=new WeakMap;return function(n,s){Reflect.defineProperty(n,s,{configurable:!0,enumerable:!0,get:function(){return t.get(this)},set:function(o){Ee(this.toSeconds(o),r,e),t.set(this,o)}})}}class Zn extends we{constructor(){const e=F(Zn.getDefaults(),arguments,["url","onload"]);super(e),this.name="Player",this._activeSources=new Set,this._buffer=new Y({onload:this._onload.bind(this,e.onload),onerror:e.onerror,reverse:e.reverse,url:e.url}),this.autostart=e.autostart,this._loop=e.loop,this._loopStart=e.loopStart,this._loopEnd=e.loopEnd,this._playbackRate=e.playbackRate,this.fadeIn=e.fadeIn,this.fadeOut=e.fadeOut}static getDefaults(){return Object.assign(we.getDefaults(),{autostart:!1,fadeIn:0,fadeOut:0,loop:!1,loopEnd:0,loopStart:0,onload:$,onerror:$,playbackRate:1,reverse:!1})}load(e){return ae(this,void 0,void 0,function*(){return yield this._buffer.load(e),this._onload(),this})}_onload(e=$){e(),this.autostart&&this.start()}_onSourceEnd(e){this.onstop(this),this._activeSources.delete(e),this._activeSources.size===0&&!this._synced&&this._state.getValueAtTime(this.now())==="started"&&(this._state.cancel(this.now()),this._state.setStateAtTime("stopped",this.now()))}start(e,t,n){return super.start(e,t,n),this}_start(e,t,n){this._loop?t=mt(t,this._loopStart):t=mt(t,0);const s=this.toSeconds(t),o=n;n=mt(n,Math.max(this._buffer.duration-s,0));let c=this.toSeconds(n);c=c/this._playbackRate,e=this.toSeconds(e);const u=new cn({url:this._buffer,context:this.context,fadeIn:this.fadeIn,fadeOut:this.fadeOut,loop:this._loop,loopEnd:this._loopEnd,loopStart:this._loopStart,onended:this._onSourceEnd.bind(this),playbackRate:this._playbackRate}).connect(this.output);!this._loop&&!this._synced&&(this._state.cancel(e+c),this._state.setStateAtTime("stopped",e+c,{implicitEnd:!0})),this._activeSources.add(u),this._loop&&De(o)?u.start(e,s):u.start(e,s,c-this.toSeconds(this.fadeOut))}_stop(e){const t=this.toSeconds(e);this._activeSources.forEach(n=>n.stop(t))}restart(e,t,n){return super.restart(e,t,n),this}_restart(e,t,n){var s;(s=[...this._activeSources].pop())===null||s===void 0||s.stop(e),this._start(e,t,n)}seek(e,t){const n=this.toSeconds(t);if(this._state.getValueAtTime(n)==="started"){const s=this.toSeconds(e);this._stop(n),this._start(n,s)}return this}setLoopPoints(e,t){return this.loopStart=e,this.loopEnd=t,this}get loopStart(){return this._loopStart}set loopStart(e){this._loopStart=e,this.buffer.loaded&&Ee(this.toSeconds(e),0,this.buffer.duration),this._activeSources.forEach(t=>{t.loopStart=e})}get loopEnd(){return this._loopEnd}set loopEnd(e){this._loopEnd=e,this.buffer.loaded&&Ee(this.toSeconds(e),0,this.buffer.duration),this._activeSources.forEach(t=>{t.loopEnd=e})}get buffer(){return this._buffer}set buffer(e){this._buffer.set(e)}get loop(){return this._loop}set loop(e){if(this._loop!==e&&(this._loop=e,this._activeSources.forEach(t=>{t.loop=e}),e)){const t=this._state.getNextState("stopped",this.now());t&&this._state.cancel(t.time)}}get playbackRate(){return this._playbackRate}set playbackRate(e){this._playbackRate=e;const t=this.now(),n=this._state.getNextState("stopped",t);n&&n.implicitEnd&&(this._state.cancel(n.time),this._activeSources.forEach(s=>s.cancelStop())),this._activeSources.forEach(s=>{s.playbackRate.setValueAtTime(e,t)})}get reverse(){return this._buffer.reverse}set reverse(e){this._buffer.reverse=e}get loaded(){return this._buffer.loaded}dispose(){return super.dispose(),this._activeSources.forEach(e=>e.dispose()),this._activeSources.clear(),this._buffer.dispose(),this}}Be([Ke(0)],Zn.prototype,"fadeIn",void 0);Be([Ke(0)],Zn.prototype,"fadeOut",void 0);class gh extends yt{constructor(){super(...arguments),this.name="GainToAudio",this._norm=new jt({context:this.context,mapping:e=>Math.abs(e)*2-1}),this.input=this._norm,this.output=this._norm}dispose(){return super.dispose(),this._norm.dispose(),this}}class ut extends j{constructor(){const e=F(ut.getDefaults(),arguments,["attack","decay","sustain","release"]);super(e),this.name="Envelope",this._sig=new fe({context:this.context,value:0}),this.output=this._sig,this.input=void 0,this.attack=e.attack,this.decay=e.decay,this.sustain=e.sustain,this.release=e.release,this.attackCurve=e.attackCurve,this.releaseCurve=e.releaseCurve,this.decayCurve=e.decayCurve}static getDefaults(){return Object.assign(j.getDefaults(),{attack:.01,attackCurve:"linear",decay:.1,decayCurve:"exponential",release:1,releaseCurve:"exponential",sustain:.5})}get value(){return this.getValueAtTime(this.now())}_getCurve(e,t){if(Ye(e))return e;{let n;for(n in fn)if(fn[n][t]===e)return n;return e}}_setCurve(e,t,n){if(Ye(n)&&Reflect.has(fn,n)){const s=fn[n];pt(s)?e!=="_decayCurve"&&(this[e]=s[t]):this[e]=s}else if(Ne(n)&&e!=="_decayCurve")this[e]=n;else throw new Error("Envelope: invalid curve: "+n)}get attackCurve(){return this._getCurve(this._attackCurve,"In")}set attackCurve(e){this._setCurve("_attackCurve","In",e)}get releaseCurve(){return this._getCurve(this._releaseCurve,"Out")}set releaseCurve(e){this._setCurve("_releaseCurve","Out",e)}get decayCurve(){return this._getCurve(this._decayCurve,"Out")}set decayCurve(e){this._setCurve("_decayCurve","Out",e)}triggerAttack(e,t=1){this.log("triggerAttack",e,t),e=this.toSeconds(e);let s=this.toSeconds(this.attack);const o=this.toSeconds(this.decay),c=this.getValueAtTime(e);if(c>0){const u=1/s;s=(1-c)/u}if(s<this.sampleTime)this._sig.cancelScheduledValues(e),this._sig.setValueAtTime(t,e);else if(this._attackCurve==="linear")this._sig.linearRampTo(t,s,e);else if(this._attackCurve==="exponential")this._sig.targetRampTo(t,s,e);else{this._sig.cancelAndHoldAtTime(e);let u=this._attackCurve;for(let l=1;l<u.length;l++)if(u[l-1]<=c&&c<=u[l]){u=this._attackCurve.slice(l),u[0]=c;break}this._sig.setValueCurveAtTime(u,e,s,t)}if(o&&this.sustain<1){const u=t*this.sustain,l=e+s;this.log("decay",l),this._decayCurve==="linear"?this._sig.linearRampToValueAtTime(u,o+l):this._sig.exponentialApproachValueAtTime(u,l,o)}return this}triggerRelease(e){this.log("triggerRelease",e),e=this.toSeconds(e);const t=this.getValueAtTime(e);if(t>0){const n=this.toSeconds(this.release);n<this.sampleTime?this._sig.setValueAtTime(0,e):this._releaseCurve==="linear"?this._sig.linearRampTo(0,n,e):this._releaseCurve==="exponential"?this._sig.targetRampTo(0,n,e):(L(Ne(this._releaseCurve),"releaseCurve must be either 'linear', 'exponential' or an array"),this._sig.cancelAndHoldAtTime(e),this._sig.setValueCurveAtTime(this._releaseCurve,e,n,t))}return this}getValueAtTime(e){return this._sig.getValueAtTime(e)}triggerAttackRelease(e,t,n=1){return t=this.toSeconds(t),this.triggerAttack(t,n),this.triggerRelease(t+this.toSeconds(e)),this}cancel(e){return this._sig.cancelScheduledValues(this.toSeconds(e)),this}connect(e,t=0,n=0){return Es(this,e,t,n),this}asArray(){return ae(this,arguments,void 0,function*(e=1024){const t=e/this.context.sampleRate,n=new Wn(1,t,this.context.sampleRate),s=this.toSeconds(this.attack)+this.toSeconds(this.decay),o=s+this.toSeconds(this.release),c=o*.1,u=o+c,l=new this.constructor(Object.assign(this.get(),{attack:t*this.toSeconds(this.attack)/u,decay:t*this.toSeconds(this.decay)/u,release:t*this.toSeconds(this.release)/u,context:n}));return l._sig.toDestination(),l.triggerAttackRelease(t*(s+c)/u,0),(yield n.render()).getChannelData(0)})}dispose(){return super.dispose(),this._sig.dispose(),this}}Be([Ke(0)],ut.prototype,"attack",void 0);Be([Ke(0)],ut.prototype,"decay",void 0);Be([Si(0,1)],ut.prototype,"sustain",void 0);Be([Ke(0)],ut.prototype,"release",void 0);const fn=(()=>{let e,t;const n=[];for(e=0;e<128;e++)n[e]=Math.sin(e/127*(Math.PI/2));const s=[],o=6.4;for(e=0;e<127;e++){t=e/127;const a=Math.sin(t*(Math.PI*2)*o-Math.PI/2)+1;s[e]=a/10+t*.83}s[127]=1;const c=[],u=5;for(e=0;e<128;e++)c[e]=Math.ceil(e/127*u)/u;const l=[];for(e=0;e<128;e++)t=e/127,l[e]=.5*(1-Math.cos(Math.PI*t));const h=[];for(e=0;e<128;e++){t=e/127;const a=Math.pow(t,3)*4+.2,d=Math.cos(a*Math.PI*2*t);h[e]=Math.abs(d*(1-t))}function p(a){const d=new Array(a.length);for(let f=0;f<a.length;f++)d[f]=1-a[f];return d}function i(a){return a.slice(0).reverse()}return{bounce:{In:p(h),Out:h},cosine:{In:n,Out:i(n)},exponential:"exponential",linear:"linear",ripple:{In:s,Out:p(s)},sine:{In:l,Out:p(l)},step:{In:c,Out:p(c)}}})();class ot extends j{constructor(){const e=F(ot.getDefaults(),arguments);super(e),this._scheduledEvents=[],this._synced=!1,this._original_triggerAttack=this.triggerAttack,this._original_triggerRelease=this.triggerRelease,this._syncedRelease=t=>this._original_triggerRelease(t),this._volume=this.output=new Bt({context:this.context,volume:e.volume}),this.volume=this._volume.volume,te(this,"volume")}static getDefaults(){return Object.assign(j.getDefaults(),{volume:0})}sync(){return this._syncState()&&(this._syncMethod("triggerAttack",1),this._syncMethod("triggerRelease",0),this.context.transport.on("stop",this._syncedRelease),this.context.transport.on("pause",this._syncedRelease),this.context.transport.on("loopEnd",this._syncedRelease)),this}_syncState(){let e=!1;return this._synced||(this._synced=!0,e=!0),e}_syncMethod(e,t){const n=this["_original_"+e]=this[e];this[e]=(...s)=>{const o=s[t],c=this.context.transport.schedule(u=>{s[t]=u,n.apply(this,s)},o);this._scheduledEvents.push(c)}}unsync(){return this._scheduledEvents.forEach(e=>this.context.transport.clear(e)),this._scheduledEvents=[],this._synced&&(this._synced=!1,this.triggerAttack=this._original_triggerAttack,this.triggerRelease=this._original_triggerRelease,this.context.transport.off("stop",this._syncedRelease),this.context.transport.off("pause",this._syncedRelease),this.context.transport.off("loopEnd",this._syncedRelease)),this}triggerAttackRelease(e,t,n,s){const o=this.toSeconds(n),c=this.toSeconds(t);return this.triggerAttack(e,o,s),this.triggerRelease(o+c),this}dispose(){return super.dispose(),this._volume.dispose(),this.unsync(),this._scheduledEvents=[],this}}class Tt extends ot{constructor(){const e=F(Tt.getDefaults(),arguments);super(e),this.portamento=e.portamento,this.onsilence=e.onsilence}static getDefaults(){return Object.assign(ot.getDefaults(),{detune:0,onsilence:$,portamento:0})}triggerAttack(e,t,n=1){this.log("triggerAttack",e,t,n);const s=this.toSeconds(t);return this._triggerEnvelopeAttack(s,n),this.setNote(e,s),this}triggerRelease(e){this.log("triggerRelease",e);const t=this.toSeconds(e);return this._triggerEnvelopeRelease(t),this}setNote(e,t){const n=this.toSeconds(t),s=e instanceof Oe?e.toFrequency():e;if(this.portamento>0&&this.getLevelAtTime(n)>.05){const o=this.toSeconds(this.portamento);this.frequency.exponentialRampTo(s,o,n)}else this.frequency.setValueAtTime(s,n);return this}}Be([Ke(0)],Tt.prototype,"portamento",void 0);class Bs extends ut{constructor(){super(F(Bs.getDefaults(),arguments,["attack","decay","sustain","release"])),this.name="AmplitudeEnvelope",this._gainNode=new re({context:this.context,gain:0}),this.output=this._gainNode,this.input=this._gainNode,this._sig.connect(this._gainNode.gain),this.output=this._gainNode,this.input=this._gainNode}dispose(){return super.dispose(),this._gainNode.dispose(),this}}class Yt extends Tt{constructor(){const e=F(Yt.getDefaults(),arguments);super(e),this.name="Synth",this.oscillator=new On(Object.assign({context:this.context,detune:e.detune,onstop:()=>this.onsilence(this)},e.oscillator)),this.frequency=this.oscillator.frequency,this.detune=this.oscillator.detune,this.envelope=new Bs(Object.assign({context:this.context},e.envelope)),this.oscillator.chain(this.envelope,this.output),te(this,["oscillator","frequency","detune","envelope"])}static getDefaults(){return Object.assign(Tt.getDefaults(),{envelope:Object.assign(cs(ut.getDefaults(),Object.keys(j.getDefaults())),{attack:.005,decay:.1,release:1,sustain:.3}),oscillator:Object.assign(cs(On.getDefaults(),[...Object.keys(we.getDefaults()),"frequency","detune"]),{type:"triangle"})})}_triggerEnvelopeAttack(e,t){if(this.envelope.triggerAttack(e,t),this.oscillator.start(e),this.envelope.sustain===0){const n=this.toSeconds(this.envelope.attack),s=this.toSeconds(this.envelope.decay);this.oscillator.stop(e+n+s)}}_triggerEnvelopeRelease(e){this.envelope.triggerRelease(e),this.oscillator.stop(e+this.toSeconds(this.envelope.release))}getLevelAtTime(e){return e=this.toSeconds(e),this.envelope.getValueAtTime(e)}dispose(){return super.dispose(),this.oscillator.dispose(),this.envelope.dispose(),this}}class zn extends Yt{constructor(){const e=F(zn.getDefaults(),arguments);super(e),this.name="MembraneSynth",this.portamento=0,this.pitchDecay=e.pitchDecay,this.octaves=e.octaves,te(this,["oscillator","envelope"])}static getDefaults(){return _t(Tt.getDefaults(),Yt.getDefaults(),{envelope:{attack:.001,attackCurve:"exponential",decay:.4,release:1.4,sustain:.01},octaves:10,oscillator:{type:"sine"},pitchDecay:.05})}setNote(e,t){const n=this.toSeconds(t),s=this.toFrequency(e instanceof Oe?e.toFrequency():e),o=s*this.octaves;return this.oscillator.frequency.setValueAtTime(o,n),this.oscillator.frequency.exponentialRampToValueAtTime(s,n+this.toSeconds(this.pitchDecay)),this}dispose(){return super.dispose(),this}}Be([Si(0)],zn.prototype,"octaves",void 0);Be([Ke(0)],zn.prototype,"pitchDecay",void 0);const xi=new Set;function js(r){xi.add(r)}function ki(r,e){const t=`registerProcessor("${r}", ${e})`;xi.add(t)}const vh=`
	/**
	 * The base AudioWorkletProcessor for use in Tone.js. Works with the {@link ToneAudioWorklet}. 
	 */
	class ToneAudioWorkletProcessor extends AudioWorkletProcessor {

		constructor(options) {
			
			super(options);
			/**
			 * If the processor was disposed or not. Keep alive until it's disposed.
			 */
			this.disposed = false;
		   	/** 
			 * The number of samples in the processing block
			 */
			this.blockSize = 128;
			/**
			 * the sample rate
			 */
			this.sampleRate = sampleRate;

			this.port.onmessage = (event) => {
				// when it receives a dispose 
				if (event.data === "dispose") {
					this.disposed = true;
				}
			};
		}
	}
`;js(vh);const yh=`
	/**
	 * Abstract class for a single input/output processor. 
	 * has a 'generate' function which processes one sample at a time
	 */
	class SingleIOProcessor extends ToneAudioWorkletProcessor {

		constructor(options) {
			super(Object.assign(options, {
				numberOfInputs: 1,
				numberOfOutputs: 1
			}));
			/**
			 * Holds the name of the parameter and a single value of that
			 * parameter at the current sample
			 * @type { [name: string]: number }
			 */
			this.params = {}
		}

		/**
		 * Generate an output sample from the input sample and parameters
		 * @abstract
		 * @param input number
		 * @param channel number
		 * @param parameters { [name: string]: number }
		 * @returns number
		 */
		generate(){}

		/**
		 * Update the private params object with the 
		 * values of the parameters at the given index
		 * @param parameters { [name: string]: Float32Array },
		 * @param index number
		 */
		updateParams(parameters, index) {
			for (const paramName in parameters) {
				const param = parameters[paramName];
				if (param.length > 1) {
					this.params[paramName] = parameters[paramName][index];
				} else {
					this.params[paramName] = parameters[paramName][0];
				}
			}
		}

		/**
		 * Process a single frame of the audio
		 * @param inputs Float32Array[][]
		 * @param outputs Float32Array[][]
		 */
		process(inputs, outputs, parameters) {
			const input = inputs[0];
			const output = outputs[0];
			// get the parameter values
			const channelCount = Math.max(input && input.length || 0, output.length);
			for (let sample = 0; sample < this.blockSize; sample++) {
				this.updateParams(parameters, sample);
				for (let channel = 0; channel < channelCount; channel++) {
					const inputSample = input && input.length ? input[channel][sample] : 0;
					output[channel][sample] = this.generate(inputSample, channel, this.params);
				}
			}
			return !this.disposed;
		}
	};
`;js(yh);const Th=`
	/**
	 * A multichannel buffer for use within an AudioWorkletProcessor as a delay line
	 */
	class DelayLine {
		
		constructor(size, channels) {
			this.buffer = [];
			this.writeHead = []
			this.size = size;

			// create the empty channels
			for (let i = 0; i < channels; i++) {
				this.buffer[i] = new Float32Array(this.size);
				this.writeHead[i] = 0;
			}
		}

		/**
		 * Push a value onto the end
		 * @param channel number
		 * @param value number
		 */
		push(channel, value) {
			this.writeHead[channel] += 1;
			if (this.writeHead[channel] > this.size) {
				this.writeHead[channel] = 0;
			}
			this.buffer[channel][this.writeHead[channel]] = value;
		}

		/**
		 * Get the recorded value of the channel given the delay
		 * @param channel number
		 * @param delay number delay samples
		 */
		get(channel, delay) {
			let readHead = this.writeHead[channel] - Math.floor(delay);
			if (readHead < 0) {
				readHead += this.size;
			}
			return this.buffer[channel][readHead];
		}
	}
`;js(Th);const wh="feedback-comb-filter",bh=`
	class FeedbackCombFilterWorklet extends SingleIOProcessor {

		constructor(options) {
			super(options);
			this.delayLine = new DelayLine(this.sampleRate, options.channelCount || 2);
		}

		static get parameterDescriptors() {
			return [{
				name: "delayTime",
				defaultValue: 0.1,
				minValue: 0,
				maxValue: 1,
				automationRate: "k-rate"
			}, {
				name: "feedback",
				defaultValue: 0.5,
				minValue: 0,
				maxValue: 0.9999,
				automationRate: "k-rate"
			}];
		}

		generate(input, channel, parameters) {
			const delayedSample = this.delayLine.get(channel, parameters.delayTime * this.sampleRate);
			this.delayLine.push(channel, input + delayedSample * parameters.feedback);
			return delayedSample;
		}
	}
`;ki(wh,bh);class Ci extends ot{constructor(){const e=F(Ci.getDefaults(),arguments,["voice","options"]);super(e),this.name="PolySynth",this._availableVoices=[],this._activeVoices=[],this._voices=[],this._gcTimeout=-1,this._averageActiveVoices=0,this._syncedRelease=s=>this.releaseAll(s),L(!Ge(e.voice),"DEPRECATED: The polyphony count is no longer the first argument.");const t=e.voice.getDefaults();this.options=Object.assign(t,e.options),this.voice=e.voice,this.maxPolyphony=e.maxPolyphony,this._dummyVoice=this._getNextAvailableVoice();const n=this._voices.indexOf(this._dummyVoice);this._voices.splice(n,1),this._gcTimeout=this.context.setInterval(this._collectGarbage.bind(this),1)}static getDefaults(){return Object.assign(ot.getDefaults(),{maxPolyphony:32,options:{},voice:Yt})}get activeVoices(){return this._activeVoices.length}_makeVoiceAvailable(e){this._availableVoices.push(e);const t=this._activeVoices.findIndex(n=>n.voice===e);this._activeVoices.splice(t,1)}_getNextAvailableVoice(){if(this._availableVoices.length)return this._availableVoices.shift();if(this._voices.length<this.maxPolyphony){const e=new this.voice(Object.assign(this.options,{context:this.context,onsilence:this._makeVoiceAvailable.bind(this)}));return L(e instanceof Tt,"Voice must extend Monophonic class"),e.connect(this.output),this._voices.push(e),e}else Vn("Max polyphony exceeded. Note dropped.")}_collectGarbage(){if(this._averageActiveVoices=Math.max(this._averageActiveVoices*.95,this.activeVoices),this._availableVoices.length&&this._voices.length>Math.ceil(this._averageActiveVoices+1)){const e=this._availableVoices.shift(),t=this._voices.indexOf(e);this._voices.splice(t,1),this.context.isOffline||e.dispose()}}_triggerAttack(e,t,n){e.forEach(s=>{const o=new kn(this.context,s).toMidi(),c=this._getNextAvailableVoice();c&&(c.triggerAttack(s,t,n),this._activeVoices.push({midi:o,voice:c,released:!1}),this.log("triggerAttack",s,t))})}_triggerRelease(e,t){e.forEach(n=>{const s=new kn(this.context,n).toMidi(),o=this._activeVoices.find(({midi:c,released:u})=>c===s&&!u);o&&(o.voice.triggerRelease(t),o.released=!0,this.log("triggerRelease",n,t))})}_scheduleEvent(e,t,n,s){L(!this.disposed,"Synth was already disposed"),n<=this.now()?e==="attack"?this._triggerAttack(t,n,s):this._triggerRelease(t,n):this.context.setTimeout(()=>{this.disposed||this._scheduleEvent(e,t,n,s)},n-this.now())}triggerAttack(e,t,n){Array.isArray(e)||(e=[e]);const s=this.toSeconds(t);return this._scheduleEvent("attack",e,s,n),this}triggerRelease(e,t){Array.isArray(e)||(e=[e]);const n=this.toSeconds(t);return this._scheduleEvent("release",e,n),this}triggerAttackRelease(e,t,n,s){const o=this.toSeconds(n);if(this.triggerAttack(e,o,s),Ne(t)){L(Ne(e),"If the duration is an array, the notes must also be an array"),e=e;for(let c=0;c<e.length;c++){const u=t[Math.min(c,t.length-1)],l=this.toSeconds(u);L(l>0,"The duration must be greater than 0"),this.triggerRelease(e[c],o+l)}}else{const c=this.toSeconds(t);L(c>0,"The duration must be greater than 0"),this.triggerRelease(e,o+c)}return this}sync(){return this._syncState()&&(this._syncMethod("triggerAttack",1),this._syncMethod("triggerRelease",1),this.context.transport.on("stop",this._syncedRelease),this.context.transport.on("pause",this._syncedRelease),this.context.transport.on("loopEnd",this._syncedRelease)),this}set(e){const t=cs(e,["onsilence","context"]);return this.options=_t(this.options,t),this._voices.forEach(n=>n.set(t)),this._dummyVoice.set(t),this}get(){return this._dummyVoice.get()}releaseAll(e){const t=this.toSeconds(e);return this._activeVoices.forEach(({voice:n})=>{n.triggerRelease(t)}),this}dispose(){return super.dispose(),this._dummyVoice.dispose(),this._voices.forEach(e=>e.dispose()),this._activeVoices=[],this._availableVoices=[],this.context.clearInterval(this._gcTimeout),this}}class Yn extends ot{constructor(){const e=F(Yn.getDefaults(),arguments,["urls","onload","baseUrl"],"urls");super(e),this.name="Sampler",this._activeSources=new Map;const t={};Object.keys(e.urls).forEach(n=>{const s=parseInt(n,10);if(L(dn(n)||Ge(s)&&isFinite(s),`url key is neither a note or midi pitch: ${n}`),dn(n)){const o=new Oe(this.context,n).toMidi();t[o]=e.urls[n]}else Ge(s)&&isFinite(s)&&(t[s]=e.urls[s])}),this._buffers=new Fs({urls:t,onload:e.onload,baseUrl:e.baseUrl,onerror:e.onerror}),this.attack=e.attack,this.release=e.release,this.curve=e.curve,this._buffers.loaded&&Promise.resolve().then(e.onload)}static getDefaults(){return Object.assign(ot.getDefaults(),{attack:0,baseUrl:"",curve:"exponential",onload:$,onerror:$,release:.1,urls:{}})}_findClosest(e){let n=0;for(;n<96;){if(this._buffers.has(e+n))return-n;if(this._buffers.has(e-n))return n;n++}throw new Error(`No available buffers for note: ${e}`)}triggerAttack(e,t,n=1){return this.log("triggerAttack",e,t,n),Array.isArray(e)||(e=[e]),e.forEach(s=>{const o=bi(new Oe(this.context,s).toFrequency()),c=Math.round(o),u=o-c,l=this._findClosest(c),h=c-l,p=this._buffers.get(h),i=wi(l+u),a=new cn({url:p,context:this.context,curve:this.curve,fadeIn:this.attack,fadeOut:this.release,playbackRate:i}).connect(this.output);a.start(t,0,p.duration/i,n),Ne(this._activeSources.get(c))||this._activeSources.set(c,[]),this._activeSources.get(c).push(a),a.onended=()=>{if(this._activeSources&&this._activeSources.has(c)){const d=this._activeSources.get(c),f=d.indexOf(a);f!==-1&&d.splice(f,1)}}}),this}triggerRelease(e,t){return this.log("triggerRelease",e,t),Array.isArray(e)||(e=[e]),e.forEach(n=>{const s=new Oe(this.context,n).toMidi();if(this._activeSources.has(s)&&this._activeSources.get(s).length){const o=this._activeSources.get(s);t=this.toSeconds(t),o.forEach(c=>{c.stop(t)}),this._activeSources.set(s,[])}}),this}releaseAll(e){const t=this.toSeconds(e);return this._activeSources.forEach(n=>{for(;n.length;)n.shift().stop(t)}),this}sync(){return this._syncState()&&(this._syncMethod("triggerAttack",1),this._syncMethod("triggerRelease",1)),this}triggerAttackRelease(e,t,n,s=1){const o=this.toSeconds(n);return this.triggerAttack(e,o,s),Ne(t)?(L(Ne(e),"notes must be an array when duration is array"),e.forEach((c,u)=>{const l=t[Math.min(u,t.length-1)];this.triggerRelease(c,o+this.toSeconds(l))})):this.triggerRelease(e,o+this.toSeconds(t)),this}add(e,t,n){if(L(dn(e)||isFinite(e),`note must be a pitch or midi: ${e}`),dn(e)){const s=new Oe(this.context,e).toMidi();this._buffers.add(s,t,n)}else this._buffers.add(e,t,n);return this}get loaded(){return this._buffers.loaded}dispose(){return super.dispose(),this._buffers.dispose(),this._activeSources.forEach(e=>{e.forEach(t=>t.dispose())}),this._activeSources.clear(),this}}Be([Ke(0)],Yn.prototype,"attack",void 0);Be([Ke(0)],Yn.prototype,"release",void 0);class Gs extends _e{constructor(){const e=F(Gs.getDefaults(),arguments,["callback","value"]);super(e),this.name="ToneEvent",this._state=new Bn("stopped"),this._startOffset=0,this._loop=e.loop,this.callback=e.callback,this.value=e.value,this._loopStart=this.toTicks(e.loopStart),this._loopEnd=this.toTicks(e.loopEnd),this._playbackRate=e.playbackRate,this._probability=e.probability,this._humanize=e.humanize,this.mute=e.mute,this._playbackRate=e.playbackRate,this._state.increasing=!0,this._rescheduleEvents()}static getDefaults(){return Object.assign(_e.getDefaults(),{callback:$,humanize:!1,loop:!1,loopEnd:"1m",loopStart:0,mute:!1,playbackRate:1,probability:1,value:null})}_rescheduleEvents(e=-1){this._state.forEachFrom(e,t=>{let n;if(t.state==="started"){t.id!==-1&&this.context.transport.clear(t.id);const s=t.time+Math.round(this.startOffset/this._playbackRate);if(this._loop===!0||Ge(this._loop)&&this._loop>1){n=1/0,Ge(this._loop)&&(n=this._loop*this._getLoopDuration());const o=this._state.getAfter(s);o!==null&&(n=Math.min(n,o.time-s)),n!==1/0&&(n=new Me(this.context,n));const c=new Me(this.context,this._getLoopDuration());t.id=this.context.transport.scheduleRepeat(this._tick.bind(this),c,new Me(this.context,s),n)}else t.id=this.context.transport.schedule(this._tick.bind(this),new Me(this.context,s))}})}get state(){return this._state.getValueAtTime(this.context.transport.ticks)}get startOffset(){return this._startOffset}set startOffset(e){this._startOffset=e}get probability(){return this._probability}set probability(e){this._probability=e}get humanize(){return this._humanize}set humanize(e){this._humanize=e}start(e){const t=this.toTicks(e);return this._state.getValueAtTime(t)==="stopped"&&(this._state.add({id:-1,state:"started",time:t}),this._rescheduleEvents(t)),this}stop(e){this.cancel(e);const t=this.toTicks(e);if(this._state.getValueAtTime(t)==="started"){this._state.setStateAtTime("stopped",t,{id:-1});const n=this._state.getBefore(t);let s=t;n!==null&&(s=n.time),this._rescheduleEvents(s)}return this}cancel(e){e=mt(e,-1/0);const t=this.toTicks(e);return this._state.forEachFrom(t,n=>{this.context.transport.clear(n.id)}),this._state.cancel(t),this}_tick(e){const t=this.context.transport.getTicksAtTime(e);if(!this.mute&&this._state.getValueAtTime(t)==="started"){if(this.probability<1&&Math.random()>this.probability)return;if(this.humanize){let n=.02;di(this.humanize)||(n=this.toSeconds(this.humanize)),e+=(Math.random()*2-1)*n}this.callback(e,this.value)}}_getLoopDuration(){return(this._loopEnd-this._loopStart)/this._playbackRate}get loop(){return this._loop}set loop(e){this._loop=e,this._rescheduleEvents()}get playbackRate(){return this._playbackRate}set playbackRate(e){this._playbackRate=e,this._rescheduleEvents()}get loopEnd(){return new Me(this.context,this._loopEnd).toSeconds()}set loopEnd(e){this._loopEnd=this.toTicks(e),this._loop&&this._rescheduleEvents()}get loopStart(){return new Me(this.context,this._loopStart).toSeconds()}set loopStart(e){this._loopStart=this.toTicks(e),this._loop&&this._rescheduleEvents()}get progress(){if(this._loop){const e=this.context.transport.ticks,t=this._state.get(e);if(t!==null&&t.state==="started"){const n=this._getLoopDuration();return(e-t.time)%n/n}else return 0}else return 0}dispose(){return super.dispose(),this.cancel(),this._state.dispose(),this}}class Nn extends _e{constructor(){const e=F(Nn.getDefaults(),arguments,["callback","interval"]);super(e),this.name="Loop",this._event=new Gs({context:this.context,callback:this._tick.bind(this),loop:!0,loopEnd:e.interval,playbackRate:e.playbackRate,probability:e.probability,humanize:e.humanize}),this.callback=e.callback,this.iterations=e.iterations}static getDefaults(){return Object.assign(_e.getDefaults(),{interval:"4n",callback:$,playbackRate:1,iterations:1/0,probability:1,mute:!1,humanize:!1})}start(e){return this._event.start(e),this}stop(e){return this._event.stop(e),this}cancel(e){return this._event.cancel(e),this}_tick(e){this.callback(e)}get state(){return this._event.state}get progress(){return this._event.progress}get interval(){return this._event.loopEnd}set interval(e){this._event.loopEnd=e}get playbackRate(){return this._event.playbackRate}set playbackRate(e){this._event.playbackRate=e}get humanize(){return this._event.humanize}set humanize(e){this._event.humanize=e}get probability(){return this._event.probability}set probability(e){this._event.probability=e}get mute(){return this._event.mute}set mute(e){this._event.mute=e}get iterations(){return this._event.loop===!0?1/0:this._event.loop}set iterations(e){e===1/0?this._event.loop=!0:this._event.loop=e}dispose(){return super.dispose(),this._event.dispose(),this}}function*Ah(r){let e=0;for(;e<r;)e=At(e,0,r-1),yield e,e++}function*Sh(r){let e=r-1;for(;e>=0;)e=At(e,0,r-1),yield e,e--}function*Ht(r,e){for(;;)yield*e(r)}function*mr(r,e){let t=e?0:r-1;for(;;)t=At(t,0,r-1),yield t,e?(t++,t>=r-1&&(e=!1)):(t--,t<=0&&(e=!0))}function*xh(r){let e=0,t=0;for(;e<r;)e=At(e,0,r-1),yield e,t++,e+=t%2?2:-1}function*kh(r){let e=r-1,t=0;for(;e>=0;)e=At(e,0,r-1),yield e,t++,e+=t%2?-2:1}function*Ch(r){for(;;)yield Math.floor(Math.random()*r)}function*Oh(r){const e=[];for(let t=0;t<r;t++)e.push(t);for(;e.length>0;){const t=e.splice(Math.floor(e.length*Math.random()),1);yield At(t[0],0,r-1)}}function*Nh(r){let e=Math.floor(Math.random()*r);for(;;)e===0?e++:e===r-1||Math.random()<.5?e--:e++,yield e}function*gr(r,e="up",t=0){switch(L(r>=1,"The number of values must be at least one"),e){case"up":yield*Ht(r,Ah);case"down":yield*Ht(r,Sh);case"upDown":yield*mr(r,!0);case"downUp":yield*mr(r,!1);case"alternateUp":yield*Ht(r,xh);case"alternateDown":yield*Ht(r,kh);case"random":yield*Ch(r);case"randomOnce":yield*Ht(r,Oh);case"randomWalk":yield*Nh(r)}}class Oi extends Nn{constructor(){const e=F(Oi.getDefaults(),arguments,["callback","values","pattern"]);super(e),this.name="Pattern",this.callback=e.callback,this._values=e.values,this._pattern=gr(e.values.length,e.pattern),this._type=e.pattern}static getDefaults(){return Object.assign(Nn.getDefaults(),{pattern:"up",values:[],callback:$})}_tick(e){const t=this._pattern.next();this._index=t.value,this._value=this._values[t.value],this.callback(e,this._value)}get values(){return this._values}set values(e){this._values=e,this.pattern=this._type}get value(){return this._value}get index(){return this._index}get pattern(){return this._type}set pattern(e){this._type=e,this._pattern=gr(this._values.length,this._type)}}class Us extends j{constructor(){const e=F(Us.getDefaults(),arguments,["fade"]);super(e),this.name="CrossFade",this._panner=this.context.createStereoPanner(),this._split=this.context.createChannelSplitter(2),this._g2a=new gh({context:this.context}),this.a=new re({context:this.context,gain:0}),this.b=new re({context:this.context,gain:0}),this.output=new re({context:this.context}),this._internalChannels=[this.a,this.b],this.fade=new fe({context:this.context,units:"normalRange",value:e.fade}),te(this,"fade"),this.context.getConstant(1).connect(this._panner),this._panner.connect(this._split),this._panner.channelCount=1,this._panner.channelCountMode="explicit",rt(this._split,this.a.gain,0),rt(this._split,this.b.gain,1),this.fade.chain(this._g2a,this._panner.pan),this.a.connect(this.output),this.b.connect(this.output)}static getDefaults(){return Object.assign(j.getDefaults(),{fade:.5})}dispose(){return super.dispose(),this.a.dispose(),this.b.dispose(),this.output.dispose(),this.fade.dispose(),this._g2a.dispose(),this._panner.disconnect(),this._split.disconnect(),this}}class vr extends j{constructor(e){super(e),this.name="Effect",this._dryWet=new Us({context:this.context}),this.wet=this._dryWet.fade,this.effectSend=new re({context:this.context}),this.effectReturn=new re({context:this.context}),this.input=new re({context:this.context}),this.output=this._dryWet,this.input.fan(this._dryWet.a,this.effectSend),this.effectReturn.connect(this._dryWet.b),this.wet.setValueAtTime(e.wet,0),this._internalChannels=[this.effectReturn,this.effectSend],te(this,"wet")}static getDefaults(){return Object.assign(j.getDefaults(),{wet:1})}connectEffect(e){return this._internalChannels.push(e),this.effectSend.chain(e,this.effectReturn),this}dispose(){return super.dispose(),this._dryWet.dispose(),this.effectSend.dispose(),this.effectReturn.dispose(),this.wet.dispose(),this}}class Hs extends j{constructor(){const e=F(Hs.getDefaults(),arguments,["pan"]);super(e),this.name="Panner",this._panner=this.context.createStereoPanner(),this.input=this._panner,this.output=this._panner,this.pan=new se({context:this.context,param:this._panner.pan,value:e.pan,minValue:-1,maxValue:1}),this._panner.channelCount=e.channelCount,this._panner.channelCountMode="explicit",te(this,"pan")}static getDefaults(){return Object.assign(j.getDefaults(),{pan:0,channelCount:1})}dispose(){return super.dispose(),this._panner.disconnect(),this.pan.dispose(),this}}const Ih="bit-crusher",Mh=`
	class BitCrusherWorklet extends SingleIOProcessor {

		static get parameterDescriptors() {
			return [{
				name: "bits",
				defaultValue: 12,
				minValue: 1,
				maxValue: 16,
				automationRate: 'k-rate'
			}];
		}

		generate(input, _channel, parameters) {
			const step = Math.pow(0.5, parameters.bits - 1);
			const val = step * Math.floor(input / step + 0.5);
			return val;
		}
	}
`;ki(Ih,Mh);class $s extends j{constructor(){const e=F($s.getDefaults(),arguments,["channels"]);super(e),this.name="Merge",this._merger=this.output=this.input=this.context.createChannelMerger(e.channels)}static getDefaults(){return Object.assign(j.getDefaults(),{channels:2})}dispose(){return super.dispose(),this._merger.disconnect(),this}}class Ni extends vr{constructor(){const e=F(Ni.getDefaults(),arguments,["decay"]);super(e),this.name="Reverb",this._convolver=this.context.createConvolver(),this.ready=Promise.resolve();const t=this.toSeconds(e.decay);Ee(t,.001),this._decay=t;const n=this.toSeconds(e.preDelay);Ee(n,0),this._preDelay=n,this.generate(),this.connectEffect(this._convolver)}static getDefaults(){return Object.assign(vr.getDefaults(),{decay:1.5,preDelay:.01})}get decay(){return this._decay}set decay(e){e=this.toSeconds(e),Ee(e,.001),this._decay=e,this.generate()}get preDelay(){return this._preDelay}set preDelay(e){e=this.toSeconds(e),Ee(e,0),this._preDelay=e,this.generate()}generate(){return ae(this,void 0,void 0,function*(){const e=this.ready,t=new Wn(2,this._decay+this._preDelay,this.context.sampleRate),n=new Cn({context:t}),s=new Cn({context:t}),o=new $s({context:t});n.connect(o,0,0),s.connect(o,0,1);const c=new re({context:t}).toDestination();o.connect(c),n.start(0),s.start(0),c.gain.setValueAtTime(0,0),c.gain.setValueAtTime(1,this._preDelay),c.gain.exponentialApproachValueAtTime(0,this._preDelay,this.decay);const u=t.render();return this.ready=u.then($),yield e,this._convolver.buffer=(yield u).get(),this})}dispose(){return super.dispose(),this._convolver.disconnect(),this}}class ue extends j{constructor(){const e=F(ue.getDefaults(),arguments,["solo"]);super(e),this.name="Solo",this.input=this.output=new re({context:this.context}),ue._allSolos.has(this.context)||ue._allSolos.set(this.context,new Set),ue._allSolos.get(this.context).add(this),this.solo=e.solo}static getDefaults(){return Object.assign(j.getDefaults(),{solo:!1})}get solo(){return this._isSoloed()}set solo(e){e?this._addSolo():this._removeSolo(),ue._allSolos.get(this.context).forEach(t=>t._updateSolo())}get muted(){return this.input.gain.value===0}_addSolo(){ue._soloed.has(this.context)||ue._soloed.set(this.context,new Set),ue._soloed.get(this.context).add(this)}_removeSolo(){ue._soloed.has(this.context)&&ue._soloed.get(this.context).delete(this)}_isSoloed(){return ue._soloed.has(this.context)&&ue._soloed.get(this.context).has(this)}_noSolos(){return!ue._soloed.has(this.context)||ue._soloed.has(this.context)&&ue._soloed.get(this.context).size===0}_updateSolo(){this._isSoloed()?this.input.gain.value=1:this._noSolos()?this.input.gain.value=1:this.input.gain.value=0}dispose(){return super.dispose(),ue._allSolos.get(this.context).delete(this),this._removeSolo(),this}}ue._allSolos=new Map;ue._soloed=new Map;class Xs extends j{constructor(){const e=F(Xs.getDefaults(),arguments,["pan","volume"]);super(e),this.name="PanVol",this._panner=this.input=new Hs({context:this.context,pan:e.pan,channelCount:e.channelCount}),this.pan=this._panner.pan,this._volume=this.output=new Bt({context:this.context,volume:e.volume}),this.volume=this._volume.volume,this._panner.connect(this._volume),this.mute=e.mute,te(this,["pan","volume"])}static getDefaults(){return Object.assign(j.getDefaults(),{mute:!1,pan:0,volume:0,channelCount:1})}get mute(){return this._volume.mute}set mute(e){this._volume.mute=e}dispose(){return super.dispose(),this._panner.dispose(),this.pan.dispose(),this._volume.dispose(),this.volume.dispose(),this}}class It extends j{constructor(){const e=F(It.getDefaults(),arguments,["volume","pan"]);super(e),this.name="Channel",this._solo=this.input=new ue({solo:e.solo,context:this.context}),this._panVol=this.output=new Xs({context:this.context,pan:e.pan,volume:e.volume,mute:e.mute,channelCount:e.channelCount}),this.pan=this._panVol.pan,this.volume=this._panVol.volume,this._solo.connect(this._panVol),te(this,["pan","volume"])}static getDefaults(){return Object.assign(j.getDefaults(),{pan:0,volume:0,mute:!1,solo:!1,channelCount:1})}get solo(){return this._solo.solo}set solo(e){this._solo.solo=e}get muted(){return this._solo.muted||this.mute}get mute(){return this._panVol.mute}set mute(e){this._panVol.mute=e}_getBus(e){return It.buses.has(e)||It.buses.set(e,new re({context:this.context})),It.buses.get(e)}send(e,t=0){const n=this._getBus(e),s=new re({context:this.context,units:"decibels",gain:t});return this.connect(s),s.connect(n),s}receive(e){return this._getBus(e).connect(this),this}dispose(){return super.dispose(),this._panVol.dispose(),this.pan.dispose(),this.volume.dispose(),this._solo.dispose(),this}}It.buses=new Map;const ld=Ve().transport;Ve().destination;Ve().destination;Ve().listener;Ve().draw;const hd=Ve();export{Ci as P,Ni as R,ld as T,fr as _,ad as a,cd as b,Oi as c,hd as d,ih as g,Ri as h,ud as s};

import { useState } from "react";
import { Search, UserPlus } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import ActionButton from "@/components/ui/ActionButton";
import { useAuth } from "@/context/auth-context";
import { getPlayerTitle } from "@/services/playerTitlesService";
import PlayerProfileModal from "./PlayerProfileModal";

interface FriendData {
  id: string;
  username: string;
  level: number;
  xp: number;
  online: boolean;
  avatar_url?: string;
  isFriend?: boolean; // usato solo in UserSearch
  created_at?: string;
  updated_at?: string;
  games_played?: number;
  games_won?: number;
}

interface UserSearchProps {
  authUser: { id: string } | null;
  friends: FriendData[];
  hasPendingRequestFor?: (playerId: string) => boolean;
  onAddFriend?: (playerId: string) => Promise<void>;
}

const UserSearch = ({
  authUser,
  friends,
  hasPendingRequestFor,
  onAddFriend,
}: UserSearchProps) => {
  const {
    getSupabaseClient,
    searchPlayers, // 🆕 Usa la nuova funzione dal contesto
  } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<FriendData[]>([]);
  const [searching, setSearching] = useState(false);
  const [selectedPlayer, setSelectedPlayer] = useState<FriendData | null>(null);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const { toast } = useToast();

  const searchUsers = async () => {
    if (!searchQuery.trim() || !authUser) return;

    setSearching(true);
    try {
      // 🆕 USA LA NUOVA FUNZIONE searchPlayers DAL CONTESTO
      const users = await searchPlayers(searchQuery.trim(), false); // false = usa cache se disponibile

      // Mappa tutti i risultati, marcando chi è già amico
      const friendIds = friends.map((f) => f.id);
      const formattedResults: (FriendData & { isFriend: boolean })[] =
        users.map((user) => ({
          id: user.id,
          username: user.username,
          level: user.level,
          xp: user.xp,
          online: user.online,
          avatar_url: user.avatar_url,
          isFriend: friendIds.includes(user.id),
          created_at: user.created_at,
          updated_at: user.updated_at,
          games_played: user.games_played || 0,
          games_won: user.games_won || 0,
        }));

      setSearchResults(formattedResults);
    } catch (error) {
      console.error("Error searching users:", error);
      toast({
        title: "Errore",
        description: "Errore durante la ricerca utenti",
        variant: "destructive",
      });
    } finally {
      setSearching(false);
    }
  };

  const sendFriendRequest = async (targetUserId: string) => {
    if (!authUser) return;

    try {
      // 🔥 FETCH DIRETTA: Sostituisce safeDbCall per evitare blocchi
      const supabase = await getSupabaseClient();
      const { error } = await supabase.from("friend_requests").insert({
        sender_id: authUser.id,
        receiver_id: targetUserId,
        status: "pending",
      });

      if (error) throw error;

      toast({
        title: "Richiesta inviata",
        description: "Richiesta di amicizia inviata con successo",
      });

      // Remove from search results
      setSearchResults((prev) => prev.filter((u) => u.id !== targetUserId));
    } catch (error) {
      console.error("Error sending friend request:", error);
      toast({
        title: "Errore",
        description: "Impossibile inviare la richiesta di amicizia",
        variant: "destructive",
      });
    }
  };

  // Funzioni per gestire la modale del profilo
  const handlePlayerClick = (player: FriendData) => {
    setSelectedPlayer(player);
    setIsProfileModalOpen(true);
  };

  const handleCloseProfileModal = () => {
    setIsProfileModalOpen(false);
    setSelectedPlayer(null);
  };

  // Funzione per gestire l'aggiunta amico dalla modale
  const handleAddFriendFromModal = async (playerId: string) => {
    if (onAddFriend) {
      await onAddFriend(playerId);
      // Rimuovi dal risultato della ricerca dopo aver aggiunto l'amico
      setSearchResults((prev) => prev.filter((u) => u.id !== playerId));
      handleCloseProfileModal();
    } else {
      await sendFriendRequest(playerId);
      setSearchResults((prev) => prev.filter((u) => u.id !== playerId));
      handleCloseProfileModal();
    }
  };

  return (
    <>
      {/* Barra di ricerca */}
      <Card className="border-2 border-amber-800/30 shadow-md bg-amber-50/80 backdrop-blur-sm">
        <CardContent className="p-4">
          <div className="flex gap-2">
            <Input
              placeholder="Cerca utenti per nome..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && searchUsers()}
              className="flex-1"
            />
            <Button
              onClick={searchUsers}
              disabled={searching || !searchQuery.trim()}
              className="bg-romagna-rust hover:bg-romagna-terracotta text-white"
            >
              {searching ? (
                <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
              ) : (
                <Search className="h-4 w-4" />
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Risultati della ricerca */}
      {searchResults.length > 0 && (
        <div className="space-y-2">
          <h3
            className="font-semibold text-romagna-darkWood ml-1 mt-4"
            style={{
              fontFamily: "'DynaPuff', cursive",
              fontWeight: 500,
            }}
          >
            Risultati ricerca
          </h3>
          <div className="space-y-3">
            {searchResults.map((user) => (
              <Card
                key={user.id}
                onClick={() => handlePlayerClick(user)}
                className="border-2 border-amber-800/30 shadow-md bg-amber-50/80 backdrop-blur-sm cursor-pointer transition-all duration-200 hover:scale-[1.01] hover:shadow-lg hover:bg-amber-100/80 mx-1"
              >
                <CardContent className="p-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1">
                      {/* Immagine del rank a sinistra */}
                      <div className="relative">
                        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-amber-100 to-yellow-100 flex items-center justify-center border-2 border-amber-300">
                          <img
                            src={getPlayerTitle(user.level).rankImage}
                            alt={getPlayerTitle(user.level).title}
                            className="w-8 h-8 object-contain"
                          />
                        </div>
                      </div>
                      <div>
                        <div className="text-md font-semibold text-romagna-darkWood flex items-center gap-2">
                          {user.username}
                          {/* {user.isFriend && (
                            <span className="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded-full ml-1 border border-green-300">
                              già amico
                            </span>
                          )} */}
                        </div>
                        <p className="text-sm text-romagna-darkWood/70">
                          Livello {user.level} • XP: {user.xp.toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Modale profilo giocatore */}
      <PlayerProfileModal
        player={
          selectedPlayer
            ? {
                id: selectedPlayer.id,
                username: selectedPlayer.username,
                level: selectedPlayer.level,
                xp: selectedPlayer.xp,
                games_won: selectedPlayer.games_won || 0,
                games_played: selectedPlayer.games_played || 0,
                win_rate:
                  selectedPlayer.games_played > 0
                    ? Math.round(
                        ((selectedPlayer.games_won || 0) /
                          selectedPlayer.games_played) *
                          100
                      )
                    : 0,

                created_at: selectedPlayer.created_at,
                last_active: selectedPlayer.updated_at,
              }
            : null
        }
        isOpen={isProfileModalOpen}
        onClose={handleCloseProfileModal}
        currentUserId={authUser?.id}
        isFriend={false} // I risultati della ricerca non possono essere già amici
        hasPendingRequest={
          selectedPlayer && hasPendingRequestFor
            ? hasPendingRequestFor(selectedPlayer.id)
            : false
        }
        onAddFriend={handleAddFriendFromModal}
      />
    </>
  );
};

export default UserSearch;

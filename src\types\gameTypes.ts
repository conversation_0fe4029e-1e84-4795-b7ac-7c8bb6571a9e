import { ActionType } from "@/components/GameActionBadge";
import { Card as CardType, Suit } from "@/utils/game/cardUtils";
import { GameState } from "@/utils/game/gameLogic";

export type ActionAnnouncementType = {
  type: ActionType;
  playerIndex: number;
};

export type TrumpAnnouncementType = {
  suit: string | null;
  playerName: string;
  visible: boolean;
};

export type PlayerPosition = "bottom" | "left" | "top" | "right";

export interface GameBoardProps {
  gameState: GameState;
  displayCards: CardType[];
  lastPlayedCard: CardType | null;
  getPlayedCardPosition: (trickIndex: number) => {
    className: string;
    zIndex: number;
  };
  getPlayerTeam: (index: number) => number | undefined;
  trumpAnnouncement: TrumpAnnouncementType;
  actionAnnouncement: ActionAnnouncementType;
  getTrumpSuitImage: () => string;
  getTrumpSuitName: () => string;
  isMobile: boolean;
  isDesktopLayout: boolean;
}

export interface GameControlsProps {
  phase: string;
  isCurrentPlayerTurn: boolean;
  onSelectTrump: (suit: string) => void;
  onAnnounceAction: (action: string) => void;
  onDeclareWin: (teamId: number) => void;
  onStartNewRound: () => void;
  onStartNewGame: () => void;
  onReturnToMenu: () => void;
  message: string;
  currentPlayerTeam: number;
  isTrickStart: boolean;
  gameScore: number[];
  roundScore: number[];
  isMobile: boolean;
  canDeclareVictory: boolean;
  isOnlineMode: boolean;
}

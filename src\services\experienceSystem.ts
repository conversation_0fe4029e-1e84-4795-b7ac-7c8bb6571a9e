// Sistema di esperienza bilanciato per Marafone Romagnolo
// Questo file definisce tutte le costanti e funzioni per il sistema di progressione

// ========== CONFIGURAZIONE XP PER DIFFICOLTÀ E RISULTATO ==========

/**
 * Tabella XP basata su difficoltà e risultato
 * Struttura: [vittoria, sconfitta] per ogni difficoltà
 */
export const XP_TABLE = {
  easy: {
    victory: 40, // Aumentato da 30 - Base XP per vittoria facile
    defeat: 12, // Aumentato da 8 - XP consolazione per sconfitta facile
  },
  medium: {
    victory: 60, // Aumentato da 50 - +50% rispetto a facile
    defeat: 20, // Aumentato da 15 - +67% rispetto a facile
  },
  hard: {
    victory: 85, // Leggermente aumentato da 80 - molto più gratificante
    defeat: 30, // Aumentato da 25 - anche le sconfitte valgono di più
  },
} as const;

/**
 * Bonus XP aggiuntivi per situazioni speciali
 */
export const XP_BONUSES = {
  maraffa: 15, // Bonus per ogni maraffa realizzata
  perfectGame: 20, // Bonus per vittoria con 0 punti avversari
  comeback: 10, // Bonus per vittoria da situazione di svantaggio
  dominantWin: 15, // 🎯 NUOVO: Bonus per vittoria dominante (>10 punti di differenza)
  firstWinOfDay: 25, // Bonus giornaliero per prima vittoria
  winStreak: {
    // Bonus per serie di vittorie consecutive
    3: 15,
    5: 30,
    10: 60,
  },
} as const;

// ========== CONFIGURAZIONE LIVELLI E PROGRESSIONE ==========

/**
 * Configurazione della progressione livelli
 *
 * PROGRESSIONE INFINITA: Non esiste un livello massimo!
 * I giocatori possono continuare a salire di livello all'infinito.
 *
 * Formula: XP_richiesto = baseXpForLevel * (growthFactor^(level-1)) + (linearGrowth * level)
 *
 * Questa formula ibrida garantisce:
 * - Crescita inizialmente moderata per i primi livelli
 * - Crescita più significativa per livelli intermedi
 * - Progressione comunque possibile anche a livelli molto alti
 */
export const LEVEL_PROGRESSION = {
  // XP base per il primo livello (ridotto per livellamento più veloce)
  baseXpForLevel: 60,

  // Fattore di crescita per livello (ridotto per progressione più rapida nei primi livelli)
  growthFactor: 1.12,

  // XP aggiuntivo fisso per livello (ridotto per livellamento più veloce)
  linearGrowth: 30,
} as const;

// ========== FUNZIONI DI CALCOLO ==========

/**
 * Calcola l'XP necessario per raggiungere un livello specifico
 */
export const getXpRequiredForLevel = (level: number): number => {
  if (level <= 1) return 0;

  let totalXp = 0;
  for (let i = 2; i <= level; i++) {
    // Formula ibrida: crescita esponenziale + crescita lineare
    const exponentialPart = Math.floor(
      LEVEL_PROGRESSION.baseXpForLevel *
        Math.pow(LEVEL_PROGRESSION.growthFactor, i - 2)
    );
    const linearPart = LEVEL_PROGRESSION.linearGrowth * (i - 2);

    totalXp += exponentialPart + linearPart;
  }

  return totalXp;
};

/**
 * Calcola l'XP necessario per passare al livello successivo
 */
export const getXpForNextLevel = (currentLevel: number): number => {
  const currentLevelXp = getXpRequiredForLevel(currentLevel);
  const nextLevelXp = getXpRequiredForLevel(currentLevel + 1);
  return nextLevelXp - currentLevelXp;
};

/**
 * Calcola il livello basato sull'XP totale
 *
 * PROGRESSIONE INFINITA: Questa funzione supporta livelli illimitati.
 * Include un safeguard a 10.000 livelli per prevenire loop infiniti
 * in caso di errori di calcolo, ma in pratica è praticamente impossibile
 * raggiungere questo limite con il gameplay normale.
 */
export const calculateLevelFromXp = (totalXp: number): number => {
  if (totalXp <= 0) return 1;

  let level = 1;
  // Rimuoviamo il limite massimo - progressione infinita
  while (getXpRequiredForLevel(level + 1) <= totalXp) {
    level++;
    // Safeguard per evitare loop infiniti in caso di errori di calcolo
    // 10.000 livelli richiederebbero miliardi di XP - praticamente irraggiungibile
    if (level > 10000) {
      console.warn("Livello troppo alto raggiunto, limitando a 10000");
      break;
    }
  }

  return level;
};

/**
 * Calcola la percentuale di progresso verso il livello successivo
 */
export const getProgressToNextLevel = (
  totalXp: number,
  currentLevel: number
): number => {
  // Non c'è più un livello massimo - progressione infinita
  const currentLevelXp = getXpRequiredForLevel(currentLevel);
  const nextLevelXp = getXpRequiredForLevel(currentLevel + 1);
  const progressXp = totalXp - currentLevelXp;
  const requiredXp = nextLevelXp - currentLevelXp;

  return Math.min(Math.floor((progressXp / requiredXp) * 100), 100);
};

/**
 * Calcola quante partite servono approssimativamente per il livello successivo
 */
export const getEstimatedGamesForNextLevel = (
  totalXp: number,
  currentLevel: number,
  averageDifficulty: "easy" | "medium" | "hard" = "medium",
  winRate: number = 0.5
): number => {
  // Non c'è più un livello massimo - sempre possibile salire di livello
  const currentLevelXp = getXpRequiredForLevel(currentLevel);
  const nextLevelXp = getXpRequiredForLevel(currentLevel + 1);
  const xpNeeded = nextLevelXp - totalXp;

  // Calcola XP medio per partita basato su difficoltà e win rate
  const victoryXp = XP_TABLE[averageDifficulty].victory;
  const defeatXp = XP_TABLE[averageDifficulty].defeat;
  const averageXpPerGame = victoryXp * winRate + defeatXp * (1 - winRate);

  return Math.ceil(xpNeeded / averageXpPerGame);
};

// ========== FUNZIONI DI CALCOLO XP PER PARTITA ==========

/**
 * Calcola l'XP guadagnato per una partita specifica
 */
export const calculateGameXp = (params: {
  isWinner: boolean;
  difficulty: "easy" | "medium" | "hard";
  maraffeMade?: number;
  isPerfectGame?: boolean;
  isComeback?: boolean;
  isDominantWin?: boolean; // 🎯 NUOVO: Parametro per vittoria dominante
  isFirstWinOfDay?: boolean;
  currentWinStreak?: number;
}): {
  baseXp: number;
  bonusXp: number;
  totalXp: number;
  breakdown: string[];
} => {
  const breakdown: string[] = [];

  // XP base per risultato e difficoltà
  const baseXp = params.isWinner
    ? XP_TABLE[params.difficulty].victory
    : XP_TABLE[params.difficulty].defeat;

  // 🔧 NOMI DIFFICOLTÀ CORRETTI
  const difficultyNames = {
    easy: "Principiante",
    medium: "Esperto",
    hard: "Maestro",
  };

  breakdown.push(
    `${params.isWinner ? "Vittoria" : "Sconfitta"} (${
      difficultyNames[params.difficulty]
    }): +${baseXp} XP`
  );

  let bonusXp = 0;

  // Bonus per maraffe
  if (params.maraffeMade && params.maraffeMade > 0) {
    const maraffaBonus = params.maraffeMade * XP_BONUSES.maraffa;
    bonusXp += maraffaBonus;
    breakdown.push(`Maraffa (${params.maraffeMade}x): +${maraffaBonus} XP`);
  }

  // Bonus per vittoria perfetta
  if (params.isPerfectGame && params.isWinner) {
    bonusXp += XP_BONUSES.perfectGame;
    breakdown.push(`Vittoria perfetta: +${XP_BONUSES.perfectGame} XP`);
  }
  // Bonus per comeback
  if (params.isComeback && params.isWinner) {
    bonusXp += XP_BONUSES.comeback;
    breakdown.push(`Rimonta: +${XP_BONUSES.comeback} XP`);
  }

  // 🎯 NUOVO: Bonus per vittoria dominante
  if (params.isDominantWin && params.isWinner) {
    bonusXp += XP_BONUSES.dominantWin;
    breakdown.push(`Vittoria dominante: +${XP_BONUSES.dominantWin} XP`);
  }

  // Bonus prima vittoria del giorno
  if (params.isFirstWinOfDay && params.isWinner) {
    bonusXp += XP_BONUSES.firstWinOfDay;
    breakdown.push(
      `Prima vittoria del giorno: +${XP_BONUSES.firstWinOfDay} XP`
    );
  }

  // Bonus serie di vittorie
  if (params.currentWinStreak && params.isWinner) {
    const streakBonus = getWinStreakBonus(params.currentWinStreak);
    if (streakBonus > 0) {
      bonusXp += streakBonus;
      breakdown.push(
        `Serie di ${params.currentWinStreak} vittorie: +${streakBonus} XP`
      );
    }
  }

  const totalXp = baseXp + bonusXp;

  return {
    baseXp,
    bonusXp,
    totalXp,
    breakdown,
  };
};

/**
 * Calcola il bonus per serie di vittorie consecutive
 */
export const getWinStreakBonus = (winStreak: number): number => {
  if (winStreak >= 10) return XP_BONUSES.winStreak[10];
  if (winStreak >= 5) return XP_BONUSES.winStreak[5];
  if (winStreak >= 3) return XP_BONUSES.winStreak[3];
  return 0;
};

// ========== UTILITY FUNCTIONS ==========

/**
 * Genera una tabella dei livelli con XP richiesti (per debug/visualizzazione)
 */
export const generateLevelTable = (
  maxLevel: number = 20
): Array<{
  level: number;
  totalXp: number;
  xpForThisLevel: number;
  estimatedGamesEasy: number;
  estimatedGamesMedium: number;
  estimatedGamesHard: number;
}> => {
  const table = [];

  for (let level = 1; level <= maxLevel; level++) {
    const totalXp = getXpRequiredForLevel(level);
    const xpForThisLevel = level === 1 ? 0 : getXpForNextLevel(level - 1);

    // Stima partite necessarie per raggiungere questo livello (con 50% win rate)
    const estimatedGamesEasy = Math.ceil(
      xpForThisLevel / ((XP_TABLE.easy.victory + XP_TABLE.easy.defeat) / 2)
    );
    const estimatedGamesMedium = Math.ceil(
      xpForThisLevel / ((XP_TABLE.medium.victory + XP_TABLE.medium.defeat) / 2)
    );
    const estimatedGamesHard = Math.ceil(
      xpForThisLevel / ((XP_TABLE.hard.victory + XP_TABLE.hard.defeat) / 2)
    );

    table.push({
      level,
      totalXp,
      xpForThisLevel,
      estimatedGamesEasy,
      estimatedGamesMedium,
      estimatedGamesHard,
    });
  }

  return table;
};

/**
 * Verifica se è stata raggiunta la prima vittoria del giorno
 */
export const isFirstWinOfDay = (lastWinDate?: string): boolean => {
  if (!lastWinDate) return true;

  const today = new Date().toDateString();
  const lastWin = new Date(lastWinDate).toDateString();

  return today !== lastWin;
};

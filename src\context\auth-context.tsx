import React, {
  create<PERSON>ontext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import { Session } from "@supabase/supabase-js";
import type { SupabaseClient } from "@supabase/supabase-js";

// Types
export interface Profile {
  id: string;
  email?: string;
  username: string;
  created_at: string;
  updated_at: string;
}

export interface UserStats {
  id?: string;
  user_id?: string;
  games_played: number;
  games_won: number;
  level: number;
  xp: number;
  created_at?: string;
  updated_at?: string;
  gamesPlayed: number;
  gamesWon: number;
}

export interface Friend {
  id: string;
  user_id: string;
  friend_id: string;
  created_at: string;
  friend: {
    id: string;
    username: string;
    avatar_url?: string | null;
    level: number;
    xp?: number;
    games_won?: number;
    games_played?: number;
    win_rate?: number;
    created_at?: string;
    updated_at?: string;
  };
}

export interface FriendRequest {
  id: string;
  sender_id: string;
  receiver_id: string;
  created_at: string;
  status: "pending" | "accepted" | "rejected";
  sender: {
    id: string;
    username: string;
    avatar_url?: string | null;
  };
}

export interface GameInvite {
  id: string;
  sender_id: string;
  receiver_id: string;
  created_at: string;
  updated_at: string;
  status: "pending" | "accepted" | "rejected";
  sender: {
    id: string;
    username: string;
    avatar_url?: string | null;
  };
}

export interface LeaderboardPlayer {
  id: string;
  username: string;
  level: number;
  xp: number;
  games_won: number;
  games_played: number;
  win_rate: number;
  position?: number;
  avatar_url?: string | null;
  created_at?: string;
  updated_at?: string;
}

export interface SearchablePlayer {
  id: string;
  username: string;
  level: number;
  xp: number;
  online: boolean;
  avatar_url?: string | null;
  created_at?: string;
  updated_at?: string;
  games_played?: number;
  games_won?: number;
}

interface AuthContextType {
  session: Session | null;
  user: Profile | null;
  userStats: UserStats | null;
  isLoading: boolean;
  isLoggedIn: boolean;
  isOnlineFeatureEnabled: boolean;
  signIn: (email: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  fetchUserProfile: () => Promise<void>;
  updateProfile: ({ username }: { username: string }) => Promise<void>;
  updateEmail: (email: string) => Promise<void>;
  updatePassword: (
    currentPassword: string,
    newPassword: string
  ) => Promise<void>;
  login: (
    email: string,
    password: string
  ) => Promise<{ success: boolean; error?: string }>;
  register: (
    email: string,
    password: string,
    username: string
  ) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  checkUsernameExists: (username: string) => Promise<boolean>;
  getFriends: (forceRefresh?: boolean) => Promise<Friend[]>;
  getFriendRequests: () => Promise<FriendRequest[]>;
  getSentFriendRequests: () => Promise<FriendRequest[]>;
  invalidateFriendsCache: () => void;
  acceptFriendRequest: (requestId: string) => Promise<boolean>;
  rejectFriendRequest: (requestId: string) => Promise<boolean>;
  sendFriendRequest: (userId: string) => Promise<boolean>;
  removeFriend: (friendId: string) => Promise<boolean>;
  getGameInvites: () => Promise<GameInvite[]>;
  acceptGameInvite: (inviteId: string) => Promise<boolean>;
  rejectGameInvite: (inviteId: string) => Promise<boolean>;
  sendGameInvite: (friendId: string) => Promise<boolean>;
  fetchUserStats: (userId: string) => Promise<void>;
  debugSessionState: () => void;
  getSupabaseClient: () => Promise<SupabaseClient>;
  getLeaderboard: (
    forceRefresh?: boolean,
    offset?: number,
    limit?: number
  ) => Promise<LeaderboardPlayer[]>;
  searchPlayers: (
    query: string,
    forceRefresh?: boolean
  ) => Promise<SearchablePlayer[]>;
  invalidateLeaderboardCache: () => void;
  invalidateSearchCache: () => void;
  getCachedFriends: () => { data: Friend[] | null; isValid: boolean };
  getCachedFriendRequests: () => {
    data: FriendRequest[] | null;
    isValid: boolean;
  };
  getCachedSentFriendRequests: () => {
    data: FriendRequest[] | null;
    isValid: boolean;
  };
  apiCallWithAuth: (url: string, options?: RequestInit) => Promise<any>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<Profile | null>(null);
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const isOnlineFeatureEnabled = true;

  // 🎯 FUNZIONE DI RIPRISTINO SESSIONE ROBUSTA
  const attemptSessionRecovery = async (): Promise<any> => {
    console.log("🔄 Tentativo ripristino sessione...");

    try {
      const { supabase, sessionManager } = await import(
        "@/integrations/supabase/client"
      );

      // Strategia 1: Usa SessionManager per refresh intelligente
      try {
        console.log("🔄 Tentativo 1: SessionManager refresh...");
        const recoveredSession = await sessionManager.ensureValidSession();
        if (recoveredSession?.access_token) {
          console.log("✅ Sessione ripristinata tramite SessionManager");
          return recoveredSession;
        }
      } catch (managerError) {
        console.warn("⚠️ SessionManager fallito:", managerError);
      }

      // Strategia 2: Refresh diretto con timeout più lungo
      try {
        console.log("🔄 Tentativo 2: Refresh diretto...");
        const refreshResult = await Promise.race([
          supabase.auth.refreshSession(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error("Refresh timeout esteso")), 15000)
          ),
        ]);
        const { data, error } = refreshResult as any;

        if (data?.session && !error) {
          console.log("✅ Sessione ripristinata tramite refresh diretto");
          return data.session;
        }
      } catch (refreshError) {
        console.warn("⚠️ Refresh diretto fallito:", refreshError);
      }

      // Strategia 3: Controllo sessione esistente
      try {
        console.log("🔄 Tentativo 3: Controllo sessione esistente...");
        const {
          data: { session },
          error,
        } = await supabase.auth.getSession();
        if (session?.access_token && !error) {
          console.log("✅ Sessione esistente ancora valida");
          return session;
        }
      } catch (sessionError) {
        console.warn("⚠️ Controllo sessione fallito:", sessionError);
      }

      console.error("❌ Tutti i tentativi di ripristino falliti");
      return null;
    } catch (error) {
      console.error("❌ Errore durante ripristino sessione:", error);
      return null;
    }
  };

  // Cache per amici e richieste - durata 5 minuti
  const [friendsCache, setFriendsCache] = useState<{
    data: Friend[] | null;
    timestamp: number;
  }>({ data: null, timestamp: 0 });

  const [friendRequestsCache, setFriendRequestsCache] = useState<{
    data: FriendRequest[] | null;
    timestamp: number;
  }>({ data: null, timestamp: 0 });

  const [sentFriendRequestsCache, setSentFriendRequestsCache] = useState<{
    data: FriendRequest[] | null;
    timestamp: number;
  }>({ data: null, timestamp: 0 });

  // Cache per classifiche - durata 10 minuti
  const [leaderboardCache, setLeaderboardCache] = useState<{
    data: LeaderboardPlayer[] | null;
    timestamp: number;
  }>({ data: null, timestamp: 0 });

  // Cache per ricerca giocatori - durata 2 minuti (più corta perché i risultati cambiano spesso)
  const [searchCache, setSearchCache] = useState<{
    query: string;
    data: SearchablePlayer[] | null;
    timestamp: number;
  }>({ query: "", data: null, timestamp: 0 });

  const CACHE_DURATION = 5 * 60 * 1000; // 5 minuti
  const LEADERBOARD_CACHE_DURATION = 10 * 60 * 1000; // 10 minuti
  const SEARCH_CACHE_DURATION = 2 * 60 * 1000; // 2 minuti
  const ERROR_CACHE_DURATION = 30 * 1000; // 30 secondi per cache in caso di errore

  // Controllo proattivo validità sessione
  useEffect(() => {
    if (!isOnlineFeatureEnabled || !session) {
      console.log("💔 Controllo sessione SKIP - condizioni non soddisfatte:", {
        isOnlineFeatureEnabled,
        hasSession: !!session,
      });
      return;
    }

    // Controlla immediatamente se la sessione è valida
    const checkSessionValidity = async () => {
      const now = Date.now();
      const expiresAt = session.expires_at ? session.expires_at * 1000 : 0;
      const minutesUntilExpiry = Math.round((expiresAt - now) / 60000);

      console.log(
        `🔍 Controllo sessione: scade tra ${minutesUntilExpiry} minuti`
      );

      // Se scade tra meno di 5 minuti, refresha proattivamente
      if (expiresAt - now < 5 * 60 * 1000) {
        console.log("🔄 Sessione vicina alla scadenza, refresh proattivo...");
        try {
          const { supabase } = await import("@/integrations/supabase/client");
          const { data, error } = await supabase.auth.refreshSession();

          if (error || !data?.session) {
            console.error("❌ Refresh proattivo fallito:", error);
            return;
          }

          console.log("✅ Refresh proattivo completato");
          setSession(data.session);
        } catch (error) {
          console.error("❌ Errore refresh proattivo:", error);
        }
      }
    };

    checkSessionValidity();

    // Gestione visibilità DISABILITATA per evitare reset profilo
    const handleVisibilityChange = () => {
      if (!document.hidden && session?.access_token) {
        console.log("👁️ App tornata in foreground, controllo sessione...");

        const now = Date.now();
        const expiresAt = session.expires_at ? session.expires_at * 1000 : 0;
        const minutesUntilExpiry = Math.round((expiresAt - now) / 60000);

        console.log(`🔍 Sessione info: scade tra ${minutesUntilExpiry} minuti`);

        // 🔄 CONTROLLO ATTIVO SOLO SE SESSIONE VICINA ALLA SCADENZA
        if (minutesUntilExpiry < 5 && minutesUntilExpiry > 0) {
          console.log(
            `⚠️ Sessione scade tra ${minutesUntilExpiry} minuti - refresh necessario`
          );
          checkSessionValidity();
        } else if (minutesUntilExpiry <= 0) {
          console.log("❌ Sessione scaduta, ma NESSUN LOGOUT AUTOMATICO");
          // DISABILITATO: signOut(); - L'utente deve fare logout manualmente
        } else {
          console.log("✅ Sessione ancora valida, nessun controllo necessario");
        }
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [session, isOnlineFeatureEnabled, setSession]);

  // Debug dello stato della sessione con test integrati
  const debugSessionState = useCallback(async () => {
    if (!session) {
      console.log("🔍 DEBUG: Nessuna sessione attiva");
      return;
    }

    const expiresAt = session.expires_at ? session.expires_at * 1000 : 0;
    const now = Date.now();
    const timeUntilExpiry = expiresAt - now;

    console.log("🔍 DEBUG Sessione:", {
      isValid: timeUntilExpiry > 0,
      expiresIn: Math.round(timeUntilExpiry / 60000) + " minuti",
      userId: session.user?.id,
      accessToken: session.access_token ? "presente" : "mancante",
      refreshToken: session.refresh_token ? "presente" : "mancante",
    });

    // Esegui test rapido del SessionManager
    try {
      const { quickSessionTest } = await import("@/utils/sessionTest");
      await quickSessionTest();
    } catch (error) {
      console.error("❌ Errore test sessione:", error);
    }
  }, [session]);

  // Funzione di test completo del sistema sessione (disponibile globalmente)
  const testSessionSystem = useCallback(async () => {
    try {
      const { runAllSessionTests } = await import("@/utils/sessionTest");
      await runAllSessionTests();
    } catch (error) {
      console.error("❌ Errore test sistema sessione:", error);
    }
  }, []);

  // Esponi funzioni di test globalmente per debug (solo in development)
  useEffect(() => {
    if (import.meta.env.DEV) {
      (window as any).testSessionSystem = testSessionSystem;
      (window as any).debugSessionState = debugSessionState;

      // Aggiungi test specifico per il problema degli 8 minuti
      (window as any).testApiCallAfterTime = async () => {
        try {
          const { testApiCallAfterTime } = await import("@/utils/sessionTest");
          return await testApiCallAfterTime();
        } catch (error) {
          console.error("❌ Errore test API call:", error);
          return false;
        }
      };

      // Aggiungi test diretto senza SessionManager
      (window as any).testDirectApiCall = async () => {
        try {
          const { testDirectApiCall } = await import("@/utils/sessionTest");
          return await testDirectApiCall();
        } catch (error) {
          console.error("❌ Errore test diretto:", error);
          return false;
        }
      };

      // Aggiungi test super semplificato
      (window as any).testSimpleApiCall = async () => {
        try {
          const { testSimpleApiCall } = await import("@/utils/sessionTest");
          return await testSimpleApiCall();
        } catch (error) {
          console.error("❌ Errore test semplice:", error);
          return false;
        }
      };

      // Aggiungi test refresh sessione
      (window as any).testSessionRefresh = async () => {
        try {
          const { testSessionRefresh } = await import("@/utils/sessionTest");
          return await testSessionRefresh();
        } catch (error) {
          console.error("❌ Errore test refresh:", error);
          return false;
        }
      };

      console.log(
        "🔧 Funzioni debug disponibili: testDirectApiCall(), testSimpleApiCall(), testSessionRefresh()"
      );
    }
  }, [testSessionSystem, debugSessionState]);

  // Funzione per invalidare la cache degli amici
  const invalidateFriendsCache = useCallback(() => {
    setFriendsCache({ data: null, timestamp: 0 });
    setFriendRequestsCache({ data: null, timestamp: 0 });
    setSentFriendRequestsCache({ data: null, timestamp: 0 });
    console.log("🗑️ Cache amici e richieste invalidata");
  }, []);

  // Funzione per invalidare la cache delle classifiche
  const invalidateLeaderboardCache = useCallback(() => {
    setLeaderboardCache({ data: null, timestamp: 0 });
    console.log("🗑️ Cache classifiche invalidata");
  }, []);

  // Funzione per invalidare la cache delle ricerche
  const invalidateSearchCache = useCallback(() => {
    setSearchCache({ query: "", data: null, timestamp: 0 });
    console.log("🗑️ Cache ricerche invalidata");
  }, []);

  // Funzioni per accesso DIRETTO alla cache senza triggerare fetch
  const getCachedFriends = useCallback(() => {
    const cacheAge =
      friendsCache.timestamp > 0
        ? Date.now() - friendsCache.timestamp
        : Infinity;

    // Se la cache è molto vecchia o vuota dopo un errore, considerala non valida
    const effectiveCacheDuration =
      friendsCache.data &&
      friendsCache.data.length === 0 &&
      cacheAge < ERROR_CACHE_DURATION
        ? ERROR_CACHE_DURATION // Cache più breve per errori
        : CACHE_DURATION;

    const isValid =
      friendsCache.data !== null &&
      friendsCache.timestamp > 0 &&
      cacheAge < effectiveCacheDuration;

    console.log(
      `🔍 getCachedFriends: cache età ${
        friendsCache.timestamp > 0 ? Math.round(cacheAge / 60000) : "N/A"
      }min, valida: ${isValid}, dati: ${
        friendsCache.data?.length || 0
      }, durata: ${Math.round(effectiveCacheDuration / 60000)}min`
    );

    return {
      data: isValid ? friendsCache.data : null,
      isValid,
    };
  }, [friendsCache, CACHE_DURATION, ERROR_CACHE_DURATION]);

  const getCachedFriendRequests = useCallback(() => {
    const cacheAge =
      friendRequestsCache.timestamp > 0
        ? Date.now() - friendRequestsCache.timestamp
        : Infinity;
    const isValid =
      friendRequestsCache.data !== null &&
      friendRequestsCache.timestamp > 0 &&
      cacheAge < CACHE_DURATION;

    console.log(
      `🔍 getCachedFriendRequests: cache età ${
        friendRequestsCache.timestamp > 0 ? Math.round(cacheAge / 60000) : "N/A"
      }min, valida: ${isValid}, dati: ${friendRequestsCache.data?.length || 0}`
    );

    return {
      data: isValid ? friendRequestsCache.data : null,
      isValid,
    };
  }, [friendRequestsCache, CACHE_DURATION]);

  const getCachedSentFriendRequests = useCallback(() => {
    const cacheAge =
      sentFriendRequestsCache.timestamp > 0
        ? Date.now() - sentFriendRequestsCache.timestamp
        : Infinity;
    const isValid =
      sentFriendRequestsCache.data !== null &&
      sentFriendRequestsCache.timestamp > 0 &&
      cacheAge < CACHE_DURATION;

    console.log(
      `🔍 getCachedSentFriendRequests: cache età ${
        sentFriendRequestsCache.timestamp > 0
          ? Math.round(cacheAge / 60000)
          : "N/A"
      }min, valida: ${isValid}, dati: ${
        sentFriendRequestsCache.data?.length || 0
      }`
    );

    return {
      data: isValid ? sentFriendRequestsCache.data : null,
      isValid,
    };
  }, [sentFriendRequestsCache, CACHE_DURATION]);

  // Client Supabase lazy loading
  const getSupabaseClient = useCallback(async () => {
    if (!isOnlineFeatureEnabled) {
      throw new Error("Online features are disabled");
    }
    const { supabase } = await import("@/integrations/supabase/client");
    return supabase;
  }, [isOnlineFeatureEnabled]);

  // Funzione helper con controllo validità sessione e refresh automatico migliorato
  const apiCallWithAuth = useCallback(
    async (url: string, options: RequestInit = {}) => {
      console.log("🚀 apiCallWithAuth: controllo validità sessione...");

      // Controlla se la sessione è valida
      if (!session?.access_token) {
        console.log("❌ Nessuna sessione disponibile, tentativo refresh...");
        const refreshedSession = await attemptSessionRecovery();
        if (!refreshedSession) {
          throw new Error("Nessuna sessione disponibile e refresh fallito");
        }
        setSession(refreshedSession);
      }

      // Controlla se il token è scaduto o sta per scadere (5 minuti prima)
      const now = Date.now();
      const expiresAt = session?.expires_at ? session.expires_at * 1000 : 0;
      const fiveMinutesFromNow = now + 5 * 60 * 1000; // 5 minuti in millisecondi

      if (expiresAt <= fiveMinutesFromNow) {
        console.log(
          "🔄 Token scaduto o in scadenza, tentativo refresh preventivo..."
        );
        try {
          // 🎯 NUOVO SISTEMA DI RIPRISTINO SESSIONE
          const newSession = await attemptSessionRecovery();
          if (newSession) {
            console.log("✅ Sessione ripristinata con successo");
            setSession(newSession);
            // Usa il nuovo token
            const response = await fetch(url, {
              ...options,
              headers: {
                ...options.headers,
                Authorization: `Bearer ${newSession.access_token}`,
              },
            });
            return response;
          } else {
            throw new Error("Impossibile ripristinare la sessione");
          }
        } catch (recoveryError) {
          console.error("❌ Ripristino sessione fallito:", recoveryError);
          throw new Error("Sessione scaduta e ripristino fallito");
        }
      }

      // Token ancora valido, usa quello corrente
      console.log("✅ Token valido, procedo con chiamata");
      const response = await fetch(url, {
        ...options,
        headers: {
          ...options.headers,
          apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
          Authorization: `Bearer ${session.access_token}`,
          "Content-Type": "application/json",
          Prefer: "return=representation",
        },
      });

      if (!response.ok) {
        // Se otteniamo 401, prova a refreshare una volta
        if (response.status === 401) {
          console.log("🔄 Ricevuto 401, tentativo ripristino sessione...");
          try {
            // 🎯 USA IL SISTEMA DI RIPRISTINO ROBUSTO
            const newSession = await attemptSessionRecovery();
            if (!newSession) {
              throw new Error("Ripristino sessione fallito dopo 401");
            }

            console.log("✅ Sessione ripristinata dopo 401");
            setSession(newSession);

            // Riprova la chiamata con il nuovo token
            const retryResponse = await fetch(url, {
              ...options,
              headers: {
                ...options.headers,
                apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
                Authorization: `Bearer ${newSession.access_token}`,
                "Content-Type": "application/json",
                Prefer: "return=representation",
              },
            });

            if (!retryResponse.ok) {
              throw new Error(
                `HTTP ${retryResponse.status}: ${retryResponse.statusText}`
              );
            }

            return retryResponse.json();
          } catch (refreshError) {
            console.error("❌ Refresh fallito dopo 401:", refreshError);
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
        }

        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response.json();
    },
    [session, setSession]
  );

  // Inizializzazione autenticazione
  useEffect(() => {
    if (!isOnlineFeatureEnabled) {
      setUser(null);
      setIsLoggedIn(false);
      setSession(null);
      setIsLoading(false);
      return;
    }

    const initializeAuth = async () => {
      try {
        const supabase = await getSupabaseClient();
        const { sessionManager } = await import(
          "@/integrations/supabase/client"
        );

        // 🔄 FUNZIONE HELPER PER CARICARE PROFILO E STATISTICHE
        const loadUserProfile = async (session: any, isInitialLoad = false) => {
          if (!session?.user) {
            setUser(null);
            setIsLoggedIn(false);
            setUserStats(null);
            return;
          }

          try {
            // Carica profilo
            let profileData = null;
            try {
              const { data, error, status } = await supabase
                .from("profiles")
                .select(`username, created_at, updated_at`)
                .eq("id", session.user.id)
                .single();

              if (!error || status === 406) {
                profileData = data;
              }
            } catch (error) {
              console.error("Errore caricamento profilo:", error);
            }

            const dynamicTitle = await getDynamicPlayerTitle();
            const extendedUser: Profile = {
              ...session.user,
              username: profileData?.username || dynamicTitle,
              created_at: profileData?.created_at || new Date().toISOString(),
              updated_at: profileData?.updated_at || new Date().toISOString(),
            };
            setUser(extendedUser);
            setIsLoggedIn(true);

            // Carica statistiche tramite profileService (con cache)
            try {
              const { getActiveProfile, syncStatsOnLogin } = await import(
                "../services/profileService"
              );

              // 🔄 SINCRONIZZAZIONE AL LOGIN: Solo al primo caricamento
              if (isInitialLoad) {
                console.log("🔄 Eseguendo sincronizzazione al login...");
                const syncResult = await syncStatsOnLogin();

                if (syncResult.success) {
                  if (syncResult.conflicts) {
                    console.log("⚠️ Conflitti risolti:", syncResult.conflicts);
                  }
                  console.log("✅ Sincronizzazione completata");
                } else {
                  console.warn(
                    "⚠️ Sincronizzazione fallita, continuo con profilo normale"
                  );
                }
              }

              const activeProfile = await getActiveProfile();

              if (activeProfile.stats) {
                const userStats: UserStats = {
                  user_id: session.user.id,
                  games_played: activeProfile.stats.totalGames,
                  games_won: activeProfile.stats.gamesWon,
                  level: activeProfile.stats.level,
                  xp: activeProfile.stats.xp,
                  created_at: activeProfile.stats.createdAt,
                  updated_at: activeProfile.stats.updatedAt,
                  gamesPlayed: activeProfile.stats.totalGames,
                  gamesWon: activeProfile.stats.gamesWon,
                };
                setUserStats(userStats);
                window.dispatchEvent(new CustomEvent("statsUpdated"));
              } else {
                // Default stats se nessuna statistica trovata
                const defaultStats: UserStats = {
                  games_played: 0,
                  games_won: 0,
                  level: 1,
                  xp: 0,
                  gamesPlayed: 0,
                  gamesWon: 0,
                };
                setUserStats(defaultStats);
              }
            } catch (error) {
              console.error("Errore caricamento statistiche:", error);
            }
          } catch (error) {
            console.error("Errore loadUserProfile:", error);
          }
        };

        const getInitialSession = async () => {
          setIsLoading(true);

          try {
            // Usa il SessionManager per ottenere una sessione valida
            const session = await sessionManager.ensureValidSession();
            setSession(session);

            // Carica profilo e statistiche
            await loadUserProfile(session, true);
          } catch (error) {
            console.error("Errore getInitialSession:", error);
            setUser(null);
            setIsLoggedIn(false);
            setSession(null);
          } finally {
            setIsLoading(false);
          }
        };

        await getInitialSession();

        // 🔄 AVVIA BACKGROUND REFRESH AUTOMATICO
        try {
          sessionManager.startBackgroundRefresh();
          console.log("🔄 Background refresh automatico avviato");
        } catch (error) {
          console.warn("⚠️ Errore avvio background refresh:", error);
        }

        // 🔄 LISTENER PER CAMBIAMENTI DI AUTENTICAZIONE
        const {
          data: { subscription },
        } = supabase.auth.onAuthStateChange(async (event, session) => {
          console.log(`🔐 Auth state change: ${event}`, { session: !!session });

          // 🎯 GESTIONE SPECIFICA PER TOKEN_REFRESHED
          if (event === "TOKEN_REFRESHED") {
            console.log(
              "✅ Token automatically refreshed - aggiornamento sessione"
            );
            setSession(session);

            // 🔄 RICARICA PROFILO DOPO REFRESH TOKEN PER EVITARE PERDITA DATI
            if (session?.user) {
              console.log("🔄 Ricaricamento profilo dopo refresh token...");
              await loadUserProfile(session, false);
            }
            return;
          }

          // 🎯 GESTIONE ALTRI EVENTI
          if (event === "SIGNED_IN" || event === "SIGNED_OUT") {
            setSession(session);
            await loadUserProfile(session, event === "SIGNED_IN");
          } else {
            // Per altri eventi, aggiorna solo la sessione
            setSession(session);
          }
        });

        // 🔄 TIMER PER CONTROLLO PERIODICO SESSIONE (ogni 3 minuti - più frequente)
        const sessionCheckInterval = setInterval(async () => {
          try {
            if (!session?.access_token) {
              console.log("⚠️ Controllo periodico: nessun token disponibile");
              return;
            }

            const now = Date.now();
            const expiresAt = session.expires_at
              ? session.expires_at * 1000
              : 0;
            const minutesUntilExpiry = Math.round((expiresAt - now) / 60000);

            console.log(
              `🔍 Controllo periodico: token scade tra ${minutesUntilExpiry} minuti`
            );

            // Se scade tra meno di 10 minuti, forza un refresh (più conservativo)
            if (minutesUntilExpiry < 10 && minutesUntilExpiry > 0) {
              console.log(
                `🔄 Controllo periodico: token scade tra ${minutesUntilExpiry} minuti, refresh proattivo...`
              );
              try {
                const refreshedSession =
                  await sessionManager.ensureValidSession();
                if (refreshedSession && refreshedSession !== session) {
                  setSession(refreshedSession);
                  console.log("✅ Sessione refreshata dal controllo periodico");

                  // Ricarica il profilo per evitare perdita di dati
                  if (refreshedSession.user) {
                    await loadUserProfile(refreshedSession, false);
                  }
                }
              } catch (refreshError) {
                console.error("❌ Errore refresh proattivo:", refreshError);

                // Retry con fallback diretto se SessionManager fallisce
                try {
                  console.log("🔄 Tentativo fallback refresh diretto...");
                  const { data, error } = await supabase.auth.refreshSession();
                  if (data?.session && !error) {
                    setSession(data.session);
                    console.log("✅ Fallback refresh riuscito");

                    // Ricarica il profilo anche per fallback
                    if (data.session.user) {
                      await loadUserProfile(data.session, false);
                    }
                  } else {
                    throw error || new Error("Fallback refresh fallito");
                  }
                } catch (fallbackError) {
                  console.error(
                    "❌ Anche fallback refresh fallito:",
                    fallbackError
                  );

                  // DISABILITATO LOGOUT AUTOMATICO - L'utente deve fare logout manualmente
                  if (
                    refreshError.message?.includes("refresh_token_not_found") ||
                    refreshError.message?.includes("invalid_grant")
                  ) {
                    console.log(
                      "🚪 Token non valido, ma NESSUN LOGOUT AUTOMATICO"
                    );
                    // DISABILITATO: await signOut(); - L'utente deve fare logout manualmente
                  }
                }
              }
            }
          } catch (error) {
            console.warn("⚠️ Errore controllo periodico sessione:", error);
          }
        }, 4 * 60 * 1000); // Ogni 4 minuti (ridotto carico)

        // 🔄 LISTENER PER REFRESH SESSIONE DAL SESSION MANAGER
        const handleSessionRefresh = async (event: CustomEvent) => {
          const { session: refreshedSession } = event.detail;
          console.log(
            "🔄 Ricevuto evento sessionRefreshed, aggiornamento profilo..."
          );

          if (refreshedSession?.user) {
            setSession(refreshedSession);
            await loadUserProfile(refreshedSession, false);
            console.log("✅ Profilo aggiornato dopo refresh sessione");
          }
        };

        window.addEventListener(
          "sessionRefreshed",
          handleSessionRefresh as EventListener
        );

        return () => {
          subscription?.unsubscribe();
          clearInterval(sessionCheckInterval);
          window.removeEventListener(
            "sessionRefreshed",
            handleSessionRefresh as EventListener
          );
        };
      } catch (error) {
        console.log("Supabase initialization skipped:", error);
        setUser(null);
        setIsLoggedIn(false);
        setSession(null);
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, [isOnlineFeatureEnabled, getSupabaseClient]);

  // Funzioni di autenticazione
  const signIn = async (email: string) => {
    if (!isOnlineFeatureEnabled) return;
    setIsLoading(true);
    try {
      const supabase = await getSupabaseClient();
      const { error } = await supabase.auth.signInWithOtp({ email });
      if (error) throw error;
      alert("Check your email for the login link!");
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      alert(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (email: string, password: string) => {
    if (!isOnlineFeatureEnabled) return;
    setIsLoading(true);
    try {
      const supabase = await getSupabaseClient();
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });
      if (error) throw error;
      alert("Check your email for the verification link!");
      console.log(data);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      alert(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // 🎯 FUNZIONE HELPER PER OTTENERE IL TITOLO DINAMICO BASATO SUL LIVELLO
  const getDynamicPlayerTitle = async (): Promise<string> => {
    try {
      const { loadUnifiedData } = await import(
        "../services/unifiedStorageService"
      );
      const { getPlayerTitle } = await import(
        "../services/playerTitlesService"
      );

      const userData = loadUnifiedData();
      const playerTitle = getPlayerTitle(userData.level);
      return playerTitle.title;
    } catch (error) {
      console.warn("Errore nel caricamento titolo dinamico:", error);
      return "Pulcino Romagnolo"; // Fallback al titolo base
    }
  };

  // 🧹 FUNZIONE DI PULIZIA COMPLETA DATI UTENTE
  const clearAllUserData = async () => {
    console.log("🧹 Inizio pulizia completa dati utente...");

    try {
      // 1. Reset dello stato locale del context
      setUser(null);
      setSession(null);
      setIsLoggedIn(false);
      setUserStats(null);

      // 2. Invalida tutte le cache del context
      invalidateFriendsCache();
      invalidateLeaderboardCache();
      invalidateSearchCache();

      // 3. Pulisci cache unificata
      const { unifiedCache } = await import("../services/unifiedCacheService");
      unifiedCache.clear();

      // 4. Pulisci cache profilo
      const { invalidateProfileCache } = await import(
        "../services/profileService"
      );
      invalidateProfileCache();

      // 5. Pulisci cache immagini
      const { unifiedImageCache } = await import(
        "../utils/ui/unifiedImageCache"
      );
      unifiedImageCache.clearCache();

      // 6. Reset dati unificati (mantiene solo impostazioni di base)
      const { resetAllData, saveUnifiedData } = await import(
        "../services/unifiedStorageService"
      );
      resetAllData();

      // Ottieni il titolo dinamico per il livello 1 (default)
      const { getPlayerTitle } = await import(
        "../services/playerTitlesService"
      );
      const defaultTitle = getPlayerTitle(1).title;

      // Salva solo le impostazioni essenziali per il prossimo utente
      saveUnifiedData({
        isOfflineMode: true,
        username: defaultTitle,
        title: defaultTitle,
        audioEnabled: true,
        soundEffectsEnabled: true,
        musicEnabled: false,
        masterVolume: 0.7,
        soundEffectsVolume: 0.8,
        musicVolume: 0.2,
        difficulty: "easy",
        victoryPoints: "31",
        selectedTableMat: "marrone",
      });

      // 7. Pulisci localStorage di eventuali altri dati
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (
          key &&
          (key.includes("supabase") ||
            key.includes("auth") ||
            key.includes("user") ||
            key.includes("profile") ||
            key.includes("friends") ||
            key.includes("cache"))
        ) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach((key) => localStorage.removeItem(key));

      // 8. Pulisci sessionStorage
      sessionStorage.clear();

      console.log("✅ Pulizia completa dati utente completata");
    } catch (error) {
      console.error("❌ Errore durante pulizia dati utente:", error);
      // Anche se c'è un errore, forza il reset dello stato locale
      setUser(null);
      setSession(null);
      setIsLoggedIn(false);
      setUserStats(null);
    }
  };

  const signOut = async () => {
    console.log("🚪 LOGOUT BRUTALE - INIZIATO");

    // 🔥 PULIZIA IMMEDIATA E BRUTALE - NESSUN TIMEOUT
    setUser(null);
    setIsLoggedIn(false);
    setUserStats(null);
    setSession(null);
    setIsLoading(false);

    console.log("✅ Stato locale pulito");

    // 🔥 PULIZIA STORAGE SINCRONA
    try {
      localStorage.clear();
      sessionStorage.clear();
      console.log("✅ Storage pulito");
    } catch (e) {
      console.warn("Storage già pulito");
    }

    // 🔥 LOGOUT REMOTO IN BACKGROUND (NON BLOCCA)
    if (isOnlineFeatureEnabled) {
      setTimeout(async () => {
        try {
          const supabase = await getSupabaseClient();
          await supabase.auth.signOut();
          console.log("✅ Logout remoto completato in background");
        } catch (error) {
          console.warn("⚠️ Logout remoto fallito (non critico):", error);
        }
      }, 100);
    }

    // 🔥 PULIZIA DATI UTENTE IN BACKGROUND (NON BLOCCA)
    setTimeout(async () => {
      try {
        await clearAllUserData();
        console.log("✅ Dati utente puliti in background");
      } catch (error) {
        console.warn("⚠️ Pulizia dati fallita (non critico):", error);
      }
    }, 200);

    console.log("🚪 LOGOUT BRUTALE - COMPLETATO IMMEDIATAMENTE");

    // 🔄 REFRESH AUTOMATICO IMMEDIATO PER RIPRISTINARE DATI DEFAULT
    setTimeout(() => {
      window.location.reload();
    }, 100);
  };

  // 🔐 BACKUP SESSIONE PER AGGIORNAMENTI ANDROID
  useEffect(() => {
    if (session?.access_token && user) {
      try {
        const sessionBackup = {
          session: session,
          user: user,
          timestamp: Date.now(),
          version: "1.0",
        };
        localStorage.setItem("session_backup", JSON.stringify(sessionBackup));
        console.log("💾 Backup sessione salvato per aggiornamenti Android");
      } catch (error) {
        console.warn("⚠️ Errore salvataggio backup sessione:", error);
      }
    }
  }, [session, user]);

  // 🔐 RIPRISTINO SESSIONE DA BACKUP
  useEffect(() => {
    if (!session && !isLoading) {
      try {
        const backupData = localStorage.getItem("session_backup");
        if (backupData) {
          const backup = JSON.parse(backupData);
          const isRecent =
            Date.now() - backup.timestamp < 7 * 24 * 60 * 60 * 1000; // 7 giorni

          if (isRecent && backup.session?.access_token) {
            console.log("🔄 Ripristino sessione da backup Android...");
            setSession(backup.session);
            setUser(backup.user);
            setIsLoggedIn(true);
            console.log("✅ Sessione ripristinata da backup");
          } else {
            localStorage.removeItem("session_backup");
            console.log("🗑️ Backup sessione scaduto, rimosso");
          }
        }
      } catch (error) {
        console.warn("⚠️ Errore ripristino backup sessione:", error);
        localStorage.removeItem("session_backup");
      }
    }
  }, [session, isLoading]);

  // Fetch user profile con gestione sessione migliorata
  const fetchUserProfile = useCallback(async () => {
    if (!user || !isOnlineFeatureEnabled) {
      console.log("❌ fetchUserProfile: condizioni non soddisfatte");
      return;
    }

    try {
      console.log("🔄 fetchUserProfile: inizio fetch profilo utente");
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;

      // Usa la nuova funzione con gestione automatica della sessione
      const data = await apiCallWithAuth(
        `${supabaseUrl}/rest/v1/profiles?id=eq.${user.id}&select=username,created_at,updated_at`,
        {
          headers: {
            Accept: "application/vnd.pgrst.object+json",
          },
        }
      );

      if (data) {
        console.log("✅ fetchUserProfile: dati ricevuti dal server");
        const dynamicTitle = await getDynamicPlayerTitle();
        const updatedUser = {
          ...user,
          username: data.username || dynamicTitle,
          created_at: data.created_at || new Date().toISOString(),
          updated_at: data.updated_at || new Date().toISOString(),
        };
        setUser(updatedUser);
      }
    } catch (error) {
      console.error("❌ Errore fetchUserProfile:", error);
    }
  }, [user, isOnlineFeatureEnabled, apiCallWithAuth]);

  // Update profile con gestione sessione migliorata
  const updateProfile = async ({ username }: { username: string }) => {
    if (!user || !isOnlineFeatureEnabled) {
      throw new Error("Condizioni non soddisfatte per updateProfile");
    }

    try {
      console.log("🔄 Aggiornamento username:", username);

      // Limita l'username a massimo 18 caratteri
      const truncatedUsername = username.substring(0, 18);

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;

      // Usa la nuova funzione con gestione automatica della sessione
      await apiCallWithAuth(
        `${supabaseUrl}/rest/v1/profiles?id=eq.${user.id}`,
        {
          method: "PATCH",
          headers: {
            Prefer: "return=minimal",
          },
          body: JSON.stringify({ username: truncatedUsername }),
        }
      );

      console.log("✅ Username aggiornato nel database");

      // Aggiorna il profilo locale
      await fetchUserProfile();
    } catch (error) {
      console.error("❌ Errore updateProfile:", error);
      throw error;
    }
  };

  // Email e password
  const updateEmail = async (email: string) => {
    if (!isOnlineFeatureEnabled) return;
    try {
      const supabase = await getSupabaseClient();
      const { error } = await supabase.auth.updateUser({ email });
      if (error) throw error;
    } catch (error) {
      console.error("Error updating email:", error);
      throw error;
    }
  };

  const updatePassword = async (
    currentPassword: string,
    newPassword: string
  ) => {
    if (!isOnlineFeatureEnabled) return;
    try {
      const supabase = await getSupabaseClient();
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user?.email || "",
        password: currentPassword,
      });

      if (signInError) {
        throw new Error("Password attuale errata");
      }

      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });
      if (error) throw error;
    } catch (error) {
      console.error("Error updating password:", error);
      throw error;
    }
  };

  // Login/register
  const login = async (email: string, password: string) => {
    if (!isOnlineFeatureEnabled) {
      return { success: false, error: "Funzionalità online disabilitate" };
    }

    try {
      const supabase = await getSupabaseClient();
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      return { success: false, error: errorMessage };
    }
  };

  const register = async (
    email: string,
    password: string,
    username: string
  ) => {
    if (!isOnlineFeatureEnabled) {
      return { success: false, error: "Funzionalità online disabilitate" };
    }

    try {
      // Nota: Il trigger del database handle_new_user() ora estrae automaticamente
      // i primi 18 caratteri dell'email prima della @ come username predefinito
      // Questo parametro username viene limitato a 18 caratteri per consistenza
      const truncatedUsername = username.substring(0, 18);

      const supabase = await getSupabaseClient();
      const { error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username: truncatedUsername,
          },
        },
      });

      if (signUpError) {
        return { success: false, error: signUpError.message };
      }

      return { success: true };
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      return { success: false, error: errorMessage };
    }
  };

  const logout = async () => {
    try {
      console.log("🚪 LOGOUT - INIZIATO");

      // 🔥 PULIZIA IMMEDIATA DELLO STATO REACT
      setUser(null);
      setIsLoggedIn(false);
      setUserStats(null);
      setSession(null);
      setIsLoading(false);

      console.log("✅ Stato React pulito immediatamente");

      // Il logout deve sempre funzionare, anche se le funzionalità online sono disabilitate
      if (isOnlineFeatureEnabled) {
        const supabase = await getSupabaseClient();
        await supabase.auth.signOut();
      }

      // 🧹 PULIZIA COMPLETA DI TUTTI I DATI UTENTE
      await clearAllUserData();

      console.log("🚪 LOGOUT - COMPLETATO");
    } catch (error) {
      console.error("Error during logout:", error);
      // Anche in caso di errore, assicurati che lo stato sia pulito
      setUser(null);
      setIsLoggedIn(false);
      setUserStats(null);
      setSession(null);
      setIsLoading(false);
      throw error;
    }
  };

  const checkUsernameExists = async (username: string) => {
    if (!isOnlineFeatureEnabled) return false;
    try {
      const supabase = await getSupabaseClient();
      const { data } = await supabase
        .from("profiles")
        .select("id")
        .eq("username", username)
        .single();
      return !!data;
    } catch {
      return false;
    }
  };

  // getFriends con cache integrata e query semplificate SENZA JOIN
  const getFriends = useCallback(
    async (forceRefresh = false): Promise<Friend[]> => {
      const startTime = Date.now();
      console.log(
        `🔄 getFriends chiamata - forceRefresh: ${forceRefresh}, timestamp: ${startTime}`
      );

      // Verifica condizioni di base
      if (!user) {
        console.log("❌ getFriends SKIP: user non presente");
        return [];
      }
      if (!isOnlineFeatureEnabled) {
        console.log("❌ getFriends SKIP: funzionalità online disabilitate");
        return [];
      }
      if (!session?.access_token) {
        console.log("❌ getFriends SKIP: sessione o access_token mancante", {
          sessionExists: !!session,
          accessTokenExists: !!session?.access_token,
        });
        return [];
      }

      console.log(
        `✅ getFriends: condizioni base soddisfatte per user ${user.id}`
      );

      // Controlla cache se non è force refresh
      if (!forceRefresh) {
        const cacheAge = Date.now() - friendsCache.timestamp;

        // Usa durata cache più breve se l'ultima risposta era vuota (possibile errore)
        const effectiveCacheDuration =
          friendsCache.data &&
          friendsCache.data.length === 0 &&
          cacheAge < ERROR_CACHE_DURATION
            ? ERROR_CACHE_DURATION
            : CACHE_DURATION;

        const isCacheValid =
          friendsCache.data && cacheAge < effectiveCacheDuration;

        console.log(
          `🔍 Cache check - età: ${Math.round(
            cacheAge / 60000
          )}min, valida: ${isCacheValid}, dati: ${
            friendsCache.data?.length || 0
          }, durata: ${Math.round(
            effectiveCacheDuration / 60000
          )}min, timestamp: ${friendsCache.timestamp}`
        );

        if (isCacheValid) {
          console.log(
            `✅ Friends da cache (età: ${Math.round(
              cacheAge / 60000
            )}min) - RETURN ANTICIPATO`
          );
          return friendsCache.data;
        } else {
          console.log(
            `🔄 Cache non valida o forceRefresh - procedo con fetch dal server`
          );
        }
      } else {
        console.log(
          `🔄 ForceRefresh richiesto - ignoro cache e procedo con fetch`
        );
      }

      try {
        console.log("🌐 Caricamento friends dal server...");

        const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;

        // Usa la nuova funzione con gestione automatica della sessione
        const friendsData = await apiCallWithAuth(
          `${supabaseUrl}/rest/v1/friendships?select=id,user_id,friend_id,created_at&or=(user_id.eq.${user.id},friend_id.eq.${user.id})&limit=50`
        );

        console.log(
          `✅ Fetch friendships completata: ${
            friendsData?.length || 0
          } relazioni trovate`
        );

        if (!friendsData || friendsData.length === 0) {
          setFriendsCache({
            data: [],
            timestamp: Date.now(),
          });
          return [];
        }

        // Estrai gli ID degli amici
        const friendIds = new Set<string>();
        const relationMap = new Map<
          string,
          { id: string; user_id: string; friend_id: string; created_at: string }
        >();

        friendsData.forEach(
          (rel: {
            id: string;
            user_id: string;
            friend_id: string;
            created_at: string;
          }) => {
            const friendId =
              rel.user_id === user.id ? rel.friend_id : rel.user_id;
            friendIds.add(friendId);
            relationMap.set(friendId, rel);
          }
        );

        console.log(`👥 ID amici estratti: ${friendIds.size}`);

        // FETCH per profiles con gestione sessione migliorata
        const idsParam = Array.from(friendIds)
          .map((id) => `id.eq.${id}`)
          .join(",");

        let profilesData: Array<{
          id: string;
          username: string;
          created_at?: string;
          updated_at?: string;
        }>;
        try {
          profilesData = await apiCallWithAuth(
            `${supabaseUrl}/rest/v1/profiles?select=id,username,created_at,updated_at&or=(${idsParam})&limit=50`
          );
        } catch (error) {
          console.error("❌ Errore fetch profiles:", error);
          // In caso di errore sui profili, restituiamo amici con dati minimali
          const minimalFriends: Friend[] = Array.from(friendIds).map(
            (friendId) => {
              const rel = relationMap.get(friendId);
              return {
                id: rel?.id || `${user.id}-${friendId}`,
                user_id: user.id,
                friend_id: friendId,
                created_at: rel?.created_at || new Date().toISOString(),
                friend: {
                  id: friendId,
                  username: "Caricamento...",
                  avatar_url: null,
                  level: 1,
                },
              };
            }
          );

          setFriendsCache({
            data: minimalFriends,
            timestamp: Date.now(),
          });

          console.log(
            `⚠️ Profili non caricati, restituiti ${minimalFriends.length} amici minimi`
          );
          return minimalFriends;
        }

        console.log(`👤 Profili caricati: ${profilesData?.length || 0}`);

        // FETCH per game_stats degli amici con gestione sessione migliorata
        let statsData: Array<{
          user_id: string;
          level: number;
          xp: number;
          games_won: number;
          games_played: number;
        }> = [];
        if (friendIds.size > 0) {
          try {
            const statsIdsParam = Array.from(friendIds)
              .map((id) => `user_id.eq.${id}`)
              .join(",");

            statsData = await apiCallWithAuth(
              `${supabaseUrl}/rest/v1/game_stats?select=user_id,level,xp,games_won,games_played&or=(${statsIdsParam})&limit=50`
            );

            console.log(
              `📊 Statistiche amici caricate: ${statsData?.length || 0}`
            );
          } catch (error) {
            console.warn("⚠️ Errore fetch statistiche amici:", error);
          }
        }

        // Crea una mappa delle statistiche per ID utente
        const statsMap = new Map();
        (statsData || []).forEach((stat) => {
          statsMap.set(stat.user_id, stat);
        });

        // Combina i dati
        const friends: Friend[] = (profilesData || []).map(
          (profile: {
            id: string;
            username: string;
            created_at?: string;
            updated_at?: string;
          }) => {
            const rel = relationMap.get(profile.id);
            const stats = statsMap.get(profile.id);
            return {
              id: rel?.id || `${user.id}-${profile.id}`,
              user_id: user.id,
              friend_id: profile.id,
              created_at: rel?.created_at || new Date().toISOString(),
              friend: {
                id: profile.id,
                username: profile.username || "Utente",
                avatar_url: null,
                level: stats?.level || 1,
                xp: stats?.xp || 0,
                games_won: stats?.games_won || 0,
                games_played: stats?.games_played || 0,
                win_rate:
                  stats?.games_played > 0
                    ? Math.round((stats.games_won / stats.games_played) * 100)
                    : 0,
                created_at: profile.created_at,
                updated_at: profile.updated_at,
              },
            };
          }
        );

        // Salva in cache
        setFriendsCache({
          data: friends,
          timestamp: Date.now(),
        });

        console.log(
          `✅ Friends caricati e salvati in cache: ${friends.length} amici`
        );
        console.log(
          `⏱️ getFriends completata in ${Date.now() - startTime}ms - SUCCESS`
        );
        return friends;
      } catch (error) {
        console.error("❌ Errore getFriends:", error);
        console.log(
          `⏱️ getFriends fallita in ${Date.now() - startTime}ms - ERROR`
        );

        // Fallback finale: restituisci cache o array vuoto
        const fallbackData = friendsCache.data || [];
        console.log(
          `🔄 Fallback alla cache dopo errore: ${fallbackData.length} amici`
        );

        // Se è un errore di rete e c'è cache valida, usala con durata ridotta
        if (fallbackData.length > 0) {
          setFriendsCache({
            data: fallbackData,
            timestamp: Date.now() - CACHE_DURATION + ERROR_CACHE_DURATION, // Cache per 30s
          });
        }

        return fallbackData;
      }
    },
    [
      user,
      isOnlineFeatureEnabled,
      session,
      CACHE_DURATION,
      ERROR_CACHE_DURATION,
      friendsCache,
      setFriendsCache,
      apiCallWithAuth,
    ]
  );

  // Friend requests con timeout e logging dettagliato
  const getFriendRequests = useCallback(
    async (forceRefresh = false): Promise<FriendRequest[]> => {
      const startTime = Date.now();
      console.log(
        `🔄 getFriendRequests chiamata - forceRefresh: ${forceRefresh}, timestamp: ${startTime}`
      );

      // Verifica condizioni di base
      if (!user) {
        console.log("❌ getFriendRequests SKIP: user non presente");
        return [];
      }
      if (!isOnlineFeatureEnabled) {
        console.log(
          "❌ getFriendRequests SKIP: funzionalità online disabilitate"
        );
        return [];
      }
      if (!session?.access_token) {
        console.log(
          "❌ getFriendRequests SKIP: sessione o access_token mancante",
          {
            sessionExists: !!session,
            accessTokenExists: !!session?.access_token,
          }
        );
        return [];
      }

      console.log(
        `✅ getFriendRequests: condizioni base soddisfatte per user ${user.id}`
      );

      // Controlla cache se non è force refresh
      if (!forceRefresh) {
        const cacheAge = Date.now() - friendRequestsCache.timestamp;
        const isCacheValid =
          friendRequestsCache.data && cacheAge < CACHE_DURATION;

        console.log(
          `🔍 FriendRequests Cache check - età: ${Math.round(
            cacheAge / 60000
          )}min, valida: ${isCacheValid}, dati: ${
            friendRequestsCache.data?.length || 0
          }, timestamp: ${friendRequestsCache.timestamp}`
        );

        if (isCacheValid) {
          console.log(
            `✅ FriendRequests da cache (età: ${Math.round(
              cacheAge / 60000
            )}min) - RETURN ANTICIPATO`
          );
          return friendRequestsCache.data;
        } else {
          console.log(
            `🔄 FriendRequests cache non valida - procedo con fetch dal server`
          );
        }
      } else {
        console.log(
          `🔄 FriendRequests forceRefresh richiesto - ignoro cache e procedo con fetch`
        );
      }

      try {
        console.log(
          `🌐 getFriendRequests: inizio FETCH DIRETTA dal server per user ${user.id}`
        );

        console.log(`📡 Fetch friend_requests per receiver_id: ${user.id}`);

        const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;

        // Usa la nuova funzione con gestione automatica della sessione
        const data = await apiCallWithAuth(
          `${supabaseUrl}/rest/v1/friend_requests?select=id,sender_id,receiver_id,created_at,status,sender:profiles!sender_id(id,username,avatar_url)&receiver_id=eq.${user.id}&status=eq.pending&limit=50`
        );

        console.log(
          `✅ Fetch friend_requests completata: ${
            data?.length || 0
          } richieste ricevute`
        );

        // Trasforma i dati per essere compatibili con FriendRequest
        const transformedData: FriendRequest[] = (data || []).map(
          (item: Record<string, unknown>) => ({
            id: item.id as string,
            sender_id: item.sender_id as string,
            receiver_id: item.receiver_id as string,
            created_at: item.created_at as string,
            status: "pending" as const,
            sender: item.sender as {
              id: string;
              username: string;
              avatar_url?: string;
            },
          })
        );

        // Aggiorna cache
        setFriendRequestsCache({
          data: transformedData,
          timestamp: Date.now(),
        });

        console.log(
          `✅ FriendRequests processate e salvate in cache: ${transformedData.length} richieste`
        );
        console.log(
          `⏱️ getFriendRequests completata in ${
            Date.now() - startTime
          }ms - SUCCESS`
        );
        return transformedData;
      } catch (error) {
        console.error("❌ Errore getFriendRequests:", error);
        console.log(
          `⏱️ getFriendRequests fallita in ${Date.now() - startTime}ms - ERROR`
        );

        // Fallback alla cache se disponibile
        const fallbackData = friendRequestsCache.data || [];
        console.log(
          `🔄 Fallback alla cache dopo errore: ${fallbackData.length} richieste`
        );
        return fallbackData;
      }
    },
    [
      user,
      isOnlineFeatureEnabled,
      session,
      CACHE_DURATION,
      friendRequestsCache,
      setFriendRequestsCache,
      apiCallWithAuth,
    ]
  );

  // Sent friend requests (richieste inviate) con timeout e logging dettagliato
  const getSentFriendRequests = useCallback(
    async (forceRefresh = false): Promise<FriendRequest[]> => {
      const startTime = Date.now();
      console.log(
        `🔄 getSentFriendRequests chiamata - forceRefresh: ${forceRefresh}, timestamp: ${startTime}`
      );

      // Verifica condizioni di base
      if (!user) {
        console.log("❌ getSentFriendRequests SKIP: user non presente");
        return [];
      }
      if (!isOnlineFeatureEnabled) {
        console.log(
          "❌ getSentFriendRequests SKIP: funzionalità online disabilitate"
        );
        return [];
      }
      if (!session?.access_token) {
        console.log(
          "❌ getSentFriendRequests SKIP: sessione o access_token mancante",
          {
            sessionExists: !!session,
            accessTokenExists: !!session?.access_token,
          }
        );
        return [];
      }

      console.log(
        `✅ getSentFriendRequests: condizioni base soddisfatte per user ${user.id}`
      );

      // Controlla cache se non è force refresh
      if (!forceRefresh) {
        const cacheAge = Date.now() - sentFriendRequestsCache.timestamp;
        const isCacheValid =
          sentFriendRequestsCache.data && cacheAge < CACHE_DURATION;

        console.log(
          `🔍 SentFriendRequests Cache check - età: ${Math.round(
            cacheAge / 60000
          )}min, valida: ${isCacheValid}, dati: ${
            sentFriendRequestsCache.data?.length || 0
          }, timestamp: ${sentFriendRequestsCache.timestamp}`
        );

        if (isCacheValid) {
          console.log(
            `✅ SentFriendRequests da cache (età: ${Math.round(
              cacheAge / 60000
            )}min) - RETURN ANTICIPATO`
          );
          return sentFriendRequestsCache.data;
        } else {
          console.log(
            `🔄 SentFriendRequests cache non valida - procedo con fetch dal server`
          );
        }
      } else {
        console.log(
          `🔄 SentFriendRequests forceRefresh richiesto - ignoro cache e procedo con fetch`
        );
      }

      try {
        console.log(
          `🌐 getSentFriendRequests: inizio fetch dal server per user ${user.id}`
        );

        const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;

        // Usa la nuova funzione con gestione automatica della sessione
        const data = await apiCallWithAuth(
          `${supabaseUrl}/rest/v1/friend_requests?select=id,sender_id,receiver_id,created_at,status,receiver:profiles!receiver_id(id,username,avatar_url)&sender_id=eq.${user.id}&status=eq.pending&limit=50`
        );

        // Trasforma i dati per essere compatibili con FriendRequest
        const transformedData: FriendRequest[] = (data || []).map(
          (item: Record<string, unknown>) => ({
            id: item.id as string,
            sender_id: item.sender_id as string,
            receiver_id: item.receiver_id as string,
            created_at: item.created_at as string,
            status: "pending" as const,
            sender: {
              id: user.id, // Siamo noi il sender
              username: user.username || "",
              avatar_url: null,
            },
            receiver: item.receiver as {
              id: string;
              username: string;
              avatar_url?: string;
            },
          })
        );

        // Aggiorna cache
        setSentFriendRequestsCache({
          data: transformedData,
          timestamp: Date.now(),
        });

        console.log(
          `✅ SentFriendRequests processate e salvate in cache: ${transformedData.length} richieste`
        );
        console.log(
          `⏱️ getSentFriendRequests completata in ${
            Date.now() - startTime
          }ms - SUCCESS`
        );
        return transformedData;
      } catch (error) {
        console.error("❌ Errore getSentFriendRequests:", error);
        console.log(
          `⏱️ getSentFriendRequests fallita in ${
            Date.now() - startTime
          }ms - ERROR`
        );

        // Fallback alla cache se disponibile
        const fallbackData = sentFriendRequestsCache.data || [];
        console.log(
          `🔄 Fallback alla cache dopo errore: ${fallbackData.length} richieste`
        );
        return fallbackData;
      }
    },
    [
      user,
      isOnlineFeatureEnabled,
      session,
      CACHE_DURATION,
      sentFriendRequestsCache,
      setSentFriendRequestsCache,
      apiCallWithAuth,
    ]
  );

  // Azioni friend requests con fetch diretta
  const acceptFriendRequest = async (requestId: string): Promise<boolean> => {
    const startTime = Date.now();
    console.log(
      `🔄 acceptFriendRequest chiamata - requestId: ${requestId}, timestamp: ${startTime}`
    );

    // Verifica condizioni di base
    if (!user) {
      console.log("❌ acceptFriendRequest SKIP: user non presente");
      return false;
    }
    if (!isOnlineFeatureEnabled) {
      console.log(
        "❌ acceptFriendRequest SKIP: funzionalità online disabilitate"
      );
      return false;
    }
    if (!session?.access_token) {
      console.log(
        "❌ acceptFriendRequest SKIP: sessione o access_token mancante",
        {
          sessionExists: !!session,
          accessTokenExists: !!session?.access_token,
        }
      );
      return false;
    }

    console.log(
      `✅ acceptFriendRequest: condizioni base soddisfatte per request ${requestId}`
    );

    try {
      console.log(`🌐 acceptFriendRequest: accettazione richiesta...`);

      // Verifica che la sessione sia valida prima dell'operazione
      // Rimosso ensureValidSession() - Supabase gestisce automaticamente il refresh

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const accessToken = session.access_token;

      // Step 1: Get request data per ottenere sender e receiver
      console.log(`📡 Fetch dati richiesta per creare amicizia...`);
      const requestResponse = await fetch(
        `${supabaseUrl}/rest/v1/friend_requests?select=*&id=eq.${requestId}`,
        {
          method: "GET",
          headers: {
            apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
            Prefer: "return=representation",
          },
        }
      );

      if (!requestResponse.ok) {
        throw new Error(`HTTP ${requestResponse.status} per fetch request`);
      }

      const requestData = await requestResponse.json();
      if (!requestData || requestData.length === 0) {
        throw new Error("Request not found");
      }

      const request = requestData[0];
      console.log(
        `✅ Dati richiesta ottenuti: sender=${request.sender_id}, receiver=${request.receiver_id}`
      );

      // Step 2: Create friendship entries con fetch diretta
      console.log(`📤 Insert friendships con FETCH DIRETTA...`);

      let friendship1Success = false;
      let friendship2Success = false;

      // Friendship 1: receiver -> sender
      try {
        const friendship1Response = await fetch(
          `${supabaseUrl}/rest/v1/friendships`,
          {
            method: "POST",
            headers: {
              apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
              Authorization: `Bearer ${accessToken}`,
              "Content-Type": "application/json",
              Prefer: "return=minimal",
            },
            body: JSON.stringify({
              user_id: request.receiver_id,
              friend_id: request.sender_id,
            }),
          }
        );

        if (!friendship1Response.ok) {
          const errorText = await friendship1Response.text();
          console.error("❌ Errore inserimento friendship 1:", errorText);
          throw new Error(
            `HTTP ${friendship1Response.status} per friendship 1: ${errorText}`
          );
        }

        console.log("✅ Friendship 1 creata con successo");
        friendship1Success = true;
      } catch (error) {
        console.error("❌ Errore critico friendship 1:", error);
        throw error; // Se il primo inserimento fallisce, l'intera operazione fallisce
      }

      // Friendship 2: sender -> receiver
      try {
        const friendship2Response = await fetch(
          `${supabaseUrl}/rest/v1/friendships`,
          {
            method: "POST",
            headers: {
              apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
              Authorization: `Bearer ${accessToken}`,
              "Content-Type": "application/json",
              Prefer: "return=minimal",
            },
            body: JSON.stringify({
              user_id: request.sender_id,
              friend_id: request.receiver_id,
            }),
          }
        );

        if (!friendship2Response.ok) {
          const errorText = await friendship2Response.text();
          console.error("❌ Errore inserimento friendship 2:", errorText);

          // Se è un errore 403 (RLS), lo logghiamo ma non blocchiamo l'operazione
          if (friendship2Response.status === 403) {
            console.warn(
              "⚠️ Friendship 2 fallita per policy RLS - l'amicizia sarà unidirezionale ma funzionale"
            );
            friendship2Success = false;
          } else {
            throw new Error(
              `HTTP ${friendship2Response.status} per friendship 2: ${errorText}`
            );
          }
        } else {
          console.log("✅ Friendship 2 creata con successo");
          friendship2Success = true;
        }
      } catch (error: unknown) {
        console.error("❌ Errore friendship 2:", error);
        // Se il primo inserimento è riuscito, continuiamo anche se il secondo fallisce
        if (friendship1Success) {
          console.warn(
            "⚠️ Friendship 2 fallita ma friendship 1 OK - continuiamo con l'operazione"
          );
          friendship2Success = false;
        } else {
          throw error;
        }
      }

      console.log(
        `📊 Risultato friendships: friendship1=${friendship1Success}, friendship2=${friendship2Success}`
      );

      // Step 3: Delete the friend request
      console.log(`📤 Delete friend_request...`);
      const deleteResponse = await fetch(
        `${supabaseUrl}/rest/v1/friend_requests?id=eq.${requestId}`,
        {
          method: "DELETE",
          headers: {
            apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!deleteResponse.ok) {
        const errorText = await deleteResponse.text();
        console.error("❌ Errore delete request:", errorText);
        throw new Error(
          `HTTP ${deleteResponse.status} per delete request: ${errorText}`
        );
      }

      console.log("✅ Friend request eliminata con successo");

      console.log(
        `✅ Richiesta ${requestId} accettata e amicizia creata con successo`
      );
      console.log(
        `⏱️ acceptFriendRequest completata in ${
          Date.now() - startTime
        }ms - SUCCESS`
      );

      // Invalida cache per force refresh
      console.log(`🗑️ Invalidazione cache dopo accettazione richiesta...`);
      invalidateFriendsCache();

      return true;
    } catch (error) {
      console.error("❌ Errore acceptFriendRequest:", error);
      console.log(
        `⏱️ acceptFriendRequest fallita in ${Date.now() - startTime}ms - ERROR`
      );
      return false;
    }
  };

  const rejectFriendRequest = async (requestId: string): Promise<boolean> => {
    const startTime = Date.now();
    console.log(
      `🔄 rejectFriendRequest chiamata - requestId: ${requestId}, timestamp: ${startTime}`
    );

    // Verifica condizioni di base
    if (!isOnlineFeatureEnabled) {
      console.log(
        "❌ rejectFriendRequest SKIP: funzionalità online disabilitate"
      );
      return false;
    }
    if (!session?.access_token) {
      console.log(
        "❌ rejectFriendRequest SKIP: sessione o access_token mancante",
        {
          sessionExists: !!session,
          accessTokenExists: !!session?.access_token,
        }
      );
      return false;
    }

    console.log(
      `✅ rejectFriendRequest: condizioni base soddisfatte per request ${requestId}`
    );

    try {
      console.log(`🌐 rejectFriendRequest: rifiuto richiesta...`);

      // Verifica che la sessione sia valida prima dell'operazione
      // Rimosso ensureValidSession() - Supabase gestisce automaticamente il refresh

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const accessToken = session.access_token;

      console.log(`📤 Delete friend_request con FETCH DIRETTA...`);
      const deleteResponse = await fetch(
        `${supabaseUrl}/rest/v1/friend_requests?id=eq.${requestId}`,
        {
          method: "DELETE",
          headers: {
            apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!deleteResponse.ok) {
        const errorText = await deleteResponse.text();
        console.error("❌ Errore delete request:", errorText);
        throw new Error(
          `HTTP ${deleteResponse.status} per delete request: ${errorText}`
        );
      }

      console.log(`✅ Richiesta ${requestId} rifiutata con successo`);
      console.log(
        `⏱️ rejectFriendRequest completata in ${
          Date.now() - startTime
        }ms - SUCCESS`
      );

      // Invalida cache per force refresh
      console.log(`🗑️ Invalidazione cache dopo rifiuto richiesta...`);
      invalidateFriendsCache();

      return true;
    } catch (error) {
      console.error("❌ Errore rejectFriendRequest:", error);
      return false;
    }
  };

  const sendFriendRequest = async (userId: string): Promise<boolean> => {
    const startTime = Date.now();
    console.log(
      `🔄 sendFriendRequest chiamata - userId: ${userId}, timestamp: ${startTime}`
    );

    // Verifica condizioni di base
    if (!user) {
      console.log("❌ sendFriendRequest SKIP: user non presente");
      return false;
    }
    if (!isOnlineFeatureEnabled) {
      console.log(
        "❌ sendFriendRequest SKIP: funzionalità online disabilitate"
      );
      return false;
    }
    if (!session?.access_token) {
      console.log(
        "❌ sendFriendRequest SKIP: sessione o access_token mancante",
        {
          sessionExists: !!session,
          accessTokenExists: !!session?.access_token,
        }
      );
      return false;
    }

    console.log(
      `✅ sendFriendRequest: condizioni base soddisfatte - sender: ${user.id}, receiver: ${userId}`
    );

    try {
      console.log(`🌐 sendFriendRequest: invio richiesta...`);

      // Verifica che la sessione sia valida prima dell'operazione
      // Rimosso ensureValidSession() - Supabase gestisce automaticamente il refresh

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const accessToken = session.access_token;

      // Step 1: Verifica se sono già amici
      const friendshipResponse = await fetch(
        `${supabaseUrl}/rest/v1/friendships?select=id&or=(and(user_id.eq.${user.id},friend_id.eq.${userId}),and(user_id.eq.${userId},friend_id.eq.${user.id}))&limit=1`,
        {
          method: "GET",
          headers: {
            apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!friendshipResponse.ok) {
        throw new Error(
          `HTTP ${friendshipResponse.status} per verifica amicizia`
        );
      }

      const existingFriendship = await friendshipResponse.json();
      if (existingFriendship && existingFriendship.length > 0) {
        console.log(`⚠️ Già amici con ${userId}`);
        return false; // Già amici
      }

      // Step 2: Verifica richiesta pendente
      const requestResponse = await fetch(
        `${supabaseUrl}/rest/v1/friend_requests?select=id,sender_id,receiver_id&or=(and(sender_id.eq.${user.id},receiver_id.eq.${userId}),and(sender_id.eq.${userId},receiver_id.eq.${user.id}))&status=eq.pending&limit=1`,
        {
          method: "GET",
          headers: {
            apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!requestResponse.ok) {
        throw new Error(
          `HTTP ${requestResponse.status} per verifica richieste`
        );
      }

      const existingRequest = await requestResponse.json();
      if (existingRequest && existingRequest.length > 0) {
        console.log(`⚠️ Richiesta già esistente per ${userId}`);
        return false; // Richiesta già esistente
      }

      // Step 3: Invia richiesta
      console.log(`📤 Insert friend_request...`);
      const insertResponse = await fetch(
        `${supabaseUrl}/rest/v1/friend_requests`,
        {
          method: "POST",
          headers: {
            apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
            Prefer: "return=minimal",
          },
          body: JSON.stringify({
            sender_id: user.id,
            receiver_id: userId,
            status: "pending",
          }),
        }
      );

      if (!insertResponse.ok) {
        const errorText = await insertResponse.text();
        console.error("❌ Errore inserimento richiesta:", errorText);
        throw new Error(
          `HTTP ${insertResponse.status} per insert request: ${errorText}`
        );
      }

      console.log(`✅ Richiesta inviata nel DB per ${userId}`);
      console.log(
        `⏱️ sendFriendRequest completata in ${
          Date.now() - startTime
        }ms - SUCCESS`
      );

      // Invalida cache per forzare il refresh
      invalidateFriendsCache();

      return true;
    } catch (error) {
      console.error("❌ Errore sendFriendRequest:", error);
      console.log(
        `⏱️ sendFriendRequest fallita in ${Date.now() - startTime}ms - ERROR`
      );
      return false;
    }
  };

  // Game invites
  const getGameInvites = async (): Promise<GameInvite[]> => {
    if (!user || !isOnlineFeatureEnabled) return [];
    try {
      const supabase = await getSupabaseClient();
      const { data, error } = await supabase
        .from("game_invites")
        .select(
          `
          id,
          sender_id,
          receiver_id,
          created_at,
          status,
          updated_at,
          sender:profiles!sender_id(id, username, avatar_url)
        `
        )
        .eq("receiver_id", user.id)
        .eq("status", "pending");

      if (error) throw error;

      const transformedData: GameInvite[] = (data || []).map((item) => ({
        id: item.id,
        sender_id: item.sender_id,
        receiver_id: item.receiver_id,
        created_at: item.created_at,
        updated_at: item.updated_at || item.created_at,
        status: item.status as "pending" | "accepted" | "rejected",
        sender: {
          id: item.sender?.id || "",
          username: item.sender?.username || "",
          avatar_url: item.sender?.avatar_url,
        },
      }));

      return transformedData;
    } catch (error) {
      console.error("Error fetching game invites:", error);
      return [];
    }
  };

  const acceptGameInvite = async (inviteId: string): Promise<boolean> => {
    if (!isOnlineFeatureEnabled) return false;
    try {
      const supabase = await getSupabaseClient();
      const { error } = await supabase
        .from("game_invites")
        .update({ status: "accepted" })
        .eq("id", inviteId);
      if (error) throw error;
      return true;
    } catch (error) {
      console.error("Error accepting game invite:", error);
      return false;
    }
  };

  const rejectGameInvite = async (inviteId: string): Promise<boolean> => {
    if (!isOnlineFeatureEnabled) return false;
    try {
      const supabase = await getSupabaseClient();
      const { error } = await supabase
        .from("game_invites")
        .update({ status: "rejected" })
        .eq("id", inviteId);
      if (error) throw error;
      return true;
    } catch (error) {
      console.error("Error rejecting game invite:", error);
      return false;
    }
  };

  const sendGameInvite = async (friendId: string): Promise<boolean> => {
    if (!user || !isOnlineFeatureEnabled) return false;
    try {
      const supabase = await getSupabaseClient();
      const { error } = await supabase.from("game_invites").insert({
        sender_id: user.id,
        receiver_id: friendId,
        status: "pending",
      });
      if (error) throw error;
      return true;
    } catch (error) {
      console.error("Error sending game invite:", error);
      return false;
    }
  };

  // fetchUserStats tramite profileService (con cache)
  const fetchUserStats = useCallback(
    async (userId: string) => {
      if (!isOnlineFeatureEnabled || !session?.access_token) return;

      try {
        console.log(
          "🔄 Caricamento statistiche utente tramite profileService..."
        );
        const { getActiveProfile } = await import("../services/profileService");
        const activeProfile = await getActiveProfile();

        if (activeProfile.stats) {
          const userStats: UserStats = {
            user_id: userId,
            games_played: activeProfile.stats.totalGames,
            games_won: activeProfile.stats.gamesWon,
            level: activeProfile.stats.level,
            xp: activeProfile.stats.xp,
            created_at: activeProfile.stats.createdAt,
            updated_at: activeProfile.stats.updatedAt,
            gamesPlayed: activeProfile.stats.totalGames,
            gamesWon: activeProfile.stats.gamesWon,
          };
          setUserStats(userStats);
          window.dispatchEvent(new CustomEvent("statsUpdated"));
        } else {
          // Default stats se nessuna statistica trovata
          const defaultStats: UserStats = {
            games_played: 0,
            games_won: 0,
            level: 1,
            xp: 0,
            gamesPlayed: 0,
            gamesWon: 0,
          };
          setUserStats(defaultStats);
        }
      } catch (error) {
        console.error("Errore fetchUserStats:", error);
      }
    },
    [isOnlineFeatureEnabled, session]
  );

  // Effect per force auth stats refresh
  useEffect(() => {
    const handleForceAuthStatsRefresh = async (event: CustomEvent) => {
      if (!isOnlineFeatureEnabled || !user) return;
      const { userId } = event.detail;
      if (userId === user.id) {
        await fetchUserStats(userId);
      }
    };

    window.addEventListener(
      "forceAuthStatsRefresh",
      handleForceAuthStatsRefresh as EventListener
    );

    return () => {
      window.removeEventListener(
        "forceAuthStatsRefresh",
        handleForceAuthStatsRefresh as EventListener
      );
    };
  }, [isOnlineFeatureEnabled, user, fetchUserStats]);

  // getLeaderboard con cache integrata
  const getLeaderboard = useCallback(
    async (
      forceRefresh = false,
      offset = 0,
      limit = 25
    ): Promise<LeaderboardPlayer[]> => {
      if (!isOnlineFeatureEnabled || !session?.access_token) {
        return [];
      }

      // Controlla cache solo per la prima pagina (offset=0) e se non è force refresh
      if (!forceRefresh && offset === 0) {
        const cacheAge = Date.now() - leaderboardCache.timestamp;
        if (leaderboardCache.data && cacheAge < LEADERBOARD_CACHE_DURATION) {
          console.log(
            `✅ Leaderboard da cache (età: ${Math.round(cacheAge / 60000)}min)`
          );
          // Restituisce solo i primi 25 dalla cache se richiesto
          return leaderboardCache.data.slice(0, limit);
        }
      }

      try {
        console.log(
          `🏆 Caricamento leaderboard dal server... offset=${offset} limit=${limit}`
        );

        if (!session?.access_token) {
          throw new Error("Nessuna sessione valida disponibile");
        }

        const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;

        // Costruisci la query con offset e limit
        const url = `${supabaseUrl}/rest/v1/profiles?select=id,username,created_at,updated_at,game_stats!inner(level,xp,games_won,games_played)&limit=${limit}&offset=${offset}`;

        // Usa la nuova funzione con gestione automatica della sessione
        const data = await apiCallWithAuth(url);

        if (!data || !Array.isArray(data)) {
          throw new Error("Dati non validi ricevuti dal server");
        }

        // Format the data
        const leaderboardData: LeaderboardPlayer[] = data
          .filter((player) => player && player.game_stats) // Filtra dati non validi
          .map((player) => {
            // Gestisce sia array che oggetto singolo per game_stats
            const stats = Array.isArray(player.game_stats)
              ? player.game_stats[0]
              : player.game_stats;

            if (!stats) {
              console.warn("Stats mancanti per player:", player.id);
              return null;
            }

            return {
              id: player.id,
              username: player.username || "Utente sconosciuto",
              level: stats.level || 1,
              xp: stats.xp || 0,
              games_won: stats.games_won || 0,
              games_played: stats.games_played || 0,
              win_rate:
                stats.games_played > 0
                  ? (stats.games_won / stats.games_played) * 100
                  : 0,
              avatar_url: player.avatar_url,
              created_at: player.created_at,
              updated_at: player.updated_at,
            };
          })
          .filter(Boolean); // Rimuove i valori null

        // Salva in cache solo se è la prima pagina (offset = 0)
        if (offset === 0) {
          setLeaderboardCache({
            data: leaderboardData,
            timestamp: Date.now(),
          });
        }

        console.log(
          `✅ Leaderboard caricata e salvata in cache: ${leaderboardData.length} giocatori`
        );
        return leaderboardData;
      } catch (error) {
        console.error("Errore getLeaderboard:", error);
        return leaderboardCache.data || []; // Fallback alla cache in caso di errore
      }
    },
    [
      isOnlineFeatureEnabled,
      session,
      LEADERBOARD_CACHE_DURATION,
      leaderboardCache,
      setLeaderboardCache,
      apiCallWithAuth,
    ]
  );

  // searchPlayers con cache integrata per query
  const searchPlayers = useCallback(
    async (
      query: string,
      forceRefresh = false
    ): Promise<SearchablePlayer[]> => {
      if (
        !query.trim() ||
        !isOnlineFeatureEnabled ||
        !session?.access_token ||
        !user
      ) {
        return [];
      }

      const normalizedQuery = query.trim().toLowerCase();

      // Controlla cache se non è force refresh e la query è la stessa
      if (!forceRefresh && searchCache.query === normalizedQuery) {
        const cacheAge = Date.now() - searchCache.timestamp;
        if (searchCache.data && cacheAge < SEARCH_CACHE_DURATION) {
          return searchCache.data;
        }
      }

      try {
        console.log(
          `🔍 Ricerca giocatori per "${normalizedQuery}" dal server...`
        );
        const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;

        // Usa la nuova funzione con gestione automatica della sessione
        const data = await apiCallWithAuth(
          `${supabaseUrl}/rest/v1/profiles?select=id,username,created_at,updated_at,game_stats(level,xp,games_played,games_won)&username=ilike.*${encodeURIComponent(
            normalizedQuery
          )}*&id=neq.${user.id}&limit=10`
        );

        if (!data || !Array.isArray(data)) {
          throw new Error("Dati non validi ricevuti dal server");
        }

        // Format the data
        const searchResults: SearchablePlayer[] = data.map((player) => {
          const stats = player.game_stats?.[0]; // Prende il primo (e unico) record delle stats

          return {
            id: player.id,
            username: player.username,
            level: stats?.level || 1,
            xp: stats?.xp || 0,
            online: false, // Questo valore potrebbe essere implementato in futuro
            avatar_url: player.avatar_url,
            created_at: player.created_at,
            updated_at: player.updated_at,
            games_played: stats?.games_played || 0,
            games_won: stats?.games_won || 0,
          };
        });

        // Salva in cache
        setSearchCache({
          query: normalizedQuery,
          data: searchResults,
          timestamp: Date.now(),
        });

        console.log(
          `✅ Ricerca "${normalizedQuery}" completata: ${searchResults.length} risultati`
        );
        return searchResults;
      } catch (error) {
        console.error(`Errore searchPlayers per "${normalizedQuery}":`, error);
        // Se è la stessa query, ritorna i dati cache come fallback
        if (searchCache.query === normalizedQuery && searchCache.data) {
          return searchCache.data;
        }
        return [];
      }
    },
    [
      isOnlineFeatureEnabled,
      session,
      user,
      SEARCH_CACHE_DURATION,
      searchCache,
      setSearchCache,
      apiCallWithAuth,
    ]
  );

  // Funzione per rimuovere amici con logging dettagliato
  const removeFriend = async (friendId: string): Promise<boolean> => {
    const startTime = Date.now();
    console.log(
      `🔄 removeFriend chiamata - friendId: ${friendId}, timestamp: ${startTime}`
    );

    // Verifica condizioni di base
    if (!user) {
      console.log("❌ removeFriend SKIP: user non presente");
      return false;
    }
    if (!isOnlineFeatureEnabled) {
      console.log("❌ removeFriend SKIP: funzionalità online disabilitate");
      return false;
    }
    if (!session?.access_token) {
      console.log("❌ removeFriend SKIP: sessione o access_token mancante", {
        sessionExists: !!session,
        accessTokenExists: !!session?.access_token,
      });
      return false;
    }

    console.log(
      `✅ removeFriend: condizioni base soddisfatte - user: ${user.id}, friend: ${friendId}`
    );

    try {
      console.log(`🌐 removeFriend: rimozione amicizia...`);

      // Verifica che la sessione sia valida prima dell'operazione
      // Rimosso ensureValidSession() - Supabase gestisce automaticamente il refresh

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const accessToken = session.access_token;

      console.log(
        `🔍 Rimozione TUTTE le relazioni tra ${user.id} e ${friendId}`
      );

      // Delete con fetch diretta - rimuovi TUTTE le relazioni bidirezionali
      const deleteResponse = await fetch(
        `${supabaseUrl}/rest/v1/friendships?or=(and(user_id.eq.${user.id},friend_id.eq.${friendId}),and(user_id.eq.${friendId},friend_id.eq.${user.id}))`,
        {
          method: "DELETE",
          headers: {
            apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
            Prefer: "return=minimal",
          },
        }
      );

      if (!deleteResponse.ok) {
        const errorText = await deleteResponse.text();
        console.error("❌ Errore rimozione relazioni:", errorText);
        throw new Error(
          `HTTP ${deleteResponse.status} per delete friendships: ${errorText}`
        );
      }

      console.log(
        `✅ Amicizia bidirezionale rimossa completamente per ${friendId}`
      );
      console.log(
        `⏱️ removeFriend completata in ${Date.now() - startTime}ms - SUCCESS`
      );

      // Invalida cache per force refresh
      console.log(`🗑️ Invalidazione cache dopo rimozione amicizia...`);
      invalidateFriendsCache();

      return true;
    } catch (error) {
      console.error("❌ Errore removeFriend:", error);
      console.log(
        `⏱️ removeFriend fallita in ${Date.now() - startTime}ms - ERROR`
      );
      return false;
    }
  };

  // Context value object
  const value: AuthContextType = {
    session,
    user,
    userStats,
    isLoading,
    isLoggedIn,
    isOnlineFeatureEnabled,
    signIn,
    signUp,
    signOut,
    fetchUserProfile,
    updateProfile,
    updateEmail,
    updatePassword,
    login,
    register,
    logout,
    checkUsernameExists,
    getFriends,
    getFriendRequests,
    getSentFriendRequests,
    invalidateFriendsCache,

    acceptFriendRequest,
    rejectFriendRequest,
    sendFriendRequest,
    removeFriend,
    getGameInvites,
    acceptGameInvite,
    rejectGameInvite,
    sendGameInvite,
    fetchUserStats,
    debugSessionState,
    getSupabaseClient,
    getLeaderboard,
    searchPlayers,
    invalidateLeaderboardCache,
    invalidateSearchCache,
    getCachedFriends,
    getCachedFriendRequests,
    getCachedSentFriendRequests,
    apiCallWithAuth,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

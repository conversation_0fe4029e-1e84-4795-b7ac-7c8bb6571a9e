import { useMemo } from "react";
import { getGameSettings } from "@/services/gameSettingsService";

// Mapping centralizzato per i tappetini da gioco
const TABLE_MAT_CONFIG = {
  astrale: {
    image: "/images/playmat/gallo_astrale_tappetino.png",
    borderColor: "#d4af37", // Oro
  },
  imperiale: {
    image: "/images/playmat/gallo_volante_tappetino.png",
    borderColor: "#8b5e3c", // Marrone default
  },
  cyberpunk: {
    image: "/images/playmat/gallo_cyberpunk_mat.png",
    borderColor: "#8b5e3c",
  },
  anime: {
    image: "/images/playmat/gallo_anime_mat.png",
    borderColor: "#8b5e3c",
  },
  cartone: {
    image: "/images/playmat/gallo_cartoonold_mat.png",
    borderColor: "#8b5e3c",
  },
  drammatico: {
    image: "/images/playmat/gallo_drammatico_mat.png",
    borderColor: "#8b5e3c",
  },
  "logo-rosso": {
    image: "/images/logos/logo-rosso.jpg",
    borderColor: "#ffa500", // Arancione elegante
  },
  romagna: {
    image: "/images/playmat/romagna_mat.png",
    borderColor: "#8b5e3c",
  },
} as const;

export type TableMatId = keyof typeof TABLE_MAT_CONFIG;

/**
 * Hook personalizzato per gestire i tappetini da gioco
 * Ottimizzato con memoization per evitare ricalcoli inutili
 */
export const useTableMat = () => {
  const settings = getGameSettings();
  const selectedMat = settings.selectedTableMat;

  const matConfig = useMemo(() => {
    return TABLE_MAT_CONFIG[selectedMat as TableMatId] || null;
  }, [selectedMat]);

  return {
    selectedMat,
    image: matConfig?.image || null,
    borderColor: matConfig?.borderColor || "#8b5e3c",
    config: matConfig,
  };
};

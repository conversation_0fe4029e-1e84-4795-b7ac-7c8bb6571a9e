import React, { useState, useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import {
  X,
  UserPlus,
  UserMinus,
  Trophy,
  Target,
  Calendar,
  Star,
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import ActionButton from "@/components/ui/ActionButton";
import { getPlayerTitle } from "@/services/playerTitlesService";
import { useAuth } from "@/context/auth-context";
import { useAudio } from "@/hooks/useAudio";
import { useAdBannerVisibility } from "@/hooks/useAdBannerVisibility";
import { Capacitor } from "@capacitor/core";

interface PlayerStats {
  id: string;
  username: string;
  level: number;
  xp: number;
  games_won: number;
  games_played: number;
  win_rate: number;
  created_at?: string;
  last_active?: string;
}

interface PlayerProfileModalProps {
  player: PlayerStats | null;
  isOpen: boolean;
  onClose: () => void;
  currentUserId?: string;
  isFriend?: boolean;
  hasPendingRequest?: boolean;
  hasReceivedRequest?: boolean;
  onAddFriend?: (playerId: string) => Promise<void>;
  onRemoveFriend?: (playerId: string) => Promise<void>;
  onAcceptFriendRequest?: (playerId: string) => Promise<void>;
  onRejectFriendRequest?: (playerId: string) => Promise<void>;
}

const PlayerProfileModal: React.FC<PlayerProfileModalProps> = ({
  player,
  isOpen,
  onClose,
  currentUserId,
  isFriend = false,
  hasPendingRequest = false,
  hasReceivedRequest = false,
  onAddFriend,
  onRemoveFriend,
  onAcceptFriendRequest,
  onRejectFriendRequest,
}) => {
  const { playSound } = useAudio();
  const { shouldShowBanner } = useAdBannerVisibility();
  const [isLoadingFriendAction, setIsLoadingFriendAction] = useState(false);
  const [localIsFriend, setLocalIsFriend] = useState(isFriend);
  const [localHasPendingRequest, setLocalHasPendingRequest] =
    useState(hasPendingRequest);
  const [localHasReceivedRequest, setLocalHasReceivedRequest] =
    useState(hasReceivedRequest);

  // Detect Android platform
  const isAndroid = Capacitor.getPlatform() === "android";

  // ✅ Calcola l'altezza disponibile per la modale considerando footer e banner
  const footerHeight = shouldShowBanner ? 117 : 67; // Come in Index.tsx
  const availableHeight = isAndroid
    ? "100vh" // Full screen on Android
    : `calc(100vh - ${footerHeight + 32}px)`; // 32px per padding top/bottom on web

  // ✅ Ref per tracciare il player ID corrente e evitare problemi di dipendenze
  const currentPlayerIdRef = useRef<string | null>(null); // ✅ Reset stato quando cambia il player o si apre la modale
  useEffect(() => {
    if (isOpen && player?.id && player.id !== currentPlayerIdRef.current) {
      // Nuovo player - reset completo dello stato
      console.log(
        `🔄 PlayerProfileModal: Nuovo player ${player.id}, reset stato`,
        { isFriend, hasPendingRequest, hasReceivedRequest }
      );
      currentPlayerIdRef.current = player.id;
      setLocalIsFriend(isFriend);
      setLocalHasPendingRequest(hasPendingRequest);
      setLocalHasReceivedRequest(hasReceivedRequest);
      setIsLoadingFriendAction(false);
    } else if (
      isOpen &&
      player?.id &&
      player.id === currentPlayerIdRef.current
    ) {
      // Stesso player - aggiorna solo i valori dalle props
      console.log(
        `🔄 PlayerProfileModal: Stesso player ${player.id}, aggiorna props`,
        { isFriend, hasPendingRequest, hasReceivedRequest }
      );
      setLocalIsFriend(isFriend);
      setLocalHasPendingRequest(hasPendingRequest);
      setLocalHasReceivedRequest(hasReceivedRequest);
    }
  }, [isOpen, player?.id, isFriend, hasPendingRequest, hasReceivedRequest]); // ✅ Resetta lo stato quando la modale si chiude
  useEffect(() => {
    if (!isOpen) {
      console.log("🔄 PlayerProfileModal: Modale chiusa, reset completo stato");
      currentPlayerIdRef.current = null;
      setLocalIsFriend(false);
      setLocalHasPendingRequest(false);
      setLocalHasReceivedRequest(false);
      setIsLoadingFriendAction(false);
    }
  }, [isOpen]);

  if (!isOpen || !player) return null;

  const playerTitle = getPlayerTitle(player.level);
  const isOwnProfile = player.id === currentUserId;

  // Calcola le statistiche aggiuntive
  const gamesLost = player.games_played - player.games_won;
  const winStreakEstimate = Math.floor(player.win_rate / 10); // Stima basata sulla percentuale

  // 🎯 MIGLIORATO: Formattazione date più robusta con gestione retrocompatibilità
  const formatDate = (
    dateString?: string,
    isCreatedAt: boolean = false
  ): string => {
    if (!dateString) return "Non disponibile";

    try {
      const date = new Date(dateString);
      // Verifica se la data è valida
      if (isNaN(date.getTime())) return "Non disponibile";

      const now = new Date();
      const diffTime = Math.abs(now.getTime() - date.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      // Per le date di iscrizione, se è molto recente ma l'utente ha molte partite,
      // potrebbe essere un vecchio utente con data non affidabile
      if (isCreatedAt && diffDays < 30 && player.games_played > 10) {
        return "Data non disponibile";
      }

      // Se è molto recente, mostra il tempo relativo
      if (diffDays === 0) {
        return "Oggi";
      } else if (diffDays === 1) {
        return "Ieri";
      } else if (diffDays <= 7) {
        return `${diffDays} giorni fa`;
      } else {
        // Altrimenti mostra la data formattata
        return date.toLocaleDateString("it-IT", {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
        });
      }
    } catch (error) {
      console.warn("Errore formattazione data:", error);
      return "Non disponibile";
    }
  };

  const joinDate = formatDate(player.created_at, true); // true indica che è una data di iscrizione
  const lastActive = formatDate(player.last_active);
  const handleAddFriend = async () => {
    if (!onAddFriend || isLoadingFriendAction) return;

    console.log(
      `👥 PlayerProfileModal: Invio richiesta amicizia a ${player.id}`
    );
    setIsLoadingFriendAction(true);
    try {
      await onAddFriend(player.id);
      setLocalHasPendingRequest(true); // Ora c'è una richiesta pending
      console.log(
        `✅ PlayerProfileModal: Richiesta inviata con successo, localHasPendingRequest = true`
      );
      playSound("success");
    } catch (error) {
      console.error("Errore nell'aggiungere amico:", error);
      playSound("error");
    } finally {
      setIsLoadingFriendAction(false);
    }
  };

  const handleRemoveFriend = async () => {
    if (!onRemoveFriend || isLoadingFriendAction) return;

    setIsLoadingFriendAction(true);
    try {
      await onRemoveFriend(player.id);
      setLocalIsFriend(false);
      playSound("success");
    } catch (error) {
      console.error("Errore nel rimuovere amico:", error);
      playSound("error");
    } finally {
      setIsLoadingFriendAction(false);
    }
  };

  const handleAcceptFriendRequest = async () => {
    if (!onAcceptFriendRequest || isLoadingFriendAction) return;

    console.log(
      `✅ PlayerProfileModal: Accetto richiesta amicizia da ${player.id}`
    );
    setIsLoadingFriendAction(true);
    try {
      await onAcceptFriendRequest(player.id);
      setLocalIsFriend(true); // Ora sono amici
      setLocalHasReceivedRequest(false); // La richiesta non c'è più
      console.log(
        `✅ PlayerProfileModal: Richiesta accettata con successo, ora sono amici`
      );
      playSound("success");
    } catch (error) {
      console.error("Errore nell'accettare richiesta:", error);
      playSound("error");
    } finally {
      setIsLoadingFriendAction(false);
    }
  };

  const handleRejectFriendRequest = async () => {
    if (!onRejectFriendRequest || isLoadingFriendAction) return;

    console.log(
      `❌ PlayerProfileModal: Rifiuto richiesta amicizia da ${player.id}`
    );
    setIsLoadingFriendAction(true);
    try {
      await onRejectFriendRequest(player.id);
      setLocalHasReceivedRequest(false); // La richiesta non c'è più
      console.log(`✅ PlayerProfileModal: Richiesta rifiutata con successo`);
      playSound("success");
    } catch (error) {
      console.error("Errore nel rifiutare richiesta:", error);
      playSound("error");
    } finally {
      setIsLoadingFriendAction(false);
    }
  };

  const handleClose = () => {
    playSound("buttonClick");
    onClose();
  };
  console.log("player :", player);
  return createPortal(
    <div
      className={`fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center player-profile-modal-overlay ${
        isAndroid ? "p-4" : "p-4"
      }`}
      style={{
        zIndex: 99999, // Extremely high z-index to ensure it's above everything
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }}
      onClick={(e) => {
        // Close modal when clicking on backdrop
        if (e.target === e.currentTarget) {
          handleClose();
        }
      }}
    >
      {/* Pulsante chiusura fisso rispetto al viewport */}
      <ActionButton
        onClick={handleClose}
        className={`fixed ${
          isAndroid ? "top-6 right-6" : "top-4 right-4"
        } p-3 bg-red-500/90 hover:bg-red-600 text-white rounded-full transition-colors shadow-lg border-2 border-white`}
        style={{
          zIndex: 100000, // Even higher z-index for close button
        }}
      >
        <X className="h-6 w-6" />
      </ActionButton>

      <div
        className={`w-full overflow-y-auto relative player-profile-modal-content ${
          isAndroid ? "max-w-sm mx-4" : "max-w-md"
        }`}
        style={{
          maxHeight: isAndroid ? "85vh" : availableHeight,
          zIndex: 99999, // Ensure content is also properly layered
        }}
        onClick={(e) => {
          // Prevent modal from closing when clicking inside content
          e.stopPropagation();
        }}
      >
        <Card
          className="border-2 border-amber-800/30 shadow-2xl bg-gradient-to-br from-amber-50/95 via-orange-50/95 to-yellow-50/95 backdrop-blur-md rounded-xl"
          style={{
            position: "relative",
            zIndex: 99999,
          }}
        >
          <CardContent
            className={`space-y-4 ${isAndroid ? "p-4 pt-12" : "p-6"}`}
          >
            {/* ...existing code... */}
            <div className="text-center">
              <div className="relative inline-block mb-4">
                <div className="w-24 h-24 rounded-full bg-gradient-to-br from-amber-100 to-yellow-100 flex items-center justify-center border-4 border-amber-300 mx-auto">
                  <img
                    src={playerTitle.rankImage}
                    alt={playerTitle.title}
                    className="w-16 h-16 object-contain"
                  />
                </div>
                {/* Badge livello */}
                <div className="absolute -bottom-2 bg-amber-500 text-white text-sm font-bold px-3 py-1 rounded-full border-2 border-white">
                  Lv. {player.level}
                </div>
              </div>
              <h3
                className="text-2xl font-bold text-romagna-darkWood mb-1 truncate px-2"
                style={{ fontFamily: "'DynaPuff', cursive" }}
                title={player.username} // Mostra il nome completo al hover
              >
                {player.username}
              </h3>
              <p className="text-amber-700 font-medium mb-2">
                {playerTitle.title}
              </p>{" "}
            </div>
            {/* ...existing code... */}
            {/* Azioni amicizia - Solo se non è il proprio profilo */}
            {!isOwnProfile && (onAddFriend || onRemoveFriend) && (
              <div className="space-y-3">
                {localIsFriend ? (
                  <Button
                    onClick={handleRemoveFriend}
                    disabled={isLoadingFriendAction}
                    className="w-full bg-red-500 hover:bg-red-600 text-white py-3 text-lg font-semibold rounded-xl transition-all duration-200 flex items-center justify-center gap-2"
                  >
                    <UserMinus className="h-5 w-5" />
                    {isLoadingFriendAction ? "Rimozione..." : "Rimuovi Amico"}
                  </Button>
                ) : localHasReceivedRequest ? (
                  // Mostra due pulsanti per accettare o rifiutare la richiesta ricevuta
                  <div className="space-y-2">
                    <Button
                      onClick={handleAcceptFriendRequest}
                      disabled={isLoadingFriendAction}
                      className="w-full bg-green-500 hover:bg-green-600 text-white py-3 text-lg font-semibold rounded-xl transition-all duration-200 flex items-center justify-center gap-2"
                    >
                      <UserPlus className="h-5 w-5" />
                      {isLoadingFriendAction
                        ? "Accettando..."
                        : "Accetta Richiesta"}
                    </Button>
                    <Button
                      onClick={handleRejectFriendRequest}
                      disabled={isLoadingFriendAction}
                      className="w-full bg-red-500 hover:bg-red-600 text-white py-3 text-lg font-semibold rounded-xl transition-all duration-200 flex items-center justify-center gap-2"
                    >
                      <X className="h-5 w-5" />
                      {isLoadingFriendAction
                        ? "Rifiutando..."
                        : "Rifiuta Richiesta"}
                    </Button>
                  </div>
                ) : localHasPendingRequest ? (
                  <Button
                    disabled={true}
                    className="w-full bg-orange-400 text-white py-3 text-lg font-semibold rounded-xl cursor-not-allowed flex items-center justify-center gap-2"
                  >
                    <Trophy className="h-5 w-5" />
                    Richiesta Inviata
                  </Button>
                ) : (
                  <Button
                    onClick={handleAddFriend}
                    disabled={isLoadingFriendAction}
                    className="w-full bg-green-500 hover:bg-green-600 text-white py-3 text-lg font-semibold rounded-xl transition-all duration-200 flex items-center justify-center gap-2"
                  >
                    <UserPlus className="h-5 w-5" />
                    {isLoadingFriendAction ? "Invio..." : "Aggiungi Amico"}
                  </Button>
                )}
              </div>
            )}
            {/* ...existing code... */}
            {/* Statistiche principali */}
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white/70 rounded-lg p-4 text-center border border-amber-200">
                <Trophy className="h-6 w-6 text-amber-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-romagna-darkWood">
                  {player.games_won}
                </div>
                <div className="text-sm text-amber-700">Vittorie</div>
              </div>
              <div className="bg-white/70 rounded-lg p-4 text-center border border-amber-200">
                <Target className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-romagna-darkWood">
                  {player.win_rate.toFixed(1)}%
                </div>
                <div className="text-sm text-amber-700">Tasso Vittoria</div>
              </div>
            </div>
            {/* ...existing code... */}
            {/* Statistiche dettagliate */}
            <div className="space-y-3">
              <h4
                className="text-lg font-semibold text-romagna-darkWood flex items-center gap-2"
                style={{ fontFamily: "'DynaPuff', cursive" }}
              >
                Statistiche Dettagliate
              </h4>
              <div className="bg-white/70 rounded-lg p-4 border border-amber-200 space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-amber-700">Partite Giocate:</span>
                  <span className="font-semibold text-romagna-darkWood">
                    {player.games_played}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-amber-700">Partite Perse:</span>
                  <span className="font-semibold text-romagna-darkWood">
                    {gamesLost}
                  </span>
                </div>
                {/* <div className="flex justify-between items-center">
                  <span className="text-amber-700">
                    Serie Vittorie Stimate:
                  </span>
                  <span className="font-semibold text-romagna-darkWood">
                    {winStreakEstimate}
                  </span>
                </div> */}
                <div className="flex justify-between items-center">
                  <span className="text-amber-700">Data Iscrizione:</span>
                  <span className="font-semibold text-romagna-darkWood text-sm">
                    {joinDate}
                  </span>
                </div>
                {/* <div className="flex justify-between items-center">
                  <span className="text-amber-700">Ultima Attività:</span>
                  <span className="font-semibold text-romagna-darkWood text-sm">
                    {lastActive}
                  </span>
                </div> */}
              </div>
            </div>
            {/* ...existing code... */}
            {/* Progressione livello */}
            <div className="space-y-3 pb-5">
              <h4
                className="text-lg font-semibold text-romagna-darkWood"
                style={{ fontFamily: "'DynaPuff', cursive" }}
              >
                Progressione
              </h4>
              <div className="bg-white/70 rounded-lg p-4 border border-amber-200">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-amber-700">Livello Attuale:</span>
                  <span className="font-bold text-romagna-darkWood">
                    {player.level}
                  </span>
                </div>
                {/* Barra progresso XP (semplificata) */}
                <div className="w-full bg-amber-200 rounded-full h-3 mb-2">
                  <div
                    className="bg-gradient-to-r from-amber-500 to-yellow-500 h-3 rounded-full transition-all duration-300"
                    style={{
                      width: `${Math.min(100, (player.xp % 1000) / 10)}%`,
                    }}
                  ></div>
                </div>{" "}
                <div className="text-xs text-amber-600 text-center">
                  {player.xp.toLocaleString()} XP totali
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>,
    document.body
  );
};

export default PlayerProfileModal;

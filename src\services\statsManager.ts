/**
 * 🎯 GESTORE STATISTICHE UNIFICATO
 * Sistema centralizzato che elimina tutti i conflitti tra i vari sistemi
 */

import {
  calculateLevelFromXp,
  calculateGameXp,
  getXpRequiredForLevel,
  getXpForNextLevel,
  getProgressToNextLevel,
  isFirstWinOfDay,
} from "./experienceSystem";

// Interfaccia unificata per le statistiche
export interface UnifiedStats {
  level: number;
  xp: number;
  gamesPlayed: number;
  gamesWon: number;
  winRate: number;
  currentWinStreak: number;
  bestWinStreak: number;
  lastPlayed: string;
  lastWinDate?: string; // Data dell'ultima vittoria per bonus giornaliero
  updatedAt: string;
}

// Chiave unica per localStorage
const STATS_STORAGE_KEY = "maraffa_unified_stats";

// Statistiche di default
const DEFAULT_STATS: UnifiedStats = {
  level: 1,
  xp: 0,
  gamesPlayed: 0,
  gamesWon: 0,
  winRate: 0,
  currentWinStreak: 0,
  bestWinStreak: 0,
  lastPlayed: new Date().toISOString(),
  lastWinDate: undefined,
  updatedAt: new Date().toISOString(),
};

/**
 * 📖 CARICA STATISTICHE (UNICA FONTE DI VERITÀ)
 */
export const loadStats = (): UnifiedStats => {
  try {
    const stored = localStorage.getItem(STATS_STORAGE_KEY);
    if (stored) {
      const data = JSON.parse(stored) as UnifiedStats;

      // Validazione e correzione automatica
      const correctedLevel = calculateLevelFromXp(data.xp);
      if (data.level !== correctedLevel) {
        console.warn(
          `🔧 Correzione automatica livello: ${data.level} → ${correctedLevel} (XP: ${data.xp})`
        );
        data.level = correctedLevel;
        saveStats(data);
      }

      return { ...DEFAULT_STATS, ...data };
    }
  } catch (error) {
    console.error("❌ Errore caricamento statistiche:", error);
  }

  return { ...DEFAULT_STATS };
};

/**
 * 💾 SALVA STATISTICHE (UNICA FONTE DI SCRITTURA)
 */
export const saveStats = (stats: Partial<UnifiedStats>): UnifiedStats => {
  try {
    const current = loadStats();
    const updated: UnifiedStats = {
      ...current,
      ...stats,
      updatedAt: new Date().toISOString(),
    };

    // Validazione automatica del livello
    const correctLevel = calculateLevelFromXp(updated.xp);
    if (updated.level !== correctLevel) {
      console.warn(
        `🔧 Correzione automatica livello durante salvataggio: ${updated.level} → ${correctLevel}`
      );
      updated.level = correctLevel;
    }

    // Calcola win rate
    if (updated.gamesPlayed > 0) {
      updated.winRate =
        Math.round((updated.gamesWon / updated.gamesPlayed) * 100) / 100;
    }

    localStorage.setItem(STATS_STORAGE_KEY, JSON.stringify(updated));

    // Sincronizza con altri sistemi per compatibilità
    syncToOtherSystems(updated);

    console.log("✅ Statistiche salvate:", {
      level: updated.level,
      xp: updated.xp,
      games: updated.gamesPlayed,
    });

    return updated;
  } catch (error) {
    console.error("❌ Errore salvataggio statistiche:", error);
    return loadStats();
  }
};

/**
 * 🎮 AGGIORNA STATISTICHE DOPO PARTITA
 */
export const updateStatsAfterGame = (gameData: {
  isWinner: boolean;
  difficulty: "easy" | "medium" | "hard";
  maraffeMade?: number;
  isPerfectGame?: boolean;
  isComeback?: boolean;
  isDominantWin?: boolean;
  isAbandoned?: boolean;
}): {
  stats: UnifiedStats;
  leveledUp: boolean;
  xpGained: number;
  xpBreakdown: string[];
} => {
  const currentStats = loadStats();
  const oldLevel = currentStats.level;

  // Verifica se è la prima vittoria del giorno
  const isFirstWinToday =
    gameData.isWinner &&
    !gameData.isAbandoned &&
    isFirstWinOfDay(currentStats.lastWinDate);

  // Calcola XP guadagnati
  let xpResult;
  if (gameData.isAbandoned) {
    xpResult = {
      totalXp: 0,
      breakdown: ["Partita abbandonata: 0 XP"],
    };
  } else {
    xpResult = calculateGameXp({
      isWinner: gameData.isWinner,
      difficulty: gameData.difficulty,
      maraffeMade: gameData.maraffeMade || 0,
      isPerfectGame: gameData.isPerfectGame || false,
      isComeback: gameData.isComeback || false,
      isDominantWin: gameData.isDominantWin || false,
      isFirstWinOfDay: isFirstWinToday,
      currentWinStreak: currentStats.currentWinStreak,
    });
  }

  // Aggiorna statistiche
  const isActualWinner = gameData.isWinner && !gameData.isAbandoned;
  const newGamesPlayed = currentStats.gamesPlayed + 1;
  const newGamesWon = isActualWinner
    ? currentStats.gamesWon + 1
    : currentStats.gamesWon;
  const newXp = currentStats.xp + xpResult.totalXp;
  const newLevel = calculateLevelFromXp(newXp);

  // Gestisci win streak
  let newCurrentStreak = currentStats.currentWinStreak;
  let newBestStreak = currentStats.bestWinStreak;

  if (isActualWinner) {
    newCurrentStreak += 1;
    newBestStreak = Math.max(newBestStreak, newCurrentStreak);
  } else {
    newCurrentStreak = 0;
  }

  // Salva le nuove statistiche
  const updatedStats = saveStats({
    level: newLevel,
    xp: newXp,
    gamesPlayed: newGamesPlayed,
    gamesWon: newGamesWon,
    currentWinStreak: newCurrentStreak,
    bestWinStreak: newBestStreak,
    lastPlayed: new Date().toISOString(),
    // Aggiorna lastWinDate solo se è una vittoria
    ...(isActualWinner && { lastWinDate: new Date().toISOString() }),
  });

  const leveledUp = newLevel > oldLevel;

  console.log("🎯 Statistiche aggiornate:", {
    oldLevel,
    newLevel,
    leveledUp,
    xpGained: xpResult.totalXp,
    totalXp: newXp,
  });

  return {
    stats: updatedStats,
    leveledUp,
    xpGained: xpResult.totalXp,
    xpBreakdown: xpResult.breakdown,
  };
};

/**
 * 📊 OTTIENI INFORMAZIONI PROGRESSIONE
 */
export const getProgressionInfo = (stats?: UnifiedStats) => {
  const currentStats = stats || loadStats();
  const currentLevel = currentStats.level;
  const currentXp = currentStats.xp;

  const xpForCurrentLevel = getXpRequiredForLevel(currentLevel);
  const xpForNextLevel = getXpForNextLevel(currentLevel);
  const progressXp = currentXp - xpForCurrentLevel;
  const progressPercentage = getProgressToNextLevel(currentXp, currentLevel);

  return {
    currentLevel,
    currentXp,
    xpForCurrentLevel,
    xpForNextLevel,
    progressXp,
    progressPercentage,
  };
};

/**
 * 🔄 SINCRONIZZA CON ALTRI SISTEMI (per compatibilità)
 */
const syncToOtherSystems = (stats: UnifiedStats) => {
  try {
    // Sincronizza con sistema unificato esistente
    const unifiedData = {
      level: stats.level,
      xp: stats.xp,
      gamesPlayed: stats.gamesPlayed,
      gamesWon: stats.gamesWon,
      winRate: stats.winRate,
      currentWinStreak: stats.currentWinStreak,
      bestWinStreak: stats.bestWinStreak,
      lastPlayed: stats.lastPlayed,
      updatedAt: stats.updatedAt,
    };

    // Aggiorna sistema unificato esistente
    const existingUnified = localStorage.getItem("maraffa_unified_data");
    if (existingUnified) {
      const parsed = JSON.parse(existingUnified);
      const merged = { ...parsed, ...unifiedData };
      localStorage.setItem("maraffa_unified_data", JSON.stringify(merged));
    }

    // Sincronizza con sistema locale esistente
    const localStats = {
      level: stats.level,
      xp: stats.xp,
      totalGames: stats.gamesPlayed,
      gamesWon: stats.gamesWon,
      gamesLost: stats.gamesPlayed - stats.gamesWon,
      winRate: Math.round(stats.winRate),
      currentWinStreak: stats.currentWinStreak,
      bestWinStreak: stats.bestWinStreak,
      lastPlayed: stats.lastPlayed,
      updatedAt: stats.updatedAt,
      // Altri campi di default
      maraffeMade: 0,
      achievementsUnlocked: [],
      recentGames: [],
      createdAt: stats.updatedAt,
    };

    localStorage.setItem("maraffa_player_stats", JSON.stringify(localStats));
  } catch (error) {
    console.warn("⚠️ Errore sincronizzazione con altri sistemi:", error);
  }
};

// Cache per evitare riparazioni troppo frequenti
let lastRepairTime = 0;
const REPAIR_COOLDOWN = 5000; // 5 secondi di cooldown

/**
 * 🔧 RIPARA STATISTICHE CORROTTE
 */
export const repairStats = (): UnifiedStats => {
  const now = Date.now();

  // Evita riparazioni troppo frequenti
  if (now - lastRepairTime < REPAIR_COOLDOWN) {
    return loadStats(); // Ritorna stats esistenti senza log
  }

  lastRepairTime = now;
  console.log("🔧 Riparazione statistiche in corso...");

  const stats = loadStats();
  const correctLevel = calculateLevelFromXp(stats.xp);

  if (stats.level !== correctLevel) {
    console.log(
      `🔧 Correzione livello: ${stats.level} → ${correctLevel} (XP: ${stats.xp})`
    );
    return saveStats({ level: correctLevel });
  }

  console.log("✅ Statistiche già corrette");
  return stats;
};

/**
 * 🗑️ RESET STATISTICHE
 */
export const resetStats = (): UnifiedStats => {
  localStorage.removeItem(STATS_STORAGE_KEY);
  localStorage.removeItem("maraffa_unified_data");
  localStorage.removeItem("maraffa_player_stats");

  const newStats = { ...DEFAULT_STATS };
  return saveStats(newStats);
};

// Export per compatibilità con codice esistente
export const getPlayerStats = loadStats;
export const calculateLevel = calculateLevelFromXp;

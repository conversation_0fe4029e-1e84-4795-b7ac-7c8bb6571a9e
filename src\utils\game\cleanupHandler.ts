import { GameState } from "./gameLogic";
import { TrumpAnnouncement } from "./trumpHandler";
import { ActionType } from "@/components/game/GameActionBadge";

export type ActionAnnouncement = {
  type: ActionType | null;
  playerIndex: number;
};

export const cleanupGame = (
  gameState: GameState,
  setShowExitDialog: (value: boolean) => void,
  setShowHelpSheet: (value: boolean) => void,
  setShowingLastTrick: (value: boolean) => void,
  setLastPlayedCard: (value: null) => void,
  setCardHasBeenPlayed: (value: boolean) => void,
  setActionAnnouncement: (value: ActionAnnouncement) => void,
  setTrumpAnnouncement: (value: TrumpAnnouncement) => void
): void => {
  setShowExitDialog(false);
  setShowHelpSheet(false);
  setShowingLastTrick(false);
  setLastPlayedCard(null);
  setCardHasBeenPlayed(false);
  setActionAnnouncement({ type: null, playerIndex: -1 });
  setTrumpAnnouncement({ suit: null, playerName: "", visible: false });
};

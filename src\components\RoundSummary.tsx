import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Crown, Award, ArrowRight } from "lucide-react";

interface RoundSummaryProps {
  visible: boolean;
  roundScore: [number, number];
  totalScore: [number, number];
  onContinue: () => void; // Manteniamo questa prop per compatibilità
  onSkip?: () => void; // Nuova prop per saltare il countdown
  countdown: number;
}

const RoundSummary = ({
  visible,
  roundScore,
  totalScore,
  countdown,
  onSkip,
}: RoundSummaryProps) => {
  const [isVisible, setIsVisible] = useState(visible);

  useEffect(() => {
    setIsVisible(visible);
  }, [visible]);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 pointer-events-auto"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="bg-gradient-to-br from-yellow-50 to-amber-100 border-2 border-yellow-400 rounded-lg shadow-xl p-4 w-80 max-w-[90vw] text-center"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
          >
            <div className="flex justify-between items-center mb-2">
              <h2 className="text-lg font-bold text-yellow-800">
                Fine del Round
              </h2>
            </div>
            <div className="my-2 p-2 bg-amber-50 rounded-lg border border-amber-200">
              <div className="flex justify-center items-center gap-6">
                <div className="text-center">
                  <p className="font-semibold text-amber-900 text-sm">
                    Squadra 1
                  </p>
                  <p
                    className={`text-xl font-bold ${
                      roundScore[0] > roundScore[1]
                        ? "text-green-600"
                        : "text-amber-700"
                    }`}
                  >
                    {roundScore[0]}
                  </p>
                </div>
                <div className="text-amber-600 text-lg">vs</div>
                <div className="text-center">
                  <p className="font-semibold text-amber-900 text-sm">
                    Squadra 2
                  </p>
                  <p
                    className={`text-xl font-bold ${
                      roundScore[1] > roundScore[0]
                        ? "text-green-600"
                        : "text-amber-700"
                    }`}
                  >
                    {roundScore[1]}
                  </p>
                </div>
              </div>
            </div>
            <div className="flex justify-between items-center text-sm text-amber-700">
              <div className="flex items-center gap-1">
                <Award className="h-4 w-4" />
                <span>Totale:</span>
              </div>
              <div className="font-bold">
                {totalScore[0]} - {totalScore[1]}
              </div>
            </div>{" "}
            <motion.div
              className="mt-2 text-xs text-amber-600 flex items-center justify-center gap-1"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ repeat: Infinity, duration: 2 }}
            >
              <span>Nuovo round in {countdown} secondi</span>
              <ArrowRight className="h-3 w-3" />
            </motion.div>
            {/* Bottone per saltare il countdown */}
            {onSkip && (
              <motion.button
                onClick={onSkip}
                className="mt-3 px-4 py-2 bg-amber-600 hover:bg-amber-700 text-white text-sm font-medium rounded-lg transition-colors duration-200 flex items-center justify-center gap-2 mx-auto"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span>Prossima mano</span>
              </motion.button>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default RoundSummary;

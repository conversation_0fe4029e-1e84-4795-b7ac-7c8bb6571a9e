import { Card } from "../../game/cardUtils";

/**
 * Tipi relativi alle strategie dell'AI
 */

export enum AIDifficulty {
  EASY = 0,
  MEDIUM = 1,
  HARD = 2,
}

export interface AIStrategy {
  strategy:
    | "support"
    | "compete"
    | "neutral"
    | "maximize_points"
    | "compete_for_trick"
    | "support_passive";
  recommendedCard?: Card;
  reason?: string;
  expectedPoints?: number;
}

export interface TeammateAnalysis {
  teammateIsWinning: boolean;
  teammatePosition: number;
  winningCard: Card | null;
  shouldSupport: boolean;
}

export interface TrickEvaluation {
  worthiness: number;
  shouldAttempt: boolean;
}

export interface TeamTrickAnalysis {
  trickValue: number;
  potentialTeamPoints: number;
  teammateWinning: boolean;
  teammateCard: Card | null;
  shouldMaximizePoints: boolean;
  bestSupportCards: Card[];
}

export interface TrumpExhaustionResult {
  shouldExhaust: boolean;
  cardToPlay?: Card;
}

export interface StrategyContext {
  difficulty: AIDifficulty;
  gamePhase: "early" | "middle" | "late";
  isLastTrick: boolean;
  trickNumber: number;
  handSize: number;
}

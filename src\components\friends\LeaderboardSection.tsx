import React, { useState, useEffect } from "react";
import { RefreshCcw } from "lucide-react";
import ActionButton from "@/components/ui/ActionButton";
import { supabase } from "@/integrations/supabase/client";

interface LeaderboardPlayer {
  username: string;
  wins: number;
  level: number;
}

const LeaderboardSection: React.FC = () => {
  const [leaderboard, setLeaderboard] = useState<LeaderboardPlayer[]>([]);
  const [loadingLeaderboard, setLoadingLeaderboard] = useState(false);

  useEffect(() => {
    loadLeaderboard();
  }, []);

  const loadLeaderboard = async () => {
    setLoadingLeaderboard(true);
    try {
      // Load from game_stats table and join with profiles
      const { data, error } = await supabase
        .from("game_stats")
        .select(
          `
          games_won, 
          level,
          profiles!game_stats_user_id_fkey(username)
        `
        )
        .order("games_won", { ascending: false })
        .limit(10);

      if (error) throw error;

      // Map the nested data structure
      const formattedData = data.map((item) => ({
        username: item.profiles?.username || "Unknown",
        wins: item.games_won,
        level: item.level,
      }));

      // If we don't have enough real players, add some mock players
      if (formattedData.length < 10) {
        const mockPlayers = [
          { username: "RomagnoloDoc", wins: 87, level: 12 },
          { username: "PiadaMaster", wins: 76, level: 10 },
          { username: "CardKing94", wins: 65, level: 9 },
          { username: "MaraffaQueen", wins: 59, level: 8 },
          { username: "BorlengoDamer", wins: 52, level: 7 },
          { username: "CuscinettoFan", wins: 45, level: 6 },
          { username: "StrozzapretiLover", wins: 37, level: 5 },
          { username: "TrebbianoPlayer", wins: 29, level: 4 },
          { username: "SangioveseKid", wins: 22, level: 3 },
          { username: "CappellettiAce", wins: 15, level: 2 },
        ];

        // Add enough mock players to reach 10 total
        const neededMockPlayers = 10 - formattedData.length;
        const leaderboardData = [
          ...formattedData,
          ...mockPlayers.slice(0, neededMockPlayers),
        ];

        // Sort by wins
        leaderboardData.sort((a, b) => b.wins - a.wins);

        setLeaderboard(leaderboardData);
      } else {
        setLeaderboard(formattedData);
      }
    } catch (error) {
      console.error("Error loading leaderboard:", error);

      // If error, use mock data
      const mockPlayers = [
        { username: "RomagnoloDoc", wins: 87, level: 12 },
        { username: "PiadaMaster", wins: 76, level: 10 },
        { username: "CardKing94", wins: 65, level: 9 },
        { username: "MaraffaQueen", wins: 59, level: 8 },
        { username: "BorlengoDamer", wins: 52, level: 7 },
        { username: "CuscinettoFan", wins: 45, level: 6 },
        { username: "StrozzapretiLover", wins: 37, level: 5 },
        { username: "TrebbianoPlayer", wins: 29, level: 4 },
        { username: "SangioveseKid", wins: 22, level: 3 },
        { username: "CappellettiAce", wins: 15, level: 2 },
      ];

      setLeaderboard(mockPlayers);
    } finally {
      setLoadingLeaderboard(false);
    }
  };

  if (loadingLeaderboard) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin h-8 w-8 border-4 border-red-500 border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-red-700">Top 10 Giocatori</h3>

      <div className="overflow-hidden bg-orange-50 rounded-lg border border-red-200">
        <div className="grid grid-cols-12 text-sm font-medium bg-gradient-to-r from-red-100 to-yellow-100 p-3">
          <div className="col-span-1 text-center">#</div>
          <div className="col-span-7">Giocatore</div>
          <div className="col-span-2 text-center">Vittorie</div>
          <div className="col-span-2 text-center">Livello</div>
        </div>

        {leaderboard.length > 0 ? (
          <div className="divide-y divide-red-100">
            {leaderboard.map((player, index) => (
              <div
                key={index}
                className="grid grid-cols-12 p-3 hover:bg-red-50 transition-colors items-center"
              >
                <div className="col-span-1 text-center font-bold">
                  {index === 0 ? (
                    <span className="text-yellow-500">🏆</span>
                  ) : index === 1 ? (
                    <span className="text-gray-400">🥈</span>
                  ) : index === 2 ? (
                    <span className="text-amber-600">🥉</span>
                  ) : (
                    index + 1
                  )}
                </div>
                <div className="col-span-7 flex items-center gap-2">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-br from-red-400 to-yellow-400 flex items-center justify-center text-white font-medium">
                    {player.username.charAt(0).toUpperCase()}
                  </div>
                  <span className="font-medium truncate">
                    {player.username}
                  </span>
                </div>
                <div className="col-span-2 text-center">{player.wins}</div>
                <div className="col-span-2 text-center">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    Lvl {player.level}
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center p-6 text-gray-500">
            Nessun giocatore trovato
          </div>
        )}
      </div>

      <div className="flex justify-center">
        <ActionButton
          variant="outline"
          size="sm"
          onClick={loadLeaderboard}
          className="text-xs border-red-200"
        >
          <RefreshCcw className="h-3 w-3 mr-1" /> Aggiorna classifica
        </ActionButton>
      </div>
    </div>
  );
};

export default LeaderboardSection;

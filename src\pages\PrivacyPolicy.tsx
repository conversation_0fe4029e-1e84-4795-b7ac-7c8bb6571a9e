import { useIsMobile } from "@/hooks/use-mobile";
import { useHardwareBackButton } from "@/hooks/useHardwareBackButton";
import PageHeader from "@/components/common/PageHeader";
import { ShieldCheck } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { PRIVACY_POLICY } from "@/data/legalTexts";
import ReactMarkdown from "react-markdown";

const PrivacyPolicy = () => {
  const isMobile = useIsMobile();
  useHardwareBackButton("/account", isMobile);

  return (
    <div className="rules-page min-h-screen bg-gradient-to-br from-yellow-50/80 to-orange-100/60">
      <PageHeader
        title="Privacy Policy"
        icon={
          <ShieldCheck className="h-7 w-7 text-marafone-red drop-shadow-md" />
        }
        backRoute="/account"
      />

      <div className="md:max-w-4xl md:mx-auto px-0 md:px-4 mt-4">
        <main className="py-2">
          <Card className="overflow-hidden border-0 md:border-4 border-amber-800/50 shadow-xl relative bg-gradient-to-br from-amber-50/90 to-orange-100/60">
            <CardContent className="p-4 md:p-8 relative z-10 prose prose-sm md:prose-base max-w-none">
              <ReactMarkdown>{PRIVACY_POLICY}</ReactMarkdown>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  );
};

export default PrivacyPolicy;

/**
 * 🎯 SERVIZIO STORAGE UNIFICATO
 * Sostituisce localStatsService e user-state-context
 * con un unico sistema semplificato
 */

import { calculateLevelFromXp, calculateGameXp } from "./experienceSystem";

// Interfaccia unificata per tutti i dati utente
export interface UnifiedUserData {
  // Statistiche di gioco
  level: number;
  xp: number;
  gamesPlayed: number;
  gamesWon: number;
  winRate: number;
  currentWinStreak: number;
  bestWinStreak: number;

  // Profilo utente
  username: string;
  rank: string;
  title: string;

  // Impostazioni gioco
  difficulty: "easy" | "medium" | "hard";
  victoryPoints: "21" | "31" | "41";
  selectedTableMat: string;

  // Impostazioni audio
  audioEnabled: boolean;
  soundEffectsEnabled: boolean;
  musicEnabled: boolean;
  masterVolume: number;
  soundEffectsVolume: number;
  musicVolume: number;

  // Metadata
  isOfflineMode: boolean;
  lastPlayed: string;
  createdAt: string;
  updatedAt: string;
}

// Chiave unica per localStorage
const UNIFIED_STORAGE_KEY = "maraffa_unified_data";

// Dati di default
const DEFAULT_USER_DATA: UnifiedUserData = {
  // Stats
  level: 1,
  xp: 0,
  gamesPlayed: 0,
  gamesWon: 0,
  winRate: 0,
  currentWinStreak: 0,
  bestWinStreak: 0,

  // Profile
  username: "Pulcino Romagnolo",
  rank: "Principiante",
  title: "Pulcino Romagnolo",

  // Game settings
  difficulty: "easy",
  victoryPoints: "31",
  selectedTableMat: "marrone",

  // Audio settings
  audioEnabled: true,
  soundEffectsEnabled: true,
  musicEnabled: false,
  masterVolume: 0.7,
  soundEffectsVolume: 0.8,
  musicVolume: 0.2,

  // Metadata
  isOfflineMode: true,
  lastPlayed: new Date().toISOString(),
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

/**
 * 📖 CARICA DATI UNIFICATI
 */
export const loadUnifiedData = (): UnifiedUserData => {
  try {
    const stored = localStorage.getItem(UNIFIED_STORAGE_KEY);
    if (stored) {
      const data = JSON.parse(stored) as UnifiedUserData;
      // Merge con default per retrocompatibilità
      return {
        ...DEFAULT_USER_DATA,
        ...data,
        updatedAt: new Date().toISOString(),
      };
    }
  } catch (error) {
    console.warn("Errore nel caricamento dati unificati:", error);
  }

  return { ...DEFAULT_USER_DATA };
};

/**
 * 💾 SALVA DATI UNIFICATI
 */
export const saveUnifiedData = (data: Partial<UnifiedUserData>): void => {
  try {
    const current = loadUnifiedData();
    const updated = {
      ...current,
      ...data,
      updatedAt: new Date().toISOString(),
    };

    localStorage.setItem(UNIFIED_STORAGE_KEY, JSON.stringify(updated));
  } catch (error) {
    console.error("Errore nel salvataggio dati unificati:", error);
  }
};

/**
 * 🎮 AGGIORNA STATISTICHE GIOCO
 */
export const updateGameStats = (
  won: boolean,
  difficulty: "easy" | "medium" | "hard" = "medium"
): void => {
  const current = loadUnifiedData();

  const newGamesPlayed = current.gamesPlayed + 1;
  const newGamesWon = won ? current.gamesWon + 1 : current.gamesWon;
  const newWinRate =
    newGamesPlayed > 0 ? (newGamesWon / newGamesPlayed) * 100 : 0;

  let newCurrentStreak = current.currentWinStreak;
  let newBestStreak = current.bestWinStreak;

  if (won) {
    newCurrentStreak += 1;
    newBestStreak = Math.max(newBestStreak, newCurrentStreak);
  } else {
    newCurrentStreak = 0;
  }

  // Calcola XP e livello usando il sistema di esperienza unificato
  const xpResult = calculateGameXp({
    isWinner: won,
    difficulty: difficulty,
    // Parametri di default per il sistema unificato semplificato
    maraffeMade: 0,
    isPerfectGame: false,
    isComeback: false,
    isDominantWin: false,
    isFirstWinOfDay: false,
    currentWinStreak: current.currentWinStreak,
  });
  const newXp = current.xp + xpResult.totalXp;
  const newLevel = calculateLevelFromXp(newXp);

  saveUnifiedData({
    gamesPlayed: newGamesPlayed,
    gamesWon: newGamesWon,
    winRate: Math.round(newWinRate * 100) / 100,
    currentWinStreak: newCurrentStreak,
    bestWinStreak: newBestStreak,
    xp: newXp,
    level: newLevel,
    lastPlayed: new Date().toISOString(),
  });
};

/**
 * ⚙️ AGGIORNA IMPOSTAZIONI GIOCO
 */
export const updateGameSettings = (settings: {
  difficulty?: "easy" | "medium" | "hard";
  victoryPoints?: "21" | "31" | "41";
  selectedTableMat?: string;
}): void => {
  saveUnifiedData(settings);
};

/**
 * 🔊 AGGIORNA IMPOSTAZIONI AUDIO
 */
export const updateAudioSettings = (settings: {
  audioEnabled?: boolean;
  soundEffectsEnabled?: boolean;
  musicEnabled?: boolean;
  masterVolume?: number;
  soundEffectsVolume?: number;
  musicVolume?: number;
}): void => {
  saveUnifiedData(settings);
};

/**
 * 👤 AGGIORNA PROFILO UTENTE
 */
export const updateUserProfile = (profile: {
  username?: string;
  rank?: string;
  title?: string;
}): void => {
  saveUnifiedData(profile);
};

/**
 * 🧹 RESET COMPLETO
 */
export const resetAllData = (): void => {
  localStorage.removeItem(UNIFIED_STORAGE_KEY);
};

/**
 * 📤 ESPORTA DATI (BACKUP)
 */
export const exportData = (): string => {
  const data = loadUnifiedData();
  return JSON.stringify(data, null, 2);
};

/**
 * 📥 IMPORTA DATI (RESTORE)
 */
export const importData = (jsonData: string): boolean => {
  try {
    const data = JSON.parse(jsonData) as UnifiedUserData;
    saveUnifiedData(data);
    return true;
  } catch (error) {
    console.error("Errore nell'importazione dati:", error);
    return false;
  }
};

// Export per compatibilità con codice esistente
export const getPlayerStats = () => {
  const data = loadUnifiedData();

  return {
    level: data.level,
    xp: data.xp,
    totalGames: data.gamesPlayed,
    gamesWon: data.gamesWon,
    gamesLost: data.gamesPlayed - data.gamesWon,
    winRate: data.winRate,
    currentWinStreak: data.currentWinStreak,
    bestWinStreak: data.bestWinStreak,
    lastPlayed: data.lastPlayed,
    recentGames: [], // 🎯 AGGIUNTO: Inizializza sempre array vuoto per compatibilità
    maraffeMade: 0, // 🎯 AGGIUNTO: Campo richiesto da PlayerStats
    achievementsUnlocked: [], // 🎯 AGGIUNTO: Campo richiesto da PlayerStats
    lastWinDate: undefined, // 🎯 AGGIUNTO: Campo richiesto da PlayerStats
    createdAt: data.createdAt,
    updatedAt: data.updatedAt,
  };
};

export const getGameSettings = () => {
  const data = loadUnifiedData();
  return {
    difficulty: data.difficulty,
    victoryPoints: data.victoryPoints,
    selectedTableMat: data.selectedTableMat,
  };
};

/**
 * 🔄 SINCRONIZZA SISTEMI DI STATISTICHE
 * Assicura che il sistema unificato e quello locale siano allineati
 */
export const syncStatsSystems = (): void => {
  try {
    const unifiedData = loadUnifiedData();

    // Controlla se esistono dati locali
    const localStatsKey = "maraffa_player_stats";
    const localStatsRaw = localStorage.getItem(localStatsKey);

    if (localStatsRaw) {
      const localStats = JSON.parse(localStatsRaw);

      // Se i dati locali hanno più progressi, sincronizza verso il sistema unificato
      if (
        localStats.xp > unifiedData.xp ||
        localStats.level > unifiedData.level
      ) {
        console.log(
          "🔄 Sincronizzazione: dati locali più aggiornati, aggiornando sistema unificato"
        );
        saveUnifiedData({
          level: Math.max(localStats.level || 1, unifiedData.level),
          xp: Math.max(localStats.xp || 0, unifiedData.xp),
          gamesPlayed: Math.max(
            localStats.totalGames || 0,
            unifiedData.gamesPlayed
          ),
          gamesWon: Math.max(localStats.gamesWon || 0, unifiedData.gamesWon),
          currentWinStreak: Math.max(
            localStats.currentWinStreak || 0,
            unifiedData.currentWinStreak
          ),
          bestWinStreak: Math.max(
            localStats.bestWinStreak || 0,
            unifiedData.bestWinStreak
          ),
        });
      }
    }

    console.log("✅ Sistemi di statistiche sincronizzati");
  } catch (error) {
    console.error("❌ Errore nella sincronizzazione dei sistemi:", error);
  }
};

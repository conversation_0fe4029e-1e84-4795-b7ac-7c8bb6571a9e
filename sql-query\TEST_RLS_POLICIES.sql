-- Script di test rapido per verificare le policy RLS delle tabelle friendships e friend_requests
-- Eseguire questo script nel SQL Editor del Dashboard Supabase DOPO aver configurato le policy

-- ====================================================================
-- TEST POLICY FRIENDSHIPS
-- ====================================================================

-- 1. Verifica le policy esistenti per friendships
SELECT policyname, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'friendships'
ORDER BY policyname;

-- 2. Test lettura friendships (deve restituire solo le relazioni dell'utente corrente)
-- SELECT * FROM friendships LIMIT 5;

-- 3. Test per verificare se un utente può eliminare una relazione di amicizia
-- (sostituire 'YOUR_USER_ID' e 'FRIEND_USER_ID' con ID reali)
/*
DELETE FROM friendships 
WHERE (user_id = 'YOUR_USER_ID' AND friend_id = 'FRIEND_USER_ID')
   OR (user_id = 'FRIEND_USER_ID' AND friend_id = 'YOUR_USER_ID');
*/

-- ====================================================================
-- TEST POLICY FRIEND_REQUESTS
-- ====================================================================

-- 4. Verifica le policy esistenti per friend_requests
SELECT policyname, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'friend_requests'
ORDER BY policyname;

-- 5. Test lettura friend_requests (deve restituire solo le richieste dell'utente corrente)
-- SELECT * FROM friend_requests LIMIT 5;

-- 6. Test per verificare se un utente può eliminare una richiesta di amicizia
-- (sostituire 'YOUR_USER_ID' con il tuo ID reale)
/*
DELETE FROM friend_requests 
WHERE sender_id = 'YOUR_USER_ID' OR receiver_id = 'YOUR_USER_ID';
*/

-- ====================================================================
-- COMANDI UTILI PER DEBUG
-- ====================================================================

-- 7. Verifica l'utente corrente autenticato
-- SELECT auth.uid() AS current_user_id;

-- 8. Conta il numero di amicizie per l'utente corrente
-- SELECT COUNT(*) as friendship_count FROM friendships;

-- 9. Conta il numero di richieste per l'utente corrente
-- SELECT COUNT(*) as request_count FROM friend_requests;

-- 10. Verifica se RLS è abilitato sulle tabelle
SELECT schemaname, tablename, rowsecurity
FROM pg_tables 
WHERE tablename IN ('friendships', 'friend_requests');

-- Istruzioni:
-- 1. Eseguire questo script per verificare che le policy siano state create
-- 2. Decommentare e personalizzare i test con ID reali per testare le operazioni
-- 3. Se i test falliscono, le policy devono essere corrette
-- 4. Le query decommentate devono restituire solo dati pertinenti all'utente corrente

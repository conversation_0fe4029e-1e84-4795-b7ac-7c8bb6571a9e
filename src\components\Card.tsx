import React, { useState, useEffect, useRef, useCallback } from "react";
import { Card as CardType, Suit } from "@/utils/game/cardUtils";
import { cn } from "@/lib/utils";
import { cardImages } from "@/utils/game/cardImages";
import { useAudio } from "@/hooks/useAudio";
import { unifiedImageCache } from "@/utils/ui/unifiedImageCache";

interface CardProps {
  card?: CardType;
  isPlayable?: boolean;
  isSelected?: boolean;
  isRevealed?: boolean;
  onClick?: () => void;
  className?: string;
  scale?: "xs" | "sm" | "md" | "lg";
  position?: number;
  total?: number;
  isTrump?: boolean;
  hasMaraffa?: boolean;
}

// Define suit images outside component to avoid recreating on each render
const suitImages: Record<string, string> = {
  coins: "/images/semi/denari.png",
  cups: "/images/semi/coppe.png",
  swords: "/images/semi/spade.png",
  clubs: "/images/semi/bastoni.png",
};

// Corrected Italian names for cards based on Romagnole deck
const cardRankNames: Record<string, string> = {
  A: "Asso",
  "2": "Due",
  "3": "Tre",
  "4": "Quattro",
  "5": "Cinque",
  "6": "Sei",
  "7": "Sette",
  J: "Fante", // Correct Italian name for Jack
  H: "Cavallo", // Correct Italian name for Horse
  K: "Re", // Correct Italian name for King
};

// Correct Italian suit names
const suitNames: Record<string, string> = {
  coins: "Denari",
  cups: "Coppe",
  swords: "Spade",
  clubs: "Bastoni",
};

const Card = ({
  card,
  isPlayable = false,
  isSelected = false,
  isRevealed = true,
  onClick,
  className,
  scale = "md",
  isTrump = false,
  hasMaraffa = false,
}: CardProps) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Rimosso preloader separato - ora usiamo sistema unificato
  const { playSound } = useAudio();

  // Handle card click with audio
  const handleCardClick = useCallback(() => {
    if (isPlayable && onClick) {
      playSound("cardPlay");
      onClick();
    }
  }, [isPlayable, onClick, playSound]);

  // Define styles based on props
  const suitColors: Record<string, string> = {
    coins: "text-romagna-gold",
    cups: "text-romagna-rust", // Changed from blue to red
    swords: "text-romagna-blue", // Changed from rust to blue
    clubs: "text-romagna-green",
  };

  // Card scales
  const scales = {
    xs: "w-12 h-18",
    sm: "w-16 h-24",
    md: "w-20 h-30", // Normalized size
    lg: "w-24 h-36", // Not too large for better spacing
  };

  // Caricamento semplificato con sistema unificato
  useEffect(() => {
    if (card && isRevealed) {
      const { suit, rank } = card;
      const cardSrc = cardImages[suit][rank];

      // Verifica se l'immagine è già pronta
      if (unifiedImageCache.isImageReady(cardSrc)) {
        setImageLoaded(true);
        return;
      }

      // Carica l'immagine se non disponibile
      setImageLoaded(false);
      setImageError(false);

      const loadImage = async () => {
        try {
          await unifiedImageCache.loadImage(cardSrc);
          setImageLoaded(true);
        } catch (error) {
          console.warn(`Failed to load card image: ${cardSrc}`, error);
          setImageError(true);
        }
      };

      loadImage();
    }
  }, [card, isRevealed]);

  // Get the appropriate card image source
  const getCardImageSrc = () => {
    if (!card || !isRevealed) return "";
    return cardImages[card.suit][card.rank];
  };

  // Handle fallback if image fails to load
  const renderCardContent = () => {
    if (!isRevealed || !card) {
      return renderCardBack();
    }

    if (imageError) {
      // Fallback to rustic-styled simple card display if image fails to load
      return (
        <div
          className={`w-full h-full romagna-paper rounded-lg flex flex-col items-center justify-center p-2 overflow-hidden ${
            suitColors[card.suit]
          }`}
        >
          {/* Texture overlay */}
          <div className="absolute inset-0 opacity-10 romagna-scratches"></div>

          <div className="text-lg font-playfair font-bold mb-1">
            {cardRankNames[card.rank]}
          </div>

          <div className="relative w-1/2 h-1/3 my-1">
            <img
              src={suitImages[card.suit]}
              alt={suitNames[card.suit]}
              className="w-full h-full object-contain drop-shadow-sm"
            />
            {/* Decorative vintage effect */}
            <div className="absolute inset-0 rounded-full mix-blend-overlay bg-gradient-to-tr from-romagna-cream/10 to-transparent"></div>
          </div>

          <div className="text-sm mt-1 font-playfair italic">
            {suitNames[card.suit]}
          </div>

          {/* Decorative corners */}
          <div className="absolute top-1 left-1 text-xs font-bold opacity-80">
            {card.rank}
          </div>
          <div className="absolute bottom-1 right-1 text-xs font-bold opacity-80 rotate-180">
            {card.rank}
          </div>
        </div>
      );
    }

    return (
      <>
        {/* Placeholder statico che sembra una carta invece del loading spinner */}{" "}
        {!imageLoaded && !imageError && (
          <div className="absolute inset-0 rustic-paper flex flex-col items-center justify-center p-2 overflow-hidden bg-romagna-cream/95">
            {/* Pattern decorativo di sfondo */}
            <div className="absolute inset-0 opacity-5 romagna-pattern animate-background-pulse"></div>
            {/* Skeleton della carta */}{" "}
            <div className="w-full h-full flex flex-col items-center justify-center">
              {/* Simbolo del seme come placeholder */}
              <div className="relative w-1/3 h-1/3 mb-2 bg-gradient-to-br from-romagna-gold/20 to-romagna-rust/20 rounded-full flex items-center justify-center animate-background-float">
                <img
                  src={suitImages[card.suit]}
                  alt={suitNames[card.suit]}
                  className="w-full h-full object-contain opacity-40"
                />
              </div>

              {/* Nome della carta come placeholder */}
              <div className="text-lg font-playfair font-bold text-romagna-wood/40 text-center">
                {cardRankNames[card.rank]}
              </div>

              {/* Decorazioni agli angoli */}
              <div className="absolute top-2 left-2 text-sm font-bold text-romagna-wood/30">
                {card.rank}
              </div>
              <div className="absolute bottom-2 right-2 text-sm font-bold text-romagna-wood/30 rotate-180">
                {card.rank}
              </div>
            </div>
          </div>
        )}
        {/* Full card image */}
        <div className="absolute inset-0 flex items-center justify-center">
          <img
            src={getCardImageSrc()}
            alt={`${cardRankNames[card.rank]} di ${suitNames[card.suit]}`}
            className={cn(
              "w-full h-full object-contain transition-opacity duration-150",
              imageLoaded ? "opacity-100" : "opacity-0"
            )}
            onLoad={() => setImageLoaded(true)}
            onError={() => setImageError(true)}
            loading="eager" // Cambiato da lazy a eager per caricamento immediato
            decoding="async" // Ottimizzazione decodifica asincrona
          />

          {/* Vintage overlay effect on images */}
          <div className="absolute inset-0 mix-blend-soft-light bg-gradient-to-br from-romagna-cream/10 via-transparent to-romagna-rust/10 pointer-events-none"></div>

          {/* Subtle edge shadow */}
          <div className="absolute inset-0 shadow-inner pointer-events-none"></div>
        </div>
      </>
    );
  };

  // Render card back (when not revealed)
  const renderCardBack = () => (
    <div className="w-full h-full bg-gradient-to-br from-romagna-wood to-romagna-rust/80 rounded-lg flex items-center justify-center overflow-hidden">
      {/* Enhanced card back with rooster logo */}
      <div className="w-full h-full relative">
        {/* Decorative border with rustic pattern */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="absolute inset-1 border border-romagna-cream/20 rounded-lg"></div>
          <div className="absolute inset-2 border border-romagna-cream/15 rounded-lg"></div>
          <div className="absolute inset-3 border border-romagna-cream/10 rounded-lg"></div>
        </div>
        {/* Wood grain texture overlay */}
        <div className="absolute inset-0 opacity-20 romagna-wood-pattern"></div>{" "}
        {/* Corner decorations - suit images */}
        <div className="absolute top-1 left-1 w-3 h-3 animate-background-float">
          <img
            src="/images/semi/spade.png"
            alt="spade"
            className="w-full h-full object-contain opacity-70 transform -rotate-12"
          />
        </div>
        <div className="absolute top-1 right-1 w-3 h-3 animate-background-float">
          <img
            src="/images/semi/coppe.png"
            alt="coppe"
            className="w-full h-full object-contain opacity-70 transform -rotate-12"
          />
        </div>
        <div className="absolute bottom-1 left-1 w-3 h-3 animate-background-float">
          <img
            src="/images/semi/bastoni.png"
            alt="bastoni"
            className="w-full h-full object-contain opacity-70 transform -rotate-12"
          />
        </div>
        <div className="absolute bottom-1 right-1 w-3 h-3 animate-background-float">
          <img
            src="/images/semi/denari.png"
            alt="denari"
            className="w-full h-full object-contain opacity-70"
          />
        </div>
        {/* Pattern decorativo centrale */}
        <div className="absolute inset-0 flex items-center justify-center opacity-5">
          <div className="w-full h-full romagna-pattern animate-background-pulse"></div>
        </div>
        {/* Central rooster logo */}
        <div className="absolute inset-0 flex items-center justify-center animate-pulse-subtle">
          <div className="relative w-3/4 h-3/4">🃏</div>
        </div>
        {/* Card back text with traditional font */}
        <div className="absolute bottom-2 left-0 right-0 text-center">
          <div className="text-romagna-cream/80 text-xs font-playfair italic">
            Marafone Romagnolo
          </div>
        </div>
      </div>
    </div>
  );
  return (
    <div
      className={cn(
        "relative rounded-lg transition-all duration-300 card-animation",
        scales[scale],
        isPlayable ? "cursor-pointer hover:-translate-y-2" : "cursor-default",
        isSelected ? "transform -translate-y-4" : "",
        className
      )}
      data-card="true"
      data-card-rank={card?.rank}
      data-card-suit={card?.suit}
      data-card-trump={isTrump ? "true" : "false"}
      style={{
        transition: "all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
        boxShadow: isTrump
          ? "0 0 10px rgba(212, 163, 90, 0.7), 0 4px 8px -1px rgba(124, 90, 68, 0.3)"
          : isPlayable
          ? "0 0 5px rgba(139, 44, 28, 0.4), 0 4px 6px -1px rgba(124, 90, 68, 0.2)"
          : "0 2px 6px -1px rgba(124, 90, 68, 0.15), 0 1px 4px -1px rgba(124, 90, 68, 0.1)",
      }}
      onClick={isPlayable ? handleCardClick : undefined}
      title={
        card && isRevealed
          ? `${cardRankNames[card.rank]} di ${suitNames[card.suit]}`
          : "Carta"
      }
    >
      {/* Bordo dorato per il briscola */}
      {isTrump && (
        <div className="absolute -inset-0.5 rounded-lg animate-pulse-gold bg-gradient-to-tr from-romagna-gold/30 to-romagna-gold/70 -z-10"></div>
      )}{" "}
      {/* Effetto "selezione" per carte giocabili quando in hover */}
      {isPlayable && (
        <div className="absolute -inset-0.5 rounded-lg bg-gradient-to-tr from-romagna-rust/0 to-romagna-rust/0 hover:from-romagna-rust/20 hover:to-romagna-rust/50 transition-all duration-400 -z-10"></div>
      )}
      <div
        className={cn(
          "relative w-full h-full rounded-lg overflow-hidden flex flex-col",
          isRevealed ? "bg-romagna-cream/90" : "bg-transparent",
          isPlayable && "hover:bg-romagna-cream"
        )}
        style={{
          border: isPlayable
            ? "1px solid rgba(139, 44, 28, 0.4)"
            : isTrump
            ? "1px solid rgba(212, 163, 90, 0.7)"
            : "1px solid rgba(124, 90, 68, 0.2)",
        }}
      >
        {renderCardContent()}

        {/* Trump indicator */}
        {isTrump && (
          <div className="absolute top-0 left-0 m-1 z-10">
            <div className="bg-romagna-gold/80 text-xs font-bold font-playfair text-romagna-wood rounded-full h-5 w-5 flex items-center justify-center shadow-sm border border-romagna-gold/30">
              B
            </div>
          </div>
        )}

        {/* Maraffa indicator */}
        {hasMaraffa && (
          <div className="absolute top-0 left-0 m-1 z-10">
            <div className="bg-green-600/90 text-xs font-bold font-playfair text-white rounded-full h-5 w-5 flex items-center justify-center shadow-sm border border-green-500/50 animate-pulse-subtle">
              M
            </div>
          </div>
        )}

        {/* Effetto worn edges su tutte le carte */}
        <div className="absolute inset-0 rounded-lg pointer-events-none overflow-hidden romagna-worn-edges"></div>
      </div>
    </div>
  );
};

export default Card;

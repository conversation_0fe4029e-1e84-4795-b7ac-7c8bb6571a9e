import React from "react";
import PlayerHand from "@/components/PlayerHand";
import { Card as CardType, Suit } from "@/utils/game/cardUtils";
import { GameState } from "@/utils/game/gameLogic";
import { ActionType } from "@/components/GameActionBadge";
import { shouldShowMaraffaIndicator } from "@/utils/game/maraffaUtils";

type PlayerHandsProps = {
  gameState: GameState;
  validCards: CardType[];
  handlePlayCard: (card: CardType) => void;
  getPlayerPosition: (
    playerIndex: number
  ) => "bottom" | "left" | "top" | "right";
  getPlayerActionBadge: (playerIndex: number) => {
    type: ActionType | null;
    active: boolean;
  };
  isMobile: boolean;
  handCardSpacing: string; // Nuova prop per la spaziatura delle carte
  cardSize: string; // Nuova prop per la dimensione delle carte
};

const PlayerHands: React.FC<PlayerHandsProps> = ({
  gameState,
  validCards,
  handlePlayCard,
  getPlayerPosition,
  getPlayerActionBadge,
  isMobile,
  handCardSpacing,
  cardSize,
}) => {
  // Function to determine which cards should show the Maraffa indicator
  const getCardMaraffaStatus = (
    card: CardType,
    playerIndex: number
  ): boolean => {
    return shouldShowMaraffaIndicator(card, gameState, playerIndex);
  };

  return (
    <div className={`absolute inset-0 pointer-events-none ${handCardSpacing}`}>
      {gameState.players.map((player, index) => (
        <PlayerHand
          key={player.id}
          cards={player.hand}
          isCurrentPlayer={gameState.currentPlayer === index}
          isHuman={index === 0}
          playableCards={index === 0 ? validCards : []}
          onCardSelect={handlePlayCard}
          position={getPlayerPosition(index)}
          playerName={player.name}
          teamId={player.team}
          hideCards={index !== 0} // Always hide opponent cards
          trumpSuit={gameState.trumpSuit as Suit}
          cardSize={cardSize} // Applica la dimensione delle carte
          getCardMaraffaStatus={(card) => getCardMaraffaStatus(card, index)}
          gamePhase={gameState.gamePhase} // Passa la fase di gioco
        />
      ))}
    </div>
  );
};

export default PlayerHands;

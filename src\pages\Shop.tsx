import { useState } from "react";
import { env } from "../config/environment";
// Leggi la configurazione per il Pass Premium
const PASS_PREMIUM = env.features.passPremium;
import { useNavigate } from "react-router-dom";
import { useHardwareBackButton } from "@/hooks/useHardwareBackButton";
import { useIsMobile } from "@/hooks/use-mobile";
import { useAudio } from "@/hooks/useAudio";
import { useUserState } from "@/hooks/useUserState";
import {
  getGameSettings,
  updateSelectedTableMat,
} from "@/services/gameSettingsService";
import {
  tableMats,
  cardBacks,
  type TableMat,
  type CardBack,
} from "@/data/shopData";
import ShopHeader from "@/components/shop/ShopHeader";
import PremiumSection from "@/components/shop/PremiumSection";
import TableMatsSection from "@/components/shop/TableMatsSection";
import CardBacksSection from "@/components/shop/CardBacksSection";
// Rimosso react-window per mantenere la visualizzazione completa

const Shop = () => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const { playSound } = useAudio();
  const { userState } = useUserState();

  const cardBacksEnabled = false; //  abilitare/disabilitare  la sezione retri delle carte

  // Gestisce il tasto indietro hardware solo su mobile
  useHardwareBackButton("/", isMobile); // 🚀 NUOVO: Usa i dati dal sistema di stato globale persistente
  const playerStats = {
    level: userState.level,
    xp: userState.xp,
    totalGames: userState.gamesPlayed,
    gamesWon: userState.gamesWon,
    gamesLost: userState.gamesPlayed - userState.gamesWon,
    winRate: userState.winRate,
    maraffeMade: 0, // Mantenuto per compatibilità
    achievementsUnlocked: [],
    lastPlayed: new Date().toISOString(),
    currentWinStreak: 0,
    bestWinStreak: 0,
    recentGames: [],
    createdAt: new Date().toISOString(),
    updatedAt: userState.lastUpdated,
  };

  // Stati per le personalizzazioni selezionate
  const [selectedTableMat, setSelectedTableMat] = useState(() => {
    const settings = getGameSettings();
    const selectedMat = settings.selectedTableMat;

    // Verifica che il tappetino selezionato esista nella lista
    const matExists = tableMats.some((mat) => mat.id === selectedMat);
    if (!matExists) {
      // Se il tappetino non esiste, usa il primo tappetino disponibile
      const firstMat = tableMats[0];
      if (firstMat) {
        updateSelectedTableMat(firstMat.id);
        return firstMat.id;
      }
    }

    return selectedMat;
  });
  const [selectedCardBack, setSelectedCardBack] = useState("classico");
  const [hasAdPass, setHasAdPass] = useState(false);
  const [selectedPremiumOption, setSelectedPremiumOption] = useState("monthly"); // "monthly" or "lifetime"

  // 🖼️ NESSUN PRELOAD - IMMAGINI CARICATE ON-DEMAND

  // Funzione helper per calcolare se un item è sbloccato
  const isItemUnlocked = (item: TableMat | CardBack) => {
    if (item.unlockType === "free") return true;
    if (item.unlockType === "level")
      return playerStats.level >= item.unlockLevel;
    if (item.unlockType === "purchase") return item.unlocked;
    if (item.unlockType === "premium")
      return hasAdPass && playerStats.level >= item.unlockLevel;
    return item.unlocked;
  };
  // Funzione per ottenere la descrizione dello sblocco
  const getUnlockDescription = (item: TableMat | CardBack) => {
    if (item.unlockType === "level" && playerStats.level < item.unlockLevel) {
      return `Sblocca al livello ${item.unlockLevel}`;
    }
    if (item.unlockType === "premium") {
      if (!hasAdPass) {
        return "Richiede Premium Pass";
      } else if (playerStats.level < item.unlockLevel) {
        return `Premium: Livello ${item.unlockLevel}`;
      }
    }
    return null;
  };
  const handleTableMatSelect = (matId: string) => {
    const mat = tableMats.find((m) => m.id === matId);
    if (mat && isItemUnlocked(mat)) {
      setSelectedTableMat(matId);
      updateSelectedTableMat(matId);
      playSound("success");
    } else if (mat && !isItemUnlocked(mat)) {
      playSound("buttonClick");
      if (mat.unlockType === "purchase") {
        console.log(
          `Tentativo di acquisto per il tappetino: ${mat.name} - ${mat.price}`
        );
      } else if (mat.unlockType === "premium") {
        // Scrolla alla sezione premium con offset personalizzato
        const premiumSection = document.querySelector(
          ".premium-section"
        ) as HTMLElement;
        if (premiumSection) {
          const elementTop = premiumSection.offsetTop;
          const offset = 20; // Spazio dall'alto
          window.scrollTo({
            top: elementTop - offset,
            behavior: "smooth",
          });
        }
      }
    }
  };
  const handleCardBackSelect = (backId: string) => {
    const cardBack = cardBacks.find((c) => c.id === backId);
    if (cardBack && isItemUnlocked(cardBack)) {
      setSelectedCardBack(backId);
      playSound("success");
    } else if (cardBack && !isItemUnlocked(cardBack)) {
      playSound("buttonClick");
      if (cardBack.unlockType === "purchase") {
        console.log(
          `Tentativo di acquisto per il retro carta: ${cardBack.name} - ${cardBack.price}`
        );
      } else if (cardBack.unlockType === "premium") {
        // Scrolla alla sezione premium con offset personalizzato
        const premiumSection = document.querySelector(
          ".premium-section"
        ) as HTMLElement;
        if (premiumSection) {
          const elementTop = premiumSection.offsetTop;
          const offset = 20; // Spazio dall'alto
          window.scrollTo({
            top: elementTop - offset,
            behavior: "smooth",
          });
        }
      }
    }
  };

  const handleAdPassPurchase = () => {
    if (!hasAdPass) {
      playSound("buttonClick");
      // Qui implementeresti la logica di acquisto del pass premium
    }
  };
  const handleRulesClick = () => {
    playSound("buttonClick");
    navigate("/rules");
  };
  return (
    <div className="min-h-screen  flex flex-col max-w-full overflow-x-hidden">
      {/* Header */}
      <ShopHeader playerLevel={playerStats.level} />

      {/* Main content - scrollable container */}
      <div className="flex-1 px-4 pb-8 overflow-y-auto overflow-x-hidden">
        <div className="max-w-3xl mx-auto space-y-8 w-full">
          {/* Sezione Pass Premium */}
          {PASS_PREMIUM && (
            <PremiumSection
              hasAdPass={hasAdPass}
              onAdPassPurchase={handleAdPassPurchase}
            />
          )}

          {/* Sezione Tappetini da Gioco */}
          <TableMatsSection
            selectedTableMat={selectedTableMat}
            hasAdPass={hasAdPass}
            onTableMatSelect={handleTableMatSelect}
            isItemUnlocked={isItemUnlocked}
          />

          {/* Sezione Retri delle Carte */}
          {cardBacksEnabled && (
            <CardBacksSection
              selectedCardBack={selectedCardBack}
              playerLevel={playerStats.level}
              hasAdPass={hasAdPass}
              onCardBackSelect={handleCardBackSelect}
              isItemUnlocked={isItemUnlocked}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default Shop;

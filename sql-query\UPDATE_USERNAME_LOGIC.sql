-- Script per aggiornare la logica dell'username a 18 caratteri
-- Eseguire questo script nel SQL Editor del Dashboard Supabase

-- 1. Prima controlliamo lo stato attuale
SELECT 
    'Profili con username troppo lunghi' as check_type,
    COUNT(*) as count
FROM profiles 
WHERE LENGTH(username) > 18

UNION ALL

SELECT 
    'Lunghezza massima attuale',
    COALESCE(MAX(LENGTH(username)), 0)
FROM profiles;

-- 2. Aggiorna gli username troppo lunghi troncandoli a 18 caratteri
UPDATE profiles 
SET username = LEFT(username, 18)
WHERE LENGTH(username) > 18;

-- 3. Verifica se il constraint esiste già
SELECT constraint_name 
FROM information_schema.table_constraints 
WHERE table_name = 'profiles' 
AND constraint_type = 'CHECK' 
AND constraint_name = 'username_length_check';

-- 4. Rimuovi constraint esistente se presente (per evitare conflitti)
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS username_length_check;

-- 5. Aggiungi nuovo constraint per massimo 18 caratteri
ALTER TABLE profiles 
ADD CONSTRAINT username_length_check 
CHECK (LENGTH(username) <= 18 AND LENGTH(username) >= 2);

-- 6. Aggiorna o ricrea la funzione handle_new_user per gestire i 18 caratteri
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, username, avatar_url, created_at, updated_at)
  VALUES (
    new.id,
    -- Estrae i primi 18 caratteri della parte email prima della @ se presente, altrimenti usa il nome o 'Giocatore'
    CASE 
      WHEN new.email IS NOT NULL AND new.email LIKE '%@%' THEN 
        LEFT(SPLIT_PART(new.email, '@', 1), 18)
      ELSE 
        LEFT(COALESCE(new.raw_user_meta_data->>'full_name', new.raw_user_meta_data->>'name', 'Giocatore'), 18)
    END,
    new.raw_user_meta_data->>'avatar_url',
    now(),
    now()
  );
  RETURN new;
END;
$$ language plpgsql security definer;

-- 7. Verifica che il trigger esista (se non esiste, lo crea)
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- 8. Verifica i risultati finali
SELECT 
    'Verifica finale' as check_type,
    COUNT(*) as total_users,
    MAX(LENGTH(username)) as max_username_length,
    MIN(LENGTH(username)) as min_username_length
FROM profiles;

-- 9. Mostra alcuni esempi di username aggiornati
SELECT 
    id, 
    username, 
    LENGTH(username) as length,
    created_at
FROM profiles 
ORDER BY LENGTH(username) DESC, created_at DESC
LIMIT 10;

-- 10. Verifica che la funzione e il trigger siano configurati correttamente
SELECT 
    'Funzione handle_new_user' as component,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.routines 
        WHERE routine_name = 'handle_new_user'
    ) THEN 'ESISTE' ELSE 'NON ESISTE' END as status

UNION ALL

SELECT 
    'Trigger on_auth_user_created',
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'on_auth_user_created'
    ) THEN 'ESISTE' ELSE 'NON ESISTE' END

UNION ALL

SELECT 
    'Constraint username_length_check',
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'profiles' 
        AND constraint_name = 'username_length_check'
    ) THEN 'ESISTE' ELSE 'NON ESISTE' END;

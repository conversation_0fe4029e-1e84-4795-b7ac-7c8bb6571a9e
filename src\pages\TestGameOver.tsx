import React, { useState } from "react";
import GameOverModal from "@/components/GameOverModal";

// Mock di GameState compatibile con la modale
const mockGameState = {
  gameScore: [1, 18],
  // ...altri campi se necessari per evitare errori nelle funzioni di analisi XP
};

const TestGameOver: React.FC = () => {
  const [open, setOpen] = useState(true);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-yellow-100 to-orange-100">
      <button
        className="mb-6 px-6 py-3 rounded-xl bg-amber-600 text-white font-bold shadow hover:bg-amber-700 transition"
        onClick={() => setOpen(true)}
      >
        Mostra GameOverModal
      </button>
      <GameOverModal
        isOpen={open}
        gameState={mockGameState as any}
        onStartNewGame={() => alert("Nuova partita!")}
        onReturnToMenu={() => alert("Torna alla home!")}
        isOnlineMode={false}
        difficulty="medium"
      />
    </div>
  );
};

export default TestGameOver;

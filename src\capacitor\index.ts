import { SplashScreen } from "@capacitor/splash-screen";
import { App } from "@capacitor/app";
import { Capacitor } from "@capacitor/core";
import { StatusBar, Style } from "@capacitor/status-bar";
import { AudioManager } from "@/utils/audio/AudioManager";

/**
 * Inizializza i plugin di Capacitor
 */
export const initCapacitor = async (): Promise<void> => {
  // Inizializza solo sui dispositivi nativi
  if (Capacitor.isNativePlatform()) {
    try {
      // Nasconde completamente la barra di stato per esperienza fullscreen
      try {
        await StatusBar.hide();
        await StatusBar.setOverlaysWebView({ overlay: true }); // Permette al contenuto di sovrapporsi
      } catch (error) {
        console.error(
          "Errore durante la configurazione della barra di stato:",
          error
        );
      }

      // Nascondi automaticamente la splash screen dopo un ritardo
      await SplashScreen.hide();

      // Gestione dell'evento di chiusura dell'app
      App.addListener("backButton", ({ canGoBack }) => {
        const currentPath = window.location.pathname;

        // Se siamo nella pagina di gioco, gestisci il menu
        if (currentPath === "/game") {
          // Controlla se il menu è già aperto (cerca il contenuto del menu)
          const menuContent = document.querySelector('[data-state="open"]');

          if (menuContent) {
            // Il menu è aperto, chiudilo
            window.dispatchEvent(new CustomEvent("closeGameMenu"));
            return;
          } else {
            // Il menu è chiuso, aprilo
            const menuButton = document.querySelector(
              '[data-menu-trigger="true"]'
            ) as HTMLElement;
            if (menuButton) {
              // Simula un click sul pulsante del menu
              const clickEvent = new MouseEvent("click", {
                view: window,
                bubbles: true,
                cancelable: true,
              });
              menuButton.dispatchEvent(clickEvent);
              return;
            } else {
              // Fallback: usa evento personalizzato
              window.dispatchEvent(new CustomEvent("openGameMenu"));
              return;
            }
          }
        }

        // Se siamo nelle pagine Account o Rules, naviga sempre alla home
        if (currentPath === "/account" || currentPath === "/rules") {
          window.history.pushState(null, "", "/");
          window.dispatchEvent(new PopStateEvent("popstate"));
          return;
        }

        // Per tutte le altre pagine, usa il comportamento normale
        if (!canGoBack) {
          const audioManager = AudioManager.getInstance();
          audioManager.stopAllAudio().catch(() => {});
          App.exitApp();
        } else {
          // Permetti la navigazione indietro normale
          window.history.back();
        }
      });

      // 📱 GESTIONE SUPER AGGRESSIVA ANDROID - STOP IMMEDIATO AUDIO + SESSIONI
      App.addListener("appStateChange", async ({ isActive }) => {
        console.log(
          `🔄 App State Change: ${isActive ? "ACTIVE" : "BACKGROUND"}`
        );
        const audioManager = AudioManager.getInstance();

        if (!isActive) {
          console.log("🔇 APP IN BACKGROUND - STOP IMMEDIATO AUDIO");
          // Stop immediato e multiplo per essere sicuri
          audioManager.stopAllAudio().catch(() => {});
          audioManager.onAppBackground().catch(() => {});

          // Stop aggiuntivi con timeout per Android testardo
          setTimeout(() => audioManager.stopAllAudio().catch(() => {}), 50);
          setTimeout(() => audioManager.stopAllAudio().catch(() => {}), 200);
          setTimeout(() => audioManager.stopAllAudio().catch(() => {}), 500);

          // 🔐 GESTIONE SESSIONE IN BACKGROUND
          try {
            const { sessionManager } = await import(
              "@/integrations/supabase/client"
            );
            sessionManager.stopBackgroundRefresh();
            console.log("🔐 Background refresh fermato");
          } catch (error) {
            console.warn("⚠️ Errore stop background refresh:", error);
          }
        } else {
          console.log("🔊 APP IN FOREGROUND - RIPRENDI AUDIO + SESSIONI");
          audioManager.onAppForeground();

          // 🔐 GESTIONE SESSIONE IN FOREGROUND - RECOVERY AUTOMATICO
          try {
            const { sessionManager } = await import(
              "@/integrations/supabase/client"
            );

            // Avvia background refresh
            sessionManager.startBackgroundRefresh();

            // Controlla immediatamente la sessione al ritorno
            setTimeout(async () => {
              try {
                await sessionManager.ensureValidSession();
                console.log("✅ Sessione verificata al ritorno in foreground");
              } catch (error) {
                console.warn("⚠️ Errore verifica sessione al ritorno:", error);
              }
            }, 1000); // Attesa 1 secondo per stabilizzare l'app

            console.log(
              "🔐 Background refresh riavviato e sessione verificata"
            );
          } catch (error) {
            console.warn("⚠️ Errore gestione sessione foreground:", error);
          }
        }
      });

      // 📱 GESTIONE PAUSE - STOP BRUTALE AUDIO (Android specifico)
      App.addListener("pause", () => {
        console.log("🔇 ANDROID PAUSE EVENT - STOP BRUTALE AUDIO");
        const audioManager = AudioManager.getInstance();

        // Stop immediato e ripetuto
        audioManager.stopAllAudio().catch(() => {});
        audioManager.onAppBackground().catch(() => {});

        // Stop multipli per essere sicuri che funzioni
        for (let i = 0; i < 5; i++) {
          setTimeout(() => {
            audioManager.stopAllAudio().catch(() => {});
          }, i * 100);
        }
      });

      // 📱 GESTIONE RESUME - RIPRENDI AUDIO (Android specifico)
      App.addListener("resume", () => {
        console.log("🔊 ANDROID RESUME EVENT - RIPRENDI AUDIO");
        const audioManager = AudioManager.getInstance();
        audioManager.onAppForeground();
      });

      App.addListener("appUrlOpen", () => {
        const audioManager = AudioManager.getInstance();
        audioManager.stopAllAudio().catch(() => {});
      });

      // 🔥 GESTIONE TERMINAZIONE APP - STOP DEFINITIVO AUDIO
      App.addListener("appTerminated", () => {
        console.log("🔇 APP TERMINATA - STOP DEFINITIVO AUDIO");
        const audioManager = AudioManager.getInstance();
        audioManager.stopAllAudio().catch(() => {});
        audioManager.dispose();
      });

      // 🔥 GESTIONE AGGIUNTIVA PER ANDROID - LISTENER MULTIPLI
      window.addEventListener("beforeunload", () => {
        console.log("🔇 BEFOREUNLOAD - STOP AUDIO");
        const audioManager = AudioManager.getInstance();
        audioManager.stopAllAudio().catch(() => {});
      });

      window.addEventListener("unload", () => {
        console.log("🔇 UNLOAD - STOP AUDIO");
        const audioManager = AudioManager.getInstance();
        audioManager.stopAllAudio().catch(() => {});
      });

      // 🔥 GESTIONE AGGIUNTIVA - PAGEHIDE per Android
      window.addEventListener("pagehide", () => {
        console.log("🔇 PAGEHIDE - STOP AUDIO");
        const audioManager = AudioManager.getInstance();
        audioManager.stopAllAudio().catch(() => {});
      });

      // 🔥 GESTIONE BLUR - quando l'app perde il focus
      window.addEventListener("blur", () => {
        console.log("🔇 BLUR - STOP AUDIO");
        const audioManager = AudioManager.getInstance();
        audioManager.stopAllAudio().catch(() => {});
      });

      // 🔥 GESTIONE VISIBILITY CHANGE - quando la pagina diventa nascosta
      document.addEventListener("visibilitychange", () => {
        if (document.hidden) {
          console.log("🔇 VISIBILITY HIDDEN - STOP AUDIO");
          const audioManager = AudioManager.getInstance();
          audioManager.stopAllAudio().catch(() => {});
        }
      });

      // Rimuovi il listener per la modalità immersiva dato che ora vogliamo la barra di stato visibile
      // Il codice per nascondere la barra di stato è stato rimosso
    } catch (error) {
      console.error("Errore durante l'inizializzazione di Capacitor:", error);
    }
  } else {
    // Per il web browser, usa Page Visibility API
    setupWebAudioHandlers();
  }
};

/**
 * Configura i gestori audio per il web browser
 */
const setupWebAudioHandlers = () => {
  let isTabActive = true;

  document.addEventListener("visibilitychange", () => {
    const audioManager = AudioManager.getInstance();

    if (document.hidden) {
      isTabActive = false;
      audioManager.stopAllAudio().catch(() => {});
      audioManager.onAppBackground().catch(() => {});
    } else {
      isTabActive = true;
      audioManager.onAppForeground();
    }
  });

  window.addEventListener("blur", () => {
    const audioManager = AudioManager.getInstance();
    isTabActive = false;
    audioManager.stopAllAudio().catch(() => {});
    audioManager.onAppBackground().catch(() => {});
  });

  window.addEventListener("focus", () => {
    const audioManager = AudioManager.getInstance();
    isTabActive = true;
    audioManager.onAppForeground();
  });

  window.addEventListener("beforeunload", () => {
    const audioManager = AudioManager.getInstance();
    audioManager.stopAllAudio().catch(() => {});
  });

  window.addEventListener("pagehide", () => {
    const audioManager = AudioManager.getInstance();
    audioManager.onAppBackground().catch(() => {});
  });

  window.addEventListener("pageshow", (event) => {
    if (event.persisted && isTabActive) {
      const audioManager = AudioManager.getInstance();
      audioManager.onAppForeground();
    }
  });
};

import React from "react";
import GameAnnouncement from "@/components/GameAnnouncement";
import { suitNames } from "@/utils/game/cardUtils";

type TrumpAnnouncementProps = {
  trumpAnnouncement: {
    suit: string | null;
    playerName: string;
    visible: boolean;
  };
};

const TrumpAnnouncement: React.FC<TrumpAnnouncementProps> = ({
  trumpAnnouncement,
}) => {
  if (!trumpAnnouncement.visible || !trumpAnnouncement.suit) return null;

  return (
    <GameAnnouncement
      title="Briscola Selezionata!"
      message={`${trumpAnnouncement.playerName} ha scelto ${
        suitNames[trumpAnnouncement.suit]
      }`}
      suit={trumpAnnouncement.suit}
    />
  );
};

export default TrumpAnnouncement;

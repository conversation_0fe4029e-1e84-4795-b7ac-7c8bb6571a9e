import React from "react";
import { useSimpleImageCache } from "@/hooks/useSimpleImageCache";

interface ImagePreloaderProps {
  /**
   * Callback quando il precaricamento è completato
   */
  onPreloadComplete?: () => void;

  /**
   * Se avviare automaticamente il precaricamento
   */
  autoStart?: boolean;
}

/**
 * Componente che gestisce il precaricamento di tutte le immagini
 * Elimina completamente i loading delle carte durante il gioco
 */
export const ImagePreloader: React.FC<ImagePreloaderProps> = ({
  onPreloadComplete,
  autoStart = true,
}) => {
  const { isLoading, isComplete, progress } = useSimpleImageCache({
    autoPreload: autoStart,
    onComplete: onPreloadComplete,
    debug: false,
  });
  // Il sistema unificato gestisce tutto automaticamente

  // Non renderizza nulla a schermo
  return null;
};

export default ImagePreloader;

import { useNavigate } from "react-router-dom";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import {
  Check,
  Share2,
  ShieldCheck,
  ScrollText,
  BookOpen,
  Star,
  MessageSquare,
} from "lucide-react";
import { useAudio } from "@/hooks/useAudio";
import { Capacitor } from "@capacitor/core";

interface SettingsCardProps {
  gamePoints: string;
  soundEnabled: boolean;
  musicEnabled: boolean;
  soundVolume: number;
  musicVolumeState: number;
  onGamePointsChange: (points: "21" | "31" | "41") => void;
  onSoundEnabledChange: (enabled: boolean) => void;
  onMusicEnabledChange: (enabled: boolean) => void;
  onSoundVolumeChange: (volume: number) => void;
  onMusicVolumeChange: (volume: number) => void;
}

const SettingsCard = ({
  gamePoints,
  soundEnabled,
  musicEnabled,
  soundVolume,
  musicVolumeState,
  onGamePointsChange,
  onSoundEnabledChange,
  onMusicEnabledChange,
  onSoundVolumeChange,
  onMusicVolumeChange,
}: SettingsCardProps) => {
  const navigate = useNavigate();
  const { playSound } = useAudio();

  return (
    <Card className="border-2 border-amber-800/30 shadow-md bg-amber-50/80 backdrop-blur-sm">
      <CardHeader className="pb-3">
        <CardTitle
          className="text-xxl flex items-center gap-2"
          style={{
            fontFamily: "'DynaPuff', cursive",
            fontWeight: 500,
            color: "#92400e",
          }}
        >
          {/* <Settings className="h-5 w-5" />  */}
          Impostazioni
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Sezione Partita */}
        <div>
          <div className="flex items-center gap-2 mb-3">
            <img
              src="/images/stremline-icons/gamedice.png"
              alt="Volume"
              className="h-6 w-6"
            />
            <h3
              className="text-lg font-semibold text-amber-900"
              style={{
                fontFamily: "'DynaPuff', cursive",
                fontWeight: 500,
              }}
            >
              Partita
            </h3>
          </div>
          <div className="bg-amber-100/50 rounded-lg p-4 border border-amber-200">
            <p className="text-amber-800 text-sm mb-3 font-medium">
              Punteggio per vincere:
            </p>
            <div className="grid grid-cols-3 gap-3">
              {["21", "31", "41"].map((option) => (
                <div
                  key={option}
                  className={`relative rounded-lg cursor-pointer transition-all overflow-hidden ${
                    gamePoints === option
                      ? "ring-2 ring-amber-600 bg-amber-100/80 shadow-md"
                      : "border border-amber-300/50 bg-amber-50/70 hover:bg-amber-100/50"
                  }`}
                  onClick={() =>
                    onGamePointsChange(option as "21" | "31" | "41")
                  }
                >
                  {gamePoints === option && (
                    <div className="absolute top-1.5 right-1.5 w-4 h-4 bg-amber-600 rounded-full flex items-center justify-center">
                      <Check className="h-2.5 w-2.5 text-white" />
                    </div>
                  )}
                  <div className="p-2.5 text-center">
                    <div className="text-xl font-bold text-amber-800">
                      {option}
                    </div>
                    <div className="text-xs text-amber-700 mt-0.5">
                      {option === "21"
                        ? "Veloce"
                        : option === "31"
                        ? "Corta"
                        : "Classica"}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Sezione Audio */}
        <div>
          <div className="flex items-center gap-2 mb-3">
            <img
              src="/images/stremline-icons/speakervolume.png"
              alt="Volume"
              className="h-6 w-6"
            />
            <h3
              className="text-lg font-semibold text-amber-900"
              style={{
                fontFamily: "'DynaPuff', cursive",
                fontWeight: 500,
              }}
            >
              Audio
            </h3>
          </div>
          <div className="bg-amber-100/50 rounded-lg p-4 border border-amber-200 space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-amber-900 text-sm font-medium">
                Effetti Sonori
              </span>
              <Switch
                checked={soundEnabled}
                onCheckedChange={onSoundEnabledChange}
                className="data-[state=checked]:bg-amber-600 data-[state=unchecked]:bg-amber-200"
              />
            </div>
            {soundEnabled && (
              <div>
                <Slider
                  value={[soundVolume]}
                  max={100}
                  step={1}
                  onValueChange={(val) => onSoundVolumeChange(val[0])}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-amber-700 mt-1">
                  <span>0%</span>
                  <span className="text-amber-900 font-semibold">
                    {soundVolume}%
                  </span>
                  <span>100%</span>
                </div>
              </div>
            )}
            <div className="flex items-center justify-between">
              <span className="text-amber-900 text-sm font-medium">
                Musica di Sottofondo
              </span>
              <Switch
                checked={musicEnabled}
                onCheckedChange={onMusicEnabledChange}
                className="data-[state=checked]:bg-amber-600 data-[state=unchecked]:bg-amber-200"
              />
            </div>
            {musicEnabled && (
              <div>
                <Slider
                  value={[musicVolumeState]}
                  max={100}
                  step={1}
                  onValueChange={(val) => onMusicVolumeChange(val[0])}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-amber-700 mt-1">
                  <span>0%</span>
                  <span className="text-amber-900 font-semibold">
                    {musicVolumeState}%
                  </span>
                  <span>100%</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Sezione Documenti */}
        <div>
          <div className="flex items-center gap-2 mb-3">
            <img
              src="/images/stremline-icons/openbook.png"
              alt="Book"
              className="h-6 w-6"
            />
            <h3
              className="text-lg font-semibold text-amber-900"
              style={{
                fontFamily: "'DynaPuff', cursive",
                fontWeight: 500,
              }}
            >
              Altro
            </h3>
          </div>
          <div className="bg-amber-100/50 rounded-lg p-4 border border-amber-200">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              <Button
                onClick={() => {
                  playSound("buttonClick");
                  navigate("/rules");
                }}
                className="bg-amber-700 hover:bg-amber-800 text-white font-medium py-2 px-3 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2 text-sm"
              >
                <BookOpen className="h-3.5 w-3.5" />
                Regole del Gioco
              </Button>
              <Button
                onClick={() => {
                  playSound("buttonClick");
                  navigate("/feedback");
                }}
                className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium py-2 px-3 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 text-sm shadow-md hover:shadow-lg hover:scale-105"
              >
                <MessageSquare className="h-3.5 w-3.5" />
                Bacheca Feedback
              </Button>
              <Button
                onClick={async () => {
                  playSound("buttonClick");
                  const shareData = {
                    title: "Maraffa Romagnola",
                    text: "Scopri il gioco di carte tradizionale romagnolo! 🃏",
                    url: "https://play.google.com/store/apps/details?id=com.eliazavatta.maraffa",
                  };

                  try {
                    if (Capacitor.isNativePlatform()) {
                      // Su Android usa il plugin Capacitor Share nativo
                      const { Share } = await import("@capacitor/share");
                      await Share.share(shareData);
                      console.log("✅ Condivisione nativa Android completata");
                    } else {
                      // Su web usa l'API Web Share se disponibile
                      if (navigator.share) {
                        await navigator.share(shareData);
                        console.log("✅ Condivisione web completata");
                      } else {
                        // Fallback web: copia il link negli appunti
                        await navigator.clipboard.writeText(shareData.url);
                        alert("Link copiato negli appunti!");
                      }
                    }
                  } catch (error) {
                    console.log(
                      "Condivisione annullata dall'utente o errore:",
                      error
                    );
                    // Non fare nulla se l'utente annulla la condivisione
                  }
                }}
                className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium py-2 px-3 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 text-sm shadow-md hover:shadow-lg hover:scale-105"
              >
                <Share2 className="h-3.5 w-3.5" />
                Invita un amico
              </Button>
              <Button
                onClick={() => {
                  playSound("buttonClick");
                  const playStoreUrl =
                    "https://play.google.com/store/apps/details?id=com.eliazavatta.maraffa";
                  window.open(playStoreUrl, "_blank");
                }}
                className="bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white font-medium py-2 px-3 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 text-sm shadow-md hover:shadow-lg hover:scale-105"
              >
                <Star className="h-3.5 w-3.5" />
                Lascia una Recensione
              </Button>
            </div>
            {/* Bottoni legali piccoli e in fondo */}
            <div className="flex flex-row gap-2 mt-2 justify-center">
              <Button
                onClick={() => {
                  playSound("buttonClick");
                  navigate("/terms");
                }}
                className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white font-medium py-0.5 px-2 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 text-xs shadow-md hover:shadow-lg hover:scale-105 min-h-0 w-1/2"
                style={{ minWidth: 0, height: "28px" }}
              >
                <ScrollText className="h-3 w-3" />
                Termini
              </Button>
              <Button
                onClick={() => {
                  playSound("buttonClick");
                  navigate("/privacy-policy");
                }}
                className="bg-gradient-to-r from-amber-400 to-yellow-400 hover:from-amber-500 hover:to-yellow-500 text-amber-900 font-medium py-0.5 px-2 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 text-xs shadow-md hover:shadow-lg hover:scale-105 min-h-0 w-1/2"
                style={{ minWidth: 0, height: "28px" }}
              >
                <ShieldCheck className="h-3 w-3" />
                Privacy
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SettingsCard;

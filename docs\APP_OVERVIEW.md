# Marafone Romagnolo - Panoramica dell'App

## 🎮 Che cos'è Marafone Romagnolo?

**Marafone Romagnolo** è un'applicazione web progressiva (PWA) che digitalizza fedelmente il tradizionale gioco di carte romagnolo "Marafone". Sviluppata con tecnologie moderne come React e TypeScript, l'app preserva l'autenticità del gioco tradizionale offrendo al contempo un'esperienza digitale innovativa e coinvolgente.

### 🏛️ La Tradizione Digitale

Il Marafone Romagnolo è un gioco di carte storico della regione Emilia-Romagna, particolarmente diffuso in Romagna. Conosciuto anche come "Cricca" o "Terziglio" in alcune zone, appartiene alla famiglia dei giochi di presa simili alla briscola. L'app mantiene intatta l'essenza culturale del gioco, dalle regole tradizionali fino alle comunicazioni autentiche come "busso", "striscio" e "volo".

---

## 🎯 Funzionalità Principali

### 🎲 Sistema di Gioco Completo

#### Gioco Locale vs AI

- **3 livelli di difficoltà AI**: Facile, Medio e Difficile
- **Strategia AI avanzata**: Sistema intelligente che analizza le carte giocate, coopera con il compagno virtuale e adatta la strategia in base alla situazione
- **Rispetto delle regole tradizionali**: Implementazione fedele delle regole romagnole con obbligo di colore, gestione briscola e sistema di punteggio autentico

#### Gioco Online Multiplayer

- **Matchmaking intelligente**: Sistema di ricerca avversari basato sul livello di abilità
- **Partite con amici**: Creazione di stanze private per giocare con amici specifici
- **Gioco in tempo reale**: Sincronizzazione live delle mosse tra giocatori connessi
- **Sistema di riconnessione**: Possibilità di rientrare in partite interrotte

### 👥 Sistema Sociale

#### Gestione Amicizie

- **Lista amici**: Visualizzazione amici online/offline con livelli
- **Richieste di amicizia**: Invio e gestione richieste di amicizia
- **Inviti a partite**: Sistema di inviti diretti per partite tra amici

#### Classifiche e Progressione

- **Classifica globale**: Ranking dei migliori giocatori
- **Classifica amici**: Confronto prestazioni con i propri amici
- **Sistema livelli**: Progressione XP basata su vittorie e prestazioni
- **Statistiche dettagliate**: Tracciamento partite giocate, vinte, punti totali

### 🎨 Design e Esperienza Utente

#### Tema Autentico Romagnolo

- **Palette colori tradizionale**: Terracotta, crema della piadina, oro del grano
- **Texture rustiche**: Motivi che richiamano le osterie e la cultura romagnola
- **Carte tradizionali**: Mazzo di carte romagnolo-bolognese autentico

#### Interfaccia Moderna

- **Design responsive**: Ottimizzato per mobile, tablet e desktop
- **Animazioni fluide**: Transizioni eleganti per ogni azione
- **Feedback visivi**: Indicatori chiari per azioni disponibili e stato del gioco
- **Accessibilità**: Design inclusivo seguendo le linee guida WCAG

### 🔊 Sistema Audio Immersivo

#### Effetti Sonori Contestuali

- **Suoni di gioco**: Audio specifici per ogni azione (carte giocate, vittorie, sconfitte)
- **Feedback audio**: Conferme sonore per interazioni UI
- **Controlli personalizzabili**: Regolazione volume e attivazione/disattivazione suoni

### ⚡ Performance e Ottimizzazioni

#### Sistema di Cache Intelligente

- **Precaricamento immagini**: Caricamento intelligente delle carte basato sulla situazione di gioco
- **Cache browser**: Ottimizzazione per ridurre tempi di caricamento
- **Modalità offline parziale**: Possibilità di giocare vs AI anche senza connessione internet

#### PWA (Progressive Web App)

- **Installabile**: Può essere installata come app nativa su dispositivi mobili
- **Funzionamento offline**: Alcune funzionalità disponibili senza connessione
- **Aggiornamenti automatici**: Sistema di aggiornamento seamless

---

## 🎪 Modalità di Gioco

### 🤖 Gioco Locale (Offline)

#### Caratteristiche

- **Disponibile sempre**: Non richiede connessione internet
- **AI adattiva**: Compagni e avversari controllati da intelligenza artificiale
- **Apprendimento progressivo**: L'AI si adatta al livello del giocatore

#### Livelli AI Disponibili

- **Facile**: Ideale per principianti, strategie semplici
- **Medio**: Bilanciato per giocatori intermedi
- **Difficile**: Sfida per giocatori esperti, strategie avanzate

### 🌐 Gioco Online (Multiplayer)

#### Funzionalità Social

- **Trova partita veloce**: Matchmaking automatico con giocatori di livello simile
- **Crea partita privata**: Stanze personalizzabili per amici
- **Spettatori**: Possibilità di osservare partite in corso (in sviluppo)

#### Sistema di Ranking

- **Punti esperienza**: Guadagno XP basato su prestazioni
- **Livelli progressivi**: Sistema di progressione a lungo termine
- **Classifiche competitive**: Ranking globale e tra amici

---

## 🎯 Regole del Gioco Implementate

### 📋 Setup Partita

- **4 giocatori** in 2 squadre (giocatore + AI vs 2 AI o 4 giocatori reali online)
- **Mazzo 40 carte** del seme romagnolo-bolognese
- **Distribuzione**: 10 carte per giocatore
- **Briscola**: Determinata dall'ultima carta distribuita

### 🃏 Gerarchia Carte

Dal più alto al più basso:

1. **3** (carta più alta)
2. **2**
3. **Asso**
4. **Re** (figura)
5. **Cavallo** (figura)
6. **Fante** (figura)
7. **7** (scartino)
8. **6** (scartino)
9. **5** (scartino)
10. **4** (scartino - carta più bassa)

### 🎪 Meccaniche Speciali

#### La Maraffa

- **Combinazione speciale**: Asso, Due e Tre dello stesso seme di briscola
- **Bonus**: +3 punti alla squadra che la possiede
- **Dichiarazione**: Deve essere dichiarata a fine mano

#### Comunicazioni Tradizionali

Solo chi apre il turno può pronunciare:

- **"Busso"**: "Ho carte alte di questo seme, continua così"
- **"Striscio"**: "Ho carte basse di questo seme, non sprecare risorse"
- **"Volo"**: "Non ho più carte di questo seme, posso tagliare con briscola"

### 📊 Sistema di Punteggio

- **Asso**: 1 punto intero
- **Figure** (Re, Cavallo, Fante): 1/3 di punto ciascuna
- **3 e 2**: 1/3 di punto ciascuna
- **Scartini** (7,6,5,4): 0 punti
- **Ultima presa** ("bàga"): +1 punto bonus
- **Vittoria**: Prima squadra a 31 punti

---

## 🛠️ Tecnologie e Architettura

### 🏗️ Stack Tecnologico

#### Frontend

- **React 18**: Libreria UI moderna e performante
- **TypeScript**: Tipizzazione statica per codice robusto
- **Vite**: Build tool veloce per sviluppo e produzione
- **Tailwind CSS**: Framework CSS utility-first per styling rapido

#### UI Components

- **Radix UI**: Componenti accessibili e customizzabili
- **shadcn/ui**: Sistema di design components preconfigurato
- **Framer Motion**: Animazioni fluide e performanti

#### Backend e Database

- **Supabase**: Backend-as-a-Service con PostgreSQL
- **Real-time subscriptions**: Sincronizzazione live per multiplayer
- **Row Level Security**: Sicurezza dei dati a livello utente

#### Mobile

- **Capacitor**: Build nativo per Android
- **PWA**: Installazione come app web progressiva

### 🔧 Architettura del Codice

#### Organizzazione Modulare

```
src/
├── components/     # Componenti React riutilizzabili
├── pages/          # Pagine principali dell'app
├── hooks/          # Custom React hooks
├── utils/          # Logica di business e utility
├── services/       # Integrazione API e servizi esterni
├── types/          # Definizioni TypeScript
└── context/        # State management globale
```

#### Pattern Architetturali

- **Component-Based**: Componenti riutilizzabili e modulari
- **Custom Hooks**: Logica di business separata dai componenti
- **Service Layer**: Astrazione per chiamate API
- **Type Safety**: Utilizzo intensivo di TypeScript

---

## 📱 Supporto Piattaforme

### 🌐 Web Browser

- **Tutti i browser moderni**: Chrome, Firefox, Safari, Edge
- **Responsive design**: Ottimizzato per tutte le dimensioni schermo
- **PWA features**: Installabile come app web

### 📱 Mobile

- **Android**: Build nativo tramite Capacitor
- **iOS**: Supporto PWA tramite Safari (installazione web app)
- **Controlli touch**: Interfaccia ottimizzata per touch screen

### 💻 Desktop

- **Tutte le piattaforme**: Windows, macOS, Linux
- **Controlli mouse/tastiera**: Interazioni ottimizzate per desktop
- **Finestre ridimensionabili**: Layout adattivo per diverse risoluzioni

---

## 🚀 Roadmap e Sviluppi Futuri

### 📅 Versione 1.1 (In Sviluppo)

- **Tornei online**: Sistema torneo con eliminazione e classifiche
- **Chat di gioco**: Comunicazione testuale tra giocatori
- **Replay system**: Salvataggio e revisione partite passate
- **Achievement system**: Badge e traguardi per motivare i giocatori

### 🔮 Versione 1.2 (Pianificata)

- **AI con Machine Learning**: Intelligenza artificiale che impara dal comportamento dei giocatori
- **Modalità spettatore**: Osservazione partite live di altri giocatori
- **Statistiche avanzate**: Analytics dettagliate e grafici prestazioni
- **Personalizzazione UI**: Temi alternativi e customizzazioni

### 🌟 Versione 2.0 (Visione Futura)

- **App iOS nativa**: Versione nativa per App Store
- **Modalità offline completa**: Funzionalità complete senza internet
- **Integrazione social**: Condivisione risultati su social network
- **Sistema premium**: Funzionalità aggiuntive opzionali

---

## 🎖️ Obiettivi e Valori

### 🏛️ Preservazione Culturale

- **Mantenimento tradizioni**: Rispetto fedele delle regole tradizionali romagnole
- **Educazione culturale**: Introduzione del gioco alle nuove generazioni
- **Documentazione storica**: Conservazione delle varianti regionali

### 🌍 Accessibilità e Inclusività

- **Design universale**: Interfaccia accessibile per tutti
- **Multipiattaforma**: Disponibile su ogni dispositivo
- **Gratuità**: Core game completamente gratuito

### ⚡ Innovazione Tecnologica

- **Performance**: Ottimizzazioni costanti per fluidità
- **User Experience**: Interfaccia intuitiva e coinvolgente
- **Scalabilità**: Architettura pronta per crescita utenti

---

## 📊 Metriche e Impatto

### 📈 Obiettivi di Engagement

- **Ritenzione utenti**: Mantenere giocatori attivi nel tempo
- **Crescita organica**: Passaparola tra giocatori soddisfatti
- **Community building**: Creazione di una comunità di appassionati

### 🎯 KPI Principali

- **Partite completate**: Numero di partite finite con successo
- **Tempo di sessione**: Durata media delle sessioni di gioco
- **Ritorno utenti**: Frequenza di ritorno degli utenti
- **Soddisfazione**: Feedback e recensioni positive

---

## 🤝 Community e Supporto

### 💬 Canali di Supporto

- **GitHub Issues**: Report bug e richieste funzionalità
- **Email**: Supporto diretto per problemi urgenti
- **Discord Community**: Chat informale tra giocatori

### 🔄 Contributi Open Source

- **Codice aperto**: Repository pubblico per contribuzioni
- **Documentazione**: Guide dettagliate per sviluppatori
- **Feedback loop**: Ascolto attivo della community

---

_Marafone Romagnolo rappresenta l'evoluzione digitale di una tradizione secolare, combinando l'autenticità del gioco romagnolo con le possibilità offerte dalla tecnologia moderna. Un ponte tra passato e futuro, che mantiene viva la cultura locale nell'era digitale._

---

## 🔗 Documentazione Correlata

- **[📚 Indice Documentazione](./README.md)** - Panoramica completa di tutta la documentazione
- **[🏗️ Architettura Progetto](./GUIDA_REPOSITORY.md)** - Struttura tecnica e guida sviluppatori
- **[🎮 Regole di Gioco](./logica-di-gioco.md)** - Implementazione delle regole tradizionali
- **[⚡ Ottimizzazioni Performance](./OTTIMIZZAZIONI_IMMAGINI.md)** - Sistema avanzato di cache
- **[🛠️ Build e Deploy](./BUILD_SCRIPTS.md)** - Tool di sviluppo
- **[📱 Pubblicazione](./PLAY_STORE_LISTING.md)** - Informazioni per il Play Store

---

**Versione documento**: 2.0  
**Data ultima modifica**: 7 Giugno 2025  
**Autore**: Elia Zavatta

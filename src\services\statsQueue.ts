/**
 * 🔄 SISTEMA DI CODA PER SINCRONIZZAZIONE STATISTICHE
 * Gestisce il retry automatico e la sincronizzazione offline delle statistiche
 */

import { supabase } from "@/integrations/supabase/client";
import type { PlayerStats } from "@/types/playerStats";

interface QueuedStatsUpdate {
  id: string;
  userId: string;
  stats: PlayerStats;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
  priority: "low" | "normal" | "high";
}

class StatsQueue {
  private static instance: StatsQueue;
  private queue: QueuedStatsUpdate[] = [];
  private isProcessing = false;
  private processingTimer: NodeJS.Timeout | null = null;
  private readonly STORAGE_KEY = "maraffa_stats_queue";
  private readonly MAX_QUEUE_SIZE = 50;
  private readonly PROCESSING_INTERVAL = 45000; // 45 secondi (ridotto conflitto con altri timer)
  private lastSuccessfulSync: number = Date.now(); // Traccia ultima sincronizzazione riuscita
  private readonly MAX_STALE_TIME = 5 * 60 * 1000; // 5 minuti senza sync = problema

  static getInstance(): StatsQueue {
    if (!StatsQueue.instance) {
      StatsQueue.instance = new StatsQueue();
    }
    return StatsQueue.instance;
  }

  constructor() {
    this.loadQueueFromStorage();
    this.startProcessing();
  }

  // Carica coda da localStorage
  private loadQueueFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        this.queue = JSON.parse(stored);
        console.log(
          `📥 Caricati ${this.queue.length} elementi dalla coda statistiche`
        );
      }
    } catch (error) {
      console.warn("⚠️ Errore caricamento coda statistiche:", error);
      this.queue = [];
    }
  }

  // Salva coda in localStorage
  private saveQueueToStorage(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.queue));
    } catch (error) {
      console.warn("⚠️ Errore salvataggio coda statistiche:", error);
    }
  }

  // Aggiunge un aggiornamento alla coda
  addUpdate(
    userId: string,
    stats: PlayerStats,
    priority: "low" | "normal" | "high" = "normal",
    maxRetries: number = 5
  ): string {
    const id = `stats_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const update: QueuedStatsUpdate = {
      id,
      userId,
      stats,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries,
      priority,
    };

    // Rimuovi aggiornamenti duplicati per lo stesso utente (mantieni solo il più recente)
    this.queue = this.queue.filter((item) => item.userId !== userId);

    // Aggiungi nuovo aggiornamento
    this.queue.push(update);

    // Ordina per priorità e timestamp
    this.queue.sort((a, b) => {
      const priorityOrder = { high: 3, normal: 2, low: 1 };
      const priorityDiff =
        priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return a.timestamp - b.timestamp;
    });

    // Limita dimensione coda
    if (this.queue.length > this.MAX_QUEUE_SIZE) {
      this.queue = this.queue.slice(0, this.MAX_QUEUE_SIZE);
      console.warn(
        `⚠️ Coda statistiche troncata a ${this.MAX_QUEUE_SIZE} elementi`
      );
    }

    this.saveQueueToStorage();
    console.log(
      `📤 Aggiunto aggiornamento statistiche alla coda: ${id} (priorità: ${priority})`
    );

    // Avvia processamento immediato se non in corso
    if (!this.isProcessing) {
      this.processQueue();
    }

    return id;
  }

  // Avvia processamento automatico
  private startProcessing(): void {
    if (this.processingTimer) {
      clearInterval(this.processingTimer);
    }

    this.processingTimer = setInterval(() => {
      if (!this.isProcessing && this.queue.length > 0) {
        this.processQueue();
      }
    }, this.PROCESSING_INTERVAL);
  }

  // Processa la coda
  async processQueue(): Promise<void> {
    if (this.isProcessing || this.queue.length === 0) {
      return;
    }

    // Controlla se la connessione è bloccata prima di processare
    await this.forceConnectionReset();

    this.isProcessing = true;
    console.log(
      `🔄 Processamento coda statistiche: ${this.queue.length} elementi`
    );

    const processedIds: string[] = [];
    const failedUpdates: QueuedStatsUpdate[] = [];

    for (const update of this.queue) {
      try {
        const success = await this.syncStatsUpdate(update);

        if (success) {
          processedIds.push(update.id);
          this.lastSuccessfulSync = Date.now(); // Aggiorna timestamp successo
          console.log(`✅ Sincronizzazione riuscita: ${update.id}`);
        } else {
          // Incrementa retry count
          update.retryCount++;

          if (update.retryCount >= update.maxRetries) {
            console.error(
              `❌ Aggiornamento fallito definitivamente: ${update.id} (${update.retryCount}/${update.maxRetries})`
            );
            processedIds.push(update.id); // Rimuovi dalla coda
          } else {
            console.warn(
              `⚠️ Retry aggiornamento: ${update.id} (${update.retryCount}/${update.maxRetries})`
            );
            failedUpdates.push(update);
          }
        }
      } catch (error) {
        console.error(`❌ Errore processamento ${update.id}:`, error);
        update.retryCount++;

        if (update.retryCount < update.maxRetries) {
          failedUpdates.push(update);
        } else {
          processedIds.push(update.id); // Rimuovi dalla coda
        }
      }

      // Pausa tra aggiornamenti per non sovraccaricare
      await new Promise((resolve) => setTimeout(resolve, 500));
    }

    // Rimuovi elementi processati con successo
    this.queue = this.queue.filter((item) => !processedIds.includes(item.id));

    // Aggiungi elementi falliti per retry
    this.queue.push(...failedUpdates);

    this.saveQueueToStorage();
    this.isProcessing = false;

    console.log(
      `✅ Processamento completato. Rimangono ${this.queue.length} elementi in coda`
    );
  }

  // Sincronizza un singolo aggiornamento
  private async syncStatsUpdate(update: QueuedStatsUpdate): Promise<boolean> {
    try {
      // Verifica che l'utente sia ancora autenticato
      const {
        data: { user },
        error: userError,
      } = await supabase.auth.getUser();

      if (userError || !user || user.id !== update.userId) {
        console.warn(
          `⚠️ Utente non autenticato per aggiornamento ${update.id}`
        );
        return false;
      }

      // Esegui aggiornamento con timeout
      const { error } = (await Promise.race([
        supabase.from("game_stats").upsert(
          {
            user_id: update.userId,
            games_played: update.stats.totalGames,
            games_won: update.stats.gamesWon,
            level: update.stats.level,
            xp: update.stats.xp,
            updated_at: new Date().toISOString(),
          },
          { onConflict: "user_id" }
        ),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error("Timeout sync stats")), 10000)
        ),
      ])) as any;

      if (error) {
        console.error(`❌ Errore DB sync ${update.id}:`, error);
        return false;
      }

      return true;
    } catch (error) {
      console.error(`❌ Errore sync ${update.id}:`, error);
      return false;
    }
  }

  // Ottieni stato coda
  getQueueStatus(): {
    queueLength: number;
    isProcessing: boolean;
    oldestUpdate: Date | null;
    priorityBreakdown: Record<string, number>;
  } {
    const priorityBreakdown = this.queue.reduce((acc, item) => {
      acc[item.priority] = (acc[item.priority] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const oldestUpdate =
      this.queue.length > 0
        ? new Date(Math.min(...this.queue.map((item) => item.timestamp)))
        : null;

    return {
      queueLength: this.queue.length,
      isProcessing: this.isProcessing,
      oldestUpdate,
      priorityBreakdown,
    };
  }

  // Forza processamento immediato
  async forceProcess(): Promise<void> {
    if (!this.isProcessing) {
      await this.processQueue();
    }
  }

  // Controlla se la connessione è bloccata
  isConnectionStale(): boolean {
    const timeSinceLastSync = Date.now() - this.lastSuccessfulSync;
    return timeSinceLastSync > this.MAX_STALE_TIME;
  }

  // Forza reset della connessione se bloccata
  async forceConnectionReset(): Promise<void> {
    if (this.isConnectionStale()) {
      console.warn("🔄 Connessione bloccata rilevata, forzando reset...");

      try {
        // Reset del client Supabase
        const { supabase, sessionManager } = await import(
          "@/integrations/supabase/client"
        );

        // Forza refresh della sessione
        await sessionManager.refreshSession();

        // Reset timestamp
        this.lastSuccessfulSync = Date.now();

        console.log("✅ Reset connessione completato");
      } catch (error) {
        console.error("❌ Errore durante reset connessione:", error);
      }
    }
  }

  // Pulisci coda
  clearQueue(): void {
    this.queue = [];
    this.saveQueueToStorage();
    console.log("🗑️ Coda statistiche pulita");
  }

  // Cleanup
  destroy(): void {
    if (this.processingTimer) {
      clearInterval(this.processingTimer);
      this.processingTimer = null;
    }
    this.isProcessing = false;
  }
}

// Esporta istanza singleton
export const statsQueue = StatsQueue.getInstance();

// Funzione helper per aggiungere aggiornamenti
export const queueStatsUpdate = (
  userId: string,
  stats: PlayerStats,
  priority: "low" | "normal" | "high" = "normal"
): string => {
  return statsQueue.addUpdate(userId, stats, priority);
};

// Funzione helper per forzare sincronizzazione
export const forceSyncStats = async (): Promise<void> => {
  await statsQueue.forceProcess();
};

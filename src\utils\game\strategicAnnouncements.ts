/**
 * 🗣️ Sistema Dichiarazioni Strategiche - Parole
 *
 * Implementa la comunicazione strategica tra compagni attraverso:
 * - Bus<PERSON>: "Prendi questa mano e la prossima gioca ancora questo seme che prendo io!"
 * - St<PERSON>cio: "Ne ho ancora una di questo seme"
 * - Volo: "Non ho più carte di questo seme, ora posso tagliare con briscola"
 *
 * 🚫 REGOLE TEMPORALI:
 * - Gli annunci strategici sono consentiti SOLO nei primi 5 turni di una mano
 * - Dopo il turno 5, non è più possibile fare dichiarazioni strategiche
 *
 * 🎯 REGOLE BUSSO:
 * - Quando un compagno chiama "busso", il teammate deve cercare di prendere la presa
 * - Priorità alla carta più alta del seme dichiarato, poi briscole se necessario
 */

import { Card, Suit, Rank } from "./cardUtils";
import { GameState, Player } from "./gameLogic";

export type StrategicAnnouncement = "busso" | "striscio" | "volo" | null;

export interface AnnouncementInfo {
  type: StrategicAnnouncement;
  playerIndex: number;
  suit: Suit;
  trickNumber: number;
}

// Interfaccia per la memoria AI delle dichiarazioni
interface AnnouncementMemory extends AnnouncementInfo {
  timestamp: number;
}

// Interfaccia per estendere Window con proprietà AI
interface ExtendedWindow extends Window {
  aiMemory?: {
    strategicAnnouncements?: AnnouncementMemory[];
  };
}

/**
 * 🎯 Analizza se un giocatore dovrebbe fare una dichiarazione strategica
 * Solo il primo giocatore del turno può fare dichiarazioni
 */
export const shouldMakeStrategicAnnouncement = (
  player: Player,
  gameState: GameState,
  intendedCard: Card
): StrategicAnnouncement => {
  // Solo il primo giocatore del turno può dichiarare
  if (gameState.currentTrick.length > 0) {
    return null;
  }
  // Non dichiarare nel primo turno (troppo presto)
  if (gameState.trickNumber === 1) {
    return null;
  }

  // 🚫 REGOLA: Annunci strategici consentiti solo nei primi 5 turni
  if (gameState.trickNumber > 5) {
    return null;
  }

  const suitToAnalyze = intendedCard.suit;
  const cardsInSuit = player.hand.filter((card) => card.suit === suitToAnalyze);
  const trumpCards = gameState.trumpSuit
    ? player.hand.filter((card) => card.suit === gameState.trumpSuit)
    : [];
  // 🚫 VOLO: "Non ho più carte di questo seme, ora posso tagliare con briscola"
  // Condizioni: questa è l'ULTIMA carta di questo seme che ho in mano E ho briscole per tagliare
  if (cardsInSuit.length === 1 && trumpCards.length > 0) {
    return "volo";
  }

  // 📢 BUSSO: "Prendi questa mano e la prossima gioca ancora questo seme che prendo io!"
  // 🎯 REGOLA CORRETTA: sto giocando una carta di un seme di cui ho il 2, ma NON sto giocando il 2 stesso
  const hasTwoOfThisSuit = cardsInSuit.some((card) => card.rank === Rank.Two);
  const hasThreeOfThisSuit = cardsInSuit.some(
    (card) => card.rank === Rank.Three
  );
  const hasAceOfThisSuit = cardsInSuit.some((card) => card.rank === Rank.Ace);
  const isPlayingSuitWithTwo =
    hasTwoOfThisSuit &&
    intendedCard.suit === suitToAnalyze &&
    intendedCard.rank !== Rank.Two; // 🎯 NON deve essere il 2 stesso!

  if (
    isPlayingSuitWithTwo &&
    !hasThreeOfThisSuit &&
    !hasAceOfThisSuit &&
    cardsInSuit.length >= 2
  ) {
    console.log(
      `[BUSSO] 🎯 Gioco ${intendedCard.rank} di ${intendedCard.suit} - ho il 2 dello stesso seme!`
    );
    return "busso";
  }

  // 🎯 STRISCIO: "Ne ho ancora una di questo seme"
  // Condizioni: ho esattamente 2 carte di questo seme (dopo aver giocato questa ne avrò 1)
  if (cardsInSuit.length === 2) {
    return "striscio";
  }

  return null;
};

/**
 * 🧠 Valuta strategicamente quale dichiarazione fare in base alla situazione
 */
export const evaluateStrategicAnnouncement = (
  player: Player,
  gameState: GameState,
  intendedCard: Card,
  difficulty: "easy" | "medium" | "hard"
): StrategicAnnouncement => {
  const basicAnnouncement = shouldMakeStrategicAnnouncement(
    player,
    gameState,
    intendedCard
  );

  if (!basicAnnouncement) {
    return null;
  }

  // Probabilità di dichiarare in base alla difficoltà
  const announcementProbability = {
    easy: 0.4, // 40% probabilità
    medium: 0.7, // 70% probabilità
    hard: 0.9, // 90% probabilità
  };

  if (Math.random() > announcementProbability[difficulty]) {
    return null; // Non dichiara
  }

  // Valutazione strategica specifica per tipo di dichiarazione
  switch (basicAnnouncement) {
    case "busso":
      // BUSSO è molto strategico - fallo solo se ha senso
      return evaluateBussoStrategy(player, gameState, intendedCard)
        ? "busso"
        : null;

    case "volo":
      // VOLO è informativo - quasi sempre utile dichiararlo
      return Math.random() < 0.8 ? "volo" : null;

    case "striscio":
      // STRISCIO è neutro - moderatamente utile
      return Math.random() < 0.6 ? "striscio" : null;

    default:
      return null;
  }
};

/**
 * 🎯 Valuta se la strategia BUSSO è efficace
 */
const evaluateBussoStrategy = (
  player: Player,
  gameState: GameState,
  intendedCard: Card
): boolean => {
  const suitCards = player.hand.filter(
    (card) => card.suit === intendedCard.suit
  );
  const hasTwo = suitCards.some((card) => card.rank === Rank.Two);
  // Fix: annulla SEMPRE se non hai il 2 del seme, anche se chiamata erroneamente
  if (!hasTwo) return false;

  // Non dichiarare BUSSO se siamo oltre i primi 5 turni (regola aggiornata)
  if (gameState.trickNumber > 5) return false;

  // Non dichiarare se il compagno è molto lontano nella rotazione
  const teammateIndex = gameState.players.findIndex(
    (p) => p.team === player.team && p.id !== player.id
  );

  if (teammateIndex === -1) return false;

  // Più efficace se il compagno gioca presto nel turno
  const currentPlayerIndex = gameState.currentPlayer;
  const distanceToTeammate = (teammateIndex - currentPlayerIndex + 4) % 4;

  return distanceToTeammate <= 2; // Compagno gioca entro 2 posizioni
};

/**
 * 📝 Aggiorna la memoria AI con le informazioni dalla dichiarazione
 */
export const updateAIMemoryWithAnnouncement = (
  announcement: AnnouncementInfo,
  gameState: GameState
): void => {
  console.log(
    `[STRATEGIC ANNOUNCEMENTS] 📢 ${
      gameState.players[announcement.playerIndex].name
    } dichiara: ${announcement.type.toUpperCase()}`
  );

  // Aggiorna la memoria globale dell'AI
  if (typeof window !== "undefined" && (window as ExtendedWindow).aiMemory) {
    const aiMemory = (window as ExtendedWindow).aiMemory!;

    if (!aiMemory.strategicAnnouncements) {
      aiMemory.strategicAnnouncements = [];
    }

    aiMemory.strategicAnnouncements.push({
      ...announcement,
      timestamp: Date.now(),
    });

    // Mantieni solo le dichiarazioni recenti (ultimi 3 turni)
    const currentTrick = gameState.trickNumber;
    aiMemory.strategicAnnouncements = aiMemory.strategicAnnouncements.filter(
      (ann: AnnouncementMemory) => currentTrick - ann.trickNumber <= 3
    );
  }
};

/**
 * 🤝 Ottieni le dichiarazioni rilevanti per la strategia corrente
 */
export const getRelevantAnnouncements = (
  gameState: GameState,
  playerIndex: number
): AnnouncementInfo[] => {
  if (typeof window === "undefined" || !(window as ExtendedWindow).aiMemory) {
    return [];
  }

  const aiMemory = (window as ExtendedWindow).aiMemory!;
  if (!aiMemory.strategicAnnouncements) {
    return [];
  }

  const currentTrick = gameState.trickNumber;

  return aiMemory.strategicAnnouncements.filter((ann: AnnouncementMemory) => {
    // Solo dichiarazioni recenti (ultimi 2 turni)
    if (currentTrick - ann.trickNumber > 2) return false;

    // Filtra in base alla relazione con il giocatore
    const announcer = gameState.players[ann.playerIndex];
    const currentPlayer = gameState.players[playerIndex];

    // Se siamo compagni, tutte le dichiarazioni sono rilevanti
    if (announcer.team === currentPlayer.team) return true;

    // Se siamo avversari, solo alcune dichiarazioni ci interessano
    return ann.type === "volo"; // Gli avversari possono intuire il "volo"
  });
};

/**
 * 🎮 Applica la strategia basata sulle dichiarazioni del compagno
 */
export const applyTeammateAnnouncementStrategy = (
  availableCards: Card[],
  gameState: GameState,
  playerIndex: number
): {
  recommendedCards: Card[];
  strategy: string;
} => {
  const announcements = getRelevantAnnouncements(gameState, playerIndex);
  const currentPlayer = gameState.players[playerIndex];

  // Filtra solo le dichiarazioni del compagno
  const teammateAnnouncements = announcements.filter(
    (ann) =>
      gameState.players[ann.playerIndex].team === currentPlayer.team &&
      ann.playerIndex !== playerIndex
  );

  if (teammateAnnouncements.length === 0) {
    return {
      recommendedCards: availableCards,
      strategy: "Nessuna dichiarazione del compagno",
    };
  }

  const latestAnnouncement =
    teammateAnnouncements[teammateAnnouncements.length - 1];

  switch (latestAnnouncement.type) {
    case "busso":
      // Il compagno vuole che prendiamo e rigiochiamo il suo seme
      return handleBussoStrategy(availableCards, gameState, latestAnnouncement);

    case "striscio":
      // Il compagno ha ancora una carta di quel seme
      return handleStriscioStrategy(
        availableCards,
        gameState,
        latestAnnouncement
      );

    case "volo":
      // Il compagno può tagliare quel seme
      return handleVoloStrategy(availableCards, gameState, latestAnnouncement);

    default:
      return {
        recommendedCards: availableCards,
        strategy: "Dichiarazione non riconosciuta",
      };
  }
};

/**
 * 📢 Gestisce la strategia per dichiarazione BUSSO del compagno
 */
const handleBussoStrategy = (
  availableCards: Card[],
  gameState: GameState,
  announcement: AnnouncementInfo
): { recommendedCards: Card[]; strategy: string } => {
  const announcementSuit = announcement.suit;

  // 🎯 REGOLA BUSSO: Il compagno deve cercare di prendere con la carta più alta del seme
  // Priorità 1: Carte del seme dell'annuncio (3, 2, Asso in ordine di preferenza)
  const announcementSuitCards = availableCards.filter(
    (card) => card.suit === announcementSuit
  );

  if (announcementSuitCards.length > 0) {
    // Ordina le carte del seme per forza decrescente (3 > 2 > A > K > H > J > 7 > 6 > 5 > 4)
    const sortedSuitCards = announcementSuitCards.sort((a, b) => {
      const getCardOrder = (card: Card): number => {
        const order: Record<string, number> = {
          "3": 10,
          "2": 9,
          A: 8,
          K: 7,
          H: 6,
          J: 5,
          "7": 4,
          "6": 3,
          "5": 2,
          "4": 1,
        };
        return order[card.rank] || 0;
      };
      return getCardOrder(b) - getCardOrder(a);
    });

    const strongestSuitCard = sortedSuitCards[0];

    return {
      recommendedCards: [strongestSuitCard],
      strategy: `BUSSO: Prendo con ${strongestSuitCard.rank} di ${announcementSuit} (carta più forte del seme)`,
    };
  }

  // Priorità 2: Se non abbiamo carte del seme, usiamo briscole per prendere
  const trumpCards = availableCards.filter(
    (card) => card.suit === gameState.trumpSuit
  );
  if (trumpCards.length > 0) {
    // Usa la briscola più debole che può vincere
    const sortedTrumps = trumpCards.sort((a, b) => {
      const getCardOrder = (card: Card): number => {
        const order: Record<string, number> = {
          "3": 10,
          "2": 9,
          A: 8,
          K: 7,
          H: 6,
          J: 5,
          "7": 4,
          "6": 3,
          "5": 2,
          "4": 1,
        };
        return order[card.rank] || 0;
      };
      return getCardOrder(a) - getCardOrder(b); // Più debole prima
    });

    return {
      recommendedCards: [sortedTrumps[0]],
      strategy: `BUSSO: Prendo con briscola ${sortedTrumps[0].rank} (non ho carte del seme ${announcementSuit})`,
    };
  }

  // Priorità 3: Se non possiamo prendere, cerchiamo comunque di cooperare
  const strongCards = availableCards.filter((card) => {
    if (card.suit === gameState.trumpSuit) return true; // Briscole
    if (card.suit === announcementSuit) {
      return (
        card.rank === Rank.Three ||
        card.rank === Rank.Two ||
        card.rank === Rank.Ace
      );
    }
    return false;
  });

  if (strongCards.length > 0) {
    return {
      recommendedCards: strongCards,
      strategy: `BUSSO: Prendo la mano per collaborare con il compagno (seme: ${announcementSuit})`,
    };
  }

  return {
    recommendedCards: availableCards,
    strategy: "BUSSO: Non posso prendere la mano",
  };
};

/**
 * 🎯 Gestisce la strategia per dichiarazione STRISCIO del compagno
 */
const handleStriscioStrategy = (
  availableCards: Card[],
  gameState: GameState,
  announcement: AnnouncementInfo
): { recommendedCards: Card[]; strategy: string } => {
  // Il compagno ha ancora una carta di quel seme - giochiamo normalmente
  return {
    recommendedCards: availableCards,
    strategy: `STRISCIO: Il compagno ha ancora carte in ${announcement.suit}`,
  };
};

/**
 * 🚁 Gestisce la strategia per dichiarazione VOLO del compagno
 */
const handleVoloStrategy = (
  availableCards: Card[],
  gameState: GameState,
  announcement: AnnouncementInfo
): { recommendedCards: Card[]; strategy: string } => {
  const announcementSuit = announcement.suit;

  // Se abbiamo carte di quel seme, consideriamo che il compagno può tagliare
  const suitCards = availableCards.filter(
    (card) => card.suit === announcementSuit
  );

  if (suitCards.length > 0 && gameState.leadSuit === announcementSuit) {
    // Possiamo dare punti al compagno sapendo che taglierà
    const valueCards = suitCards.filter(
      (card) =>
        card.rank === Rank.Ace ||
        card.rank === Rank.King ||
        card.rank === Rank.Horse ||
        card.rank === Rank.Jack
    );

    if (valueCards.length > 0) {
      return {
        recommendedCards: valueCards,
        strategy: `VOLO: Do punti al compagno che tagliarà (seme: ${announcementSuit})`,
      };
    }
  }

  return {
    recommendedCards: availableCards,
    strategy: `VOLO: Il compagno può tagliare ${announcementSuit}`,
  };
};

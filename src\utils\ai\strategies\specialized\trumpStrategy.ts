import { GameState } from "../../../game/gameLogic";
import { Card } from "../../../game/cardUtils";
import { CardMemory, TrumpExhaustionResult } from "../../types";
import { getCardStrengthScore, getCardValue } from "../../utils/cardUtils";
import { analyzeCardMemory, createSafeCardMemory } from "../../memory/analyzer";

export const getTrumpPlayingStrategy = (
  trumpCards: Card[],
  state: GameState,
  trickNumber: number,
  totalTricks: number
): Card | null => {
  if (trumpCards.length === 0) return null;

  // Conserva le briscole migliori per la fine
  const isEndGame = trickNumber >= totalTricks - 2;
  const isLastTrick = trickNumber === totalTricks;

  // Ordina le briscole dalla più debole alla più forte
  const sortedTrumps = [...trumpCards].sort(
    (a, b) => getCardStrengthScore(a) - getCardStrengthScore(b)
  );

  // Identifica le briscole "top" (3, 2, Asso se il 3 è uscito)
  const topTrumps = identifyTopTrumps(trumpCards, state);

  if (isLastTrick && topTrumps.length > 0) {
    // Ultima presa: usa la briscola migliore
    return topTrumps[topTrumps.length - 1];
  } else if (isEndGame && topTrumps.length > 0) {
    // Fine partita: conserva le migliori, usa quelle medio-basse
    const nonTopTrumps = sortedTrumps.filter(
      (trump) => !topTrumps.includes(trump)
    );
    if (nonTopTrumps.length > 0) {
      return nonTopTrumps[0]; // Più debole tra le non-top
    }
  }

  // Strategia normale: usa dalla più debole
  return sortedTrumps[0];
};

export const identifyTopTrumps = (
  trumpCards: Card[],
  state: GameState
): Card[] => {
  try {
    const memory = analyzeCardMemory(state);
    const trumpsPlayed =
      memory.playedBySuit[state.trumpSuit?.toString() || ""] || [];

    const availableTopTrumps: Card[] = [];

    // Il 3 è sempre top se lo abbiamo
    const threeOfTrump = trumpCards.find((card) => card.rank === "3");
    if (threeOfTrump) availableTopTrumps.push(threeOfTrump);

    // Il 2 è top se il 3 è già uscito o se lo abbiamo anche noi
    const twoOfTrump = trumpCards.find((card) => card.rank === "2");
    const threeAlreadyPlayed = trumpsPlayed.some((card) => card.rank === "3");
    if (twoOfTrump && (threeAlreadyPlayed || threeOfTrump)) {
      availableTopTrumps.push(twoOfTrump);
    }

    // L'Asso è top se 3 e 2 sono già usciti o li abbiamo
    const aceOfTrump = trumpCards.find((card) => card.rank === "A");
    const twoAlreadyPlayed = trumpsPlayed.some((card) => card.rank === "2");
    if (aceOfTrump && threeAlreadyPlayed && twoAlreadyPlayed) {
      availableTopTrumps.push(aceOfTrump);
    }

    // Conserva anche Re e Cavallo se siamo in endgame e sono le carte più alte rimaste
    const trickNumber = state.trickNumber ?? 1;
    if (trickNumber >= 8) {
      const kingOfTrump = trumpCards.find((card) => card.rank === "K");
      const horseOfTrump = trumpCards.find((card) => card.rank === "H");

      // Aggiungi Re se non ci sono più carte superiori in circolazione
      if (kingOfTrump && threeAlreadyPlayed && twoAlreadyPlayed) {
        const aceAlreadyPlayed = trumpsPlayed.some((card) => card.rank === "A");
        if (aceAlreadyPlayed || !aceOfTrump) {
          availableTopTrumps.push(kingOfTrump);
        }
      }

      // Aggiungi Cavallo se necessario
      if (horseOfTrump && availableTopTrumps.length === 0) {
        availableTopTrumps.push(horseOfTrump);
      }
    }

    return availableTopTrumps;
  } catch (error) {
    // Fallback conservativo: considera solo le carte più forti disponibili
    const rankedTrumps = trumpCards.sort(
      (a, b) => getCardStrengthScore(b) - getCardStrengthScore(a)
    );
    return rankedTrumps.length > 0 ? [rankedTrumps[0]] : [];
  }
};

export const getTrumpExhaustionStrategy = (
  trumpCards: Card[],
  state: GameState,
  memory: CardMemory,
  trickNumber: number,
  totalTricks: number
): TrumpExhaustionResult => {
  if (trumpCards.length < 4) {
    return { shouldExhaust: false };
  }

  // Stima briscole avversarie rimanenti
  const opponentTrumpsEstimate =
    memory.trumpsRemaining.length - trumpCards.length;

  // Strategia efficace solo se abbiamo molte briscole e siamo nelle prime prese
  const shouldExhaust =
    trumpCards.length >= 4 &&
    trickNumber <= 6 &&
    opponentTrumpsEstimate >= 2 &&
    opponentTrumpsEstimate <= trumpCards.length;

  if (shouldExhaust) {
    // Usa briscole medio-basse per svuotare gli avversari
    const mediumTrumps = trumpCards
      .filter((card) => {
        const strength = getCardStrengthScore(card);
        return strength >= 4 && strength <= 7; // Esclude le più deboli e le più forti
      })
      .sort((a, b) => getCardStrengthScore(a) - getCardStrengthScore(b));

    if (mediumTrumps.length > 0) {
      return { shouldExhaust: true, cardToPlay: mediumTrumps[0] };
    }

    // Se non hai briscole medie, usa la più debole
    const weakestTrump = trumpCards.sort(
      (a, b) => getCardStrengthScore(a) - getCardStrengthScore(b)
    )[0];

    return { shouldExhaust: true, cardToPlay: weakestTrump };
  }

  return { shouldExhaust: false };
};

// REGOLA 3 MIGLIORATA: Conservazione intelligente delle briscole alte
export const getSmartTrumpConservationStrategy = (
  trumpCards: Card[],
  state: GameState,
  memory: CardMemory,
  trickNumber: number,
  totalTricks: number
): { shouldConserve: boolean; recommendedCard: Card | null } => {
  if (trumpCards.length === 0) {
    return { shouldConserve: false, recommendedCard: null };
  }

  const gamePhase =
    trickNumber <= 3 ? "early" : trickNumber <= 7 ? "middle" : "late";
  const topTrumps = identifyTopTrumps(trumpCards, state);
  const regularTrumps = trumpCards.filter((card) => !topTrumps.includes(card));

  // NUOVA LOGICA: Valuta il valore della presa attuale
  const currentTrickValue = state.currentTrick.reduce(
    (sum, card) => sum + getCardValue(card),
    0
  );
  const isLastTrick = trickNumber === 10;
  const isValuableTrick = currentTrickValue >= 1 || isLastTrick;

  // Valuta la situazione delle briscole rimanenti nel gioco
  const trumpsStillInGame = analyzeTrumpsInGame(state, memory);
  const opponentsLikelyHaveTrumps = trumpsStillInGame.unknownTrumps > 2;

  // REGOLA PRINCIPALE: Non sprecare briscole alte su prese con pochi punti
  if (!isValuableTrick && topTrumps.length > 0) {
    // Presa con poco valore: conserva SEMPRE le briscole alte
    if (regularTrumps.length > 0) {
      // Usa la briscola più debole tra quelle normali
      const weakestRegular = regularTrumps.sort(
        (a, b) => getCardStrengthScore(a) - getCardStrengthScore(b)
      )[0];
      return { shouldConserve: true, recommendedCard: weakestRegular };
    }

    // Se hai solo briscole top, evita di usarle per prese senza valore
    return { shouldConserve: true, recommendedCard: null };
  }

  // Strategia conservativa rafforzata nelle prime fasi
  if (gamePhase === "early" && topTrumps.length > 0) {
    // Nelle prime 3 prese, conserva le briscole top a meno che la presa non sia molto preziosa
    if (regularTrumps.length > 0) {
      // Usa briscole normali se disponibili
      const weakestRegular = regularTrumps.sort(
        (a, b) => getCardStrengthScore(a) - getCardStrengthScore(b)
      )[0];
      return { shouldConserve: true, recommendedCard: weakestRegular };
    }

    // Se hai solo briscole top, usale solo per prese molto preziose
    if (currentTrickValue >= 2 || isLastTrick) {
      const leastValuableTop = topTrumps.sort(
        (a, b) => getCardStrengthScore(a) - getCardStrengthScore(b)
      )[0];
      return { shouldConserve: true, recommendedCard: leastValuableTop };
    }

    // Presa non abbastanza preziosa: non usare briscole top
    return { shouldConserve: true, recommendedCard: null };
  }
  // Strategia media fase: equilibrio tra conservazione e uso strategico
  if (gamePhase === "middle") {
    // Anche in fase media, evita briscole top per prese con poco valore
    if (!isValuableTrick && topTrumps.length > 0) {
      if (regularTrumps.length > 0) {
        const weakestRegular = regularTrumps.sort(
          (a, b) => getCardStrengthScore(a) - getCardStrengthScore(b)
        )[0];
        return { shouldConserve: true, recommendedCard: weakestRegular };
      }
      // Nessuna briscola normale: non usare quelle top per prese senza valore
      return { shouldConserve: true, recommendedCard: null };
    }

    // Se gli avversari probabilmente hanno ancora briscole, sii più conservativo
    if (opponentsLikelyHaveTrumps && topTrumps.length > 0) {
      if (regularTrumps.length > 0) {
        return {
          shouldConserve: true,
          recommendedCard: regularTrumps[0],
        };
      }

      // Usa briscole top solo per prese preziose in fase media
      if (currentTrickValue >= 1.5 || isLastTrick) {
        const leastValuableTop = topTrumps.sort(
          (a, b) => getCardStrengthScore(a) - getCardStrengthScore(b)
        )[0];
        return { shouldConserve: true, recommendedCard: leastValuableTop };
      }
    }
  }
  // Fase finale: usa le briscole strategicamente ma sempre valutando il valore
  if (gamePhase === "late") {
    // Ultime 3 prese: sii più aggressivo ma sempre sensato
    if (topTrumps.length > 0) {
      // Anche in fase tardiva, evita sprechi su prese senza valore
      if (!isValuableTrick && !isLastTrick) {
        if (regularTrumps.length > 0) {
          const weakestRegular = regularTrumps.sort(
            (a, b) => getCardStrengthScore(a) - getCardStrengthScore(b)
          )[0];
          return { shouldConserve: true, recommendedCard: weakestRegular };
        }
        // Evita briscole top anche in fase tardiva se la presa non ha valore
        return { shouldConserve: true, recommendedCard: null };
      }
      // Usa briscole top per prese preziose o ultima presa
      const bestTopTrump = topTrumps.sort(
        (a, b) => getCardStrengthScore(b) - getCardStrengthScore(a)
      )[0];
      return { shouldConserve: false, recommendedCard: bestTopTrump };
    }
  }

  // Default: usa la briscola più debole disponibile
  const weakestTrump = trumpCards.sort(
    (a, b) => getCardStrengthScore(a) - getCardStrengthScore(b)
  )[0];

  return { shouldConserve: false, recommendedCard: weakestTrump };
};

// Analizza le briscole rimanenti nel gioco
const analyzeTrumpsInGame = (state: GameState, memory: CardMemory) => {
  const trumpSuit = state.trumpSuit;
  if (!trumpSuit) return { unknownTrumps: 0, strongTrumpsRemaining: 0 };

  const totalTrumpsInDeck = 10; // 10 carte per seme
  const trumpsPlayed = memory.playedBySuit[trumpSuit] || [];
  const trumpsPlayedCount = trumpsPlayed.length;

  // Stima delle briscole ancora in gioco (non nella nostra mano)
  const unknownTrumps = totalTrumpsInDeck - trumpsPlayedCount;

  // Conta le briscole forti ancora in circolazione
  const strongRanks = ["3", "2", "A"];
  const strongTrumpsRemaining = strongRanks.filter(
    (rank) => !trumpsPlayed.some((card) => card.rank === rank)
  ).length;

  return { unknownTrumps, strongTrumpsRemaining };
};

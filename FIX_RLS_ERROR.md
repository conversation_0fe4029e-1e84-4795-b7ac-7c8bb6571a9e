# 🚨 ERRORE RLS - RIS<PERSON>UZIONE IMMEDIATA

## Problema

```
POST https://bzqlnxftjcfcrfopcjlf.supabase.co/rest/v1/profiles?on_conflict=id 403 (Forbidden)
Errore: new row violates row-level security policy for table "profiles"
```

## ✅ SOLUZIONE RAPIDA

### 1. Vai su Supabase Dashboard

- Apri [https://app.supabase.com/](https://app.supabase.com/)
- Seleziona il progetto `bzqlnxftjcfcrfopcjlf`

### 2. Configura le Policies

- Vai su **SQL Editor**
- Copia e incolla questo codice:

```sql
-- Abilita RLS sulla tabella profiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Policy per leggere il proprio profilo
CREATE POLICY "Users can view own profile" ON profiles
FOR SELECT USING (auth.uid() = id);

-- Policy per inserire il proprio profilo
CREATE POLICY "Users can insert own profile" ON profiles
FOR INSERT WITH CHECK (auth.uid() = id);

-- Policy per aggiornare il proprio profilo
CREATE POLICY "Users can update own profile" ON profiles
FOR UPDATE USING (auth.uid() = id);
```

### 3. Esegui il codice

- Clicca **RUN** nel SQL Editor
- Dovresti vedere "Success. No rows returned"

### 4. Testa l'app

- Torna all'app e prova di nuovo il login
- La sincronizzazione dovrebbe funzionare ora

## 🔧 CONFIGURAZIONE PROVIDER OAUTH

Se ricevi errore `provider is not enabled`:

### Google OAuth

1. **Supabase Dashboard** → **Authentication** → **Providers**
2. Abilita **Google**
3. Aggiungi:
   - **Client ID**: dalla Google Cloud Console
   - **Client Secret**: dalla Google Cloud Console

### Facebook OAuth

1. **Supabase Dashboard** → **Authentication** → **Providers**
2. Abilita **Facebook**
3. Aggiungi:
   - **App ID**: da Facebook Developers
   - **App Secret**: da Facebook Developers

## 📚 Documentazione Completa

Vedi `docs/OAUTH_SETUP.md` per istruzioni dettagliate.

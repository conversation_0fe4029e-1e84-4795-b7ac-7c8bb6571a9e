-- Script SQL per configurare le Row Level Security Policies per le tabelle friendships e friend_requests
-- Eseguire questo script nel SQL Editor del Dashboard Supabase

-- ====================================================================
-- CONFIGURAZIONE RLS PER TABELLA FRIENDSHIPS (amicizie confermate)
-- ====================================================================

-- 1. <PERSON>bilita RLS sulla tabella friendships
ALTER TABLE friendships ENABLE ROW LEVEL SECURITY;

-- 2. Elimina eventuali policy esistenti (opzionale, se necessario riconfigurare)
DROP POLICY IF EXISTS "Users can view own friendships" ON friendships;
DROP POLICY IF EXISTS "Users can insert own friendships" ON friendships;
DROP POLICY IF EXISTS "Users can delete own friendships" ON friendships;

-- 3. Policy per permettere agli utenti di leggere le proprie relazioni di amicizia
-- Un utente può vedere le amicizie dove è user_id o friend_id
CREATE POLICY "Users can view own friendships" ON friendships
FOR SELECT USING (
  auth.uid() = user_id OR auth.uid() = friend_id
);

-- 4. Policy per permettere agli utenti di inserire amicizie confermate
-- Un utente può inserire una amicizia dove è user_id (chi conferma)
CREATE POLICY "Users can insert own friendships" ON friendships
FOR INSERT WITH CHECK (
  auth.uid() = user_id
);

-- 5. Policy per permettere agli utenti di aggiornare le proprie relazioni di amicizia
-- Un utente può aggiornare una amicizia dove è coinvolto
CREATE POLICY "Users can update own friendships" ON friendships
FOR UPDATE USING (
  auth.uid() = user_id OR auth.uid() = friend_id
);

-- 6. Policy per permettere agli utenti di eliminare le proprie relazioni di amicizia
-- CRITICO: Un utente può eliminare qualsiasi relazione dove è coinvolto
-- sia come user_id che come friend_id
CREATE POLICY "Users can delete own friendships" ON friendships
FOR DELETE USING (
  auth.uid() = user_id OR auth.uid() = friend_id
);

-- ====================================================================
-- CONFIGURAZIONE RLS PER TABELLA FRIEND_REQUESTS (richieste di amicizia)
-- ====================================================================

-- 7. Abilita RLS sulla tabella friend_requests
ALTER TABLE friend_requests ENABLE ROW LEVEL SECURITY;

-- 8. Elimina eventuali policy esistenti (opzionale, se necessario riconfigurare)
DROP POLICY IF EXISTS "Users can view own friend_requests" ON friend_requests;
DROP POLICY IF EXISTS "Users can insert own friend_requests" ON friend_requests;
DROP POLICY IF EXISTS "Users can update own friend_requests" ON friend_requests;
DROP POLICY IF EXISTS "Users can delete own friend_requests" ON friend_requests;

-- 9. Policy per permettere agli utenti di leggere le proprie richieste di amicizia
-- Un utente può vedere le richieste dove è sender_id (inviate) o receiver_id (ricevute)
CREATE POLICY "Users can view own friend_requests" ON friend_requests
FOR SELECT USING (
  auth.uid() = sender_id OR auth.uid() = receiver_id
);

-- 10. Policy per permettere agli utenti di inserire richieste di amicizia
-- Un utente può inserire una richiesta dove è sender_id (chi invia)
CREATE POLICY "Users can insert own friend_requests" ON friend_requests
FOR INSERT WITH CHECK (
  auth.uid() = sender_id
);

-- 11. Policy per permettere agli utenti di aggiornare le proprie richieste di amicizia
-- Un utente può aggiornare (accettare/rifiutare) una richiesta dove è receiver_id (chi riceve)
-- o aggiornare una richiesta che ha inviato (dove è sender_id)
CREATE POLICY "Users can update own friend_requests" ON friend_requests
FOR UPDATE USING (
  auth.uid() = sender_id OR auth.uid() = receiver_id
);

-- 12. Policy per permettere agli utenti di eliminare le proprie richieste di amicizia
-- Un utente può eliminare richieste dove è coinvolto (sia inviate che ricevute)
CREATE POLICY "Users can delete own friend_requests" ON friend_requests
FOR DELETE USING (
  auth.uid() = sender_id OR auth.uid() = receiver_id
);

-- ====================================================================
-- VERIFICA E TEST DELLE POLICY
-- ====================================================================

-- 13. Verifica che le policy siano state create correttamente
SELECT schemaname, tablename, policyname, cmd, qual, with_check
FROM pg_policies 
WHERE tablename IN ('friendships', 'friend_requests')
ORDER BY tablename, policyname;

-- 14. Test delle policy - eseguire per verificare che funzionino
-- (Sostituire gli UUID con ID reali per testare)
/*
-- Test SELECT friendships (deve restituire solo le relazioni dell'utente corrente)
SELECT * FROM friendships;

-- Test DELETE friendships (deve permettere di eliminare relazioni dove l'utente è coinvolto)
DELETE FROM friendships 
WHERE (user_id = auth.uid() AND friend_id = 'uuid-amico')
   OR (user_id = 'uuid-amico' AND friend_id = auth.uid());

-- Test SELECT friend_requests (deve restituire solo le richieste dell'utente corrente)
SELECT * FROM friend_requests;

-- Test DELETE friend_requests (deve permettere di eliminare richieste dove l'utente è coinvolto)
DELETE FROM friend_requests 
WHERE sender_id = auth.uid() OR receiver_id = auth.uid();
*/

-- Note importanti:
-- - La tabella friendships deve avere le colonne: user_id (uuid), friend_id (uuid), created_at (timestamp)
-- - La tabella friend_requests deve avere le colonne: sender_id (uuid), receiver_id (uuid), status (text), created_at (timestamp)
-- - user_id, friend_id, sender_id, receiver_id devono essere foreign key collegati a auth.users.id
-- - La policy di DELETE è fondamentale: permette all'utente di rimuovere qualsiasi relazione dove è coinvolto
-- - Questo risolve il problema della rimozione amici bidirezionale

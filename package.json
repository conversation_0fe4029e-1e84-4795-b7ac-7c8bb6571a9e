{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "cap:sync": "cap sync", "cap:build": "npm run build && cap sync", "cap:open:android": "cap open android", "cap:run:android": "cap run android", "cap:open:ios": "cap open ios", "cap:run:ios": "cap run ios", "cap:build:ios": "npm run build && cap sync ios"}, "dependencies": {"@capacitor-community/admob": "^7.0.3", "@capacitor-community/firebase-analytics": "^7.0.1", "@capacitor/android": "^7.2.0", "@capacitor/app": "^7.0.1", "@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/share": "^7.0.1", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@codetrix-studio/capacitor-google-auth": "^3.4.0-rc.4", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@revenuecat/purchases-capacitor": "^10.3.8", "@supabase/supabase-js": "^2.49.4", "@types/chrome": "^0.0.326", "canvas-confetti": "^1.9.2", "capacitor-app-tracking-transparency": "^2.0.1", "capacitor-rate-app": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "framer-motion": "^12.6.3", "howler": "^2.2.4", "lottie-react": "^2.4.1", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-router-dom": "^6.26.2", "sonner": "^1.5.0", "swiper": "^11.2.8", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tone": "^15.1.22", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/howler": "^2.2.12", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}
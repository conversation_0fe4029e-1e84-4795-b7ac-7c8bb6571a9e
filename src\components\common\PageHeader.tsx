import React from "react";
import { useNavigate } from "react-router-dom";
import ActionButton from "@/components/ui/ActionButton";
import { ArrowLeft, LucideIcon } from "lucide-react";

interface PageHeaderProps {
  title: string;
  icon: React.ReactNode;
  backRoute: string;
  className?: string;
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  icon,
  backRoute,
  className = "",
}) => {
  const navigate = useNavigate();

  return (
    <div
      className={`py-1.5 px-3 md:max-w-4xl md:mx-auto flex items-center gap-3 sticky top-0 z-30 border-b border-amber-300/80 shadow-lg ${className}`}
      style={{
        paddingTop: `calc(0.25rem + env(safe-area-inset-top, 0px))`,
        minHeight: 0,
        background: "linear-gradient(90deg, #fffbe8ee 0%, #ffe3b8ee 100%)",
        boxShadow: "0 2px 12px 0 #eab30822, 0 1.5px 0 #eab30833",
      }}
    >
      <ActionButton
        onClick={() => navigate(backRoute)}
        className="p-2 bg-romagna-wood/40 hover:bg-romagna-wood/60 rounded-full border border-romagna-wood/40 shadow-md backdrop-blur-sm transition-all duration-150"
      >
        <ArrowLeft className="h-5 w-5 text-romagna-darkWood" />
      </ActionButton>
      <div className="flex items-center gap-2">
        <div className="flex items-center justify-center bg-amber-200/80 rounded-lg shadow-inner p-1">
          {icon}
        </div>
        <span
          className="text-xl md:text-2xl text-amber-900 font-serif font-bold drop-shadow-sm"
          style={{
            fontFamily: "'DynaPuff', cursive",
            fontWeight: 600,
            lineHeight: 1.1,
            letterSpacing: "-0.5px",
          }}
        >
          {title}
        </span>
      </div>
      <div className="flex-1" />
    </div>
  );
};

export default PageHeader;

[{"pkg": "@capacitor-community/admob", "classpath": "com.getcapacitor.community.admob.AdMob"}, {"pkg": "@capacitor-community/firebase-analytics", "classpath": "com.getcapacitor.community.firebaseanalytics.FirebaseAnalytics"}, {"pkg": "@capacitor/app", "classpath": "com.capacitorjs.plugins.app.AppPlugin"}, {"pkg": "@capacitor/share", "classpath": "com.capacitorjs.plugins.share.SharePlugin"}, {"pkg": "@capacitor/splash-screen", "classpath": "com.capacitorjs.plugins.splashscreen.SplashScreenPlugin"}, {"pkg": "@capacitor/status-bar", "classpath": "com.capacitorjs.plugins.statusbar.StatusBarPlugin"}, {"pkg": "@codetrix-studio/capacitor-google-auth", "classpath": "com.codetrixstudio.capacitor.GoogleAuth.GoogleAuth"}, {"pkg": "@revenuecat/purchases-capacitor", "classpath": "com.revenuecat.purchases.capacitor.PurchasesPlugin"}, {"pkg": "capacitor-rate-app", "classpath": "com.capacitor.rateApp.CapacitorRateAppPlugin"}]
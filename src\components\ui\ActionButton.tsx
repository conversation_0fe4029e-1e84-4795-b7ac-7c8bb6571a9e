import React, { forwardRef } from "react";
import { cn } from "@/lib/utils";

interface ActionButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "outline" | "ghost" | "destructive";
  size?: "sm" | "md" | "lg";
  isLoading?: boolean;
  className?: string;
  children: React.ReactNode;
}

const ActionButton = forwardRef<HTMLButtonElement, ActionButtonProps>(
  (
    {
      variant = "primary",
      size = "md",
      isLoading = false,
      className,
      children,
      ...props
    },
    ref
  ) => {
    const baseStyles =
      "font-medium rounded-full transition-all duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-opacity-50";

    const variants = {
      primary:
        "bg-maraffa-primary text-white hover:bg-opacity-90 focus:ring-maraffa-primary",
      secondary:
        "bg-maraffa-secondary text-white hover:bg-opacity-90 focus:ring-maraffa-secondary",
      outline:
        "bg-transparent border border-maraffa-primary text-maraffa-primary hover:bg-maraffa-primary hover:bg-opacity-10 focus:ring-maraffa-primary",
      ghost:
        "bg-transparent text-maraffa-primary hover:bg-maraffa-primary hover:bg-opacity-10 focus:ring-maraffa-primary",
      destructive: "bg-red-600 text-white hover:bg-red-700 focus:ring-red-600",
    };

    const sizes = {
      sm: "py-1.5 px-3 text-sm",
      md: "py-2 px-4 text-base",
      lg: "py-3 px-6 text-lg",
    };

    return (
      <button
        ref={ref}
        className={cn(
          baseStyles,
          variants[variant],
          sizes[size],
          isLoading ? "opacity-70 cursor-not-allowed" : "",
          className
        )}
        disabled={isLoading || props.disabled}
        {...props}
      >
        {isLoading ? (
          <div className="flex items-center justify-center">
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
            <span>Loading...</span>
          </div>
        ) : (
          children
        )}
      </button>
    );
  }
);

ActionButton.displayName = "ActionButton";

export default ActionButton;

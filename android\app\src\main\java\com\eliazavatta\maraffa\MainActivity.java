package com.eliazavatta.maraffa;

import android.os.Bundle;
import android.os.Build;
import android.view.View;
import android.view.WindowManager;
import android.media.AudioManager;
import android.content.Context;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.view.WindowInsetsControllerCompat;
import com.getcapacitor.BridgeActivity;

public class MainActivity extends BridgeActivity {
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 🎯 ANDROID 15 COMPATIBLE - Edge-to-Edge Support
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ (API 30+) - Usa WindowInsetsController moderno
            WindowCompat.setDecorFitsSystemWindows(getWindow(), false);
            WindowInsetsControllerCompat controller = WindowCompat.getInsetsController(getWindow(), getWindow().getDecorView());
            if (controller != null) {
                controller.hide(WindowInsetsCompat.Type.systemBars());
                controller.setSystemBarsBehavior(WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE);
            }
        } else {
            // Android 10 e precedenti - Usa API legacy
            getWindow().setFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN
            );

            getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN |
                View.SYSTEM_UI_FLAG_HIDE_NAVIGATION |
                View.SYSTEM_UI_FLAG_FULLSCREEN |
                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            );
        }
    }
    
    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (hasFocus) {
            // 🎯 ANDROID 15 COMPATIBLE - Riapplica schermo intero
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Android 11+ (API 30+)
                WindowInsetsControllerCompat controller = WindowCompat.getInsetsController(getWindow(), getWindow().getDecorView());
                if (controller != null) {
                    controller.hide(WindowInsetsCompat.Type.systemBars());
                    controller.setSystemBarsBehavior(WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE);
                }
            } else {
                // Android 10 e precedenti
                getWindow().getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                    View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN |
                    View.SYSTEM_UI_FLAG_HIDE_NAVIGATION |
                    View.SYSTEM_UI_FLAG_FULLSCREEN |
                    View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                );
            }
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        // 🔇 FERMA TUTTO L'AUDIO QUANDO L'APP VA IN PAUSA
        stopAllAudioPlayback();
    }

    @Override
    public void onStop() {
        super.onStop();
        // 🔇 FERMA TUTTO L'AUDIO QUANDO L'APP SI FERMA
        stopAllAudioPlayback();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        // 🔇 FERMA TUTTO L'AUDIO QUANDO L'APP VIENE DISTRUTTA
        stopAllAudioPlayback();
    }

    /**
     * 🔇 Ferma tutto l'audio del sistema Android
     */
    private void stopAllAudioPlayback() {
        try {
            AudioManager audioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
            if (audioManager != null) {
                // Ferma tutti i flussi audio dell'app
                audioManager.abandonAudioFocus(null);

                // Invia comando JavaScript per fermare l'audio
                getBridge().getWebView().evaluateJavascript(
                    "if (window.AudioManager && window.AudioManager.getInstance) { " +
                    "window.AudioManager.getInstance().stopAllAudio().catch(() => {}); " +
                    "}", null);
            }
        } catch (Exception e) {
            // Ignora errori
        }
    }
}

/**
 * 🌐 COMPONENTE STATO CONNESSIONE
 * Mostra lo stato della connessione e delle sessioni all'utente
 */

import React from 'react';
import { useConnectionMonitor } from '@/hooks/useConnectionMonitor';
import { useAuth } from '@/context/auth-context';
import { statsQueue } from '@/services/statsQueue';
import { Wifi, WifiOff, AlertTriangle, CheckCircle, Clock } from 'lucide-react';

interface ConnectionStatusProps {
  showDetails?: boolean;
  className?: string;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ 
  showDetails = false, 
  className = '' 
}) => {
  const { isLoggedIn, isOnlineFeatureEnabled } = useAuth();
  const { connectionStatus, isHealthy, needsAttention } = useConnectionMonitor();
  const [queueStatus, setQueueStatus] = React.useState(statsQueue.getQueueStatus());

  // Aggiorna stato coda periodicamente
  React.useEffect(() => {
    const interval = setInterval(() => {
      setQueueStatus(statsQueue.getQueueStatus());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  if (!isOnlineFeatureEnabled || !isLoggedIn) {
    return null;
  }

  const getStatusIcon = () => {
    if (!connectionStatus.isOnline) {
      return <WifiOff className="w-4 h-4 text-red-500" />;
    }
    
    if (needsAttention) {
      return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    }
    
    if (isHealthy) {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    }
    
    return <Wifi className="w-4 h-4 text-blue-500" />;
  };

  const getStatusText = () => {
    if (!connectionStatus.isOnline) {
      return 'Offline';
    }
    
    if (!connectionStatus.isSessionValid) {
      return 'Sessione non valida';
    }
    
    switch (connectionStatus.connectionQuality) {
      case 'excellent':
        return 'Connessione eccellente';
      case 'good':
        return 'Connessione buona';
      case 'poor':
        return 'Connessione lenta';
      default:
        return 'Connesso';
    }
  };

  const getStatusColor = () => {
    if (!connectionStatus.isOnline) return 'text-red-500';
    if (needsAttention) return 'text-yellow-500';
    if (isHealthy) return 'text-green-500';
    return 'text-blue-500';
  };

  if (!showDetails) {
    // Versione compatta - solo icona
    return (
      <div className={`flex items-center ${className}`} title={getStatusText()}>
        {getStatusIcon()}
        {queueStatus.queueLength > 0 && (
          <div className="ml-1 flex items-center">
            <Clock className="w-3 h-3 text-orange-500" />
            <span className="text-xs text-orange-500 ml-1">{queueStatus.queueLength}</span>
          </div>
        )}
      </div>
    );
  }

  // Versione dettagliata
  return (
    <div className={`bg-white/10 backdrop-blur-sm rounded-lg p-3 ${className}`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <span className={`text-sm font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>
        
        {connectionStatus.lastCheck && (
          <span className="text-xs text-gray-400">
            {connectionStatus.lastCheck.toLocaleTimeString()}
          </span>
        )}
      </div>

      {/* Dettagli connessione */}
      <div className="space-y-1 text-xs text-gray-300">
        <div className="flex justify-between">
          <span>Qualità:</span>
          <span className={getStatusColor()}>
            {connectionStatus.connectionQuality}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Sessione:</span>
          <span className={connectionStatus.isSessionValid ? 'text-green-400' : 'text-red-400'}>
            {connectionStatus.isSessionValid ? 'Valida' : 'Non valida'}
          </span>
        </div>

        {connectionStatus.retryCount > 0 && (
          <div className="flex justify-between">
            <span>Tentativi:</span>
            <span className="text-yellow-400">{connectionStatus.retryCount}</span>
          </div>
        )}
      </div>

      {/* Stato coda sincronizzazione */}
      {queueStatus.queueLength > 0 && (
        <div className="mt-2 pt-2 border-t border-white/20">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-1">
              <Clock className="w-3 h-3 text-orange-400" />
              <span className="text-xs text-orange-400">Sincronizzazione</span>
            </div>
            <span className="text-xs text-orange-400">
              {queueStatus.queueLength} in coda
            </span>
          </div>
          
          {queueStatus.isProcessing && (
            <div className="mt-1">
              <div className="w-full bg-gray-700 rounded-full h-1">
                <div className="bg-orange-400 h-1 rounded-full animate-pulse w-1/3"></div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Indicatore di stato generale */}
      <div className="mt-2 pt-2 border-t border-white/20">
        <div className="flex items-center justify-center space-x-1">
          <div className={`w-2 h-2 rounded-full ${
            isHealthy ? 'bg-green-400' : 
            needsAttention ? 'bg-yellow-400' : 
            'bg-red-400'
          } ${isHealthy ? 'animate-pulse' : ''}`}></div>
          <span className="text-xs text-gray-300">
            {isHealthy ? 'Tutto OK' : 
             needsAttention ? 'Attenzione richiesta' : 
             'Problemi di connessione'}
          </span>
        </div>
      </div>
    </div>
  );
};

export default ConnectionStatus;

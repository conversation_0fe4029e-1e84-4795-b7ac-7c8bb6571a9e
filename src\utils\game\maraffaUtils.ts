import { Card, Suit, Rank } from "./cardUtils";
import { GameState } from "./gameLogic";

/**
 * Determines if a card should show the Maraffa indicator ("M" icon).
 * The Maraffa indicator should only be shown on the ace of trump when:
 * 1. The player has selected the trump suit for this round
 * 2. The player has A, 2, 3 of the trump suit (Maraffa)
 * 3. It's the player's ace of trump card
 * 4. It's currently the first trick of the round
 */
export const shouldShowMaraffaIndicator = (
  card: Card,
  gameState: GameState,
  playerIndex: number
): boolean => {
  // Only show if we have a trump suit selected
  if (!gameState.trumpSuit) {
    return false;
  }

  // Only show on the ace of trump
  if (card.suit !== gameState.trumpSuit || card.rank !== Rank.Ace) {
    return false;
  }

  // Only show if the current player selected the trump this round
  if (gameState.lastTrumpSelector !== playerIndex) {
    return false;
  }

  // Only show during the first trick of the round
  if (gameState.trickNumber !== 1) {
    return false;
  }

  // Only show if the player has the complete Maraffa (A, 2, 3 of trump)
  const playerHand = gameState.players[playerIndex].hand;
  const hasMaraffa = hasMaraffaInHand(playerHand, gameState.trumpSuit);

  return hasMaraffa;
};

/**
 * Checks if a player's hand contains the Maraffa (A, 2, 3 of trump suit)
 */
export const hasMaraffaInHand = (hand: Card[], trumpSuit: Suit): boolean => {
  const requiredRanks = [Rank.Ace, Rank.Two, Rank.Three];

  return requiredRanks.every((rank) =>
    hand.some((card) => card.suit === trumpSuit && card.rank === rank)
  );
};

/**
 * Alternative helper function that checks for Maraffa using string values
 * (for compatibility with existing code that uses string ranks)
 */
export const isMaraffa = (hand: Card[], trumpSuit: Suit): boolean => {
  const requiredRanks = ["A", "2", "3"];

  return requiredRanks.every((rank) =>
    hand.some((card) => card.suit === trumpSuit && card.rank === rank)
  );
};
